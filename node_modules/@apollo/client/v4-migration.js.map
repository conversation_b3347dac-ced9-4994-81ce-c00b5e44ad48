{"version": 3, "file": "v4-migration.js", "sourceRoot": "", "sources": ["../src/v4-migration.ts"], "names": [], "mappings": "AAAA;;;;;GAKG", "sourcesContent": ["/**\n * This file documents exports that have been removed from Apollo Client in 4.0.\n *\n * Executing the `removals` codemod will point removed exports to this file, where\n * docblocks will explain the removal and suggest alternatives.\n */\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport declare const ApolloConsumer: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.errors:type {\"name\":\"ApolloError\"} }\n */\nexport declare class ApolloError {}\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"Concast\"} }\n *\n * Instead of `Concast`, look into the `rxjs` [`BehaviorSubject`](https://rxjs.dev/api/index/class/BehaviorSubject) api.\n */\nexport declare class Concast {}\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"DataProxy\"} }\n *\n * You can find the types that were previously available in the `DataProxy` namespace either in the `ApolloClient` namespace or the `Cache` namespace.\n */\nexport declare const DataProxy: never;\n\n/**\n * @deprecated The `DocumentType` enum has been removed from Apollo Client 4.0, along with the `parser` API exported from `@apollo/client/react/parser`.\n *\n * This API was mostly an implementation detail and has been removed without replacement.\n */\nexport declare const DocumentType: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.renderProp:type }\n */\nexport declare const Mutation: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"ObservableSubscription\"} }\n */\nexport declare const ObservableSubscription: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"Observer\"} }\n */\nexport declare const Observer: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"OperationBatcher\", \"of\": \"`BatchLink`\"} }\n */\nexport declare const OperationBatcher: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.renderProp:type }\n */\nexport declare const Query: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"RenderPromises\", \"of\": \"`getMarkupFromTree`\"} }\n */\nexport declare const RenderPromises: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"Subscription\"} }\n */\nexport declare const Subscription: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"addNonReactiveToNamedFragments\", \"of\": \"the internal `QueryManager` class\"} }\n */\nexport declare const addNonReactiveToNamedFragments: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"asyncMap\"} }\n *\n * Consider using the `rxjs` [`mergeMap`](https://rxjs.dev/api/operators/mergeMap) operator instead.\n */\nexport declare const asyncMap: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"buildQueryFromSelectionSet\"} }\n */\nexport declare const buildQueryFromSelectionSet: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"canUseAsyncIteratorSymbol\"} }\n */\nexport declare const canUseAsyncIteratorSymbol: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"canUseLayoutEffect\"} }\n */\nexport declare const canUseLayoutEffect: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"canUseSymbol\"} }\n */\nexport declare const canUseSymbol: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"canUseWeakMap\"} }\n */\nexport declare const canUseWeakMap: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"canUseWeakSet\"} }\n */\nexport declare const canUseWeakSet: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedValue:type {\"name\":\"createMockClient\"} }\n *\n * Please create an `ApolloClient` instance with a `MockLink` manually instead.\n */\nexport declare const createMockClient: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.testingLibrary:type {\"name\":\"createSchemaFetch\"} }\n */\nexport declare const createSchemaFetch: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.testingLibrary:type {\"name\":\"createTestSchema\"} }\n */\nexport declare const createTestSchema: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"defaultCacheSizes\"} }\n */\nexport declare const defaultCacheSizes: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"fixObservableSubclass\",\"of\":\"ObservableQuery\"} }\n */\nexport declare const fixObservableSubclass: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"fromError\"} }\n */\nexport declare const fromError: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"fromPromise\"} }\n */\nexport declare const fromPromise: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"getDirectiveNames\"} }\n */\nexport declare const getDirectiveNames: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"getFragmentMaskMode\",\"of\":\"data masking\"} }\n */\nexport declare const getFragmentMaskMode: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"getInclusionDirectives\",\"of\":\"local state\"} }\n */\nexport declare const getInclusionDirectives: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"getTypenameFromResult\",\"of\":\"`InMemoryCache`\"} }\n */\nexport declare const getTypenameFromResult: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport declare const graphql: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"hasAllDirectives\"} }\n */\nexport declare const hasAllDirectives: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"hasAnyDirectives\"} }\n */\nexport declare const hasAnyDirectives: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"hasClientExports\"} }\n */\nexport declare const hasClientExports: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.errors:type {\"name\":\"isApolloError\"} }\n */\nexport declare const isApolloError: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"isApolloPayloadResult\",\"of\":\"HttpLink\"} }\n */\nexport declare const isApolloPayloadResult: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.defer:type {\"name\":\"isExecutionPatchIncrementalResult\"} }\n */\nexport declare const isExecutionPatchIncrementalResult: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.defer:type {\"name\":\"isExecutionPatchInitialResult\"} }\n */\nexport declare const isExecutionPatchInitialResult: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.defer:type {\"name\":\"isExecutionPatchResult\"} }\n */\nexport declare const isExecutionPatchResult: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"isFullyUnmaskedOperation\",\"of\":\"data masking\"} }\n */\nexport declare const isFullyUnmaskedOperation: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"isInlineFragment\"} }\n */\nexport declare const isInlineFragment: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"isStatefulPromise\"} }\n */\nexport declare const isStatefulPromise: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"itAsync\"} }\n */\nexport declare const itAsync: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"iterateObserversSafely\"} }\n */\nexport declare const iterateObserversSafely: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.defer:type {\"name\":\"mergeIncrementalData\"} }\n */\nexport declare const mergeIncrementalData: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedValue:type {\"name\":\"mockObservableLink\"} }\n */\nexport declare const mockObservableLink: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedValue:type {\"name\":\"mockSingleLink\"} }\n *\n * This utility was a wrapper around `MockLink`.\n * Please call `new MockLink(mockedResponses)` directly.\n */\nexport declare const mockSingleLink: never;\n\n/**\n * @deprecated The `operationName` function has been removed from Apollo Client 4.0, along with the `parser` API exported from `@apollo/client/react/parser`.\n *\n * This API was mostly an implementation detail and has been removed without replacement.\n */\nexport declare const operationName: never;\n\n/**\n * @deprecated The `parser` function has been removed from Apollo Client 4.0, along with the whole `@apollo/client/react/parser` entry point.\n *\n * This API was mostly an implementation detail and has been removed without replacement.\n */\nexport declare const parser: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"removeArgumentsFromDocument\"} }\n */\nexport declare const removeArgumentsFromDocument: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"removeClientSetsFromDocument\"} }\n */\nexport declare const removeClientSetsFromDocument: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"removeConnectionDirectiveFromDocument\"} }\n */\nexport declare const removeConnectionDirectiveFromDocument: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"removeFragmentSpreadFromDocument\"} }\n */\nexport declare const removeFragmentSpreadFromDocument: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedValue:type {\"name\":\"resetApolloContext\"} }\n *\n * This function was deprecated and is no longer available.\n */\nexport declare const resetApolloContext: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"serializeFetchParameter\",\"of\":\"HttpLink\"} }\n *\n * Please use `JSON.stringify` instead.\n */\nexport declare const serializeFetchParameter: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"subscribeAndCount\"} }\n */\nexport declare const subscribeAndCount: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"throwServerError\",\"of\":\"HttpLink\"} }\n *\n * Please instantiate a `ServerError` directly instead.\n */\nexport declare const throwServerError: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"tick\"} }\n */\nexport declare const tick: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.rxjs:type {\"name\":\"toPromise\"} }\n *\n * Please use the `rxjs` [`firstValueFrom`](https://rxjs.dev/api/index/function/firstValueFrom) or [`lastValueFrom`](https://rxjs.dev/api/index/function/lastValueFrom) functions instead.\n */\nexport declare const toPromise: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"transformOperation\",\"of\":\"ApolloLink.execute\"} }\n */\nexport declare const transformOperation: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"validateOperation\",\"of\":\"ApolloLink.execute\"} }\n */\nexport declare const validateOperation: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"valueToObjectRepresentation\"} }\n */\nexport declare const valueToObjectRepresentation: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"verifyDocumentType\"} }\n */\nexport declare const verifyDocumentType: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"wait\"} }\n */\nexport declare const wait: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport declare const withApollo: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"withErrorSpy\"} }\n */\nexport declare const withErrorSpy: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"withLogSpy\"} }\n */\nexport declare const withLogSpy: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport declare const withMutation: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport declare const withQuery: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport declare const withSubscription: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"withWarningSpy\"} }\n */\nexport declare const withWarningSpy: never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.renderProp:type }\n */\nexport type ApolloConsumerProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.errors:type {\"name\":\"ApolloErrorOptions\"} }\n */\nexport type ApolloErrorOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"BaseMutationOptions\"} }\n *\n * Look into `ApolloClient.MutateOptions` or `useMutation.Options` instead.\n */\nexport type BaseMutationOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"BaseQueryOptions\"} }\n *\n * Look into `ApolloClient.QueryOptions` or `useQuery.Options` instead.\n */\nexport type BaseQueryOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"BatchableRequest\"} }\n */\nexport type BatchableRequest = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type ChildDataProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type ChildMutateProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type ChildProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.errors:type {\"name\":\"ClientParseError\"} }\n */\nexport type ClientParseError = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"Masked\"} }\n */\nexport type Masked = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"MaskedDocumentNode\"} }\n */\nexport type MaskedDocumentNode = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"CommonOptions\"} }\n */\nexport type CommonOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"ConcastSourcesArray\"} }\n */\nexport type ConcastSourcesArray = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"ConcastSourcesIterable\"} }\n */\nexport type ConcastSourcesIterable = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"DataProps\"} }\n */\nexport type DataProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type DataValue = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"DirectiveInfo\"} }\n */\nexport type DirectiveInfo = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"Directives\"} }\n */\nexport type Directives = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"FetchMoreQueryOptions\"} }\n *\n * Look into `ObservableQuery.FetchMoreOptions` instead.\n */\nexport type FetchMoreQueryOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"FragmentMatcher\"} }\n */\nexport type FragmentMatcher = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"GetDirectiveConfig\"} }\n */\nexport type GetDirectiveConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"GetFragmentSpreadConfig\"} }\n */\nexport type GetFragmentSpreadConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"GetNodeConfig\"} }\n */\nexport type GetNodeConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.errors:type {\"name\":\"GraphQLErrors\"} }\n */\nexport type GraphQLErrors = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"IDocumentDefinition\"} }\n */\nexport type IDocumentDefinition = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"InclusionDirectives\"} }\n */\nexport type InclusionDirectives = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internalTesting:type {\"name\":\"IsStrictlyAny\"} }\n */\nexport type IsStrictlyAny = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"MethodKeys\"} }\n */\nexport type MethodKeys = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type MutateProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.renderProp:type }\n */\nexport type MutationComponentOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"MutationDataOptions\"} }\n *\n * Look into `ApolloClient.MutateOptions` or `useMutation.Options` instead.\n */\nexport type MutationDataOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"MutationUpdaterFn\"} }\n */\nexport type MutationUpdaterFn = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.errors:type {\"name\":\"NetworkError\"} }\n */\nexport type NetworkError = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"ObservableQueryFields\"} }\n */\nexport type ObservableQueryFields = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"OnlyRequiredProperties\"} }\n */\nexport type OnlyRequiredProperties = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type OperationOption = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type OptionProps = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"PromiseWithState\"} }\n */\nexport type PromiseWithState = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"PureQueryOptions\"} }\n */\nexport type PureQueryOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.renderProp:type }\n */\nexport type QueryComponentOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type QueryControls = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"QueryDataOptions\",\"of\":\"`getMarkupFromTree`\"} }\n */\nexport type QueryDataOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"QueryLazyOptions\"} }\n */\nexport type QueryLazyOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.internal:type {\"name\":\"ReconcilerFunction\"} }\n */\nexport type ReconcilerFunction = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RefetchQueriesFunction\"} }\n *\n * Look into using `useMutation.Options['refetchQueries']` instead.\n */\nexport type RefetchQueriesFunction = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RemoveArgumentsConfig\"} }\n */\nexport type RemoveArgumentsConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RemoveDirectiveConfig\"} }\n */\nexport type RemoveDirectiveConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RemoveFragmentDefinitionConfig\"} }\n */\nexport type RemoveFragmentDefinitionConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RemoveFragmentSpreadConfig\"} }\n */\nexport type RemoveFragmentSpreadConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RemoveNodeConfig\"} }\n */\nexport type RemoveNodeConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"RemoveVariableDefinitionConfig\"} }\n */\nexport type RemoveVariableDefinitionConfig = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"Resolver\",\"of\":\"local state\"} }\n */\nexport type Resolver = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.implementationDetail:type {\"name\":\"Resolvers\",\"of\":\"local state\"} }\n */\nexport type Resolvers = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.renderProp:type }\n */\nexport type SubscriptionComponentOptions = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"SubscriptionCurrentObservable\"} }\n */\nexport type SubscriptionCurrentObservable = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"TupleToIntersection\"} }\n */\nexport type TupleToIntersection = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.utility:type {\"name\":\"UnionToIntersection\"} }\n */\nexport type UnionToIntersection = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.removedType:type {\"name\":\"VariableValue\"} }\n */\nexport type VariableValue = never;\n\n/**\n * {@inheritDoc @apollo/client/v4-migration!Removals.HOC:type }\n */\nexport type WithApolloClient = never;\n\nexport declare namespace Removals {\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   */\n  export type removedValue = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   */\n  export type removedType = never;\n  /**\n   * @deprecated All higher-order components (HOCs) have been removed from Apollo Client 4.0 and are no longer available.\n   * Use the hooks exported from the `@apollo/client/react` package instead.\n   */\n  export type HOC = never;\n  /**\n   * @deprecated All render prop components have been removed from Apollo Client 4.0 and are no longer available.\n   * Use the hooks exported from the `@apollo/client/react` package instead.\n   */\n  export type renderProp = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * Error handling has been overhauled as a whole.\n   */\n  export type errors = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * The Observable implementation of Apollo Client has been moved from `zen-observable` to `rxjs`.\n   */\n  export type rxjs = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * This export was an implementation detail of \\{\\{of\\}\\} and is no longer available.\n   */\n  export type implementationDetail = never;\n  /**\n   * @deprecated The utility `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * It was an implementation detail that is no longer necessary and has been removed without replacement.\n   */\n  export type utility = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * The testing utilities have moved into their own package, [\\@apollo/graphql-testing-library](https://github.com/apollographql/graphql-testing-library).\n   */\n  export type testingLibrary = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * This export is considered internal and is no longer exposed.\n   */\n  export type internal = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * This was an internal testing utility that was not meant for public use.\n   * It has been removed without replacement.\n   */\n  export type internalTesting = never;\n  /**\n   * @deprecated The export `{{name}}` has been removed from Apollo Client 4.0.\n   *\n   * This export was part of a specific `\\@defer` protocol implementation.\n   * These implementations are now pluggable, so this export might not be relevant for all protocol specifications.\n   */\n  export type defer = never;\n}\n"]}