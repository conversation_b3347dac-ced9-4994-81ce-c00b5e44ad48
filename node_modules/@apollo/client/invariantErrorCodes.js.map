{"version": 3, "file": "invariantErrorCodes.js", "sourceRoot": "", "sources": ["../src/invariantErrorCodes.ts"], "names": [], "mappings": "AAIA,MAAM,CAAC,MAAM,UAAU,GAAe,EAAE,CAAC;AACzC,MAAM,CAAC,MAAM,QAAQ,GAAe,EAAE,CAAC;AACvC,MAAM,CAAC,MAAM,MAAM,GAAe,EAAE,CAAC;AACrC,MAAM,CAAC,MAAM,OAAO,GAAe,EAAE,CAAC;AACtC,MAAM,CAAC,MAAM,QAAQ,GAAe,EAAE,CAAC", "sourcesContent": ["export interface ErrorCodes {\n  [key: number]: { file: string; condition?: string; message?: string };\n}\n\nexport const errorCodes: ErrorCodes = {};\nexport const devDebug: ErrorCodes = {};\nexport const devLog: ErrorCodes = {};\nexport const devWarn: ErrorCodes = {};\nexport const devError: ErrorCodes = {};\n"]}