{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/utilities/subscriptions/relay/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAI3C,OAAO,EAAE,KAAK,EAAE,MAAM,2CAA2C,CAAC;AAElE,uEAAuE;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,iDAAiD,CAAC;AACpF,uEAAuE;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gDAAgD,CAAC;AAEpF,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAOvC,MAAM,UAAU,gCAAgC,CAC9C,GAAW,EACX,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,KAAyC,EAAE;IAE3E,OAAO,SAAS,0BAA0B,CACxC,SAA4B,EAC5B,SAA6B;QAE7B,MAAM,IAAI,GAAsB;YAC9B,aAAa,EAAE,SAAS,CAAC,IAAI;YAC7B,SAAS;YACT,KAAK,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;SAC5B,CAAC;QACF,MAAM,OAAO,GAAG,uCAAuC,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QAEvE,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YAChC,IAAI,CAAC;gBACH,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,KAAK,CAAC,UAAmB,CAAC,CAAC;YAClC,CAAC;YAED,MAAM,YAAY,GAAG,cAAc,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC;YACzE,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,YAAa,CAAC,GAAG,EAAE,OAAO,CAAC;iBACxB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;gBAEpD,IAAI,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvD,OAAO,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBACnD,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,EAAE;gBACT,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE;gBAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,uCAAuC,CAC9C,OAA+B;IAE/B,MAAM,OAAO,GAAoD;QAC/D,GAAG,kBAAkB,CAAC,OAAO;QAC7B,OAAO,EAAE;YACP,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC;YAClB,GAAG,kBAAkB,CAAC,OAAO;YAC7B,MAAM,EACJ,wEAAwE;SAC3E;KACF,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import type { GraphQLResponse, RequestParameters } from \"relay-runtime\";\nimport { Observable } from \"relay-runtime\";\n\nimport type { OperationVariables } from \"@apollo/client\";\nimport type { BaseHttpLink } from \"@apollo/client/link/http\";\nimport { maybe } from \"@apollo/client/utilities/internal/globals\";\n\n// eslint-disable-next-line local-rules/import-from-inside-other-export\nimport { readMultipartBody } from \"../../../link/http/parseAndCheckHttpResponse.js\";\n// eslint-disable-next-line local-rules/import-from-inside-other-export\nimport { fallbackHttpConfig } from \"../../../link/http/selectHttpOptionsAndBody.js\";\n\nconst backupFetch = maybe(() => fetch);\n\ntype CreateMultipartSubscriptionOptions = {\n  fetch?: WindowOrWorkerGlobalScope[\"fetch\"];\n  headers?: Record<string, string>;\n};\n\nexport function createFetchMultipartSubscription(\n  uri: string,\n  { fetch: preferredFetch, headers }: CreateMultipartSubscriptionOptions = {}\n) {\n  return function fetchMultipartSubscription(\n    operation: RequestParameters,\n    variables: OperationVariables\n  ): Observable<GraphQLResponse> {\n    const body: BaseHttpLink.Body = {\n      operationName: operation.name,\n      variables,\n      query: operation.text || \"\",\n    };\n    const options = generateOptionsForMultipartSubscription(headers || {});\n\n    return Observable.create((sink) => {\n      try {\n        options.body = JSON.stringify(body);\n      } catch (parseError) {\n        sink.error(parseError as Error);\n      }\n\n      const currentFetch = preferredFetch || maybe(() => fetch) || backupFetch;\n      const observerNext = sink.next.bind(sink);\n\n      currentFetch!(uri, options)\n        .then((response) => {\n          const ctype = response.headers?.get(\"content-type\");\n\n          if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n            return readMultipartBody(response, observerNext);\n          }\n\n          sink.error(new Error(\"Expected multipart response\"));\n        })\n        .then(() => {\n          sink.complete();\n        })\n        .catch((err: any) => {\n          sink.error(err);\n        });\n    });\n  };\n}\n\nfunction generateOptionsForMultipartSubscription(\n  headers: Record<string, string>\n) {\n  const options: { headers: Record<string, any>; body?: string } = {\n    ...fallbackHttpConfig.options,\n    headers: {\n      ...(headers || {}),\n      ...fallbackHttpConfig.headers,\n      accept:\n        \"multipart/mixed;boundary=graphql;subscriptionSpec=1.0,application/json\",\n    },\n  };\n  return options;\n}\n"]}