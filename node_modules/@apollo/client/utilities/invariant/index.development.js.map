{"version": 3, "file": "index.development.js", "sourceRoot": "", "sources": ["../../../src/utilities/invariant/index.development.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AAExE,gDAAgD;AAChD,cAAc,YAAY,CAAC;AAC3B,0DAA0D;AAC1D,OAAO,EAAE,SAAS,IAAI,aAAa,EAAE,MAAM,YAAY,CAAC;AACxD,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;IAC7B,0BAA0B;IAC1B,eAAe,EAAE,CAAC;IAClB,iBAAiB,EAAE,CAAC;IACpB,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC,EAAE,CAAC", "sourcesContent": ["import { loadDevMessages, loadErrorMessages } from \"@apollo/client/dev\";\n\n// eslint-disable-next-line no-restricted-syntax\nexport * from \"./index.js\";\n// eslint-disable-next-line local-rules/import-from-export\nimport { invariant as origInvariant } from \"./index.js\";\nexport const invariant = (() => {\n  // side effects in an IIFE\n  loadDevMessages();\n  loadErrorMessages();\n  return origInvariant;\n})();\n"]}