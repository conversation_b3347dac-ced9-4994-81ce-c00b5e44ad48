{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/utilities/invariant/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sCAAsC,CAAC;AAC/D,OAAO,EAAE,MAAM,EAAE,MAAM,2CAA2C,CAAC;AAGnE,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAC3C,uEAAuE;AACvE,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AAEzE,MAAM,cAAc,GAAG,qBAAqB,CAAC;AAC7C,MAAM,OAAO,cAAe,SAAQ,KAAK;IACvC,YAAY,OAAO,GAAG,cAAc;QAClC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;QAE3B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;CACF;AAED,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAU,CAAC;AAG7E,IAAI,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAEzE,MAAM,UAAU,SAAS,CACvB,SAAc,EACd,GAAG,IAA2C;IAE9C,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,iBAAiB,CAAC,GAAG,IAAI,CAAC,CAAC;IACnC,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB,CAA8B,IAAO;IAC7D,OAAO,UAAU,OAAyB,EAAE,GAAG,IAAW;QACxD,IAAI,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,cAAc,EAAE,CAAC;YACpD,yEAAyE;YACzE,qCAAqC;YACrC,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC;YAE5C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,MAAM,IAAI,GAAG,OAAO,CAAC;gBACrB,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,OAAO,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC1C,IAAI,GAAG,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;YAED,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAC3B,CAAC;IACH,CAAwB,CAAC;AAC3B,CAAC;AAED,SAAS,CAAC,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC7C,SAAS,CAAC,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACzC,SAAS,CAAC,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC3C,SAAS,CAAC,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAE7C,MAAM,UAAU,YAAY,CAAC,KAAqB;IAChD,MAAM,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;IAC5C,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,iBAAiB,CAC/B,OAAyB,EACzB,GAAG,cAAyB;IAE5B,OAAO,IAAI,cAAc,CACvB,kBAAkB,CAAC,OAAO,EAAE,cAAc,CAAC;QACzC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAC/C,CAAC;AACJ,CAAC;AAED,2HAA2H;AAC3H,MAAM,CAAC,MAAM,yBAAyB,GAAG,MAAM,CAAC,GAAG,CACjD,4BAA4B,GAAG,OAAO,CACvC,CAAC;AASF,SAAS,SAAS,CAAC,GAAQ;IACzB,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC3B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,CAAC;QACH,OAAO,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,oBAAoB,CAAC;IAC9B,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAyB,EACzB,cAAyB,EAAE;IAE3B,IAAI,CAAC,OAAO;QAAE,OAAO;IACrB,OAAO,CACL,MAAM,CAAC,yBAAyB,CAAC;QACjC,MAAM,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAyB,EACzB,cAAyB,EAAE;IAE3B,IAAI,CAAC,OAAO;QAAE,OAAO;IACrB,OAAO,+FAA+F,kBAAkB,CACtH,IAAI,CAAC,SAAS,CAAC;QACb,OAAO;QACP,OAAO;QACP,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;KACjC,CAAC,CACH,EAAE,CAAC;AACN,CAAC", "sourcesContent": ["import { __DEV__ } from \"@apollo/client/utilities/environment\";\nimport { global } from \"@apollo/client/utilities/internal/globals\";\n\nimport type { ErrorCodes } from \"../../invariantErrorCodes.js\";\nimport { version } from \"../../version.js\";\n// eslint-disable-next-line local-rules/import-from-inside-other-export\nimport { stringifyForDisplay } from \"../internal/stringifyForDisplay.js\";\n\nconst genericMessage = \"Invariant Violation\";\nexport class InvariantError extends Error {\n  constructor(message = genericMessage) {\n    super(message);\n    this.name = genericMessage;\n\n    Object.setPrototypeOf(this, InvariantError.prototype);\n  }\n}\n\nconst verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"] as const;\ntype VerbosityLevel = (typeof verbosityLevels)[number];\ntype ConsoleMethodName = Exclude<VerbosityLevel, \"silent\">;\nlet verbosityLevel = verbosityLevels.indexOf(__DEV__ ? \"log\" : \"silent\");\n\nexport function invariant(\n  condition: any,\n  ...args: [message?: string | number, ...any[]]\n): asserts condition {\n  if (!condition) {\n    throw newInvariantError(...args);\n  }\n}\n\nfunction wrapConsoleMethod<M extends ConsoleMethodName>(name: M) {\n  return function (message?: string | number, ...args: any[]) {\n    if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n      // Default to console.log if this host environment happens not to provide\n      // all the console.* methods we need.\n      const method = console[name] || console.log;\n\n      if (typeof message === \"number\") {\n        const arg0 = message;\n        message = getHandledErrorMsg(arg0);\n        if (!message) {\n          message = getFallbackErrorMsg(arg0, args);\n          args = [];\n        }\n      }\n\n      method(message, ...args);\n    }\n  } as (typeof console)[M];\n}\n\ninvariant.debug = wrapConsoleMethod(\"debug\");\ninvariant.log = wrapConsoleMethod(\"log\");\ninvariant.warn = wrapConsoleMethod(\"warn\");\ninvariant.error = wrapConsoleMethod(\"error\");\n\nexport function setVerbosity(level: VerbosityLevel): VerbosityLevel {\n  const old = verbosityLevels[verbosityLevel];\n  verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n  return old;\n}\n\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nexport function newInvariantError(\n  message?: string | number,\n  ...optionalParams: unknown[]\n) {\n  return new InvariantError(\n    getHandledErrorMsg(message, optionalParams) ||\n      getFallbackErrorMsg(message, optionalParams)\n  );\n}\n\n// This is duplicated between `@apollo/client/dev` and `@apollo/client/utilities/invariant` to prevent circular references.\nexport const ApolloErrorMessageHandler = Symbol.for(\n  \"ApolloErrorMessageHandler_\" + version\n);\ndeclare global {\n  interface Window {\n    [ApolloErrorMessageHandler]?: {\n      (message: string | number, args: string[]): string | undefined;\n    } & ErrorCodes;\n  }\n}\n\nfunction stringify(arg: any) {\n  if (typeof arg == \"string\") {\n    return arg;\n  }\n\n  try {\n    return stringifyForDisplay(arg, 2).slice(0, 1000);\n  } catch {\n    return \"<non-serializable>\";\n  }\n}\n\nfunction getHandledErrorMsg(\n  message?: string | number,\n  messageArgs: unknown[] = []\n) {\n  if (!message) return;\n  return (\n    global[ApolloErrorMessageHandler] &&\n    global[ApolloErrorMessageHandler](message, messageArgs.map(stringify))\n  );\n}\n\nfunction getFallbackErrorMsg(\n  message?: string | number,\n  messageArgs: unknown[] = []\n) {\n  if (!message) return;\n  return `An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#${encodeURIComponent(\n    JSON.stringify({\n      version,\n      message,\n      args: messageArgs.map(stringify),\n    })\n  )}`;\n}\n"]}