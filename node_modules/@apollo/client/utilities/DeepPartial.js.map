{"version": 3, "file": "DeepPartial.js", "sourceRoot": "", "sources": ["../../src/utilities/DeepPartial.ts"], "names": [], "mappings": "AAAA,sJAAsJ;AACtJ,EAAE;AACF,6EAA6E;AAC7E,kEAAkE", "sourcesContent": ["// Inspired by type-fest PartialDeep: https://github.com/sindresorhus/type-fest/blob/9feb8c89be9a0f2f688bf2f497230298a8e2472e/source/partial-deep.d.ts\n//\n// We're including the license to give credit to the original implementation.\n// https://github.com/sindresorhus/type-fest/blob/main/license-mit\n\n/*\n * MIT License\n *\n * Copyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\nimport type { Primitive } from \"@apollo/client/utilities/internal\";\n\ntype DeepPartialPrimitive = Primitive | Date | RegExp;\n\n/**\n * Deeply makes all properties in `T` optional.\n */\nexport type DeepPartial<T> =\n  T extends DeepPartialPrimitive ? T\n  : T extends Map<infer TKey, infer TValue> ? DeepPartialMap<TKey, TValue>\n  : T extends ReadonlyMap<infer TKey, infer TValue> ?\n    DeepPartialReadonlyMap<TKey, TValue>\n  : T extends Set<infer TItem> ? DeepPartialSet<TItem>\n  : T extends ReadonlySet<infer TItem> ? DeepPartialReadonlySet<TItem>\n  : T extends (...args: any[]) => unknown ? T | undefined\n  : T extends object ?\n    T extends (\n      ReadonlyArray<infer TItem> // Test for arrays/tuples\n    ) ?\n      TItem[] extends (\n        T // Test for non-tuples\n      ) ?\n        readonly TItem[] extends T ?\n          ReadonlyArray<DeepPartial<TItem | undefined>>\n        : Array<DeepPartial<TItem | undefined>>\n      : DeepPartialObject<T>\n    : DeepPartialObject<T>\n  : unknown;\n\ntype DeepPartialMap<TKey, TValue> = {} & Map<\n  DeepPartial<TKey>,\n  DeepPartial<TValue>\n>;\n\ntype DeepPartialReadonlyMap<TKey, TValue> = {} & ReadonlyMap<\n  DeepPartial<TKey>,\n  DeepPartial<TValue>\n>;\n\ntype DeepPartialSet<T> = {} & Set<DeepPartial<T>>;\ntype DeepPartialReadonlySet<T> = {} & ReadonlySet<DeepPartial<T>>;\n\ntype DeepPartialObject<T extends object> = {\n  [K in keyof T]?: DeepPartial<T[K]>;\n};\n"]}