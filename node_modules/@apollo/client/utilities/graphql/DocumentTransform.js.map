{"version": 3, "file": "DocumentTransform.js", "sources": ["../../../src/utilities/graphql/DocumentTransform.ts"], "sourcesContent": ["import { WeakCache } from \"@wry/caches\";\nimport { <PERSON><PERSON> } from \"@wry/trie\";\nimport type { DocumentNode } from \"graphql\";\nimport { wrap } from \"optimism\";\n\nimport { checkDocument } from \"@apollo/client/utilities/internal\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nimport { cacheSizes } from \"../caching/sizes.js\";\n\nexport type DocumentTransformCacheKey = ReadonlyArray<unknown>;\n\ntype TransformFn = (document: DocumentNode) => DocumentNode;\n\ninterface DocumentTransformOptions {\n  /**\n   * Determines whether to cache the transformed GraphQL document. Caching can\n   * speed up repeated calls to the document transform for the same input\n   * document. Set to `false` to completely disable caching for the document\n   * transform. When disabled, this option takes precedence over the [`getCacheKey`](#getcachekey)\n   * option.\n   *\n   * @defaultValue `true`\n   */\n  cache?: boolean;\n  /**\n   * Defines a custom cache key for a GraphQL document that will determine whether to re-run the document transform when given the same input GraphQL document. Returns an array that defines the cache key. Return `undefined` to disable caching for that GraphQL document.\n   *\n   * > [!NOTE]\n   * > The items in the array can be any type, but each item needs to be\n   * > referentially stable to guarantee a stable cache key.\n   *\n   * @defaultValue `(document) => [document]`\n   */\n  getCacheKey?: (\n    document: DocumentNode\n  ) => DocumentTransformCacheKey | undefined;\n}\n\nfunction identity(document: DocumentNode) {\n  return document;\n}\n\n/**\n * A class for transforming GraphQL documents. See the [Document transforms\n * documentation](https://www.apollographql.com/docs/react/data/document-transforms) for more details on using them.\n *\n * @example\n *\n * ```ts\n * import { DocumentTransform } from \"@apollo/client/utilities\";\n * import { visit } from \"graphql\";\n *\n * const documentTransform = new DocumentTransform((doc) => {\n *   return visit(doc, {\n *     // ...\n *   });\n * });\n *\n * const transformedDoc = documentTransform.transformDocument(myDocument);\n * ```\n */\nexport class DocumentTransform {\n  private readonly transform: TransformFn;\n  private cached: boolean;\n\n  private readonly resultCache = new WeakSet<DocumentNode>();\n\n  // This default implementation of getCacheKey can be overridden by providing\n  // options.getCacheKey to the DocumentTransform constructor. In general, a\n  // getCacheKey function may either return an array of keys (often including\n  // the document) to be used as a cache key, or undefined to indicate the\n  // transform for this document should not be cached.\n  private getCacheKey(\n    document: DocumentNode\n  ): DocumentTransformCacheKey | undefined {\n    return [document];\n  }\n\n  /**\n   * Creates a DocumentTransform that returns the input document unchanged.\n   *\n   * @returns The input document\n   */\n  static identity() {\n    // No need to cache this transform since it just returns the document\n    // unchanged. This should save a bit of memory that would otherwise be\n    // needed to populate the `documentCache` of this transform.\n    return new DocumentTransform(identity, { cache: false });\n  }\n\n  /**\n   * Creates a DocumentTransform that conditionally applies one of two transforms.\n   *\n   * @param predicate - Function that determines which transform to apply\n   * @param left - Transform to apply when `predicate` returns `true`\n   * @param right - Transform to apply when `predicate` returns `false`. If not provided, it defaults to `DocumentTransform.identity()`.\n   * @returns A DocumentTransform that conditionally applies a document transform based on the predicate\n   *\n   * @example\n   *\n   * ```ts\n   * import { isQueryOperation } from \"@apollo/client/utilities\";\n   *\n   * const conditionalTransform = DocumentTransform.split(\n   *   (document) => isQueryOperation(document),\n   *   queryTransform,\n   *   mutationTransform\n   * );\n   * ```\n   */\n  static split(\n    predicate: (document: DocumentNode) => boolean,\n    left: DocumentTransform,\n    right: DocumentTransform = DocumentTransform.identity()\n  ) {\n    return Object.assign(\n      new DocumentTransform(\n        (document) => {\n          const documentTransform = predicate(document) ? left : right;\n\n          return documentTransform.transformDocument(document);\n        },\n        // Reasonably assume both `left` and `right` transforms handle their own caching\n        { cache: false }\n      ),\n      { left, right }\n    );\n  }\n\n  constructor(transform: TransformFn, options: DocumentTransformOptions = {}) {\n    this.transform = transform;\n\n    if (options.getCacheKey) {\n      // Override default `getCacheKey` function, which returns [document].\n      this.getCacheKey = options.getCacheKey;\n    }\n    this.cached = options.cache !== false;\n\n    this.resetCache();\n  }\n\n  /**\n   * Resets the internal cache of this transform, if it is cached.\n   */\n  resetCache() {\n    if (this.cached) {\n      const stableCacheKeys = new Trie<WeakKey>();\n      this.performWork = wrap(\n        DocumentTransform.prototype.performWork.bind(this),\n        {\n          makeCacheKey: (document) => {\n            const cacheKeys = this.getCacheKey(document);\n            if (cacheKeys) {\n              invariant(\n                Array.isArray(cacheKeys),\n                \"`getCacheKey` must return an array or undefined\"\n              );\n              return stableCacheKeys.lookupArray(cacheKeys);\n            }\n          },\n          max: cacheSizes[\"documentTransform.cache\"],\n          cache: WeakCache<any, any>,\n        }\n      );\n    }\n  }\n\n  private performWork(document: DocumentNode) {\n    checkDocument(document);\n    return this.transform(document);\n  }\n\n  /**\n   * Transforms a GraphQL document using the configured transform function.\n   *\n   * @remarks\n   *\n   * Note that `transformDocument` caches the transformed document. Calling\n   * `transformDocument` again with the already-transformed document will\n   * immediately return it.\n   *\n   * @param document - The GraphQL document to transform\n   * @returns The transformed document\n   *\n   * @example\n   *\n   * ```ts\n   * const document = gql`\n   *   # ...\n   * `;\n   *\n   * const documentTransform = new DocumentTransform(transformFn);\n   * const transformedDocument = documentTransform.transformDocument(document);\n   * ```\n   */\n  transformDocument(document: DocumentNode) {\n    // If a user passes an already transformed result back to this function,\n    // immediately return it.\n    if (this.resultCache.has(document)) {\n      return document;\n    }\n\n    const transformedDocument = this.performWork(document);\n\n    this.resultCache.add(transformedDocument);\n\n    return transformedDocument;\n  }\n\n  /**\n   * Combines this document transform with another document transform. The\n   * returned document transform first applies the current document transform,\n   * then applies the other document transform.\n   *\n   * @param otherTransform - The transform to apply after this one\n   * @returns A new DocumentTransform that applies both transforms in sequence\n   *\n   * @example\n   *\n   * ```ts\n   * const combinedTransform = addTypenameTransform.concat(\n   *   removeDirectivesTransform\n   * );\n   * ```\n   */\n  concat(otherTransform: DocumentTransform): DocumentTransform {\n    return Object.assign(\n      new DocumentTransform(\n        (document) => {\n          return otherTransform.transformDocument(\n            this.transformDocument(document)\n          );\n        },\n        // Reasonably assume both transforms handle their own caching\n        { cache: false }\n      ),\n      {\n        left: this,\n        right: otherTransform,\n      }\n    );\n  }\n\n  /**\n   * @internal\n   * Used to iterate through all transforms that are concatenations or `split` links.\n   */\n  readonly left?: DocumentTransform;\n  /**\n   * @internal\n   * Used to iterate through all transforms that are concatenations or `split` links.\n   */\n  readonly right?: DocumentTransform;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC;AACvC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC;AAEhC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;AAE/B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;AA+BhD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,EAAxC;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;AACjB;AAEA,CAAA,CAAA;;;;;;;;;;;;;;;;;;CAkBA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;IACmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;IAClB,CAAV,CAAA,CAAA,CAAA,CAAA,CAAgB;IAEG,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,EAAqC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAA5C,CAA4D;IAE1D,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CACjB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,EAD1B;QAGI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC;IACnB;IAEA,CAAF,CAAA;;;;KAIA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,EAAA;QACI,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,EAAE,EAAE,CAA7C,CAAA,CAAA,CAAA,CAAkD,EAAE,CAApD,CAAA,CAAA,CAAA,EAAA,CAA2D,CAAC;IAC1D;IAEA,CAAF,CAAA;;;;;;;;;;;;;;;;;;;KAmBA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CACV,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkD,EAC9C,CAFJ,CAAA,CAAA,CAE2B,EACvB,CAHJ,CAAA,CAAA,CAAA,EAAA,EAG+B,CAH/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGgD,CAAC,CAHjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGyD,CAHzD,CAG2D,EAH3D;QAKI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAClB,CADN,CAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CACnB,CAAC,CAFT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEiB,EAAE,CAFnB,EAAA;YAGU,CAAV,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,EAAE,EAAE,CAA1D,CAAA,CAAA,EAA+D,EAAE,CAAjE,CAAA,CAAA,CAAA,CAAsE;YAE5D,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6D,CAAC;QACtD,CAAC;QACD,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,EAAA,CAAwB,CACjB,EACD,EAAE,CAFR,CAAA,CAAA,CAEY,EAAE,CAFd,CAAA,CAAA,CAAA,EAAA,CAEqB,CAChB;IACH;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,EAAE,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0E,CAA1E,CAA4E,EAA5E;QACI,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;QAE1B,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE;YACvB,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;QACxC;QACA,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAyC;QAErC,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAnB,CAAqB;IACnB;IAEA,CAAF,CAAA;;KAEA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAZ,EAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE;YACf,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B,CAA9B,CAAA,EAAkC,CAAlC,CAAA,CAAA,CAAsC,CAAtC,CAAiD;YAC3C,CAAN,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAA6B,CACrB,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyB,CAAC,CAD1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmC,CAAC,CADpC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+C,CAAC,CADhD,CAAA,CAAA,CACoD,CAAC,CADrD,CAAA,CAAA,CACyD,CAAC,EAClD;gBACE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,EAAE,CAAnC,EAAA;oBACY,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B,CAA9B,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD,CAAC;oBAC5C,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE;wBACb,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACgB,CADhB,CAAA,CAAA,CAAA,CACqB,CAAC,CADtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAC6B,CAAC,CAD9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuC,MAExB;wBACD,CAAd,CAAA,CAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC;oBAC/C;gBACF,CAAC;gBACD,CAAV,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC;gBAC1C,CAAV,CAAA,CAAA,CAAA,CAAe,EAAE,CAAA,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAA;YACpC,CAAS,CACF;QACH;IACF;IAEQ,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,EAA5C;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC;QACvB,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC;IACjC;IAEA,CAAF,CAAA;;;;;;;;;;;;;;;;;;;;;;KAsBA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,EAA1C;QACI,CAAJ,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,EAAE;YAClC,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;QACjB;QAEA,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAhC,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC;QAEtD,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC;QAEzC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;IAC5B;IAEA,CAAF,CAAA;;;;;;;;;;;;;;;KAeA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,EAA1C;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAClB,CADN,CAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CACnB,CAAC,CAFT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEiB,EAAE,CAFnB,EAAA;YAGU,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CACrC,CADZ,CAAA,CAAA,CACgB,CAAC,CADjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkC,CAAC,CADnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2C,CAAC,CACjC;QACH,CAAC;QACD,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,EAAA,CAAwB,CACjB,EACD;YACE,CAAR,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAkB;YACV,CAAR,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B;QAC7B,CAAO,CACF;IACH;;;;;;;IAMS,CAAX,CAAA,CAAA,CAAe;;;;;;;IAKJ,CAAX,CAAA,CAAA,CAAA,CAAgB;AAChB;"}