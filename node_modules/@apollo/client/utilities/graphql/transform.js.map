{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/transform.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEtC,MAAM,cAAc,GAAc;IAChC,IAAI,EAAE,IAAI,CAAC,KAAK;IAChB,IAAI,EAAE;QACJ,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,KAAK,EAAE,YAAY;KACpB;CACF,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAChD,UAAiC,GAAU;IACzC,OAAO,KAAK,CAAC,GAAG,EAAE;QAChB,YAAY,EAAE;YACZ,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM;gBACtB,gDAAgD;gBAChD,IACE,MAAM;oBACL,MAAkC,CAAC,IAAI;wBACtC,IAAI,CAAC,oBAAoB,EAC3B,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,OAAO;gBACT,CAAC;gBAED,6DAA6D;gBAC7D,mCAAmC;gBACnC,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;oBACzC,OAAO,CACL,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK;wBAC7B,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY;4BACpC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CACnD,CAAC;gBACJ,CAAC,CAAC,CAAC;gBACH,IAAI,IAAI,EAAE,CAAC;oBACT,OAAO;gBACT,CAAC;gBAED,qEAAqE;gBACrE,iDAAiD;gBACjD,MAAM,KAAK,GAAG,MAAmB,CAAC;gBAClC,IACE,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK;oBACzB,KAAK,CAAC,UAAU;oBAChB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EACvD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,gEAAgE;gBAChE,OAAO;oBACL,GAAG,IAAI;oBACP,UAAU,EAAE,CAAC,GAAG,UAAU,EAAE,cAAc,CAAC;iBAC5C,CAAC;YACJ,CAAC;SACF;KACF,CAAC,CAAC;AACL,CAAC,EACD;IACE,KAAK,CAAC,KAAgB;QACpB,OAAO,KAAK,KAAK,cAAc,CAAC;IAClC,CAAC;CACF,CACF,CAAC", "sourcesContent": ["import type { ASTNode, FieldNode, OperationDefinitionNode } from \"graphql\";\nimport { Kind, visit } from \"graphql\";\n\nconst TYPENAME_FIELD: FieldNode = {\n  kind: Kind.FIELD,\n  name: {\n    kind: Kind.NAME,\n    value: \"__typename\",\n  },\n};\n\n/**\n * Adds `__typename` to all selection sets in the document except for the root\n * selection set.\n *\n * @param doc - The `ASTNode` to add `__typename` to\n *\n * @example\n *\n * ```ts\n * const document = gql`\n *   # ...\n * `;\n *\n * const withTypename = addTypenameToDocument(document);\n * ```\n */\nexport const addTypenameToDocument = Object.assign(\n  function <TNode extends ASTNode>(doc: TNode): TNode {\n    return visit(doc, {\n      SelectionSet: {\n        enter(node, _key, parent) {\n          // Don't add __typename to OperationDefinitions.\n          if (\n            parent &&\n            (parent as OperationDefinitionNode).kind ===\n              Kind.OPERATION_DEFINITION\n          ) {\n            return;\n          }\n\n          // No changes if no selections.\n          const { selections } = node;\n          if (!selections) {\n            return;\n          }\n\n          // If selections already have a __typename, or are part of an\n          // introspection query, do nothing.\n          const skip = selections.some((selection) => {\n            return (\n              selection.kind === Kind.FIELD &&\n              (selection.name.value === \"__typename\" ||\n                selection.name.value.lastIndexOf(\"__\", 0) === 0)\n            );\n          });\n          if (skip) {\n            return;\n          }\n\n          // If this SelectionSet is @export-ed as an input variable, it should\n          // not have a __typename field (see issue #4691).\n          const field = parent as FieldNode;\n          if (\n            field.kind === Kind.FIELD &&\n            field.directives &&\n            field.directives.some((d) => d.name.value === \"export\")\n          ) {\n            return;\n          }\n\n          // Create and return a new SelectionSet with a __typename Field.\n          return {\n            ...node,\n            selections: [...selections, TYPENAME_FIELD],\n          };\n        },\n      },\n    });\n  },\n  {\n    added(field: FieldNode): boolean {\n      return field === TYPENAME_FIELD;\n    },\n  }\n);\n"]}