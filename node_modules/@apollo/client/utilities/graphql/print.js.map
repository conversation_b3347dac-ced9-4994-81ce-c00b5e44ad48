{"version": 3, "file": "print.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/print.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,IAAI,SAAS,EAAE,MAAM,SAAS,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAE,MAAM,sCAAsC,CAAC;AAC/D,OAAO,EACL,oBAAoB,EACpB,mBAAmB,GACpB,MAAM,mCAAmC,CAAC;AAE3C,OAAO,EAAE,UAAU,EAAqB,MAAM,qBAAqB,CAAC;AAEpE,IAAI,UAAkD,CAAC;AAEvD;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAChC,CAAC,GAAY,EAAE,EAAE;IACf,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QACxB,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,EACD;IACE,KAAK;QACH,UAAU,GAAG,IAAI,oBAAoB,CACnC,UAAU,CAAC,KAAK,sCAA2B,CAC5C,CAAC;IACJ,CAAC;CACF,CACF,CAAC;AACF,KAAK,CAAC,KAAK,EAAE,CAAC;AAEd,IAAI,OAAO,EAAE,CAAC;IACZ,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC", "sourcesContent": ["import type { ASTNode } from \"graphql\";\nimport { print as origPrint } from \"graphql\";\n\nimport { __DEV__ } from \"@apollo/client/utilities/environment\";\nimport {\n  AutoCleanedWeakCache,\n  registerGlobalCache,\n} from \"@apollo/client/utilities/internal\";\n\nimport { cacheSizes, defaultCacheSizes } from \"../caching/index.js\";\n\nlet printCache!: AutoCleanedWeakCache<ASTNode, string>;\n\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n *\n * @remarks This is the same function as the GraphQL.js `print` function but\n * with an added cache to avoid recomputation when encountering the same\n * `ASTNode` more than once.\n */\nexport const print = Object.assign(\n  (ast: ASTNode) => {\n    let result = printCache.get(ast);\n\n    if (!result) {\n      result = origPrint(ast);\n      printCache.set(ast, result);\n    }\n    return result;\n  },\n  {\n    reset() {\n      printCache = new AutoCleanedWeakCache<ASTNode, string>(\n        cacheSizes.print || defaultCacheSizes.print\n      );\n    },\n  }\n);\nprint.reset();\n\nif (__DEV__) {\n  registerGlobalCache(\"print\", () => (printCache ? printCache.size : 0));\n}\n"]}