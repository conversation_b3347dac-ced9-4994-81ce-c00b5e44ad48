{"version": 3, "file": "storeUtils.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/storeUtils.ts"], "names": [], "mappings": "AAOA;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,WAAW,CAAC,GAAQ;IAClC,OAAO,OAAO,CACZ,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAChE,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Representation of a reference object inside the cache.\n */\nexport interface Reference {\n  readonly __ref: string;\n}\n\n/**\n * Determines if a given object is a reference object.\n *\n * @param obj - The object to check if its a reference object\n *\n * @example\n *\n * ```ts\n * import { isReference } from \"@apollo/client/utilities\";\n *\n * isReference({ __ref: \"User:1\" }); // true\n * isReference({ __typename: \"User\", id: 1 }); // false\n * ```\n */\nexport function isReference(obj: any): obj is Reference {\n  return Boolean(\n    obj && typeof obj === \"object\" && typeof obj.__ref === \"string\"\n  );\n}\n\n/**\n * Represents the union of valid values that can be stored in the cache.\n */\nexport type StoreValue =\n  | number\n  | string\n  | string[]\n  | Reference\n  | Reference[]\n  | null\n  | undefined\n  | void\n  | Object;\n\n/**\n * Represents an object that is stored in the cache.\n */\nexport interface StoreObject {\n  __typename?: string;\n  [storeFieldName: string]: StoreValue;\n}\n\n/**\n * Workaround for a TypeScript quirk:\n * types per default have an implicit index signature that makes them\n * assignable to `StoreObject`.\n * interfaces do not have that implicit index signature, so they cannot\n * be assigned to `StoreObject`.\n * This type just maps over a type or interface that is passed in,\n * implicitly adding the index signature.\n * That way, the result can be assigned to `StoreObject`.\n *\n * This is important if some user-defined interface is used e.g.\n * in cache.modify, where the `toReference` method expects a\n * `StoreObject` as input.\n */\nexport type AsStoreObject<T extends { __typename?: string }> = {\n  [K in keyof T]: T[K];\n};\n"]}