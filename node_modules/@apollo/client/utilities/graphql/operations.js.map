{"version": 3, "file": "operations.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/operations.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AAE3E,SAAS,WAAW,CAClB,QAAsB,EACtB,SAAgD;IAEhD,OAAO,sBAAsB,CAAC,QAAQ,CAAC,EAAE,SAAS,KAAK,SAAS,CAAC;AACnE,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,mBAAmB,CAAC,QAAsB;IACxD,OAAO,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,gBAAgB,CAAC,QAAsB;IACrD,OAAO,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,uBAAuB,CAAC,QAAsB;IAC5D,OAAO,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AAC/C,CAAC", "sourcesContent": ["import type { DocumentNode } from \"@apollo/client\";\nimport { getOperationDefinition } from \"@apollo/client/utilities/internal\";\n\nfunction isOperation(\n  document: DocumentNode,\n  operation: \"query\" | \"mutation\" | \"subscription\"\n) {\n  return getOperationDefinition(document)?.operation === operation;\n}\n\n/**\n * Determine if a document is a mutation document.\n *\n * @remarks\n * If you are authoring an Apollo link, you might not need this utility.\n * Prefer using the `operationType` property the `operation` object instead.\n *\n * @param document - The GraphQL document to check\n * @returns A boolean indicating if the document is a mutation operation\n *\n * @example\n *\n * ```ts\n * import { isMutationOperation } from \"@apollo/client/utilities\";\n *\n * const mutation = gql`\n *   mutation MyMutation {\n *     # ...\n *   }\n * `;\n *\n * isMutationOperation(mutation); // true\n * ```\n */\nexport function isMutationOperation(document: DocumentNode) {\n  return isOperation(document, \"mutation\");\n}\n\n/**\n * Determine if a document is a query document.\n *\n * @remarks\n * If you are authoring an Apollo link, you might not need this utility.\n * Prefer using the `operationType` property the `operation` object instead.\n *\n * @param document - The GraphQL document to check\n * @returns A boolean indicating if the document is a query operation\n *\n * @example\n *\n * ```ts\n * import { isQueryOperation } from \"@apollo/client/utilities\";\n *\n * const query = gql`\n *   query MyQuery {\n *     # ...\n *   }\n * `;\n *\n * isQueryOperation(query); // true\n * ```\n */\nexport function isQueryOperation(document: DocumentNode) {\n  return isOperation(document, \"query\");\n}\n\n/**\n * Determine if a document is a subscription document.\n *\n * @remarks\n * If you are authoring an Apollo link, you might not need this utility.\n * Prefer using the `operationType` property the `operation` object instead.\n *\n * @param document - The GraphQL document to check\n * @returns A boolean indicating if the document is a subscription operation\n *\n * @example\n *\n * ```ts\n * import { isSubscriptionOperation } from \"@apollo/client/utilities\";\n *\n * const subscription = gql`\n *   subscription MySubscription {\n *     # ...\n *   }\n * `;\n *\n * isSubscriptionOperation(subscription); // true\n * ```\n */\nexport function isSubscriptionOperation(document: DocumentNode) {\n  return isOperation(document, \"subscription\");\n}\n"]}