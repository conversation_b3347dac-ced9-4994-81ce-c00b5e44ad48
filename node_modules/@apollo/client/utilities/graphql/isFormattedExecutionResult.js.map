{"version": 3, "file": "isFormattedExecutionResult.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/isFormattedExecutionResult.ts"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAM,UAAU,0BAA0B,CACxC,MAAe;IAEf,OAAO,CACL,CAAC,CAAC,MAAM;QACR,CAAC,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC;QACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CACvB,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,YAAY,CACpE,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["import type { FormattedExecutionResult } from \"graphql\";\n\n/**\n * Determines whether the given object is a valid GraphQL execution result\n * according to the GraphQL specification.\n *\n * @remarks\n *\n * A valid execution result must be an object that contains only `data`,\n * `errors`, and/or `extensions` properties. At least one of `data` or `errors`\n * must be present.\n *\n * @param result - The object to test\n * @returns `true` if the object conforms to the GraphQL execution result format\n *\n * @example\n *\n * ```ts\n * import { isFormattedExecutionResult } from \"@apollo/client/utilities\";\n *\n * // Valid execution result\n * const validResult = { data: { user: { name: \"<PERSON>\" } } };\n * console.log(isFormattedExecutionResult(validResult)); // true\n *\n * // Invalid - contains non-standard properties\n * const invalidResult = { data: {}, customField: \"value\" };\n * console.log(isFormattedExecutionResult(invalidResult)); // false\n * ```\n */\nexport function isFormattedExecutionResult(\n  result?: object\n): result is FormattedExecutionResult {\n  return (\n    !!result &&\n    (\"errors\" in result || \"data\" in result) &&\n    Object.keys(result).every(\n      (key) => key === \"errors\" || key === \"data\" || key === \"extensions\"\n    )\n  );\n}\n"]}