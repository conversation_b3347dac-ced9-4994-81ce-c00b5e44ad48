{"version": 3, "file": "pagination.js", "sourceRoot": "", "sources": ["../../../src/utilities/policies/pagination.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAG/B,OAAO,EAAE,SAAS,EAAE,MAAM,mCAAmC,CAAC;AAI9D;;;;;;GAMG;AACH,MAAM,UAAU,gBAAgB,CAC9B,UAAmB,KAAK;IAExB,OAAO;QACL,OAAO;QACP,KAAK,CAAC,QAAQ,EAAE,QAAQ;YACtB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC1D,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,qBAAqB,CACnC,UAAmB,KAAK;IAExB,OAAO;QACL,OAAO;QACP,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE;YAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAEjD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,IAAI,EAAE,CAAC;oBACT,gDAAgD;oBAChD,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;oBAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;wBACzC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,8DAA8D;oBAC9D,4DAA4D;oBAC5D,8DAA8D;oBAC9D,2BAA2B;oBAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC;AAuCD,yEAAyE;AACzE,yEAAyE;AACzE,mEAAmE;AACnE;;;;;;GAMG;AACH,MAAM,UAAU,oBAAoB,CAClC,UAAmB,KAAK;IAExB,OAAO;QACL,OAAO;QAEP,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE;YACnC,IAAI,CAAC,QAAQ;gBAAE,OAAO,QAAQ,CAAC;YAE/B,MAAM,KAAK,GAAwB,EAAE,CAAC;YACtC,IAAI,eAAe,GAAG,EAAE,CAAC;YACzB,IAAI,cAAc,GAAG,EAAE,CAAC;YACxB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC9B,iEAAiE;gBACjE,0DAA0D;gBAC1D,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;oBACrC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;wBAChB,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;wBACvD,cAAc,GAAG,IAAI,CAAC,MAAM,IAAI,cAAc,CAAC;oBACjD,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,KAAK,cAAc,EAAE,CAAC;gBAC3D,eAAe,GAAG,EAAE,CAAC;YACvB,CAAC;YAED,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;YAE3D,OAAO;gBACL,iEAAiE;gBACjE,8DAA8D;gBAC9D,4DAA4D;gBAC5D,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACtB,KAAK;gBACL,QAAQ,EAAE;oBACR,GAAG,QAAQ,CAAC,QAAQ;oBACpB,sEAAsE;oBACtE,4CAA4C;oBAC5C,WAAW,EAAE,WAAW,IAAI,eAAe;oBAC3C,SAAS,EAAE,SAAS,IAAI,cAAc;iBACvC;aACF,CAAC;QACJ,CAAC;QAED,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE;YACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,QAAQ,GAAG,aAAa,EAAE,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,MAAM,aAAa,GACjB,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACd,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC1B,IAAI,WAAW,CAAC,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;wBACtC,gEAAgE;wBAChE,yDAAyD;wBACzD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAS,QAAQ,EAAE,IAAI,CAAC,CAAC;oBAClD,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC;gBACJ,CAAC,CAAC,EAAE,CAAC;YAEP,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC;gBAC9B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC;gBAC5C,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBACnC,MAAM,QAAQ,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACzD,gEAAgE;gBAChE,mDAAmD;gBACnD,IAAI,SAAS,IAAI,WAAW,EAAE,CAAC;oBAC7B,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;gBACjC,CAAC;gBACD,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;oBAC1B,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC9B,CAAC;gBACD,kDAAkD;gBAClD,yDAAyD;gBACzD,MAAM,WAAW,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC;gBAClD,IAAI,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;oBAChC,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE;wBAC7B,QAAQ,EAAE;4BACR,WAAW,EAAE,WAAW;yBACzB;qBACF,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM,UAAU,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC;gBAC/C,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC7B,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE;wBAC7B,QAAQ,EAAE;4BACR,SAAS,EAAE,UAAU;yBACtB;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC5B,IAAI,MAAM,GAAkB,EAAE,CAAC;YAE/B,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACvB,kEAAkE;gBAClE,kEAAkE;gBAClE,0CAA0C;gBAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrE,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBACf,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;oBACpC,+BAA+B;gBACjC,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtE,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClD,MAAM,GAAG,EAAE,CAAC;YACd,CAAC;iBAAM,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAC1B,8DAA8D;gBAC9D,gEAAgE;gBAChE,kEAAkE;gBAClE,MAAM,GAAG,EAAE,CAAC;YACd,CAAC;YAED,MAAM,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,CAAC,CAAC;YAEvD,MAAM,QAAQ,GAAmB;gBAC/B,iEAAiE;gBACjE,oEAAoE;gBACpE,mEAAmE;gBACnE,kEAAkE;gBAClE,2DAA2D;gBAC3D,GAAG,QAAQ,CAAC,QAAQ;gBACpB,GAAG,QAAQ,CAAC,QAAQ;aACrB,CAAC;YAEF,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,MAAM,EACJ,eAAe,EACf,WAAW,EACX,WAAW,EACX,SAAS,EACT,GAAG,MAAM,EACV,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBAEtB,8DAA8D;gBAC9D,kEAAkE;gBAClE,6DAA6D;gBAC7D,gCAAgC;gBAChC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAEhC,2DAA2D;gBAC3D,iEAAiE;gBACjE,iEAAiE;gBACjE,+DAA+D;gBAC/D,gEAAgE;gBAChE,+DAA+D;gBAC/D,oDAAoD;gBACpD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,IAAI,KAAK,CAAC,KAAK,eAAe;wBAC5B,QAAQ,CAAC,eAAe,GAAG,eAAe,CAAC;oBAC7C,IAAI,KAAK,CAAC,KAAK,WAAW;wBAAE,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;gBACjE,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,IAAI,KAAK,CAAC,KAAK,WAAW;wBAAE,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;oBAC/D,IAAI,KAAK,CAAC,KAAK,SAAS;wBAAE,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;gBAC3D,CAAC;YACH,CAAC;YAED,OAAO;gBACL,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACtB,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACtB,KAAK;gBACL,QAAQ;aACT,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,2DAA2D;AAC3D,MAAM,SAAS,GAAG,CAAC,GAAwB,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACvE,MAAM,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAExC,SAAS,aAAa;IACpB,OAAO;QACL,KAAK,EAAE,EAAE;QACT,QAAQ,EAAE;YACR,eAAe,EAAE,KAAK;YACtB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,EAAE;SACd;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { __rest } from \"tslib\";\n\nimport type { FieldPolicy, Reference } from \"@apollo/client/cache\";\nimport { mergeDeep } from \"@apollo/client/utilities/internal\";\n\ntype KeyArgs = FieldPolicy<any>[\"keyArgs\"];\n\n/**\n * A basic pagination field policy that always concatenates new\n * results onto the existing array, without examining options.args.\n *\n * @param keyArgs - `keyArgs` that should be applied to the field policy\n * @returns The field policy that handles concatenating field results.\n */\nexport function concatPagination<T = Reference>(\n  keyArgs: KeyArgs = false\n): FieldPolicy<T[]> {\n  return {\n    keyArgs,\n    merge(existing, incoming) {\n      return existing ? [...existing, ...incoming] : incoming;\n    },\n  };\n}\n\n/**\n * A basic field policy that uses `options.args.{offset,limit}` to splice\n * the incoming data into the existing array. If your arguments are called\n * something different (like `args.{start,count}`), feel free to copy/paste\n * this implementation and make the appropriate changes.\n *\n * @param keyArgs - `keyArgs` that should be applied to the field policy\n * @returns The field policy that handles offset/limit pagination\n */\nexport function offsetLimitPagination<T = Reference>(\n  keyArgs: KeyArgs = false\n): FieldPolicy<T[]> {\n  return {\n    keyArgs,\n    merge(existing, incoming, { args }) {\n      const merged = existing ? existing.slice(0) : [];\n\n      if (incoming) {\n        if (args) {\n          // Assume an offset of 0 if args.offset omitted.\n          const { offset = 0 } = args;\n          for (let i = 0; i < incoming.length; ++i) {\n            merged[offset + i] = incoming[i];\n          }\n        } else {\n          // It's unusual (probably a mistake) for a paginated field not\n          // to receive any arguments, so you might prefer to throw an\n          // exception here, instead of recovering by appending incoming\n          // onto the existing array.\n          merged.push(...incoming);\n        }\n      }\n\n      return merged;\n    },\n  };\n}\n\n// Whether TRelayEdge<TNode> is a normalized Reference or a non-normalized\n// object, it needs a .cursor property where the relayStylePagination\n// merge function can store cursor strings taken from pageInfo. Storing an\n// extra reference.cursor property should be safe, and is easier than\n// attempting to update the cursor field of the normalized StoreObject\n// that the reference refers to, or managing edge wrapper objects\n// (something I attempted in #7023, but abandoned because of #7088).\ntype TRelayEdge<TNode> =\n  | {\n      cursor?: string;\n      node: TNode;\n    }\n  | (Reference & { cursor?: string });\n\nexport type TRelayPageInfo = {\n  hasPreviousPage: boolean;\n  hasNextPage: boolean;\n  startCursor: string;\n  endCursor: string;\n};\n\ntype TExistingRelay<TNode> = Readonly<{\n  edges: TRelayEdge<TNode>[];\n  pageInfo: TRelayPageInfo;\n}>;\n\ntype TIncomingRelay<TNode> = {\n  edges?: TRelayEdge<TNode>[];\n  pageInfo?: TRelayPageInfo;\n};\n\ntype RelayFieldPolicy<TNode> = FieldPolicy<\n  TExistingRelay<TNode> | null,\n  TIncomingRelay<TNode> | null,\n  TIncomingRelay<TNode> | null\n>;\n\n// As proof of the flexibility of field policies, this function generates\n// one that handles Relay-style pagination, without Apollo Client knowing\n// anything about connections, edges, cursors, or pageInfo objects.\n/**\n * A field policy that attempts to handle pagination for fields that adhere to\n * the [Relay Connections Spec](https://relay.dev/graphql/connections.htm).\n *\n * @param keyArgs - `keyArgs` that should be applied to the field policy\n * @returns The field policy that handles Relay pagination\n */\nexport function relayStylePagination<TNode extends Reference = Reference>(\n  keyArgs: KeyArgs = false\n): RelayFieldPolicy<TNode> {\n  return {\n    keyArgs,\n\n    read(existing, { canRead, readField }) {\n      if (!existing) return existing;\n\n      const edges: TRelayEdge<TNode>[] = [];\n      let firstEdgeCursor = \"\";\n      let lastEdgeCursor = \"\";\n      existing.edges.forEach((edge) => {\n        // Edges themselves could be Reference objects, so it's important\n        // to use readField to access the edge.edge.node property.\n        if (canRead(readField(\"node\", edge))) {\n          edges.push(edge);\n          if (edge.cursor) {\n            firstEdgeCursor = firstEdgeCursor || edge.cursor || \"\";\n            lastEdgeCursor = edge.cursor || lastEdgeCursor;\n          }\n        }\n      });\n\n      if (edges.length > 1 && firstEdgeCursor === lastEdgeCursor) {\n        firstEdgeCursor = \"\";\n      }\n\n      const { startCursor, endCursor } = existing.pageInfo || {};\n\n      return {\n        // Some implementations return additional Connection fields, such\n        // as existing.totalCount. These fields are saved by the merge\n        // function, so the read function should also preserve them.\n        ...getExtras(existing),\n        edges,\n        pageInfo: {\n          ...existing.pageInfo,\n          // If existing.pageInfo.{start,end}Cursor are undefined or \"\", default\n          // to firstEdgeCursor and/or lastEdgeCursor.\n          startCursor: startCursor || firstEdgeCursor,\n          endCursor: endCursor || lastEdgeCursor,\n        },\n      };\n    },\n\n    merge(existing, incoming, { args, isReference, readField }) {\n      if (!existing) {\n        existing = makeEmptyData();\n      }\n\n      if (!incoming) {\n        return existing;\n      }\n\n      const incomingEdges =\n        incoming.edges ?\n          incoming.edges.map((edge) => {\n            if (isReference((edge = { ...edge }))) {\n              // In case edge is a Reference, we read out its cursor field and\n              // store it as an extra property of the Reference object.\n              edge.cursor = readField<string>(\"cursor\", edge);\n            }\n            return edge;\n          })\n        : [];\n\n      if (incoming.pageInfo) {\n        const { pageInfo } = incoming;\n        const { startCursor, endCursor } = pageInfo;\n        const firstEdge = incomingEdges[0];\n        const lastEdge = incomingEdges[incomingEdges.length - 1];\n        // In case we did not request the cursor field for edges in this\n        // query, we can still infer cursors from pageInfo.\n        if (firstEdge && startCursor) {\n          firstEdge.cursor = startCursor;\n        }\n        if (lastEdge && endCursor) {\n          lastEdge.cursor = endCursor;\n        }\n        // Cursors can also come from edges, so we default\n        // pageInfo.{start,end}Cursor to {first,last}Edge.cursor.\n        const firstCursor = firstEdge && firstEdge.cursor;\n        if (firstCursor && !startCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              startCursor: firstCursor,\n            },\n          });\n        }\n        const lastCursor = lastEdge && lastEdge.cursor;\n        if (lastCursor && !endCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              endCursor: lastCursor,\n            },\n          });\n        }\n      }\n\n      let prefix = existing.edges;\n      let suffix: typeof prefix = [];\n\n      if (args && args.after) {\n        // This comparison does not need to use readField(\"cursor\", edge),\n        // because we stored the cursor field of any Reference edges as an\n        // extra property of the Reference object.\n        const index = prefix.findIndex((edge) => edge.cursor === args.after);\n        if (index >= 0) {\n          prefix = prefix.slice(0, index + 1);\n          // suffix = []; // already true\n        }\n      } else if (args && args.before) {\n        const index = prefix.findIndex((edge) => edge.cursor === args.before);\n        suffix = index < 0 ? prefix : prefix.slice(index);\n        prefix = [];\n      } else if (incoming.edges) {\n        // If we have neither args.after nor args.before, the incoming\n        // edges cannot be spliced into the existing edges, so they must\n        // replace the existing edges. See #6592 for a motivating example.\n        prefix = [];\n      }\n\n      const edges = [...prefix, ...incomingEdges, ...suffix];\n\n      const pageInfo: TRelayPageInfo = {\n        // The ordering of these two ...spreads may be surprising, but it\n        // makes sense because we want to combine PageInfo properties with a\n        // preference for existing values, *unless* the existing values are\n        // overridden by the logic below, which is permitted only when the\n        // incoming page falls at the beginning or end of the data.\n        ...incoming.pageInfo,\n        ...existing.pageInfo,\n      };\n\n      if (incoming.pageInfo) {\n        const {\n          hasPreviousPage,\n          hasNextPage,\n          startCursor,\n          endCursor,\n          ...extras\n        } = incoming.pageInfo;\n\n        // If incoming.pageInfo had any extra non-standard properties,\n        // assume they should take precedence over any existing properties\n        // of the same name, regardless of where this page falls with\n        // respect to the existing data.\n        Object.assign(pageInfo, extras);\n\n        // Keep existing.pageInfo.has{Previous,Next}Page unless the\n        // placement of the incoming edges means incoming.hasPreviousPage\n        // or incoming.hasNextPage should become the new values for those\n        // properties in existing.pageInfo. Note that these updates are\n        // only permitted when the beginning or end of the incoming page\n        // coincides with the beginning or end of the existing data, as\n        // determined using prefix.length and suffix.length.\n        if (!prefix.length) {\n          if (void 0 !== hasPreviousPage)\n            pageInfo.hasPreviousPage = hasPreviousPage;\n          if (void 0 !== startCursor) pageInfo.startCursor = startCursor;\n        }\n        if (!suffix.length) {\n          if (void 0 !== hasNextPage) pageInfo.hasNextPage = hasNextPage;\n          if (void 0 !== endCursor) pageInfo.endCursor = endCursor;\n        }\n      }\n\n      return {\n        ...getExtras(existing),\n        ...getExtras(incoming),\n        edges,\n        pageInfo,\n      };\n    },\n  };\n}\n\n// Returns any unrecognized properties of the given object.\nconst getExtras = (obj: Record<string, any>) => __rest(obj, notExtras);\nconst notExtras = [\"edges\", \"pageInfo\"];\n\nfunction makeEmptyData(): TExistingRelay<any> {\n  return {\n    edges: [],\n    pageInfo: {\n      hasPreviousPage: false,\n      hasNextPage: true,\n      startCursor: \"\",\n      endCursor: \"\",\n    },\n  };\n}\n"]}