import type { FieldPolicy, Reference } from "@apollo/client/cache";
type KeyArgs = FieldPolicy<any>["keyArgs"];
/**
 * A basic pagination field policy that always concatenates new
 * results onto the existing array, without examining options.args.
 *
 * @param keyArgs - `keyArgs` that should be applied to the field policy
 * @returns The field policy that handles concatenating field results.
 */
export declare function concatPagination<T = Reference>(keyArgs?: KeyArgs): FieldPolicy<T[]>;
/**
 * A basic field policy that uses `options.args.{offset,limit}` to splice
 * the incoming data into the existing array. If your arguments are called
 * something different (like `args.{start,count}`), feel free to copy/paste
 * this implementation and make the appropriate changes.
 *
 * @param keyArgs - `keyArgs` that should be applied to the field policy
 * @returns The field policy that handles offset/limit pagination
 */
export declare function offsetLimitPagination<T = Reference>(keyArgs?: KeyArgs): FieldPolicy<T[]>;
type TRelayEdge<TNode> = {
    cursor?: string;
    node: TNode;
} | (Reference & {
    cursor?: string;
});
export type TRelayPageInfo = {
    hasPreviousPage: boolean;
    hasNextPage: boolean;
    startCursor: string;
    endCursor: string;
};
type TExistingRelay<TNode> = Readonly<{
    edges: TRelayEdge<TNode>[];
    pageInfo: TRelayPageInfo;
}>;
type TIncomingRelay<TNode> = {
    edges?: TRelayEdge<TNode>[];
    pageInfo?: TRelayPageInfo;
};
type RelayFieldPolicy<TNode> = FieldPolicy<TExistingRelay<TNode> | null, TIncomingRelay<TNode> | null, TIncomingRelay<TNode> | null>;
/**
 * A field policy that attempts to handle pagination for fields that adhere to
 * the [Relay Connections Spec](https://relay.dev/graphql/connections.htm).
 *
 * @param keyArgs - `keyArgs` that should be applied to the field policy
 * @returns The field policy that handles Relay pagination
 */
export declare function relayStylePagination<TNode extends Reference = Reference>(keyArgs?: KeyArgs): RelayFieldPolicy<TNode>;
export {};
//# sourceMappingURL=pagination.d.ts.map