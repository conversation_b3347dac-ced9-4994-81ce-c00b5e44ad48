{"version": 3, "file": "graphQLResultHasError.js", "sources": ["../../../src/utilities/internal/graphQLResultHasError.ts"], "sourcesContent": ["import type { FormattedExecutionResult } from \"graphql\";\n\n/** @internal */\nexport function graphQLResultHasError(\n  result: FormattedExecutionResult<any>\n): boolean {\n  return !!result.errors?.length;\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CACnC,CADF,CAAA,CAAA,CAAA,CAAA,CACuC,EADvC;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAC,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAxB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAgC;AAChC;"}