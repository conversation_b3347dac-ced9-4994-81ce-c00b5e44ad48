{"version": 3, "file": "checkDocument.js", "sources": ["../../../src/utilities/internal/checkDocument.ts"], "sourcesContent": ["// Checks the document for errors and throws an exception if there is an error.\n\nimport { WeakCache } from \"@wry/caches\";\nimport type { ASTNode } from \"graphql\";\nimport type { DocumentNode, OperationTypeNode } from \"graphql\";\nimport { Kind, visit } from \"graphql\";\nimport { wrap } from \"optimism\";\n\nimport { __DEV__ } from \"@apollo/client/utilities/environment\";\nimport {\n  invariant,\n  newInvariantError,\n} from \"@apollo/client/utilities/invariant\";\n\nimport { defaultCacheSizes } from \"../../utilities/caching/sizes.js\";\nimport { cacheSizes } from \"../caching/sizes.js\";\n\nimport { getOperationName } from \"./getOperationName.js\";\n\n/**\n * Checks the document for errors and throws an exception if there is an error.\n *\n * @internal\n */\nexport const checkDocument: (\n  doc: DocumentNode,\n  expectedType?: OperationTypeNode\n) => void = wrap(\n  (doc: DocumentNode, expectedType?: OperationTypeNode): void => {\n    invariant(\n      doc && doc.kind === \"Document\",\n      `Expecting a parsed GraphQL document. Perhaps you need to wrap the query \\\nstring in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql`\n    );\n    const operations = doc.definitions.filter(\n      (d) => d.kind === \"OperationDefinition\"\n    );\n    if (__DEV__) {\n      doc.definitions.forEach((definition) => {\n        if (\n          definition.kind !== \"OperationDefinition\" &&\n          definition.kind !== \"FragmentDefinition\"\n        ) {\n          throw newInvariantError(\n            `Schema type definitions not allowed in queries. Found: \"%s\"`,\n            definition.kind\n          );\n        }\n      });\n\n      invariant(\n        operations.length <= 1,\n        `Ambiguous GraphQL document: contains %s operations`,\n        operations.length\n      );\n    }\n\n    if (expectedType) {\n      invariant(\n        operations.length == 1 && operations[0].operation === expectedType,\n        `Running a %s requires a graphql ` + `%s, but a %s was used instead.`,\n        expectedType,\n        expectedType,\n        operations[0].operation\n      );\n    }\n\n    visit(doc, {\n      Field(field, _, __, path) {\n        if (\n          field.alias &&\n          (field.alias.value === \"__typename\" ||\n            field.alias.value.startsWith(\"__ac_\")) &&\n          field.alias.value !== field.name.value\n        ) {\n          // not using `invariant` so path calculation only happens in error case\n          let current: ASTNode = doc,\n            fieldPath: string[] = [];\n          for (const key of path) {\n            current = (current as any)[key];\n            if (current.kind === Kind.FIELD) {\n              fieldPath.push(current.alias?.value || current.name.value);\n            }\n          }\n          fieldPath.splice(-1, 1, field.name.value);\n\n          throw newInvariantError(\n            '`%s` is a forbidden field alias name in the selection set for field `%s` in %s \"%s\".',\n            field.alias.value,\n            fieldPath.join(\".\"),\n            operations[0].operation,\n            getOperationName(doc, \"(anonymous)\")\n          );\n        }\n      },\n    });\n  },\n  {\n    max: cacheSizes[\"checkDocument\"] || defaultCacheSizes[\"checkDocument\"],\n    cache: WeakCache,\n  }\n);\n"], "names": [], "mappings": "AAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC;AAGvC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;AACrC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;AAE/B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAC9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EACL,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACW,EACT,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmB,EAFnB,EAAA,CAAA,CAAA,CAAA,EAGO,CAHP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAG2C;AAG3C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;AAEhD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD;;;;;;;;AAOxD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAGY,CAHZ,CAAA,CAAA,CAGgB,CACd,CAAC,CAJH,CAAA,CAIoB,EAAE,CAJtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIsD,EAAQ,CAJ9D,EAAA;IAKI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACM,CADN,CAAA,EAAA,CAAA,EACa,CADb,CAAA,CACgB,CAAC,CADjB,CAAA,CAAA,EAAA,CAAA,CAAA,EAC0B,CAD1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAIK;IACD,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAA6C,CACvC,CAAC,CAAC,EAAE,CADV,EACa,CAAC,CAAC,CADf,CAAA,CAAA,EAAA,CAAA,CAAA,EACwB,CADxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC6C,CACxC;IACD,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE;QACX,CAAN,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,EAAE,CAA3C,EAAA;YACQ,CAAR,EAAA,CACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoB,CAAC,CADrB,CAAA,CAAA,EAAA,CAAA,CAAA,EAC8B,CAD9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;gBAEU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,EACxC;gBACA,CAAV,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAEY,CAFZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEsB,CAAC,CAFvB,CAAA,CAAA,EAGW;YACH;QACF,CAAC,CAAC;QAEF,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC6B,MAErB,CAHR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGkB,CAAC,CAHnB,CAAA,CAAA,CAAA,CAAA,EAIO;IACH;IAEA,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE;QAChB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC6B,EAD7B,CAAA,EACkC,CADlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4C,CAAC,CAAC,CAAC,CAAC,CADhD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC8D,CAD9D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;YAGQ,CAHR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAIQ,CAJR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAKQ,CALR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKkB,CAAC,CAAC,CAAC,CAAC,CALtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAMO;IACH;IAEA,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAa,EAAE;QACT,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAC,EAAE,CAAtB,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAA8B,EAA9B;YACQ,CAAR,EAAA,CACU,CADV,CAAA,CAAA,CAAA,CACe,CAAC,CADhB,CAAA,CAAA,CAAA,EAAA,CAAA;gBAEU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;oBACY,CAAZ,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,EAAjD,CAAA;gBACU,CAAV,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAgD,EACtC;gBACA,CAAV,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;gBACU,CAAV,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAoC,EACxB,CADZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACkC,CADlC,CACoC;gBAC1B,CAAV,CAAA,EAAA,CAAe,CAAf,CAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,EAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,EAAE;oBACtB,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAA0C,CAAC;oBAC/B,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAA2C,EAAE;wBAC/B,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAA0C,CAA1C,CAA4C,CAA5C,CAAA,CAAA,CAAA,EAAA,CAAA,EAAqD,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAC,CAA7D,CAAA,CAAA,CAAiE,CAAC,CAAlE,CAAA,CAAA,CAAA,CAAuE,CAAC;oBAC5D;gBACF;gBACA,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAlC,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAkD,CAAC;gBAEzC,CAAV,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;oBAEY,CAFZ,CAAA,CAAA,CAAA,CAEiB,CAAC,CAFlB,CAAA,CAAA,CAAA,CAEuB,CAAC,CAFxB,CAAA,CAAA,CAAA;oBAGY,CAHZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGqB,CAAC,CAHtB,CAAA,CAAA,CAG0B,CAAC,CAH3B,CAAA,CAG8B;oBAClB,CAJZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIsB,CAAC,CAAC,CAAC,CAAC,CAJ1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBAKY,CALZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAK4B,CAAC,CAL7B,CAAA,CAKgC,EAAE,CALlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAK+C;iBACpC;YACH;QACF,CAAC;IACP,CAAK,CAAC;AACJ,CAAC,EACD;IACE,CAAJ,CAAA,CAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EAAnC,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAA0E;IACtE,CAAJ,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;AACpB,CAAG,CACF;"}