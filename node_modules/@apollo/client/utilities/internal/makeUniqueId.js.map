{"version": 3, "file": "makeUniqueId.js", "sources": ["../../../src/utilities/internal/makeUniqueId.ts"], "sourcesContent": ["const prefixCounts = new Map<string, number>();\n\n/**\n * These IDs won't be globally unique, but they will be unique within this\n * process, thanks to the counter, and unguessable thanks to the random suffix.\n *\n * @internal\n */\nexport function makeUniqueId(prefix: string) {\n  const count = prefixCounts.get(prefix) || 1;\n  prefixCounts.set(prefix, count + 1);\n  return `${prefix}:${count}:${Math.random().toString(36).slice(2)}`;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,EAAyB,CAAzB,CAAA,CAA4B,CAA5B,CAA8C;;;;;;;;;AAQ9C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAA2C,EAA3C;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAvC,CAAA,EAA4C,CAAC;IAC3C,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,EAAA,EAAmC,CAAC,CAAC;IACnC,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAlB,CAAA,CAAA,CAAsB,CAAtB,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAAA,CAAA,CAA+B,CAA/B,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAA0C,CAA1C,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAwD,CAAC,CAAC,CAA1D,CAAA,CAAA,CAAA,CAA+D,CAAC,CAAC,CAAC,CAAlE,CAAoE;AACpE;"}