{"version": 3, "file": "isDocumentNode.js", "sources": ["../../../src/utilities/internal/isDocumentNode.ts"], "sourcesContent": ["import type { DocumentNode } from \"graphql\";\n\nimport { isNonNullObject } from \"./isNonNullObject.js\";\n\n/** @internal */\nexport function isDocumentNode(value: unknown): value is DocumentNode {\n  return (\n    isNonNullObject(value) &&\n    (value as DocumentNode).kind === \"Document\" &&\n    Array.isArray((value as DocumentNode).definitions)\n  );\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;;;;;;AAGtD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAA6C,EAA7C;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CACL,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CAAA,CACyB,EADzB,CAAA;QAEK,CAAL,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,EAAA,CAAA,CAAA,EAAqC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAE,CAAnB,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CACnD;AACH;"}