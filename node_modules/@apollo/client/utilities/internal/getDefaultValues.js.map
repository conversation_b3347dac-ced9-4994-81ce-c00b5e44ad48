{"version": 3, "file": "getDefaultValues.js", "sources": ["../../../src/utilities/internal/getDefaultValues.ts"], "sourcesContent": ["import type { OperationDefinitionNode, ValueNode } from \"graphql\";\n\nimport { valueToObjectRepresentation } from \"./valueToObjectRepresentation.js\";\n\n/** @internal */\nexport function getDefaultValues(\n  definition: OperationDefinitionNode | undefined\n): Record<string, any> {\n  const defaultValues = {};\n  const defs = definition && definition.variableDefinitions;\n  if (defs && defs.length) {\n    defs.forEach((def) => {\n      if (def.defaultValue) {\n        valueToObjectRepresentation(\n          defaultValues,\n          def.variable.name,\n          def.defaultValue as ValueNode\n        );\n      }\n    });\n  }\n  return defaultValues;\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8E;;;;;;AAG9E,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAC9B,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiD,EADjD;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAA0B;IACxB,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,EAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D;IACzD,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,EAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE;QACvB,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAC,CAAlB,CAAA,CAAqB,EAAE,CAAvB,EAAA;YACM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAAE;gBACpB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CACzB,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuB,EACb,CAFV,CAAA,CAEa,CAAC,CAFd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEsB,CAAC,CAFvB,CAAA,CAAA,CAE2B,EACjB,CAHV,CAAA,CAGa,CAAC,CAHd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGuC,CAC9B;YACH;QACF,CAAC,CAAC;IACJ;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;AACtB;"}