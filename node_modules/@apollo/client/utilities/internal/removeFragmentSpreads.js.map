{"version": 3, "file": "removeFragmentSpreads.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/removeFragmentSpreads.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,MAAM,UAAU,2BAA2B,CAAC,QAAsB;IAChE,OAAO,KAAK,CAAC,QAAQ,EAAE;QACrB,cAAc,CAAC,IAAI;YACjB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC;gBAClE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\nimport { visit } from \"graphql\";\n\nexport function removeMaskedFragmentSpreads(document: DocumentNode) {\n  return visit(document, {\n    FragmentSpread(node) {\n      if (!node.directives?.some(({ name }) => name.value === \"unmask\")) {\n        return null;\n      }\n    },\n  });\n}\n"]}