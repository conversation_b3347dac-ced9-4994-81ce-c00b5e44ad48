{"version": 3, "file": "filterMap.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/filterMap.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC;AASlC,MAAM,UAAU,SAAS,CACvB,EAA6C,EAC7C,WAAW,GAAG,GAAG,EAAE,CAAC,SAAS;IAE7B,OAAO,CAAC,MAAM,EAAE,EAAE,CAChB,IAAI,UAAU,CAAI,CAAC,UAAU,EAAE,EAAE;QAC/B,IAAI,OAAO,GAAG,WAAW,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC,SAAS,CAAC;YACtB,IAAI,CAAC,KAAK;gBACR,IAAI,MAAqB,CAAC;gBAC1B,IAAI,CAAC;oBACH,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC9B,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC;gBACD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,OAAO;gBACT,CAAC;gBACD,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;YACD,KAAK,CAAC,GAAG;gBACP,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;YACD,QAAQ;gBACN,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,CAAC;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC", "sourcesContent": ["import type { OperatorFunction } from \"rxjs\";\nimport { Observable } from \"rxjs\";\n\nexport function filterMap<T, R>(\n  fn: (value: T, context: undefined) => R | undefined\n): OperatorFunction<T, R>;\nexport function filterMap<T, R, Context>(\n  fn: (value: T, context: Context) => R | undefined,\n  makeContext: () => NoInfer<Context>\n): OperatorFunction<T, R>;\nexport function filterMap<T, R>(\n  fn: (value: T, context: any) => R | undefined,\n  makeContext = () => undefined\n): OperatorFunction<T, R> {\n  return (source) =>\n    new Observable<R>((subscriber) => {\n      let context = makeContext();\n      return source.subscribe({\n        next(value) {\n          let result: R | undefined;\n          try {\n            result = fn(value, context);\n          } catch (e) {\n            subscriber.error(e);\n          }\n          if (result === undefined) {\n            return;\n          }\n          subscriber.next(result);\n        },\n        error(err) {\n          subscriber.error(err);\n        },\n        complete() {\n          subscriber.complete();\n        },\n      });\n    });\n}\n"]}