{"version": 3, "file": "makeReference.js", "sources": ["../../../src/utilities/internal/makeReference.ts"], "sourcesContent": ["import type { Reference } from \"@apollo/client/utilities\";\n\n/** @internal */\nexport function makeReference(id: string): Reference {\n  return { __ref: String(id) };\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAwC,EAAxC;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,EAAE,CAAX,CAAA,CAAA,CAAA,CAAgB,EAAE,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAA2B,EAA3B,CAA8B;AAC9B;"}