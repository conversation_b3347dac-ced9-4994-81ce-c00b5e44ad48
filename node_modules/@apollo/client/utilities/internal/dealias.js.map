{"version": 3, "file": "dealias.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/dealias.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,uEAAuE;AACvE,4EAA4E;AAC5E,oBAAoB;AACpB,MAAM,UAAU,OAAO,CACrB,UAAkD,EAClD,YAA8B;IAE9B,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,MAAM,IAAI,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;IAE/B,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;QAChD,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import type { SelectionSetNode } from \"graphql\";\nimport { Kind } from \"graphql\";\n\n// Note: this is a shallow dealias function. We might consider a future\n// improvement of dealiasing all nested data. Until that need arises, we can\n// keep this simple.\nexport function dealias(\n  fieldValue: Record<string, any> | null | undefined,\n  selectionSet: SelectionSetNode\n) {\n  if (!fieldValue) {\n    return fieldValue;\n  }\n\n  const data = { ...fieldValue };\n\n  for (const selection of selectionSet.selections) {\n    if (selection.kind === Kind.FIELD && selection.alias) {\n      data[selection.name.value] = fieldValue[selection.alias.value];\n      delete data[selection.alias.value];\n    }\n  }\n\n  return data;\n}\n"]}