{"version": 3, "file": "index.production.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/index.production.ts"], "names": [], "mappings": "AAAA,gDAAgD;AAChD,cAAc,YAAY,CAAC;AAE3B,SAAS,WAAW;IAClB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACxD,CAAC;AACD,MAAM,CAAC,MAAM,6BAA6B,GACtC,WAAqF,EACvF,8BAA8B,GAC5B,WAAsF,EACxF,+BAA+B,GAC7B,WAAuF,CAAC", "sourcesContent": ["// eslint-disable-next-line no-restricted-syntax\nexport * from \"./index.js\";\n\nfunction unsupported() {\n  throw new Error(\"only supported in development mode\");\n}\nexport const getApolloCacheMemoryInternals =\n    unsupported as typeof import(\"./getMemoryInternals.js\").getApolloCacheMemoryInternals,\n  getApolloClientMemoryInternals =\n    unsupported as typeof import(\"./getMemoryInternals.js\").getApolloClientMemoryInternals,\n  getInMemoryCacheMemoryInternals =\n    unsupported as typeof import(\"./getMemoryInternals.js\").getInMemoryCacheMemoryInternals;\n"]}