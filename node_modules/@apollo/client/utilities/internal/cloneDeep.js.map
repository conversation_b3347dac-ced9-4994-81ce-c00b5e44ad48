{"version": 3, "file": "cloneDeep.js", "sources": ["../../../src/utilities/internal/cloneDeep.ts"], "sourcesContent": ["const { toString } = Object.prototype;\n\n/**\n * Deeply clones a value to create a new instance.\n *\n * @internal\n */\nexport function cloneDeep<T>(value: T): T {\n  return __cloneDeep(value);\n}\n\nfunction __cloneDeep<T>(val: T, seen?: Map<any, any>): T {\n  switch (toString.call(val)) {\n    case \"[object Array]\": {\n      seen = seen || new Map();\n      if (seen.has(val)) return seen.get(val);\n      const copy: T & any[] = (val as any).slice(0);\n      seen.set(val, copy);\n      copy.forEach(function (child, i) {\n        copy[i] = __cloneDeep(child, seen);\n      });\n      return copy;\n    }\n\n    case \"[object Object]\": {\n      seen = seen || new Map();\n      if (seen.has(val)) return seen.get(val);\n      // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n      // possible in all JS environments, so we will assume they exist/work.\n      const copy = Object.create(Object.getPrototypeOf(val));\n      seen.set(val, copy);\n      Object.keys(val as T & Record<string, any>).forEach((key) => {\n        copy[key] = __cloneDeep((val as any)[key], seen);\n      });\n      return copy;\n    }\n\n    default:\n      return val;\n  }\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,EAAM,EAAE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;;;;;;;;AAOrC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAI,CAA7B,CAAA,CAAA,CAAA,CAAqC,EAArC;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC;AAC3B;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAI,CAAxB,CAAA,CAA8B,EAAE,CAAhC,CAAA,CAAA,CAAoD,EAApD;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAA2B,CAAC,EAAE;QAC1B,CAAJ,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE;YACrB,CAAN,CAAA,CAAA,EAAA,EAAa,CAAb,CAAA,CAAA,EAAA,CAAA,EAAqB,CAArB,CAAA,EAAyB,CAAzB,CAAA,CAA4B,CAA5B,CAA8B;YACxB,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAsB,CAAC;gBAAE,CAAzB,CAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAA4C,CAAC;YACvC,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,EAAA,EAA+B,CAA/B,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAC,CAAC;YAC7C,CAAN,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAwB,CAAC;YACnB,CAAN,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAA6B,CAA7B,CAAA,CAAA,CAAA,CAAkC,EAAE,CAAC,EAArC;gBACQ,CAAR,CAAA,CAAA,CAAY,CAAC,CAAC,EAAd,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAmC,EAAE,CAArC,CAAA,CAAA,CAAyC,CAAC;YACpC,CAAC,CAAC;YACF,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAiB;QACb;QAEA,CAAJ,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAAE;YACtB,CAAN,CAAA,CAAA,EAAA,EAAa,CAAb,CAAA,CAAA,EAAA,CAAA,EAAqB,CAArB,CAAA,EAAyB,CAAzB,CAAA,CAA4B,CAA5B,CAA8B;YACxB,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAsB,CAAC;gBAAE,CAAzB,CAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAA4C,CAAC;YACvC,CAAN,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACM,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAvD,CAAA,CAA0D,CAAC,CAAC;YACtD,CAAN,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAwB,CAAC;YACnB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAgD,CAAC,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC,CAAC,CAA3D,CAAA,CAA8D,EAAE,CAAhE,EAAA;gBACQ,CAAR,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAgB,EAAhB,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAE,CAAjC,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAgD,CAAC,EAAE,CAAnD,CAAA,CAAA,CAAuD,CAAC;YAClD,CAAC,CAAC;YACF,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAiB;QACb;QAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAgB;IACd;AACF;"}