{"version": 3, "file": "getGraphQLErrorsFromResult.js", "sources": ["../../../src/utilities/internal/getGraphQLErrorsFromResult.ts"], "sourcesContent": ["import type { GraphQLFormattedError } from \"graphql\";\n\n/** @internal */\nexport function getGraphQLErrorsFromResult(result: {\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n}): Array<GraphQLFormattedError> {\n  return [...(result.errors || [])];\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAEC,EAFD;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAC,CAAV,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA+B,CAA/B,CAAiC,CAAC,CAAC;AACnC;"}