{"version": 3, "file": "getFragmentDefinition.js", "sources": ["../../../src/utilities/internal/getFragmentDefinition.ts"], "sourcesContent": ["import type { DocumentNode, FragmentDefinitionNode } from \"graphql\";\n\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\n/** @internal */\nexport function getFragmentDefinition(\n  doc: DocumentNode\n): FragmentDefinitionNode {\n  invariant(\n    doc.kind === \"Document\",\n    `Expecting a parsed GraphQL document. Perhaps you need to wrap the query \\\nstring in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql`\n  );\n\n  invariant(\n    doc.definitions.length <= 1,\n    \"Fragment must have exactly one definition.\"\n  );\n\n  const fragmentDef = doc.definitions[0] as FragmentDefinitionNode;\n\n  invariant(\n    fragmentDef.kind === \"FragmentDefinition\",\n    \"Must be a fragment definition.\"\n  );\n\n  return fragmentDef as FragmentDefinitionNode;\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;;;;;;AAG9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CACnC,CADF,CAAA,CACmB,EADnB;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CADJ,CAAA,CACO,CAAC,CADR,CAAA,CAAA,EAAA,CAAA,CAAA,EACiB,CADjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAIG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CADJ,CAAA,CACO,CAAC,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC8B,KAE3B;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAC,CAA2B;IAEhE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACe,CAAC,CADhB,CAAA,CAAA,EAAA,CAAA,CAAA,EACyB,CADzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAGG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C;AAC9C;"}