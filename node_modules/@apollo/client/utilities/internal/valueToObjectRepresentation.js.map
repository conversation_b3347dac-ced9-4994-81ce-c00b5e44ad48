{"version": 3, "file": "valueToObjectRepresentation.js", "sources": ["../../../src/utilities/internal/valueToObjectRepresentation.ts"], "sourcesContent": ["import type { EnumValueNode, NameNode, ValueNode } from \"graphql\";\nimport { Kind } from \"graphql\";\n\nimport { newInvariantError } from \"@apollo/client/utilities/invariant\";\n\n/** @internal */\nexport function valueToObjectRepresentation(\n  argObj: any,\n  name: NameNode,\n  value: ValueNode,\n  variables?: Object\n) {\n  if (value.kind === Kind.INT || value.kind === Kind.FLOAT) {\n    argObj[name.value] = Number(value.value);\n  } else if (value.kind === Kind.BOOLEAN || value.kind === Kind.STRING) {\n    argObj[name.value] = value.value;\n  } else if (value.kind === Kind.OBJECT) {\n    const nestedArgObj = {};\n    value.fields.map((obj) =>\n      valueToObjectRepresentation(nestedArgObj, obj.name, obj.value, variables)\n    );\n    argObj[name.value] = nestedArgObj;\n  } else if (value.kind === Kind.VARIABLE) {\n    const variableValue = (variables || ({} as any))[value.name.value];\n    argObj[name.value] = variableValue;\n  } else if (value.kind === Kind.LIST) {\n    argObj[name.value] = value.values.map((listValue) => {\n      const nestedArgArrayObj = {};\n      valueToObjectRepresentation(\n        nestedArgArrayObj,\n        name,\n        listValue,\n        variables\n      );\n      return (nestedArgArrayObj as any)[name.value];\n    });\n  } else if (value.kind === Kind.ENUM) {\n    argObj[name.value] = (value as EnumValueNode).value;\n  } else if (value.kind === Kind.NULL) {\n    argObj[name.value] = null;\n  } else {\n    throw newInvariantError(\n      `The inline argument \"%s\" of kind \"%s\"` +\n        \"is not supported. Use variables instead of inline arguments to \" +\n        \"overcome this limitation.\",\n      name.value,\n      (value as any).kind\n    );\n  }\n}\n"], "names": [], "mappings": "AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAE9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE;;;;;;AAGtE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CACzC,CADF,CAAA,CAAA,CAAA,CAAA,CACa,EACX,CAFF,CAAA,CAAA,CAEgB,EACd,CAHF,CAAA,CAAA,CAAA,CAGkB,EAChB,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIoB,EAJpB;IAME,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,EAAA,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,EAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,EAAA,CAAA,CAAA,EAAgD,CAAhD,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAA0D,EAAE;QACxD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAA2C,CAAC;IAC1C;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,EAAA,CAAA,CAAA,EAA2D,CAA3D,CAAA,CAAA,CAA+D,CAAC,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAsE,EAAE;QACpE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAoC;IAClC;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAE;QACrC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAA2B;QACvB,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAoB,CAAC,CAAC,CAAtB,CAAA,CAAyB,EAAE,CAA3B,EACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,CAAC,CADlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC8C,EAAE,CADhD,CAAA,CACmD,CAAC,CADpD,CAAA,CAAA,CACwD,EAAE,CAD1D,CAAA,CAC6D,CAAC,CAD9D,CAAA,CAAA,CAAA,CACmE,EAAE,CADrE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC8E,CAAC,CAC1E;QACD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;IACnC;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,EAAE;QACvC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAyC,CAAzC,CAAmD,CAAC,CAAC,CAArD,CAAA,CAAA,CAAA,CAA0D,CAAC,CAA3D,CAAA,CAAA,CAA+D,CAAC,CAAhE,CAAA,CAAA,CAAA,CAAqE,CAAC;QAClE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC;IACpC;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAqC,EAAE;QACnC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAyC,CAAC,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,EAAE,CAAtD,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAhC,CAAkC;YAC5B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CACzB,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyB,EACjB,CAFR,CAAA,CAAA,CAEY,EACJ,CAHR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGiB,EACT,CAJR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIiB,CACV;YACD,CAAN,CAAA,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAkD,CAAC;QAC/C,CAAC,CAAC;IACJ;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAqC,EAAE;QACnC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAuD;IACrD;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAqC,EAAE;QACnC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAArB,EAAyB,CAAzB,CAAA,CAAA,CAA6B;IAC3B;IAAF,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAIM,CADN,CAAA,CAAA,CACU,CAAC,CADX,CAAA,CAAA,CAAA,GAEO,CAFP,CAAA,CAAA,CAAA,CAEoB,CAAC,CAFrB,CAAA,CAAA,EAGK;IACH;AACF;"}