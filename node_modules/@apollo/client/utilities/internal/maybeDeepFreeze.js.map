{"version": 3, "file": "maybeDeepFreeze.js", "sources": ["../../../src/utilities/internal/maybeDeepFreeze.ts"], "sourcesContent": ["import { __DEV__ } from \"@apollo/client/utilities/environment\";\n\nimport { deepFreeze } from \"./deepFreeze.js\";\n\n/** @internal */\nexport function maybeDeepFreeze<T>(obj: T): T {\n  if (__DEV__) {\n    deepFreeze(obj);\n  }\n  return obj;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;;;;;;AAG5C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAI,CAAnC,CAAA,CAAyC,EAAzC;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE;QACX,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,CAAC;IACjB;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAY;AACZ;"}