{"version": 3, "file": "caches.js", "sources": ["../../../src/utilities/internal/caches.ts"], "sourcesContent": ["import { StrongCache, WeakCache } from \"@wry/caches\";\n\ninterface CleanableCache {\n  size: number;\n  max?: number;\n  clean: () => void;\n}\nconst scheduledCleanup = new WeakSet<CleanableCache>();\nfunction schedule(cache: CleanableCache) {\n  if (cache.size <= (cache.max || -1)) {\n    return;\n  }\n  if (!scheduledCleanup.has(cache)) {\n    scheduledCleanup.add(cache);\n    setTimeout(() => {\n      cache.clean();\n      scheduledCleanup.delete(cache);\n    }, 100);\n  }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport const AutoCleanedWeakCache = function (\n  max?: number | undefined,\n  dispose?: ((value: any, key: any) => void) | undefined\n) {\n  /*\n  Some builds of `WeakCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  const cache = new WeakCache(max, dispose);\n  cache.set = function (key: any, value: any) {\n    const ret = WeakCache.prototype.set.call(this, key, value);\n    schedule(this as any as CleanableCache);\n    return ret;\n  };\n  return cache;\n} as any as typeof WeakCache;\n/**\n * @internal\n */\nexport type AutoCleanedWeakCache<K extends object, V> = WeakCache<K, V>;\n\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport const AutoCleanedStrongCache = function (\n  max?: number | undefined,\n  dispose?: ((value: any, key: any) => void) | undefined\n) {\n  /*\n  Some builds of `StrongCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  const cache = new StrongCache(max, dispose);\n  cache.set = function (key: any, value: any) {\n    const ret = StrongCache.prototype.set.call(this, key, value);\n    schedule(this as any as CleanableCache);\n    return ret;\n  };\n  return cache;\n} as any as typeof StrongCache;\n/**\n * @internal\n */\nexport type AutoCleanedStrongCache<K, V> = StrongCache<K, V>;\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAuC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD;AAOpD,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAApC,CAAsD;AACtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAuC,EAAvC;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,EAAA,CAAA,EAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,EAAA,CAAA,EAAkC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA;IACE;IACA,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAiC,CAAC,EAAE;QAChC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC;QAC3B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,EAAkB,CAAlB,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAiB,CAAjB,CAAmB;YACb,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAmC,CAAC;QAChC,CAAC,EAAE,CAAP,CAAA,CAAU,CAAC;IACT;AACF;;;;;;;;;;;;;;AAYA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CACE,CADF,CAAA,CAC0B,EACxB,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAEwD,EAFxD;IAIE,CAAF;;;;;;IAMA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAiC,EAAE,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;IACzC,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,EAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAwB,CAAxB,CAAA,CAAgC,EAAE,CAAlC,CAAA,CAAA,CAAA,CAA4C,EAA5C;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAiD,EAAE,CAAnD,CAAA,CAAsD,EAAE,CAAxD,CAAA,CAAA,CAAA,CAA6D,CAAC;QAC1D,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAA0C,CAAC;QACvC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAc;IACZ,CAAC;IACD,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc;AACd,CAA4B;;;;;;;;;;;;;;AAiB5B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CACE,CADF,CAAA,CAC0B,EACxB,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAEwD,EAFxD;IAIE,CAAF;;;;;;IAMA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAmC,EAAE,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC;IAC3C,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,EAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAwB,CAAxB,CAAA,CAAgC,EAAE,CAAlC,CAAA,CAAA,CAAA,CAA4C,EAA5C;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAmD,EAAE,CAArD,CAAA,CAAwD,EAAE,CAA1D,CAAA,CAAA,CAAA,CAA+D,CAAC;QAC5D,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAA0C,CAAC;QACvC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAc;IACZ,CAAC;IACD,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc;AACd,CAA8B;"}