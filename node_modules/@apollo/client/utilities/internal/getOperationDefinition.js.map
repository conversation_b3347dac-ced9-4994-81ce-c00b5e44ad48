{"version": 3, "file": "getOperationDefinition.js", "sources": ["../../../src/utilities/internal/getOperationDefinition.ts"], "sourcesContent": ["import type { DocumentNode, OperationDefinitionNode } from \"graphql\";\n\nimport { checkDocument } from \"./checkDocument.js\";\n\n/** @internal */\nexport function getOperationDefinition(\n  doc: DocumentNode\n): OperationDefinitionNode | undefined {\n  checkDocument(doc);\n  return doc.definitions.filter(\n    (definition): definition is OperationDefinitionNode =>\n      definition.kind === \"OperationDefinition\"\n  )[0];\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD;;;;;;AAGlD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CACpC,CADF,CAAA,CACmB,EADnB;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAmB,CAAC;IAClB,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B,CAC3B,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACe,EAAyC,CADxD,EAEM,CAFN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEgB,CAAC,CAFjB,CAAA,CAAA,EAAA,CAAA,CAAA,EAE0B,CAF1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE+C,CAC5C,CAAC,CAAC,CAAC;AACN;"}