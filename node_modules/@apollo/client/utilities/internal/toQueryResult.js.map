{"version": 3, "file": "toQueryResult.js", "sources": ["../../../src/utilities/internal/toQueryResult.ts"], "sourcesContent": ["import type { ApolloClient, ObservableQuery } from \"@apollo/client\";\n\n/** @internal */\nexport function toQueryResult<TData = unknown>(\n  value: ObservableQuery.Result<TData>\n) {\n  const result: ApolloClient.QueryResult<TData> = {\n    data: value.data as TData | undefined,\n  };\n\n  if (value.error) {\n    result.error = value.error;\n  }\n\n  return result;\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAC3B,CADF,CAAA,CAAA,CAAA,CACsC,EADtC;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkD;QAC9C,CAAJ,CAAA,CAAA,CAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAyC;IACzC,CAAG;IAED,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAiB,EAAE;QACf,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAA8B;IAC5B;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe;AACf;"}