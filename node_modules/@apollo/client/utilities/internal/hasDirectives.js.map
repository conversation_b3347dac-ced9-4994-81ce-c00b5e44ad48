{"version": 3, "file": "hasDirectives.js", "sources": ["../../../src/utilities/internal/hasDirectives.ts"], "sourcesContent": ["import type { ASTNode } from \"graphql\";\nimport { BREAK, visit } from \"graphql\";\n\n/** @internal */\nexport function hasDirectives(names: string[], root: ASTNode, all?: boolean) {\n  const nameSet = new Set(names);\n  const uniqueCount = nameSet.size;\n\n  visit(root, {\n    Directive(node) {\n      if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n        return BREAK;\n      }\n    },\n  });\n\n  // If we found all the names, nameSet will be empty. If we only care about\n  // finding some of them, the < condition is sufficient.\n  return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\n"], "names": [], "mappings": "AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC;;;;;;AAGtC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAA6C,EAAE,CAA/C,CAAA,CAAA,CAA4D,EAAE,CAA9D,CAAA,CAA2E,EAA3E;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,EAAsB,CAAtB,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC;IAC9B,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAkC;IAEhC,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAY,EAAE;QACV,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAkB,EAAlB;YACM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAwC,EAAxC,CAAA,EAA6C,CAAC,CAAC,CAA/C,CAAA,EAAA,CAAA,EAAsD,CAAC,CAAvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC,CAA/D,CAAA,CAAA,CAAmE,CAAC,EAAE;gBAC9D,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAoB;YACd;QACF,CAAC;IACL,CAAG,CAAC;IAEF,CAAF,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAa,EAAE,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,EAA6B,EAAE,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,EAAA,EAA8C,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD;AACzD;"}