{"version": 3, "file": "isArray.js", "sources": ["../../../src/utilities/internal/isArray.ts"], "sourcesContent": ["/**\n * A version of Array.isArray that works better with readonly arrays.\n *\n * @internal\n */\nexport const isArray: (a: any) => a is any[] | readonly any[] = Array.isArray;\n"], "names": [], "mappings": ";;;;;;;AAKA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgE,CAAhE,CAAA,CAAA,CAAA,CAAqE,CAAC,CAAtE,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6E;"}