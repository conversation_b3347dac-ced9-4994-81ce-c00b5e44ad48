{"version": 3, "file": "storeKeyNameFromField.js", "sources": ["../../../src/utilities/internal/storeKeyNameFromField.ts"], "sourcesContent": ["import type { FieldNode } from \"graphql\";\n\nimport { getStoreKeyName } from \"./getStoreKeyName.js\";\nimport { valueToObjectRepresentation } from \"./valueToObjectRepresentation.js\";\n\n/** @internal */\nexport function storeKeyNameFromField(\n  field: FieldNode,\n  variables?: Object\n): string {\n  let directivesObj: any = null;\n  if (field.directives) {\n    directivesObj = {};\n    field.directives.forEach((directive) => {\n      directivesObj[directive.name.value] = {};\n\n      if (directive.arguments) {\n        directive.arguments.forEach(({ name, value }) =>\n          valueToObjectRepresentation(\n            directivesObj[directive.name.value],\n            name,\n            value,\n            variables\n          )\n        );\n      }\n    });\n  }\n\n  let argObj: any = null;\n  if (field.arguments && field.arguments.length) {\n    argObj = {};\n    field.arguments.forEach(({ name, value }) =>\n      valueToObjectRepresentation(argObj, name, value, variables)\n    );\n  }\n\n  return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;AACtD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8E;;;;;;AAG9E,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CACnC,CADF,CAAA,CAAA,CAAA,CACkB,EAChB,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEoB,EAFpB;IAIE,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAA+B;IAC7B,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE;QACpB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAsB;QAClB,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAE,CAAzC,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAwC,EAAxC,EAA4C,CAA5C,CAA8C;YAExC,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE;gBACvB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAAC,EAAE,CAAvC,CAAA,CAAA,CAA2C,EAAE,CAA7C,CAAA,CAAA,CAAA,EAAA,CAAoD,EAAE,CAAtD,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqC,CACzB,CAFZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEyB,CAAC,CAF1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmC,CAAC,CAFpC,CAAA,CAAA,CAEwC,CAAC,CAFzC,CAAA,CAAA,CAAA,CAE8C,CAAC,EACnC,CAHZ,CAAA,CAAA,CAGgB,EACJ,CAJZ,CAAA,CAAA,CAAA,CAIiB,EACL,CALZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKqB,CACV,CACF;YACH;QACF,CAAC,CAAC;IACJ;IAEA,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAwB;IACtB,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,EAAE;QAC7C,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAA,EAAa,CAAb,CAAe;QACX,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAAC,EAAE,CAA/B,CAAA,CAAA,CAAmC,EAAE,CAArC,CAAA,CAAA,CAAA,EAAA,CAA4C,EAAE,CAA9C,EACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,CAAC,CADlC,CAAA,CAAA,CAAA,CAAA,CACwC,EAAE,CAD1C,CAAA,CAAA,CAC8C,EAAE,CADhD,CAAA,CAAA,CAAA,CACqD,EAAE,CADvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgE,CAAC,CAC5D;IACH;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAyC,EAAE,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAiD,EAAE,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgE,CAAC;AACjE;"}