{"version": 3, "file": "deepFreeze.js", "sources": ["../../../src/utilities/internal/deepFreeze.ts"], "sourcesContent": ["import { __DEV__ } from \"@apollo/client/utilities/environment\";\n\nimport { isNonNullObject } from \"./isNonNullObject.js\";\n\n/** @internal only to be imported in tests */\nexport function deepFreeze(value: any) {\n  const workSet = new Set([value]);\n  workSet.forEach((obj) => {\n    if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n      Object.getOwnPropertyNames(obj).forEach((name) => {\n        if (isNonNullObject(obj[name])) workSet.add(obj[name]);\n      });\n    }\n  });\n  return value;\n}\n\nfunction shallowFreeze<T extends object>(obj: T): T | null {\n  if (__DEV__ && !Object.isFrozen(obj)) {\n    try {\n      Object.freeze(obj);\n    } catch (e) {\n      // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n      // they all throw a TypeError when you try, so we re-throw any exceptions\n      // that are not TypeErrors, since that would be unexpected.\n      if (e instanceof TypeError) return null;\n      throw e;\n    }\n  }\n  return obj;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;;;;;;AAGtD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAqC,EAArC;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,EAAsB,CAAtB,CAAA,CAAyB,CAAC,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAC;IAChC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAC,CAAnB,CAAA,CAAsB,EAAE,CAAxB,EAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAA2B,EAA3B,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAA9C,CAAA,CAAiD,EAAjD,CAAA,CAAA,EAAuD,CAAvD,CAAA,CAA0D,EAAE;YACtD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAoC,CAAC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAAC,CAA/C,CAAA,CAAA,CAAmD,EAAE,CAArD,EAAA;gBACQ,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAoC,CAAC,CAAC;oBAAE,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,CAAhD,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAuD,CAAC,CAAxD,CAAA,CAAA,CAA4D,CAAC,CAAC;YACxD,CAAC,CAAC;QACJ;IACF,CAAC,CAAC;IACF,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc;AACd;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAmB,CAAzC,CAAA,CAA+C,EAA/C;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAqC,CAAC,EAAE;QACpC,CAAJ,CAAA,EAAQ;YACF,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAuB,CAAC;QACpB;QAAE,CAAN,CAAA,CAAA,CAAA,EAAA,CAAa,CAAC,EAAE;YACV,CAAN,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;YACM,CAAN,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,EAAA,CAAU,EAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC;gBAAE,CAAlC,CAAA,CAAA,CAAA,CAAA,EAAyC,CAAzC,CAAA,CAAA,CAA6C;YACvC,CAAN,CAAA,CAAA,CAAA,EAAY,CAAC;QACT;IACF;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAY;AACZ;"}