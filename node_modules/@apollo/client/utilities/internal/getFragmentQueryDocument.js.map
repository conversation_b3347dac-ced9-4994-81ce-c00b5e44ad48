{"version": 3, "file": "getFragmentQueryDocument.js", "sources": ["../../../src/utilities/internal/getFragmentQueryDocument.ts"], "sourcesContent": ["import type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  Kind,\n  OperationTypeNode,\n} from \"graphql\";\n\nimport {\n  invariant,\n  newInvariantError,\n} from \"@apollo/client/utilities/invariant\";\n\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo {\n *   a\n *   b\n *   c\n * }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * {\n *   ...foo\n * }\n *\n * fragment foo on Foo {\n *   a\n *   b\n *   c\n * }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n *\n * @internal\n */\nexport function getFragmentQueryDocument(\n  document: DocumentNode,\n  fragmentName?: string\n): DocumentNode {\n  let actualFragmentName = fragmentName;\n\n  // Build an array of all our fragment definitions that will be used for\n  // validations. We also do some validations on the other definitions in the\n  // document while building this list.\n  const fragments: Array<FragmentDefinitionNode> = [];\n  document.definitions.forEach((definition) => {\n    // Throw an error if we encounter an operation definition because we will\n    // define our own operation definition later on.\n    if (definition.kind === \"OperationDefinition\") {\n      throw newInvariantError(\n        `Found a %s operation%s. ` +\n          \"No operations are allowed when using a fragment as a query. Only fragments are allowed.\",\n        definition.operation,\n        definition.name ? ` named '${definition.name.value}'` : \"\"\n      );\n    }\n    // Add our definition to the fragments array if it is a fragment\n    // definition.\n    if (definition.kind === \"FragmentDefinition\") {\n      fragments.push(definition);\n    }\n  });\n\n  // If the user did not give us a fragment name then let us try to get a\n  // name from a single fragment in the definition.\n  if (typeof actualFragmentName === \"undefined\") {\n    invariant(\n      fragments.length === 1,\n      `Found %s fragments. \\`fragmentName\\` must be provided when there is not exactly 1 fragment.`,\n      fragments.length\n    );\n    actualFragmentName = fragments[0].name.value;\n  }\n\n  // Generate a query document with an operation that simply spreads the\n  // fragment inside of it.\n  const query: DocumentNode = {\n    ...document,\n    definitions: [\n      {\n        kind: \"OperationDefinition\" as Kind.OPERATION_DEFINITION,\n        // OperationTypeNode is an enum\n        operation: \"query\" as OperationTypeNode,\n        selectionSet: {\n          kind: \"SelectionSet\" as Kind.SELECTION_SET,\n          selections: [\n            {\n              kind: \"FragmentSpread\" as Kind.FRAGMENT_SPREAD,\n              name: {\n                kind: \"Name\" as Kind.NAME,\n                value: actualFragmentName,\n              },\n            },\n          ],\n        },\n      },\n      ...document.definitions,\n    ],\n  };\n\n  return query;\n}\n"], "names": [], "mappings": "AAOA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EACL,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACW,EACT,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmB,EAFnB,EAAA,CAAA,CAAA,CAAA,EAGO,CAHP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAG2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoC3C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CACtC,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACwB,EACtB,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEuB,EAFvB;IAIE,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC;IAErC,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmD,CAAnD,CAAqD;IACnD,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,EAAE,CAA5C,EAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,EAAE;YAC7C,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;gBAGQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBAEQ,CAFR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEkB,CAAC,CAFnB,CAAA,CAAA,EAEwB,EAAE,EAF1B,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAEqC,CAFrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE+C,CAAC,CAFhD,CAAA,CAAA,CAEoD,CAAC,CAFrD,CAAA,CAAA,CAAA,CAE0D,CAF1D,CAAA,EAE8D,EAAE,CAFhE;aAGO;QACH;QACA,CAAJ,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,EAAE;YAC5C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC;QAC5B;IACF,CAAC,CAAC;IAEF,CAAF,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,EAAE;QAC7C,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACe,CAAC,CADhB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC2B,OAErB,CAHN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGe,CAAC,CAHhB,CAAA,CAAA,CAAA,CAAA,EAIK;QACD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAC,CAAC,CAAC,CAAtC,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAgD;IAC9C;IAEA,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAA8B;QAC1B,CAAJ,CAAA,CAAO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;QACX,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE;YACX;gBACE,CAAR,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgE;gBACxD,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;gBACQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C;gBACvC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE;oBACZ,CAAV,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD;oBAC1C,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE;wBACV;4BACE,CAAd,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D;4BAC9C,CAAd,CAAA,CAAA,CAAkB,EAAE;gCACJ,CAAhB,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAyC;gCACzB,CAAhB,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC;4BACzC,CAAe;wBACf,CAAa;oBACb,CAAW;gBACX,CAAS;YACT,CAAO;YACD,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B;QAC7B,CAAK;IACL,CAAG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc;AACd;"}