{"version": 3, "file": "isNonEmptyArray.js", "sources": ["../../../src/utilities/internal/isNonEmptyArray.ts"], "sourcesContent": ["/** @internal */\nexport function isNonEmptyArray<T>(\n  value: ArrayLike<T> | null | undefined\n): value is Array<T> {\n  return Array.isArray(value) && value.length > 0;\n}\n"], "names": [], "mappings": ";;;;;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAC7B,CADF,CAAA,CAAA,CAAA,CACwC,EADxC;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAA4B,EAA5B,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgD,CAAC;AACjD;"}