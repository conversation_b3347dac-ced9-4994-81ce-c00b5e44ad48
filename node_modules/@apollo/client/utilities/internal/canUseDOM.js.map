{"version": 3, "file": "canUseDOM.js", "sources": ["../../../src/utilities/internal/canUseDOM.ts"], "sourcesContent": ["import { maybe } from \"@apollo/client/utilities/internal/globals\";\n\n/** @internal */\nexport const canUseDOM =\n  typeof maybe(() => window.document.createElement) === \"function\";\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE;;;;;;AAGjE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACE,CADF,CAAA,CAAA,CAAA,CAAA,EACS,CADT,CAAA,CAAA,CAAA,CACc,CAAC,CADf,EACkB,CADlB,EACqB,CADrB,CAAA,CAAA,CAAA,CAAA,CAC2B,CAAC,CAD5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoC,CAAC,CADrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkD,EADlD,CAAA,CAAA,EACwD,CADxD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkE;"}