{"version": 3, "file": "isField.js", "sources": ["../../../src/utilities/internal/isField.ts"], "sourcesContent": ["import type { FieldNode, SelectionNode } from \"graphql\";\n\n/** @internal */\nexport function isField(selection: SelectionNode): selection is FieldNode {\n  return selection.kind === \"Field\";\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,EAAhD;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;AACnC;"}