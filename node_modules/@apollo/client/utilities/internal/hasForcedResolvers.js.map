{"version": 3, "file": "hasForcedResolvers.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/hasForcedResolvers.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEvC,MAAM,UAAU,kBAAkB,CAAC,QAAiB;IAClD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,KAAK,CAAC,QAAQ,EAAE;QACd,SAAS,EAAE;YACT,KAAK,CAAC,IAAI;gBACR,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnD,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAClC,CAAC,GAAG,EAAE,EAAE,CACN,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ;wBAC3B,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,cAAc;wBACjC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAC3B,CAAC;oBACF,IAAI,cAAc,EAAE,CAAC;wBACnB,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;SACF;KACF,CAAC,CAAC;IACH,OAAO,cAAc,CAAC;AACxB,CAAC", "sourcesContent": ["import type { ASTNode } from \"graphql\";\nimport { BREAK, visit } from \"graphql\";\n\nexport function hasForcedResolvers(document: ASTNode) {\n  let forceResolvers = false;\n  visit(document, {\n    Directive: {\n      enter(node) {\n        if (node.name.value === \"client\" && node.arguments) {\n          forceResolvers = node.arguments.some(\n            (arg) =>\n              arg.name.value === \"always\" &&\n              arg.value.kind === \"BooleanValue\" &&\n              arg.value.value === true\n          );\n          if (forceResolvers) {\n            return BREAK;\n          }\n        }\n      },\n    },\n  });\n  return forceResolvers;\n}\n"]}