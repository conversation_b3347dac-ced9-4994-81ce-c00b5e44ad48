{"version": 3, "file": "preventUnhandledRejection.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/preventUnhandledRejection.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,yBAAyB,CAAI,OAAmB;IAC9D,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAExB,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["export function preventUnhandledRejection<T>(promise: Promise<T>): Promise<T> {\n  promise.catch(() => {});\n\n  return promise;\n}\n"]}