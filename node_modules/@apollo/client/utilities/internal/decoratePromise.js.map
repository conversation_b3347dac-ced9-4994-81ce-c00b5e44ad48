{"version": 3, "file": "decoratePromise.js", "sources": ["../../../src/utilities/internal/decoratePromise.ts"], "sourcesContent": ["import type { DecoratedPromise } from \"./types/DecoratedPromise.js\";\nimport type { FulfilledPromise } from \"./types/FulfilledPromise.js\";\nimport type { PendingPromise } from \"./types/PendingPromise.js\";\nimport type { RejectedPromise } from \"./types/RejectedPromise.js\";\n\nfunction isDecoratedPromise<TValue>(\n  promise: Promise<TValue>\n): promise is DecoratedPromise<TValue> {\n  return \"status\" in promise;\n}\n\n/** @internal */\nexport function decoratePromise<TValue>(\n  promise: Promise<TValue>\n): DecoratedPromise<TValue> {\n  if (isDecoratedPromise(promise)) {\n    return promise;\n  }\n\n  const pendingPromise = promise as PendingPromise<TValue>;\n  pendingPromise.status = \"pending\";\n\n  pendingPromise.then(\n    (value) => {\n      if (pendingPromise.status === \"pending\") {\n        const fulfilledPromise =\n          pendingPromise as unknown as FulfilledPromise<TValue>;\n\n        fulfilledPromise.status = \"fulfilled\";\n        fulfilledPromise.value = value;\n      }\n    },\n    (reason: unknown) => {\n      if (pendingPromise.status === \"pending\") {\n        const rejectedPromise =\n          pendingPromise as unknown as RejectedPromise<TValue>;\n\n        rejectedPromise.status = \"rejected\";\n        rejectedPromise.reason = reason;\n      }\n    }\n  );\n\n  return promise as DecoratedPromise<TValue>;\n}\n"], "names": [], "mappings": "AAKA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CACzB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,EAD1B;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;AAC5B;;;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAC7B,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,EAD1B;IAGE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,EAAE;QAC/B,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;IAChB;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D;IACxD,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;IAEjC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAqB,CACjB,CAAC,CADL,CAAA,CAAA,CAAA,CACU,EAAE,CADZ,EAAA;QAEM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE;YACvC,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+D;YAEvD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C;YACrC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAsC;QAChC;IACF,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CACoB,EAAE,CADtB,EAAA;QAEM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE;YACvC,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC8D;YAEtD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C;YACnC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC;QACjC;IACF,CAAC,CACF;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;AAC5C;"}