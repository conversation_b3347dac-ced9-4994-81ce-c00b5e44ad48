{"version": 3, "file": "createFulfilledPromise.js", "sources": ["../../../src/utilities/internal/createFulfilledPromise.ts"], "sourcesContent": ["import type { FulfilledPromise } from \"./types/FulfilledPromise.js\";\n\n/** @internal */\nexport function createFulfilledPromise<TValue>(value: TValue) {\n  const promise = Promise.resolve(value) as FulfilledPromise<TValue>;\n\n  promise.status = \"fulfilled\";\n  promise.value = value;\n\n  return promise;\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAS,CAA/C,CAAA,CAAA,CAAA,CAA4D,EAA5D;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAuC,CAA6B;IAElE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;IAC5B,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB;IAErB,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;AAChB;"}