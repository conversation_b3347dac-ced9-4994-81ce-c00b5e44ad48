{"version": 3, "file": "argumentsObjectFromField.js", "sources": ["../../../src/utilities/internal/argumentsObjectFromField.ts"], "sourcesContent": ["import type { DirectiveNode, FieldNode } from \"graphql\";\n\nimport { valueToObjectRepresentation } from \"./valueToObjectRepresentation.js\";\n\n/** @internal */\nexport function argumentsObjectFromField(\n  field: FieldNode | DirectiveNode,\n  variables?: Record<string, any>\n): Object | null {\n  if (field.arguments && field.arguments.length) {\n    const argObj: Object = {};\n    field.arguments.forEach(({ name, value }) =>\n      valueToObjectRepresentation(argObj, name, value, variables)\n    );\n    return argObj;\n  }\n  return null;\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8E;;;;;;AAG9E,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CACtC,CADF,CAAA,CAAA,CAAA,CACkC,EAChC,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEiC,EAFjC;IAIE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,EAAE;QAC7C,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAA6B;QACzB,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAAC,EAAE,CAA/B,CAAA,CAAA,CAAmC,EAAE,CAArC,CAAA,CAAA,CAAA,EAAA,CAA4C,EAAE,CAA9C,EACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,CAAC,CADlC,CAAA,CAAA,CAAA,CAAA,CACwC,EAAE,CAD1C,CAAA,CAAA,CAC8C,EAAE,CADhD,CAAA,CAAA,CAAA,CACqD,EAAE,CADvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgE,CAAC,CAC5D;QACD,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB;IACf;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAa;AACb;"}