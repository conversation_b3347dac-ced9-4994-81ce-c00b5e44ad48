{"version": 3, "file": "DeepMerger.js", "sources": ["../../../src/utilities/internal/DeepMerger.ts"], "sourcesContent": ["import { isNonNullObject } from \"./isNonNullObject.js\";\n\nconst { hasOwnProperty } = Object.prototype;\n\ntype ReconcilerFunction<TContextArgs extends any[]> = (\n  this: DeepMerger<TContextArgs>,\n  target: Record<string | number, any>,\n  source: Record<string | number, any>,\n  property: string | number,\n  ...context: TContextArgs\n) => any;\n\nconst defaultReconciler: ReconcilerFunction<any[]> = function (\n  target,\n  source,\n  property\n) {\n  return this.merge(target[property], source[property]);\n};\n\n/** @internal */\nexport class DeepMerger<TContextArgs extends any[]> {\n  constructor(\n    private reconciler: ReconcilerFunction<TContextArgs> = defaultReconciler as any as ReconcilerFunction<TContextArgs>\n  ) {}\n\n  public merge(target: any, source: any, ...context: TContextArgs): any {\n    if (isNonNullObject(source) && isNonNullObject(target)) {\n      Object.keys(source).forEach((sourceKey) => {\n        if (hasOwnProperty.call(target, sourceKey)) {\n          const targetValue = target[sourceKey];\n          if (source[sourceKey] !== targetValue) {\n            const result = this.reconciler(\n              target,\n              source,\n              sourceKey,\n              ...context\n            );\n            // A well-implemented reconciler may return targetValue to indicate\n            // the merge changed nothing about the structure of the target.\n            if (result !== targetValue) {\n              target = this.shallowCopyForMerge(target);\n              target[sourceKey] = result;\n            }\n          }\n        } else {\n          // If there is no collision, the target can safely share memory with\n          // the source, and the recursion can terminate here.\n          target = this.shallowCopyForMerge(target);\n          target[sourceKey] = source[sourceKey];\n        }\n      });\n\n      return target;\n    }\n\n    // If source (or target) is not an object, let source replace target.\n    return source;\n  }\n\n  public isObject = isNonNullObject;\n\n  private pastCopies = new Set<any>();\n\n  public shallowCopyForMerge<T>(value: T): T {\n    if (isNonNullObject(value)) {\n      if (!this.pastCopies.has(value)) {\n        if (Array.isArray(value)) {\n          value = (value as any).slice(0);\n        } else {\n          value = {\n            __proto__: Object.getPrototypeOf(value),\n            ...value,\n          };\n        }\n        this.pastCopies.add(value);\n      }\n    }\n    return value;\n  }\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;AAEtD,CAAA,CAAA,CAAA,CAAA,EAAM,EAAE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C;AAU3C,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqD,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CACE,CADF,CAAA,CAAA,CAAA,CAAA,CACQ,EACN,CAFF,CAAA,CAAA,CAAA,CAAA,CAEQ,EACN,CAHF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGU,EAHV;IAKE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,EAAE,CAAtC,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAC;AACvD,CAAC;;;;;;AAGD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;IAEY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IADE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACY,CADZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAC2D,CAD3D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuH,EADvH;QACY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;IACjB;IAEI,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAA0B,EAAE,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAE,CAAzC,CAAA,CAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE,EAAjE;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAA8B,EAA9B,CAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC,EAAE;YACtD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,EAAE,CAA9C,EAAA;gBACQ,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAsC,EAAE,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,EAAE;oBAC1C,CAAV,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC;oBACrC,CAAV,EAAA,CAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,EAA9B,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,EAAE;wBACrC,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAC5B,CADd,CAAA,CAAA,CAAA,CAAA,CACoB,EACN,CAFd,CAAA,CAAA,CAAA,CAAA,CAEoB,EACN,CAHd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGuB,EACT,CAJd,CAAA,CAIiB,CAJjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAIwB,CACX;wBACD,CAAZ,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;wBACY,CAAZ,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;wBACY,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,EAAE;4BAC1B,CAAd,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC;4BACzC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,EAA9B,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAwC;wBAC5B;oBACF;gBACF;gBAAR,CAAA,CAAA,CAAA,EAAe;oBACL,CAAV,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;oBACU,CAAV,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;oBACU,CAAV,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC;oBACzC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAA1B,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC;gBACvC;YACF,CAAC,CAAC;YAEF,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAmB;QACf;QAEA,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB;IACf;IAEO,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;IAEzB,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,EAA2B,CAA3B,CAAA,CAA8B,CAA9B,CAAqC;IAE5B,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAI,CAAhC,CAAA,CAAA,CAAA,CAAwC,EAAxC;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAA6B,CAAC,EAAE;YAC1B,CAAN,EAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAoC,CAAC,EAAE;gBAC/B,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC,EAAE;oBACxB,CAAV,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAC,CAAC;gBACjC;gBAAR,CAAA,CAAA,CAAA,EAAe;oBACL,CAAV,CAAA,CAAA,CAAA,EAAA,EAAkB;wBACN,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAkD,CAAC;wBACvC,CAAZ,CAAA,CAAe,CAAf,CAAA,CAAA,CAAA,CAAoB;oBACpB,CAAW;gBACH;gBACA,CAAR,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAiC,CAAC;YAC5B;QACF;QACA,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAgB;IACd;AACF;"}