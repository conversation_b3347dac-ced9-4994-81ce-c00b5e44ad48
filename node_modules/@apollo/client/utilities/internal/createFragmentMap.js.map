{"version": 3, "file": "createFragmentMap.js", "sources": ["../../../src/utilities/internal/createFragmentMap.ts"], "sourcesContent": ["import type { FragmentDefinitionNode } from \"graphql\";\n\nimport type { FragmentMap } from \"./types/FragmentMap.js\";\n\n/**\n * Utility function that takes a list of fragment definitions and makes a hash out of them\n * that maps the name of the fragment to the fragment definition.\n *\n * @internal\n */\nexport function createFragmentMap(\n  fragments: FragmentDefinitionNode[] = []\n): FragmentMap {\n  const symTable: FragmentMap = {};\n  fragments.forEach((fragment) => {\n    symTable[fragment.name.value] = fragment;\n  });\n  return symTable;\n}\n"], "names": [], "mappings": ";;;;;;;;AAUA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAC/B,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACwC,CADxC,CAC0C,EAD1C;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAhC,CAAkC;IAChC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE,CAA/B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAgC,EAAhC,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;IAC1C,CAAC,CAAC;IACF,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;AACjB;"}