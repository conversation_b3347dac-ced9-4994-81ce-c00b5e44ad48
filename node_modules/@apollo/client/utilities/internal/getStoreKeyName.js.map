{"version": 3, "file": "getStoreKeyName.js", "sources": ["../../../src/utilities/internal/getStoreKeyName.ts"], "sourcesContent": ["import { canonicalStringify } from \"./canonicalStringify.js\";\n\ntype Directives = {\n  [directiveName: string]: {\n    [argName: string]: any;\n  };\n};\n\nconst KNOWN_DIRECTIVES: string[] = [\n  \"connection\",\n  \"include\",\n  \"skip\",\n  \"client\",\n  \"rest\",\n  \"export\",\n  \"nonreactive\",\n];\n\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nlet storeKeyNameStringify: (value: any) => string = canonicalStringify;\n\n/** @internal */\nexport const getStoreKeyName = Object.assign(\n  function (\n    fieldName: string,\n    args?: Record<string, any> | null,\n    directives?: Directives\n  ): string {\n    if (\n      args &&\n      directives &&\n      directives[\"connection\"] &&\n      directives[\"connection\"][\"key\"]\n    ) {\n      if (\n        directives[\"connection\"][\"filter\"] &&\n        (directives[\"connection\"][\"filter\"] as string[]).length > 0\n      ) {\n        const filterKeys =\n          directives[\"connection\"][\"filter\"] ?\n            (directives[\"connection\"][\"filter\"] as string[])\n          : [];\n        filterKeys.sort();\n\n        const filteredArgs = {} as { [key: string]: any };\n        filterKeys.forEach((key) => {\n          filteredArgs[key] = args[key];\n        });\n\n        const stringifiedArgs: string = storeKeyNameStringify(filteredArgs);\n        if (stringifiedArgs !== \"{}\") {\n          return `${directives[\"connection\"][\"key\"]}(${stringifiedArgs})`;\n        }\n      }\n      return directives[\"connection\"][\"key\"];\n    }\n\n    let completeFieldName: string = fieldName;\n\n    if (args) {\n      // We can't use `JSON.stringify` here since it's non-deterministic,\n      // and can lead to different store key names being created even though\n      // the `args` object used during creation has the same properties/values.\n      const stringifiedArgs: string = storeKeyNameStringify(args);\n      if (stringifiedArgs !== \"{}\") {\n        completeFieldName += `(${stringifiedArgs})`;\n      }\n    }\n\n    if (directives) {\n      Object.keys(directives).forEach((key) => {\n        if (KNOWN_DIRECTIVES.indexOf(key) !== -1) return;\n        if (directives[key] && Object.keys(directives[key]).length) {\n          completeFieldName += `@${key}(${storeKeyNameStringify(\n            directives[key]\n          )})`;\n        } else {\n          completeFieldName += `@${key}`;\n        }\n      });\n    }\n\n    return completeFieldName;\n  },\n  {\n    setStringify(s: typeof storeKeyNameStringify) {\n      const previous = storeKeyNameStringify;\n      storeKeyNameStringify = s;\n      return previous;\n    },\n  }\n);\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D;AAQ5D,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmC;IACjC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc;IACZ,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;IACT,CAAF,CAAA,CAAA,CAAA,CAAA,CAAQ;IACN,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU;IACR,CAAF,CAAA,CAAA,CAAA,CAAA,CAAQ;IACN,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU;IACR,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;AACf,CAAC;AAED,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,EAAI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoD,CAApD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE;;;;;;AAGtE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAA4C,CAC1C,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAEI,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEqB,EACjB,CAHJ,CAAA,CAAA,CAGqC,EACjC,CAJJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAI2B,EAJ3B;IAMI,CAAJ,EAAA,CACM,CADN,CAAA,CAAA,EAAA,CAAA;QAEM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAA7B,CAAA;QACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAoC,CAAC,EAC/B;QACA,CAAN,EAAA,CACQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+B,CAAC,CAAC,CADjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyC,EADzC,CAAA;YAES,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAc,CAAC,CAAzD,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkE,CAAC,EAC3D;YACA,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoB,CAAC,CADrB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,CAAC,CAAC,CADnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2C,EAAE;gBAChC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C;gBACpC,EAAE,CAAZ,CAAc;YACN,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAuB,CAAvB,CAAyB;YAEjB,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAyD;YACjD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAAC,CAA5B,CAAA,CAA+B,EAAE,CAAjC,EAAA;gBACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAA0B,EAA1B,EAA8B,CAA9B,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAsC,CAAC;YAC/B,CAAC,CAAC;YAEF,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6D,CAAC,CAA9D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0E,CAAC;YACnE,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAoC,EAAE;gBAC5B,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAkD,CAAC,CAAnD,CAAA,CAAA,CAAuD,CAAvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE,CAAtE,CAAA,CAAyE;YACjE;QACF;QACA,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAA2C,CAAC;IACxC;IAEA,CAAJ,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C;IAEzC,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAY,EAAE;QACR,CAAN,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACM,CAAN,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACM,CAAN,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAC,CAA5D,CAAA,CAAA,CAAgE,CAAC;QAC3D,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAkC,EAAE;YAC5B,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAhD,CAAA,CAAmD;QAC7C;IACF;IAEA,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE;QACd,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAC,CAAvC,CAAA,CAA0C,EAAE,CAA5C,EAAA;YACQ,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAwC,EAAxC,CAAA,CAAA,EAA8C,CAAC,CAAC;gBAAE,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA;YACQ,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAA0B,EAA1B,CAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAyD,CAAC,CAAC,CAAC,CAA5D,CAAA,CAAA,CAAA,CAAA,CAAkE,EAAE;gBAC1D,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA+B,CAA/B,CAAA,CAAA,CAAmC,CAAnC,CAAA,CAAsC,CAAtC,CAAA,CAAA,CAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+D,CACnD,CADZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACsB,CAAC,CADvB,CAAA,CAC0B,CAAC,CAChB,CAFX,CAAA,CAEc;YACN;YAAR,CAAA,CAAA,CAAA,EAAe;gBACL,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA+B,CAA/B,CAAA,CAAA,CAAmC,CAAnC,CAAA,CAAsC,CAAtC,CAAwC;YAChC;QACF,CAAC,CAAC;IACJ;IAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;AAC1B,CAAC,EACD;IACE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAA+B,EAAhD;QACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;QACtC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B,CAAC;QACzB,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;IACjB,CAAC;AACL,CAAG,CACF;"}