{"version": 3, "file": "compact.js", "sources": ["../../../src/utilities/internal/compact.ts"], "sourcesContent": ["import type { TupleToIntersection } from \"./types/TupleToIntersection.js\";\n\n/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n *\n * @internal\n */\nexport function compact<TArgs extends any[]>(\n  ...objects: TArgs\n): TupleToIntersection<TArgs> {\n  const result = {} as TupleToIntersection<TArgs>;\n\n  objects.forEach((obj) => {\n    if (!obj) return;\n    Object.keys(obj).forEach((key) => {\n      const value = (obj as any)[key];\n      if (value !== void 0) {\n        result[key] = value;\n      }\n    });\n  });\n\n  return result;\n}\n"], "names": [], "mappings": ";;;;;;;;AAQA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CACrB,CADF,CAAA,CACK,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,EADnB;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAiD;IAE/C,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAC,CAAnB,CAAA,CAAsB,EAAE,CAAxB,EAAA;QACI,CAAJ,EAAA,CAAQ,CAAC,CAAT,CAAA,CAAY;YAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAmB,CAAC,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAAC,CAA9B,CAAA,CAAiC,EAAE,CAAnC,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAoC,CAAC;YAC/B,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoB,CAApB,CAAA,CAAA,EAAyB,CAAC,EAAE;gBACpB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAkB,EAAlB,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAA2B;YACrB;QACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe;AACf;"}