{"version": 3, "file": "omitDeep.js", "sources": ["../../../src/utilities/internal/omitDeep.ts"], "sourcesContent": ["import { isPlainObject } from \"./isPlainObject.js\";\nimport type { DeepOmit } from \"./types/DeepOmit.js\";\n\n/** @internal */\nexport function omitDeep<T, K extends string>(value: T, key: K) {\n  return __omitDeep(value, key);\n}\n\nfunction __omitDeep<T, K extends string>(\n  value: T,\n  key: K,\n  known = new Map<any, any>()\n): DeepOmit<T, K> {\n  if (known.has(value)) {\n    return known.get(value);\n  }\n\n  let modified = false;\n\n  if (Array.isArray(value)) {\n    const array: any[] = [];\n    known.set(value, array);\n\n    value.forEach((value, index) => {\n      const result = __omitDeep(value, key, known);\n      modified ||= result !== value;\n\n      array[index] = result;\n    });\n\n    if (modified) {\n      return array as DeepOmit<T, K>;\n    }\n  } else if (isPlainObject(value)) {\n    const obj = Object.create(Object.getPrototypeOf(value));\n    known.set(value, obj);\n\n    Object.keys(value).forEach((k) => {\n      if (k === key) {\n        modified = true;\n        return;\n      }\n\n      const result = __omitDeep(value[k], key, known);\n      modified ||= result !== value[k];\n\n      obj[k] = result;\n    });\n\n    if (modified) {\n      return obj;\n    }\n  }\n\n  return value as DeepOmit<T, K>;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD;;;;;;AAIlD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAsB,CAA9C,CAAA,CAAA,CAAA,CAAsD,EAAE,CAAxD,CAAA,CAA8D,EAA9D;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAA8B,CAAC;AAC/B;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CACjB,CADF,CAAA,CAAA,CAAA,CACU,EACR,CAFF,CAAA,CAEQ,EACN,CAHF,CAAA,CAAA,CAAA,EAAA,EAGU,CAHV,CAAA,EAGc,CAHd,CAAA,CAGiB,CAHjB,CAG6B,EAH7B;IAKE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,CAAC,EAAE;QACpB,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC;IACzB;IAEA,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAsB;IAEpB,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAyB,CAAC,EAAE;QACxB,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAA2B;QACvB,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC;QAEvB,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAAA,CAA+B,EAAE,CAAjC,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAqC,EAAE,CAAvC,CAAA,CAA0C,EAAE,CAA5C,CAAA,CAAA,CAAA,CAAiD,CAAC;YAC5C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAmC;YAE7B,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAiB,EAAjB,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B;QACvB,CAAC,CAAC;QAEF,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE;YACZ,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAoC;QAChC;IACF;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAgC,CAAC,EAAE;QAC/B,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAA,CAAA,CAAyD,CAAC,CAAC;QACvD,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAwB,CAAC;QAErB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAAC,CAAC,EAAE,CAAnC,EAAA;YACM,CAAN,EAAA,CAAU,EAAV,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAmB,EAAE;gBACb,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAuB;gBACf,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA;YACM;YAEA,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAC,CAAC,EAAE,CAA1C,CAAA,CAA6C,EAAE,CAA/C,CAAA,CAAA,CAAA,CAAoD,CAAC;YAC/C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAmC,CAAC,CAAC,CAAC;YAEhC,CAAN,CAAA,CAAS,CAAC,CAAC,EAAX,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAqB;QACjB,CAAC,CAAC;QAEF,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE;YACZ,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAgB;QACZ;IACF;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAgC;AAChC;"}