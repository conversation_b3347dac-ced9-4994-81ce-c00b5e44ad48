{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/utilities/internal/index.ts"], "names": [], "mappings": "AAeA,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,0BAA0B,EAAE,MAAM,iCAAiC,CAAC;AAC7E,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,yBAAyB,EAAE,MAAM,gCAAgC,CAAC;AAC3E,OAAO,EAAE,4BAA4B,EAAE,MAAM,mCAAmC,CAAC;AACjF,OAAO,EAAE,2BAA2B,EAAE,MAAM,4BAA4B,CAAC;AACzE,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAE7D,OAAO,EACL,6BAA6B,EAC7B,8BAA8B,EAC9B,+BAA+B,EAC/B,mBAAmB,GACpB,MAAM,mCAAmC,CAAC;AAE3C,OAAO,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC", "sourcesContent": ["export type { DecoratedPromise } from \"./types/DecoratedPromise.js\";\nexport type { DeepOmit } from \"./types/DeepOmit.js\";\nexport type { FragmentMap } from \"./types/FragmentMap.js\";\nexport type { FragmentMapFunction } from \"./types/FragmentMapFunction.js\";\nexport type { FulfilledPromise } from \"./types/FulfilledPromise.js\";\nexport type { IsAny } from \"./types/IsAny.js\";\nexport type { NoInfer } from \"./types/NoInfer.js\";\nexport type { PendingPromise } from \"./types/PendingPromise.js\";\nexport type { Prettify } from \"./types/Prettify.js\";\nexport type { Primitive } from \"./types/Primitive.js\";\nexport type { RejectedPromise } from \"./types/RejectedPromise.js\";\nexport type { RemoveIndexSignature } from \"./types/RemoveIndexSignature.js\";\nexport type { VariablesOption } from \"./types/VariablesOption.js\";\nexport type { DocumentationTypes } from \"./types/DocumentationTypes.js\";\n\nexport { argumentsObjectFromField } from \"./argumentsObjectFromField.js\";\nexport { canUseDOM } from \"./canUseDOM.js\";\nexport { checkDocument } from \"./checkDocument.js\";\nexport { cloneDeep } from \"./cloneDeep.js\";\nexport { compact } from \"./compact.js\";\nexport { createFragmentMap } from \"./createFragmentMap.js\";\nexport { createFulfilledPromise } from \"./createFulfilledPromise.js\";\nexport { createRejectedPromise } from \"./createRejectedPromise.js\";\nexport { dealias } from \"./dealias.js\";\nexport { decoratePromise } from \"./decoratePromise.js\";\nexport { DeepMerger } from \"./DeepMerger.js\";\nexport { getDefaultValues } from \"./getDefaultValues.js\";\nexport { getFragmentFromSelection } from \"./getFragmentFromSelection.js\";\nexport { getFragmentQueryDocument } from \"./getFragmentQueryDocument.js\";\nexport { getFragmentDefinition } from \"./getFragmentDefinition.js\";\nexport { getFragmentDefinitions } from \"./getFragmentDefinitions.js\";\nexport { getGraphQLErrorsFromResult } from \"./getGraphQLErrorsFromResult.js\";\nexport { getMainDefinition } from \"./getMainDefinition.js\";\nexport { getOperationDefinition } from \"./getOperationDefinition.js\";\nexport { getOperationName } from \"./getOperationName.js\";\nexport { getQueryDefinition } from \"./getQueryDefinition.js\";\nexport { getStoreKeyName } from \"./getStoreKeyName.js\";\nexport { graphQLResultHasError } from \"./graphQLResultHasError.js\";\nexport { hasDirectives } from \"./hasDirectives.js\";\nexport { hasForcedResolvers } from \"./hasForcedResolvers.js\";\nexport { isArray } from \"./isArray.js\";\nexport { isDocumentNode } from \"./isDocumentNode.js\";\nexport { isField } from \"./isField.js\";\nexport { isNonEmptyArray } from \"./isNonEmptyArray.js\";\nexport { isNonNullObject } from \"./isNonNullObject.js\";\nexport { isPlainObject } from \"./isPlainObject.js\";\nexport { makeReference } from \"./makeReference.js\";\nexport { makeUniqueId } from \"./makeUniqueId.js\";\nexport { maybeDeepFreeze } from \"./maybeDeepFreeze.js\";\nexport { mergeDeep } from \"./mergeDeep.js\";\nexport { mergeDeepArray } from \"./mergeDeepArray.js\";\nexport { mergeOptions } from \"./mergeOptions.js\";\nexport { omitDeep } from \"./omitDeep.js\";\nexport { preventUnhandledRejection } from \"./preventUnhandledRejection.js\";\nexport { removeDirectivesFromDocument } from \"./removeDirectivesFromDocument.js\";\nexport { removeMaskedFragmentSpreads } from \"./removeFragmentSpreads.js\";\nexport { resultKeyNameFromField } from \"./resultKeyNameFromField.js\";\nexport { shouldInclude } from \"./shouldInclude.js\";\nexport { storeKeyNameFromField } from \"./storeKeyNameFromField.js\";\nexport { stringifyForDisplay } from \"./stringifyForDisplay.js\";\nexport { toQueryResult } from \"./toQueryResult.js\";\nexport { filterMap } from \"./filterMap.js\";\nexport { equalByQuery } from \"./equalByQuery.js\";\nexport { canonicalStringify } from \"./canonicalStringify.js\";\n\nexport {\n  getApolloCacheMemoryInternals,\n  getApolloClientMemoryInternals,\n  getInMemoryCacheMemoryInternals,\n  registerGlobalCache,\n} from \"../internal/getMemoryInternals.js\";\n\nexport { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\n\nexport type { ApplyHKT } from \"./types/ApplyHKT.js\";\nexport type { ApplyHKTImplementationWithDefault } from \"./types/ApplyHKTImplementationWithDefault.js\";\n"]}