{"version": 3, "file": "shouldInclude.js", "sources": ["../../../src/utilities/internal/shouldInclude.ts"], "sourcesContent": ["import type {\n  ArgumentNode,\n  BooleanValueNode,\n  DirectiveNode,\n  SelectionNode,\n  ValueNode,\n  VariableNode,\n} from \"graphql\";\n\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\n/** @internal */\nexport function shouldInclude(\n  { directives }: SelectionNode,\n  variables?: Record<string, any>\n): boolean {\n  if (!directives || !directives.length) {\n    return true;\n  }\n  return getInclusionDirectives(directives).every(\n    ({ directive, ifArgument }) => {\n      let evaledValue: boolean = false;\n      if (ifArgument.value.kind === \"Variable\") {\n        evaledValue =\n          variables && variables[(ifArgument.value as VariableNode).name.value];\n        invariant(\n          evaledValue !== void 0,\n          `Invalid variable referenced in @%s directive.`,\n          directive.name.value\n        );\n      } else {\n        evaledValue = (ifArgument.value as BooleanValueNode).value;\n      }\n      return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n    }\n  );\n}\n\nfunction isInclusionDirective({ name: { value } }: DirectiveNode): boolean {\n  return value === \"skip\" || value === \"include\";\n}\n\ntype InclusionDirectives = Array<{\n  directive: DirectiveNode;\n  ifArgument: ArgumentNode;\n}>;\n\nfunction getInclusionDirectives(\n  directives: ReadonlyArray<DirectiveNode>\n): InclusionDirectives {\n  const result: InclusionDirectives = [];\n\n  if (directives && directives.length) {\n    directives.forEach((directive) => {\n      if (!isInclusionDirective(directive)) return;\n\n      const directiveArguments = directive.arguments;\n      const directiveName = directive.name.value;\n\n      invariant(\n        directiveArguments && directiveArguments.length === 1,\n        `Incorrect number of arguments for the @%s directive.`,\n        directiveName\n      );\n\n      const ifArgument = directiveArguments![0];\n      invariant(\n        ifArgument.name && ifArgument.name.value === \"if\",\n        `Invalid argument for the @%s directive.`,\n        directiveName\n      );\n\n      const ifValue: ValueNode = ifArgument.value;\n\n      // means it has to be a variable value if this is a valid @skip or @include directive\n      invariant(\n        ifValue &&\n          (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"),\n        `Argument for the @%s directive must be a variable or a boolean value.`,\n        directiveName\n      );\n\n      result.push({ directive, ifArgument });\n    });\n  }\n\n  return result;\n}\n"], "names": [], "mappings": "AASA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;;;;;;AAG9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAC3B,EAAE,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAC+B,EAC7B,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEiC,EAFjC;IAIE,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAE;QACrC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAe;IACb;WACO,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAiD,CAC7C,CAAC,EAAE,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgB,EAAE,CADlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAC8B,EAAE,CADhC,EAAA;QAEM,CAAN,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAsC;QAChC,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,EAAE;YACxC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;gBACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAE,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAmE,CAAC,CAApE,CAAA,CAAA,CAAwE,CAAC,CAAzE,CAAA,CAAA,CAAA,CAA8E,CAAC;YACvE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC0B,CAD1B,CAAA,CAAA,EAC+B,OAErB,CAHV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGmB,CAAC,CAHpB,CAAA,CAAA,CAGwB,CAAC,CAHzB,CAAA,CAAA,CAAA,EAIS;QACH;QAAN,CAAA,CAAA,CAAA,EAAa;YACL,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAA4D,CAAC,CAA7D,CAAA,CAAA,CAAA,CAAkE;QAC5D;QACA,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,EAA6C,EAAE,CAAC,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA4D,EAAE,CAA9D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE;IACrE,CAAC;AAEL;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,EAAE,CAAhC,CAAA,CAAA,CAAoC,EAAE,EAAE,CAAxC,CAAA,CAAA,CAAA,EAAA,EAAA,CAAgE,EAAhE;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAuC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;AAChD;AAOA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAC7B,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0C,EAD1C;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsC,CAAtC,CAAwC;IAEtC,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAqC,EAAE;QACnC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,EAAE,CAAnC,EAAA;YACM,CAAN,EAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC;gBAAE,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA;YAEM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD;YAC9C,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAgD;YAE1C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC8B,CAD9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgD,CAAC,CADjD,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC4D,OAEpD,CAHR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAIO;YAED,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAAC,CAAC;YACzC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,EAAA,CAAA,EAC2B,CAD3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqC,CAAC,CADtC,CAAA,CAAA,CAC0C,CAAC,CAD3C,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EACqD,CADrD,CAAA,CAAA,OAGQ,CAHR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAIO;YAED,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAiD;YAE3C,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACQ,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;gBAEU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,EAAA,CAAA,CAAA,EAA2D,CAA3D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE,OAEjE,CAFR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAGO;YAED,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAiB,CAAC,EAAE,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAA2C,CAAC;QACxC,CAAC,CAAC;IACJ;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe;AACf;"}