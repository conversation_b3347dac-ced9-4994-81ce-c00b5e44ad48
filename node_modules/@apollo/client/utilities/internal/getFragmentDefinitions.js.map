{"version": 3, "file": "getFragmentDefinitions.js", "sources": ["../../../src/utilities/internal/getFragmentDefinitions.ts"], "sourcesContent": ["import type { DocumentNode, FragmentDefinitionNode } from \"graphql\";\n\n/** @internal */\nexport function getFragmentDefinitions(\n  doc: DocumentNode\n): FragmentDefinitionNode[] {\n  return doc.definitions.filter(\n    (definition): definition is FragmentDefinitionNode =>\n      definition.kind === \"FragmentDefinition\"\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CACpC,CADF,CAAA,CACmB,EADnB;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B,CAC3B,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACe,EAAwC,CADvD,EAEM,CAFN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEgB,CAAC,CAFjB,CAAA,CAAA,EAAA,CAAA,CAAA,EAE0B,CAF1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE8C,CAC3C;AACH;"}