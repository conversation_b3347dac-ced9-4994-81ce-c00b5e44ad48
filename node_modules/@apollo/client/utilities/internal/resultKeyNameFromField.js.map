{"version": 3, "file": "resultKeyNameFromField.js", "sources": ["../../../src/utilities/internal/resultKeyNameFromField.ts"], "sourcesContent": ["import type { FieldNode } from \"graphql\";\n\n/** @internal */\nexport function resultKeyNameFromField(field: FieldNode): string {\n  return field.alias ? field.alias.value : field.name.value;\n}\n"], "names": [], "mappings": ";;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAuD,EAAvD;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,EAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,EAAyC,EAAE,CAA3C,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAAA,CAA2D;AAC3D;"}