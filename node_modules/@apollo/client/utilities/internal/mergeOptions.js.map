{"version": 3, "file": "mergeOptions.js", "sources": ["../../../src/utilities/internal/mergeOptions.ts"], "sourcesContent": ["import type { ApolloClient, OperationVariables } from \"@apollo/client\";\n\nimport { compact } from \"./compact.js\";\n\ntype OptionsUnion<TData, TVariables extends OperationVariables> =\n  | ApolloClient.WatchQueryOptions<TData, TVariables>\n  | ApolloClient.QueryOptions<TData, TVariables>\n  | ApolloClient.MutateOptions<TData, TVariables, any>;\n\n/** @internal */\nexport function mergeOptions<\n  TDefaultOptions extends Partial<OptionsUnion<any, any>>,\n  TOptions extends TDefaultOptions,\n>(\n  defaults: TDefaultOptions | Partial<TDefaultOptions> | undefined,\n  options: TOptions | Partial<TOptions>\n): TOptions & TDefaultOptions {\n  return compact(\n    defaults,\n    options,\n    options.variables && {\n      variables: compact({\n        ...(defaults && defaults.variables),\n        ...options.variables,\n      }),\n    }\n  );\n}\n"], "names": [], "mappings": "AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC;;;;;;AAQtC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAI1B,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIkE,EAChE,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAKuC,EALvC;IAOE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CACZ,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACY,EACR,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAEW,EACP,CAHJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAGW,CAAC,CAHZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAGyB;QACnB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC;YACjB,CAAR,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;YACnC,CAAR,CAAA,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;QAC5B,CAAO,CAAC;IACR,CAAK,CACF;AACH;"}