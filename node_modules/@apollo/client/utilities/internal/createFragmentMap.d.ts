import type { FragmentDefinitionNode } from "graphql";
import type { FragmentMap } from "./types/FragmentMap.js";
/**
* Utility function that takes a list of fragment definitions and makes a hash out of them
* that maps the name of the fragment to the fragment definition.
*
* @internal
* 
* @deprecated This is an internal API and should not be used directly. This can be removed or changed at any time.
*/
export declare function createFragmentMap(fragments?: FragmentDefinitionNode[]): FragmentMap;
//# sourceMappingURL=createFragmentMap.d.ts.map
