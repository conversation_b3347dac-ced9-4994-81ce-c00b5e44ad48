{"version": 3, "file": "getMainDefinition.js", "sources": ["../../../src/utilities/internal/getMainDefinition.ts"], "sourcesContent": ["import type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  OperationDefinitionNode,\n} from \"graphql\";\n\nimport { newInvariantError } from \"@apollo/client/utilities/invariant\";\n\nimport { checkDocument } from \"./checkDocument.js\";\n\n/**\n * Returns the first operation definition from a GraphQL document. The function\n * prioritizes operation definitions over fragment definitions, which makes it\n * suitable for documents that may contain both. If no operation definition is\n * found, the first fragment definition will be returned. If no definitions are\n * found, an error is thrown.\n *\n * @remarks\n *\n * Use this function when you need to perform more advanced tasks with the main\n * definition AST node. If you want to determine when a document is a specific\n * operation type, prefer the `isQueryOperation`, `isMutationOperation`, and\n * `isSubscriptionOperation` utility functions instead.\n *\n * @param queryDoc - The GraphQL document to extract the definition from\n * @returns The main operation or fragment definition AST node\n *\n * @example\n *\n * ```ts\n * import { gql } from \"@apollo/client\";\n * import { getMainDefinition } from \"@apollo/client/utilities\";\n *\n * const query = gql`\n *   query GetUser($id: ID!) {\n *     user(id: $id) {\n *       name\n *       email\n *     }\n *   }\n * `;\n *\n * const definition = getMainDefinition(query);\n * ```\n *\n * @throws When the document contains no operation or fragment definitions\n */\nexport function getMainDefinition(\n  queryDoc: DocumentNode\n): OperationDefinitionNode | FragmentDefinitionNode {\n  checkDocument(queryDoc);\n\n  let fragmentDefinition;\n\n  for (let definition of queryDoc.definitions) {\n    if (definition.kind === \"OperationDefinition\") {\n      return definition;\n    }\n\n    if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n      // we do this because we want to allow multiple fragment definitions\n      // to precede an operation definition.\n      fragmentDefinition = definition;\n    }\n  }\n\n  if (fragmentDefinition) {\n    return fragmentDefinition;\n  }\n\n  throw newInvariantError(\n    \"Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment.\"\n  );\n}\n"], "names": [], "mappings": "AAMA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE;AAEtE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD;AAElD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAC/B,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACwB,EADxB;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC;IAEvB,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB;IAEtB,CAAF,CAAA,EAAA,CAAO,CAAP,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE;QAC3C,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,EAAE;YAC7C,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;QACnB;QAEA,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,EAAE;YACnE,CAAN,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;QACjC;IACF;IAEA,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE;QACtB,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B;IAC3B;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAEG;AACH;"}