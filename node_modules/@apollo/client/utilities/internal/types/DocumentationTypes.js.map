{"version": 3, "file": "DocumentationTypes.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/DocumentationTypes.ts"], "names": [], "mappings": "", "sourcesContent": ["import type {\n  Observable,\n  Observer,\n  OperatorFunction,\n  Subscription,\n} from \"rxjs\";\n\nimport type {\n  DataValue,\n  <PERSON>rror<PERSON>ike,\n  MaybeMasked,\n  NetworkStatus,\n  OperationVariables,\n} from \"@apollo/client\";\n\n/**\n * This namespace contains simplified interface versions of existing, complicated, types in Apollo Client.\n * These interfaces are used in the documentation to provide a more readable\n * and understandable API reference.\n */\nexport declare namespace DocumentationTypes {\n  export interface DataState<TData> {\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#data:member} */\n    data?:\n      | DataValue.Complete<TData>\n      | DataValue.Streaming<TData>\n      | DataValue.Partial<TData>\n      | undefined;\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#dataState:member} */\n    dataState: \"complete\" | \"streaming\" | \"partial\" | \"empty\";\n  }\n\n  export interface VariableOptions<TVariables extends OperationVariables> {\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n    variables?: TVariables;\n  }\n\n  export interface ApolloQueryResult<TData> extends DataState<TData> {\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n    error?: ErrorLike;\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#loading:member} */\n    loading: boolean;\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n    networkStatus: NetworkStatus;\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#partial:member} */\n    partial: boolean;\n  }\n\n  export interface RxjsObservable<TData> {\n    pipe<OperatorResult>(\n      ...operators:\n        | [\n            OperatorFunction<\n              Observable<ApolloQueryResult<TData>>,\n              OperatorResult\n            >,\n          ]\n        | [\n            OperatorFunction<Observable<ApolloQueryResult<TData>>, unknown>,\n            ...OperatorFunction<unknown, unknown>[],\n            OperatorFunction<unknown, OperatorResult>,\n          ]\n    ): Observable<OperatorResult>;\n\n    subscribe(\n      observer:\n        | Partial<Observer<ApolloQueryResult<MaybeMasked<TData>>>>\n        | ((value: ApolloQueryResult<MaybeMasked<TData>>) => void)\n    ): Subscription;\n  }\n}\n"]}