{"version": 3, "file": "DecoratedPromise.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/DecoratedPromise.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { FulfilledPromise } from \"./FulfilledPromise.js\";\nimport type { PendingPromise } from \"./PendingPromise.js\";\nimport type { RejectedPromise } from \"./RejectedPromise.js\";\n\n/** @internal */\nexport type DecoratedPromise<TValue> =\n  | PendingPromise<TValue>\n  | FulfilledPromise<TValue>\n  | RejectedPromise<TValue>;\n"]}