{"version": 3, "file": "ApplyHKT.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/ApplyHKT.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { HKT } from \"@apollo/client/utilities\";\n\n/**\n * @internal\n */\n\nexport type ApplyHKT<\n  fn extends HKT,\n  arg1,\n  arg2 = never,\n  arg3 = never,\n  arg4 = never,\n> = (fn & {\n  arg1: arg1;\n  arg2: arg2;\n  arg3: arg3;\n  arg4: arg4;\n})[\"return\"];\n"]}