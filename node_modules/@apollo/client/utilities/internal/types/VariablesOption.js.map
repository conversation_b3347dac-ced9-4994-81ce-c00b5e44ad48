{"version": 3, "file": "VariablesOption.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/VariablesOption.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { OperationVariables } from \"@apollo/client\";\n\n/** @internal */\nexport type VariablesOption<TVariables extends OperationVariables> =\n  {} extends TVariables ?\n    {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n      variables?: TVariables;\n    }\n  : {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#variables:member} */\n      variables: TVariables;\n    };\n"]}