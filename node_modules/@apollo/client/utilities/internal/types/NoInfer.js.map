{"version": 3, "file": "NoInfer.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/NoInfer.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Helper type that allows using a type in a way that cannot be \"widened\" by inference on the value it is used on.\n *\n * This type was first suggested [in this Github discussion](https://github.com/microsoft/TypeScript/issues/14829#issuecomment-504042546).\n *\n * Example usage:\n *\n * ```ts\n * export function useQuery<\n *   TData = unknown,\n *   TVariables extends OperationVariables = OperationVariables,\n * >(\n *   query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n *   options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> = {}\n * );\n * ```\n *\n * In this case, `TData` and `TVariables` should be inferred from `query`, but never widened from something in `options`.\n *\n * So, in this code example:\n *\n * ```ts\n * declare const typedNode: TypedDocumentNode<{ foo: string }, { bar: number }>;\n * const { variables } = useQuery(typedNode, {\n *   variables: { bar: 4, nonExistingVariable: \"string\" },\n * });\n * ```\n *\n * Without the use of `NoInfer`, `variables` would now be of the type `{ bar: number, nonExistingVariable: \"string\" }`.\n * With `NoInfer`, it will instead give an error on `nonExistingVariable`.\n *\n * @deprecated use the official `NoInfer` type instead.\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\n"]}