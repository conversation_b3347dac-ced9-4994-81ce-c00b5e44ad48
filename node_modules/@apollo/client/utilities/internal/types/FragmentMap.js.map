{"version": 3, "file": "FragmentMap.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/FragmentMap.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { FragmentDefinitionNode } from \"graphql\";\n\n/**\n * Describes a map from fragment names to fragment definitions.\n *\n * @internal\n */\nexport interface FragmentMap {\n  [fragmentName: string]: FragmentDefinitionNode;\n}\n"]}