{"version": 3, "file": "TupleToIntersection.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/TupleToIntersection.ts"], "names": [], "mappings": "", "sourcesContent": ["export type TupleToIntersection<T extends any[]> =\n  T extends [infer A] ? A\n  : T extends [infer A, infer B] ? A & B\n  : T extends [infer A, infer B, infer C] ? A & B & C\n  : T extends [infer A, infer B, infer C, infer D] ? A & B & C & D\n  : T extends [infer A, infer B, infer C, infer D, infer E] ? A & B & C & D & E\n  : T extends (infer U)[] ? U\n  : any;\n"]}