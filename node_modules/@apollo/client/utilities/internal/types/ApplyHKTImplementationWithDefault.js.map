{"version": 3, "file": "ApplyHKTImplementationWithDefault.js", "sourceRoot": "", "sources": ["../../../../src/utilities/internal/types/ApplyHKTImplementationWithDefault.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { HKT } from \"@apollo/client/utilities\";\n\nimport type { ApplyHKT } from \"./ApplyHKT.js\";\n\n/**\n * @internal\n */\n\nexport type ApplyHKTImplementationWithDefault<\n  Implementation,\n  Name extends string,\n  DefaultImplementation extends Record<Name, HKT>,\n  arg1,\n  arg2 = never,\n  arg3 = never,\n  arg4 = never,\n> = ApplyHKT<\n  Implementation extends {\n    [name in Name]: infer Implementation extends HKT;\n  } ?\n    Implementation\n  : DefaultImplementation[Name],\n  arg1,\n  arg2,\n  arg3,\n  arg4\n>;\n"]}