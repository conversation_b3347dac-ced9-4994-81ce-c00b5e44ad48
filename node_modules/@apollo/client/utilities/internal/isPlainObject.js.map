{"version": 3, "file": "isPlainObject.js", "sources": ["../../../src/utilities/internal/isPlainObject.ts"], "sourcesContent": ["/** @internal */\nexport function isPlainObject(\n  obj: unknown\n): obj is Record<string | number, any> {\n  return (\n    obj !== null &&\n    typeof obj === \"object\" &&\n    (Object.getPrototypeOf(obj) === Object.prototype ||\n      Object.getPrototypeOf(obj) === null)\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAC3B,CADF,CAAA,CACc,EADd;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CACL,CADJ,CAAA,EAAA,CAAA,CAAA,EACY,CADZ,CAAA,CAAA,EAAA,CAAA;QAEI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,EAAA,CAAA,CAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAC,CAAL,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAA8B,EAA9B,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAA+B,EAA/B,CAAA,CAAA,EAAqC,CAArC,CAAA,CAAA,CAAyC,CAAC,CACvC;AACH;"}