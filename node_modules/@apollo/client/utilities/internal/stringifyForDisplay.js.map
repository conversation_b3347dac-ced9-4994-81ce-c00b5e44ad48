{"version": 3, "file": "stringifyForDisplay.js", "sources": ["../../../src/utilities/internal/stringifyForDisplay.ts"], "sourcesContent": ["import { makeUniqueId } from \"./makeUniqueId.js\";\n\n/** @internal */\nexport function stringifyForDisplay(value: any, space = 0): string {\n  const undefId = makeUniqueId(\"stringifyForDisplay\");\n  return JSON.stringify(\n    value,\n    (_, value) => {\n      return value === void 0 ? undefId : value;\n    },\n    space\n  )\n    .split(JSON.stringify(undefId))\n    .join(\"<undefined>\");\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;;;;;;AAGhD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAA8C,EAAE,CAAhD,CAAA,CAAA,CAAA,EAAA,EAAwD,CAAC,EAAzD;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC;IACnD,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CACnB,CADJ,CAAA,CAAA,CAAA,CACS,EACL,CAAC,CAAC,EAAE,CAFR,CAAA,CAAA,CAAA,CAEa,EAAE,CAFf,EAAA;QAGM,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,EAA4B,EAAE,EAAE,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAwC,EAAE,CAA1C,CAAA,CAAA,CAAA,CAA+C;IAC3C,CAAC,EACD,CADJ,CAAA,CAAA,CAAA,CACS;QAET,CAAK,CAAL,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC;QAClC,CAAK,CAAL,CAAA,CAAA,CAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC;AACxB;"}