import type { DocumentNode, OperationTypeNode } from "graphql";
/**
* Checks the document for errors and throws an exception if there is an error.
*
* @internal
* 
* @deprecated This is an internal API and should not be used directly. This can be removed or changed at any time.
*/
export declare const checkDocument: (doc: DocumentNode, expectedType?: OperationTypeNode) => void;
//# sourceMappingURL=checkDocument.d.ts.map
