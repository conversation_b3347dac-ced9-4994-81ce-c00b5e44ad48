{"version": 3, "file": "getMemoryInternals.js", "sources": ["../../../src/utilities/internal/getMemoryInternals.ts"], "sourcesContent": ["import type { Optimistic<PERSON>rapperFunction } from \"optimism\";\n\nimport type {\n  ApolloC<PERSON>,\n  ApolloLink,\n  DocumentTransform,\n  InMemoryCache,\n} from \"@apollo/client\";\nimport type { ApolloClient } from \"@apollo/client\";\nimport { __DEV__ } from \"@apollo/client/utilities/environment\";\n\nimport type { CacheSizes } from \"../caching/sizes.js\";\nimport { cacheSizes, defaultCacheSizes } from \"../caching/sizes.js\";\n\nconst globalCaches: {\n  print?: () => number;\n  canonicalStringify?: () => number;\n} = {};\n\nexport function registerGlobalCache(\n  name: keyof typeof globalCaches,\n  getSize: () => number\n) {\n  globalCaches[name] = getSize;\n}\n\n/**\n * Transformative helper type to turn a function of the form\n *\n * ```ts\n * (this: any) => R;\n * ```\n *\n * into a function of the form\n *\n * ```ts\n * () => R;\n * ```\n *\n * preserving the return type, but removing the `this` parameter.\n *\n * @remarks\n *\n * Further down in the definitions of `_getApolloClientMemoryInternals`,\n * `_getApolloCacheMemoryInternals` and `_getInMemoryCacheMemoryInternals`,\n * having the `this` parameter annotation is extremely useful for type checking\n * inside the function.\n *\n * If this is preserved in the exported types, though, it leads to a situation\n * where `ApolloCache.getMemoryInternals` is a function that requires a `this`\n * of the type `ApolloCache`, while the extending class `InMemoryCache` has a\n * `getMemoryInternals` function that requires a `this` of the type\n * `InMemoryCache`.\n * This is not compatible with TypeScript's inheritence system (although it is\n * perfectly correct), and so TypeScript will complain loudly.\n *\n * We still want to define our functions with the `this` annotation, though,\n * and have the return type inferred.\n * (This requirement for return type inference here makes it impossible to use\n * a function overload that is more explicit on the inner overload than it is\n * on the external overload.)\n *\n * So in the end, we use this helper to remove the `this` annotation from the\n * exported function types, while keeping it in the internal implementation.\n */\ntype RemoveThis<T> = T extends (this: any) => infer R ? () => R : never;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getApolloClientMemoryInternals =\n  __DEV__ ?\n    (_getApolloClientMemoryInternals as RemoveThis<\n      typeof _getApolloClientMemoryInternals\n    >)\n  : undefined;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getInMemoryCacheMemoryInternals =\n  __DEV__ ?\n    (_getInMemoryCacheMemoryInternals as RemoveThis<\n      typeof _getInMemoryCacheMemoryInternals\n    >)\n  : undefined;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getApolloCacheMemoryInternals =\n  __DEV__ ?\n    (_getApolloCacheMemoryInternals as RemoveThis<\n      typeof _getApolloCacheMemoryInternals\n    >)\n  : undefined;\n\nfunction getCurrentCacheSizes() {\n  // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n  const defaults: Record<keyof CacheSizes, number> = {\n    canonicalStringify: defaultCacheSizes[\"canonicalStringify\"],\n    checkDocument: defaultCacheSizes[\"checkDocument\"],\n    print: defaultCacheSizes[\"print\"],\n    \"documentTransform.cache\": defaultCacheSizes[\"documentTransform.cache\"],\n    \"queryManager.getDocumentInfo\":\n      defaultCacheSizes[\"queryManager.getDocumentInfo\"],\n    \"PersistedQueryLink.persistedQueryHashes\":\n      defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"],\n    \"fragmentRegistry.transform\":\n      defaultCacheSizes[\"fragmentRegistry.transform\"],\n    \"fragmentRegistry.lookup\": defaultCacheSizes[\"fragmentRegistry.lookup\"],\n    \"fragmentRegistry.findFragmentSpreads\":\n      defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"],\n    \"cache.fragmentQueryDocuments\":\n      defaultCacheSizes[\"cache.fragmentQueryDocuments\"],\n    \"removeTypenameFromVariables.getVariableDefinitions\":\n      defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"],\n    \"inMemoryCache.maybeBroadcastWatch\":\n      defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"],\n    \"inMemoryCache.executeSelectionSet\":\n      defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"],\n    \"inMemoryCache.executeSubSelectedArray\":\n      defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"],\n  };\n  return Object.fromEntries(\n    Object.entries(defaults).map(([k, v]) => [\n      k,\n      cacheSizes[k as keyof CacheSizes] || v,\n    ])\n  );\n}\n\nfunction _getApolloClientMemoryInternals(this: ApolloClient) {\n  if (!__DEV__) throw new Error(\"only supported in development mode\");\n\n  return {\n    limits: getCurrentCacheSizes(),\n    sizes: {\n      print: globalCaches.print?.(),\n      canonicalStringify: globalCaches.canonicalStringify?.(),\n      links: linkInfo(this.link),\n      queryManager: {\n        getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n        documentTransforms: transformInfo(\n          this[\"queryManager\"].documentTransform\n        ),\n      },\n      ...(this.cache.getMemoryInternals?.() as Partial<\n        ReturnType<typeof _getApolloCacheMemoryInternals>\n      > &\n        Partial<ReturnType<typeof _getInMemoryCacheMemoryInternals>>),\n    },\n  };\n}\n\nfunction _getApolloCacheMemoryInternals(this: ApolloCache) {\n  return {\n    cache: {\n      fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n    },\n  };\n}\n\nfunction _getInMemoryCacheMemoryInternals(this: InMemoryCache) {\n  const fragments = this.config.fragments as\n    | undefined\n    | {\n        findFragmentSpreads?: Function;\n        transform?: Function;\n        lookup?: Function;\n      };\n\n  return {\n    ..._getApolloCacheMemoryInternals.apply(this as any),\n    addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]),\n    inMemoryCache: {\n      executeSelectionSet: getWrapperInformation(\n        this[\"storeReader\"][\"executeSelectionSet\"]\n      ),\n      executeSubSelectedArray: getWrapperInformation(\n        this[\"storeReader\"][\"executeSubSelectedArray\"]\n      ),\n      maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n    },\n    fragmentRegistry: {\n      findFragmentSpreads: getWrapperInformation(\n        fragments?.findFragmentSpreads\n      ),\n      lookup: getWrapperInformation(fragments?.lookup),\n      transform: getWrapperInformation(fragments?.transform),\n    },\n  };\n}\n\nfunction isWrapper(f?: Function): f is OptimisticWrapperFunction<any, any> {\n  return !!f && \"dirtyKey\" in f;\n}\n\nfunction getWrapperInformation(f?: Function) {\n  return isWrapper(f) ? f.size : undefined;\n}\n\nfunction isDefined<T>(value: T | undefined | null): value is T {\n  return value != null;\n}\n\nfunction transformInfo(transform?: DocumentTransform) {\n  return recurseTransformInfo(transform).map((cache) => ({ cache }));\n}\n\nfunction recurseTransformInfo(transform?: DocumentTransform): number[] {\n  return transform ?\n      [\n        getWrapperInformation(transform?.[\"performWork\"]),\n        ...recurseTransformInfo(transform?.[\"left\"]),\n        ...recurseTransformInfo(transform?.[\"right\"]),\n      ].filter(isDefined)\n    : [];\n}\n\nfunction linkInfo(link?: ApolloLink): unknown[] {\n  return link ?\n      [\n        link?.getMemoryInternals?.(),\n        ...linkInfo(link?.left),\n        ...linkInfo(link?.right),\n      ].filter(isDefined)\n    : [];\n}\n"], "names": [], "mappings": "AASA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAG9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA8C,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmE;AAEnE,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAGI,CAHJ,CAGM;AAEN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CACjC,CADF,CAAA,CAAA,CACiC,EAC/B,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAEuB,EAFvB;IAIE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAmB,EAAnB,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAC9B;;;;;;;AA+CA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACE,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,EACU;IACL,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAGE,EAAE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;;;;;;;AAMb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACE,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,EACU;IACL,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAGE,EAAE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;;;;;;;AAMb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACE,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,EACU;IACL,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAGE,EAAE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;AAEb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAA7B,EAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqD;QACjD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAtB,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAA+D;QAC3D,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAjB,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAqD;QACjD,CAAJ,CAAA,CAAA,CAAA,CAAS,EAAT,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAqC;QACjC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAA7B,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAA2E;QACvE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAlC,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CACuD;QACnD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAA7C,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CACkE;QAC9D,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAhC,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CACqD;QACjD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAA7B,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAA2E;QACvE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,EAA1C,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAC+D;QAC3D,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAlC,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CACuD;QACnD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD,EAAxD,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAC6E;QACzE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAvC,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAC4D;QACxD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAvC,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAC4D;QACxD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,EAA3C,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CACgE;IAChE,CAAG;IACD,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CACvB,CADJ,CAAA,CAAA,CAAA,CAAA,CACU,CAAC,CADX,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CAAC,CAAC,CAD7B,CAAA,CACgC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAD1C,EAC6C;QACvC,CAAC;QACD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAqB,EAAtC,CAAA,EAA2C,CAAC;IAC5C,CAAK,CAAC,CACH;AACH;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,EAAA;IACE,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc;QAAE,CAAhB,CAAA,CAAA,CAAA,EAAsB,CAAtB,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAoE,CAAC;IAEnE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAkC;QAC9B,CAAJ,CAAA,CAAA,CAAA,CAAS,EAAE;YACL,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAA/B,CAAiC,CAAjC,CAAmC;YAC7B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAzD,CAA2D,CAA3D,CAA6D;YACvD,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAA+B,CAAC;YAC1B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE;gBACZ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC,CAAC,CAAhE,CAAA,CAAA,CAAoE;gBAC5D,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAAE,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAC/B,CADV,CAAA,CAAA,CACc,CAAC,CADf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC6B,CAAC,CAAC,CAD/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgD,CACvC;YACT,CAAO;YACD,CAAN,CAAA,CAAU,CAAV,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAvC,CAAyC,CAAzC,CAGqE;QACrE,CAAK;IACL,CAAG;AACH;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAvC,EAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAS,EAAE;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,EAAE,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAA,CAAwD,CAAC,CAAzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE,CAAC,CAAC;QAC3E,CAAK;IACL,CAAG;AACH;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAzC,EAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAMO;IAEL,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAuD,CAAC;QACpD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAE,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,CAAhD,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CAAC,CAAC;QACzE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE;YACb,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CACxC,CADR,CAAA,CAAA,CACY,CAAC,CADb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,CAAC,CAAC,CAD5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiD,CAAC,CAC3C;YACD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAC5C,CADR,CAAA,CAAA,CACY,CAAC,CADb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,CAAC,CAAC,CAD5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqD,CAAC,CAC/C;YACD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CAAC,CAAC;QAC7E,CAAK;QACD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE;YAChB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CACxC,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB,CADjB,CACmB,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACsC,CAC/B;YACD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAA7C,CAA+C,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC;YAChD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAhD,CAAkD,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAC;QAC5D,CAAK;IACL,CAAG;AACH;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAY,EAA/B;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAC,CAAC,EAAX,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA8B,CAAC;AAC/B;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAAY,EAA3C;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAA1B,CAAA,CAAA,EAA+B,EAAE,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C;AAC1C;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAI,CAAtB,CAAA,CAAA,CAAA,CAAiD,EAAjD;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,EAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAsB;AACtB;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,EAApD;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAC,CAAzC,CAAA,CAA4C,CAAC,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAmD,EAAE,CAArD,EAAwD,CAAC,EAAE,CAA3D,CAAA,CAAA,CAAA,EAAA,CAAkE,CAAC,CAAC;AACpE;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,EAA3D;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAmB;QACb;YACE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAvC,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD,CAAC,CAAC;YACjD,CAAR,CAAA,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAzC,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC,CAAC;YAC5C,CAAR,CAAA,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAzC,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAAC;QACrD,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB;QACpB,EAAE,CAAN,CAAQ;AACR;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAmC,EAAnC;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,EAAc;QACR;YACE,CAAR,CAAA,CAAA,CAAY,CAAZ,CAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAkC,CAAlC,CAAoC;YAC5B,CAAR,CAAA,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAwB,CAAxB,CAA0B,CAA1B,CAAA,CAAA,CAA8B,CAAC;YACvB,CAAR,CAAA,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAwB,CAAxB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC;QAChC,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB;QACpB,EAAE,CAAN,CAAQ;AACR;"}