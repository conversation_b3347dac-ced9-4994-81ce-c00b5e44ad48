{"version": 3, "file": "getOperationName.js", "sources": ["../../../src/utilities/internal/getOperationName.ts"], "sourcesContent": ["import type { DocumentNode, OperationDefinitionNode } from \"graphql\";\n\ntype OperationDefinitionWithName = OperationDefinitionNode & {\n  name: NonNullable<OperationDefinitionNode[\"name\"]>;\n};\n\n/** @internal */\nexport function getOperationName<\n  TFallback extends string | null | undefined = undefined,\n>(doc: DocumentNode, fallback?: TFallback): string | TFallback {\n  return (\n    doc.definitions.find(\n      (definition): definition is OperationDefinitionWithName =>\n        definition.kind === \"OperationDefinition\" && !!definition.name\n    )?.name.value ?? (fallback as TFallback)\n  );\n}\n"], "names": [], "mappings": ";;;;;AAOA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAE9B,CAFF,CAAA,CAEmB,EAAE,CAFrB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEyC,EAFzC;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CACL,CADJ,CAAA,CACO,CAAC,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CACwB,CAClB,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEiB,EAA6C,CAF9D,EAGQ,CAHR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGkB,CAAC,CAHnB,CAAA,CAAA,EAAA,CAAA,CAAA,EAG4B,CAH5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAGqD,CAAC,CAAC,CAHvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGiE,CAAC,CAHlE,CAAA,CAAA,CAGsE,CACjE,CAJL,CAIO,CAJP,CAAA,CAAA,CAIW,CAAC,CAJZ,CAAA,CAAA,CAAA,EAAA,CAAA,EAIsB,CAJtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAI4C,CACzC;AACH;"}