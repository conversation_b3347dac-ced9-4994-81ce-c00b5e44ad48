{"version": 3, "file": "isNonNullObject.js", "sources": ["../../../src/utilities/internal/isNonNullObject.ts"], "sourcesContent": ["/** @internal */\nexport function isNonNullObject(\n  obj: unknown\n): obj is Record<string | number, any> {\n  return obj !== null && typeof obj === \"object\";\n}\n"], "names": [], "mappings": ";;;;;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAC7B,CADF,CAAA,CACc,EADd;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,EAAA,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,EAAA,CAAA,CAAA,EAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;AAChD;"}