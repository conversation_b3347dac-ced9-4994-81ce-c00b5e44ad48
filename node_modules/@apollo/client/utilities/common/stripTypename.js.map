{"version": 3, "file": "stripTypename.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/stripTypename.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mCAAmC,CAAC;AAE7D;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,aAAa,CAAI,KAAQ;IACvC,OAAO,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACvC,CAAC", "sourcesContent": ["import { omitDeep } from \"@apollo/client/utilities/internal\";\n\n/**\n * Deeply removes all `__typename` properties in the given object or array.\n *\n * @param value - The object or array that should have `__typename` removed.\n * @returns The object with all `__typename` properties removed.\n *\n * @example\n *\n * ```ts\n * stripTypename({\n *   __typename: \"User\",\n *   id: 1,\n *   profile: { __typename: \"<PERSON>\", name: \"<PERSON>\" },\n * });\n * // => { id: 1, profile: { name: \"<PERSON>\"}}\n * ```\n */\nexport function stripTypename<T>(value: T) {\n  return omitDeep(value, \"__typename\");\n}\n"]}