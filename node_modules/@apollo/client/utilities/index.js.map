{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/utilities/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC;AAKlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AAEnE,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,0BAA0B,EAAE,MAAM,yCAAyC,CAAC;AAQrF,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAEtD,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;AAE/D,OAAO,EACL,mBAAmB,EACnB,gBAAgB,EAChB,uBAAuB,GACxB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,kBAAkB,EAClB,iBAAiB,GAClB,MAAM,mCAAmC,CAAC;AAE3C,OAAO,EACL,gBAAgB,EAChB,qBAAqB,EACrB,oBAAoB,GACrB,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAE1D,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC", "sourcesContent": ["export { Observable } from \"rxjs\";\n\nexport type { DeepPartial } from \"./DeepPartial.js\";\n\nexport type { DocumentTransformCacheKey } from \"./graphql/DocumentTransform.js\";\nexport { DocumentTransform } from \"./graphql/DocumentTransform.js\";\n\nexport { print } from \"./graphql/print.js\";\nexport { isFormattedExecutionResult } from \"./graphql/isFormattedExecutionResult.js\";\n\nexport type {\n  AsStoreObject,\n  Reference,\n  StoreObject,\n  StoreValue,\n} from \"./graphql/storeUtils.js\";\nexport { isReference } from \"./graphql/storeUtils.js\";\n\nexport { addTypenameToDocument } from \"./graphql/transform.js\";\n\nexport {\n  isMutationOperation,\n  isQueryOperation,\n  isSubscriptionOperation,\n} from \"./graphql/operations.js\";\nexport {\n  canonicalStringify,\n  getMainDefinition,\n} from \"@apollo/client/utilities/internal\";\n\nexport {\n  concatPagination,\n  offsetLimitPagination,\n  relayStylePagination,\n} from \"./policies/pagination.js\";\n\nexport { stripTypename } from \"./common/stripTypename.js\";\n\nexport { cacheSizes } from \"./caching/index.js\";\nexport type { CacheSizes } from \"./caching/index.js\";\n\nexport type { HKT } from \"./HKT.js\";\n"]}