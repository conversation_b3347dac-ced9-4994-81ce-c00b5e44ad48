{"version": 3, "file": "HKT.js", "sourceRoot": "", "sources": ["../../src/utilities/HKT.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * A helper interface to implement Higher-Kinded-Types (HKT) in TypeScript.\n *\n * @example\n * For example usage, see `src/masking/GraphQLCodegenDataMasking.ts`.\n *\n * @beta\n * The Higher-Kinded-Types implementation might change between minor versions,\n * as we discover ways of making it more performant and/or our requirements to\n * the HKT implementation change.\n *\n * We still want to encourage you to provide your own implementations of the types\n * we make overridable this way, but keep in mind that this might require some\n * extra work updating.\n *\n * @example\n *\n * ```ts\n * interface Concat extends HKT {\n *   arg1: string;\n *   arg2: string;\n *   return: `${this[\"arg1\"]}${this[\"arg2\"]}`;\n * }\n *\n * type Result = ApplyHKT<Concat, \"Hello, \", \"world!\">;\n * // Result is \"Hello, world!\"\n * ```\n */\nexport interface HKT {\n  arg1: unknown;\n  arg2: unknown;\n  arg3: unknown;\n  arg4: unknown;\n  return: unknown;\n}\n"]}