export { Observable } from "rxjs";
export { DocumentTransform } from "./graphql/DocumentTransform.js";
export { print } from "./graphql/print.js";
export { isFormattedExecutionResult } from "./graphql/isFormattedExecutionResult.js";
export { isReference } from "./graphql/storeUtils.js";
export { addTypenameToDocument } from "./graphql/transform.js";
export { isMutationOperation, isQueryOperation, isSubscriptionOperation, } from "./graphql/operations.js";
export { canonicalStringify, getMainDefinition, } from "@apollo/client/utilities/internal";
export { concatPagination, offsetLimitPagination, relayStylePagination, } from "./policies/pagination.js";
export { stripTypename } from "./common/stripTypename.js";
export { cacheSizes } from "./caching/index.js";
//# sourceMappingURL=index.js.map