{"version": 3, "file": "mockLink.js", "sources": ["../../../../src/testing/core/mocking/mockLink.ts"], "sourcesContent": ["import { equal } from \"@wry/equality\";\nimport type { DocumentNode } from \"graphql\";\nimport { asapScheduler, Observable, observeOn, throwError } from \"rxjs\";\n\nimport type { OperationVariables } from \"@apollo/client\";\nimport { ApolloLink } from \"@apollo/client/link\";\nimport type { Unmasked } from \"@apollo/client/masking\";\nimport { addTypenameToDocument, print } from \"@apollo/client/utilities\";\nimport {\n  checkDocument,\n  cloneDeep,\n  getDefaultValues,\n  getOperationDefinition,\n  isDocumentNode,\n  makeUniqueId,\n  removeDirectivesFromDocument,\n} from \"@apollo/client/utilities/internal\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\n/** @internal */\ntype CovariantUnaryFunction<out Arg, out Ret> = { fn(arg: Arg): Ret }[\"fn\"];\n\ntype VariableMatcher<V = Record<string, any>> = CovariantUnaryFunction<\n  V,\n  boolean\n>;\n\ninterface NormalizedMockedResponse {\n  original: MockLink.MockedResponse;\n  request: MockLink.MockedRequest;\n  variablesWithDefaults: Record<string, any>;\n  maxUsageCount: number;\n  result?: ApolloLink.Result | MockLink.ResultFunction<ApolloLink.Result, any>;\n  error?: Error;\n  delay: number | MockLink.DelayFunction;\n}\n\ntype UnmatchedVariables = Array<\n  Record<string, any> | \"<undefined>\" | `<function ${string}>`\n>;\n\nexport declare namespace MockLink {\n  export type DelayFunction = (operation: ApolloLink.Operation) => number;\n  export type Delay = number | DelayFunction;\n  export interface DefaultOptions {\n    delay?: MockLink.Delay;\n  }\n\n  export interface MockedRequest<\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    query: DocumentNode;\n    variables?: TVariables | VariableMatcher<TVariables>;\n  }\n\n  export interface MockedResponse<\n    // @ts-ignore\n    out TData = Record<string, any>,\n    out TVariables extends OperationVariables = Record<string, any>,\n  > {\n    request: MockedRequest<TVariables>;\n    maxUsageCount?: number;\n    result?:\n      | ApolloLink.Result<Unmasked<TData>>\n      | ResultFunction<ApolloLink.Result<Unmasked<TData>>, TVariables>;\n    error?: Error;\n    delay?: number | MockLink.DelayFunction;\n  }\n\n  export type ResultFunction<\n    T,\n    V = Record<string, any>,\n  > = CovariantUnaryFunction<V, T>;\n\n  export interface Options {\n    showWarnings?: boolean;\n    defaultOptions?: DefaultOptions;\n  }\n}\n\nexport function realisticDelay({\n  min = 20,\n  max = 50,\n}: { min?: number; max?: number } = {}): MockLink.DelayFunction {\n  invariant(max > min, \"realisticDelay: `min` must be less than `max`\");\n\n  return () => Math.floor(Math.random() * (max - min) + min);\n}\n\nexport class MockLink extends ApolloLink {\n  public operation!: ApolloLink.Operation;\n  public showWarnings: boolean = true;\n\n  private defaultDelay: MockLink.Delay;\n  private mockedResponsesByKey: { [key: string]: NormalizedMockedResponse[] } =\n    {};\n\n  public static defaultOptions: MockLink.DefaultOptions = {\n    delay: realisticDelay(),\n  };\n\n  constructor(\n    mockedResponses: ReadonlyArray<\n      MockLink.MockedResponse<Record<string, any>, Record<string, any>>\n    >,\n    options: MockLink.Options = {}\n  ) {\n    super();\n    const defaultOptions = options.defaultOptions ?? MockLink.defaultOptions;\n\n    this.showWarnings = options.showWarnings ?? true;\n    this.defaultDelay = defaultOptions?.delay ?? realisticDelay();\n\n    if (mockedResponses) {\n      mockedResponses.forEach((mockedResponse) => {\n        this.addMockedResponse(mockedResponse);\n      });\n    }\n  }\n\n  public addMockedResponse(mockedResponse: MockLink.MockedResponse) {\n    validateMockedResponse(mockedResponse);\n\n    const normalized = this.normalizeMockedResponse(mockedResponse);\n    this.getMockedResponses(normalized.request).push(normalized);\n  }\n\n  public request(\n    operation: ApolloLink.Operation\n  ): Observable<ApolloLink.Result> {\n    this.operation = operation;\n    const unmatchedVars: UnmatchedVariables = [];\n    const mocks = this.getMockedResponses(operation);\n\n    const index = mocks.findIndex((mock) => {\n      const { variables } = mock.request;\n\n      if (typeof variables === \"function\") {\n        const matched = variables(operation.variables);\n\n        if (!matched) {\n          unmatchedVars.push(`<function ${variables.name}>`);\n        }\n\n        return matched;\n      }\n\n      const withDefaults = mock.variablesWithDefaults;\n\n      if (equal(withDefaults, operation.variables)) {\n        return true;\n      }\n\n      unmatchedVars.push(\n        // Include default variables from the query in unmatched variables\n        // output\n        Object.keys(withDefaults).length > 0 ?\n          withDefaults\n        : variables || \"<undefined>\"\n      );\n      return false;\n    });\n\n    const matched = index >= 0 ? mocks[index] : void 0;\n\n    if (!matched) {\n      const message = getErrorMessage(operation, unmatchedVars);\n\n      if (this.showWarnings) {\n        console.warn(\n          message +\n            \"\\nThis typically indicates a configuration error in your mocks \" +\n            \"setup, usually due to a typo or mismatched variable.\"\n        );\n      }\n\n      return throwError(() => new Error(message)).pipe(\n        observeOn(asapScheduler)\n      );\n    }\n\n    if (matched.maxUsageCount > 1) {\n      matched.maxUsageCount--;\n    } else {\n      mocks.splice(index, 1);\n    }\n\n    const delay =\n      typeof matched.delay === \"function\" ?\n        matched.delay(operation)\n      : matched.delay;\n\n    if (!matched.result && !matched.error && delay !== Infinity) {\n      return throwError(\n        () =>\n          new Error(\n            `Mocked response should contain either \\`result\\`, \\`error\\` or a \\`delay\\` of \\`Infinity\\`:\\n${stringifyMockedResponse(\n              matched.original\n            )}`\n          )\n      );\n    }\n\n    if (matched.delay === Infinity) {\n      return new Observable();\n    }\n\n    return new Observable((observer) => {\n      const timer = setTimeout(() => {\n        if (matched.error) {\n          return observer.error(matched.error);\n        }\n\n        if (matched.result) {\n          observer.next(\n            typeof matched.result === \"function\" ?\n              matched.result(operation.variables)\n            : matched.result\n          );\n        }\n        observer.complete();\n      }, delay);\n\n      return () => {\n        clearTimeout(timer);\n      };\n    });\n  }\n\n  private getMockedResponses(request: MockLink.MockedRequest) {\n    const key = JSON.stringify({\n      query: print(addTypenameToDocument(request.query)),\n    });\n\n    let mockedResponses = this.mockedResponsesByKey[key];\n\n    if (!mockedResponses) {\n      mockedResponses = this.mockedResponsesByKey[key] = [];\n    }\n\n    return mockedResponses;\n  }\n\n  private normalizeMockedResponse(\n    mockedResponse: MockLink.MockedResponse\n  ): NormalizedMockedResponse {\n    const { request } = mockedResponse;\n    const response = cloneDeep(mockedResponse) as NormalizedMockedResponse;\n\n    response.original = mockedResponse;\n    response.request.query = getServerQuery(request.query);\n    response.maxUsageCount ??= 1;\n    response.variablesWithDefaults = {\n      ...getDefaultValues(getOperationDefinition(request.query)),\n      ...request.variables,\n    };\n    response.delay ??= this.defaultDelay;\n\n    return response;\n  }\n}\n\nfunction getErrorMessage(\n  operation: ApolloLink.Operation,\n  unmatchedVars: UnmatchedVariables\n) {\n  return `No more mocked responses for the query:\n${print(operation.query)}\n\nRequest variables: ${stringifyForDebugging(operation.variables)}\n${\n  unmatchedVars.length > 0 ?\n    `\nFailed to match variables against ${unmatchedVars.length} mock${\n      unmatchedVars.length === 1 ? \"\" : \"s\"\n    } for this query. The available mocks had the following variables:\n${unmatchedVars.map((d) => `  ${stringifyForDebugging(d)}`).join(\"\\n\")}\n`\n  : \"\"\n}`;\n}\n\nfunction getServerQuery(query: DocumentNode) {\n  const queryWithoutClientOnlyDirectives = removeDirectivesFromDocument(\n    [{ name: \"connection\" }, { name: \"nonreactive\" }, { name: \"unmask\" }],\n    query\n  );\n\n  invariant(queryWithoutClientOnlyDirectives, \"query is required\");\n\n  const serverQuery = removeDirectivesFromDocument(\n    [{ name: \"client\", remove: true }],\n    queryWithoutClientOnlyDirectives\n  );\n\n  invariant(\n    serverQuery,\n    \"Cannot mock a client-only query. Mocked responses should contain at least one non-client field.\"\n  );\n\n  return serverQuery;\n}\n\nfunction validateMockedResponse(mock: MockLink.MockedResponse) {\n  checkDocument(mock.request.query);\n\n  invariant(\n    (mock.maxUsageCount ?? 1) > 0,\n    \"Mocked response `maxUsageCount` must be greater than 0. Given %s\",\n    mock.maxUsageCount\n  );\n}\n\n/** @internal */\nexport function stringifyMockedResponse(\n  mockedResponse: MockLink.MockedResponse\n) {\n  return JSON.stringify(\n    mockedResponse,\n    (_, value) => {\n      if (isDocumentNode(value)) {\n        return print(value);\n      }\n\n      if (typeof value === \"function\") {\n        return \"<function>\";\n      }\n\n      return value;\n    },\n    2\n  );\n}\n\nexport interface MockApolloLink extends ApolloLink {\n  operation?: ApolloLink.Operation;\n}\n\n// This is similiar to the stringifyForDisplay utility we ship, but includes\n// support for NaN in addition to undefined. More values may be handled in the\n// future. This is not added to the primary stringifyForDisplay helper since it\n// is used for the cache and other purposes. We need this for debuggging only.\nfunction stringifyForDebugging(value: any, space = 0): string {\n  if (typeof value === \"string\") {\n    return value;\n  }\n\n  const undefId = makeUniqueId(\"undefined\");\n  const nanId = makeUniqueId(\"NaN\");\n\n  return JSON.stringify(\n    value,\n    (_, value) => {\n      if (value === void 0) {\n        return undefId;\n      }\n\n      if (Number.isNaN(value)) {\n        return nanId;\n      }\n\n      return value;\n    },\n    space\n  )\n    .replace(new RegExp(JSON.stringify(undefId), \"g\"), \"<undefined>\")\n    .replace(new RegExp(JSON.stringify(nanId), \"g\"), \"NaN\");\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;AAErC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAiE,CAAjE,CAAA,CAAA,CAAA,CAAA,CAAuE;AAGvE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;AAEhD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,EAAE,CAAhC,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA6C,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE;AACvE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EACL,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACe,EACb,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEW,EACT,CAHF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGkB,EAChB,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIwB,EACtB,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKgB,EACd,CANF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAMc,EACZ,CAPF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAO8B,EAP9B,EAAA,CAAA,CAAA,CAAA,EAQO,CARP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAQ0C;AAC1C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AA+D9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,EAC7B,CADF,CAAA,EAAA,EACQ,CADR,CACU,EACR,CAFF,CAAA,EAAA,EAEQ,CAFR,CAEU,EAFV,EAAA,EAGoC,CAHpC,CAGsC,EAHtC;IAIE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,EAAA,EAAkB,CAAlB,CAAA,MAAuE;IAErE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAqC,CAArC,EAAA,EAA0C,CAAC,CAA3C,CAAA,EAAA,EAAiD,CAAjD,CAAA,CAAoD,EAApD,EAAwD,CAAxD,CAAA,CAA2D,CAAC;AAC5D;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;IACS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;IACT,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAqC;IAE3B,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;IACZ,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACI,CADJ,CACM;IAEG,CAAT,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0D;QACtD,CAAJ,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA2B;IAC3B,CAAG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGK,EACD,CAJJ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAIgC,CAJhC,CAIkC,EAJlC;QAMI,CAAJ,CAAA,CAAA,CAAA,CAAS,CAAT,CAAW;QACP,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAqD,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6D,CAAC,CAA9D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4E;QAExE,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAgD,CAAhD,CAAA,CAAA,CAAoD;QAChD,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAtC,CAAwC,CAAxC,CAAA,CAAA,CAAA,EAAA,CAAA,EAAiD,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+D,CAA/D,CAAiE;QAE7D,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE;YACnB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE,CAA/C,EAAA;gBACQ,CAAR,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC;YACxC,CAAC,CAAC;QACJ;IACF;IAEO,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE,EAAlE;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC;QAEtC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE,CAAC;QAC/D,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAAC,CAAhD,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+D,CAAC;IAC9D;IAEO,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CACZ,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmC,EADnC;QAGI,CAAJ,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;QAC1B,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8C,CAA9C,CAAgD;QAC5C,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC;QAEhD,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAC,CAAnC,CAAA,CAAA,CAAuC,EAAE,CAAzC,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,EAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC;YAElC,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,EAAE;gBACnC,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC;gBAE9C,CAAR,EAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE;oBACZ,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAA,CAAwD,CAAxD,CAAA,CAA2D,CAAC;gBACpD;gBAEA,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAChB;YAEA,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD;YAE/C,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,EAAE,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,EAAE;gBAC5C,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAmB;YACb;YAEA,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAwB;YAChB,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACQ,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2C,EAAE;gBACnC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAC7B;YACD,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAkB;QACd,CAAC,CAAC;QAEF,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,EAAA,CAAA,EAA6B,EAAE,EAAE,CAAjC,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAA4C,EAAE,EAAE,CAAhD,CAAA,CAAA,EAAqD,CAAC;QAElD,CAAJ,EAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE;YACZ,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,EAAE,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC;YAEzD,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE;gBACrB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAoB,CACV,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;oBAEY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA;oBACY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE,CACzD;YACH;YAEA,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B,CAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,CAAC,CAAC,CAAlD,CAAA,CAAA,CAAsD,CAC9C,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB,CAAC,CADlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+B,CAAC,CACzB;QACH;QAEA,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAC,EAAE;YAC7B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAA6B;QACzB;QAAJ,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAC,CAAC;QACxB;QAEA,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,EAAA,EACM,CADN,CAAA,CAAA,CAAA,CAAA,EACa,CADb,CAAA,CAAA,CAAA,CAAA,CAAA,CACoB,CAAC,CADrB,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC+B,CAD/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAC0C;YAClC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;YACzB,EAAE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAqB;QAEjB,CAAJ,EAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,EAAA,CAAA,EAA6C,CAA7C,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAuD,CAAvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+D,EAAE;YAC3D,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CACf,CADR,EACW,CADX,EAEU,CAFV,CAAA,EAEc,CAFd,CAAA,CAAA,CAAA,CAEmB,CACP,CAHZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAG4G,CAH5G,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGmI,CACrH,CAJd,CAAA,CAAA,CAAA,CAAA,CAAA,CAIqB,CAAC,CAJtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAI8B,CACjB,CALb,CAKe,CACJ,CACJ;QACH;QAEA,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE;YAC9B,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAA6B;QACzB;QAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EAAE,CAArC,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,EAAkC,CAAlC,EAAA;gBACQ,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAyB,EAAE;oBACjB,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAA6C,CAAC;gBACtC;gBAEA,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B,EAAE;oBAClB,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAuB,CACX,CADZ,CAAA,CAAA,CAAA,CAAA,EACmB,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,CAAC,CAD3B,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EACsC,CADtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACiD;wBACnC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD;wBACpC,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAA4B,CACjB;gBACH;gBACA,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA2B;YACrB,CAAC,EAAE,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC;YAET,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,EAAgB,CAAhB,EAAA;gBACQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC;YACrB,CAAC;QACH,CAAC,CAAC;IACJ;IAEQ,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,EAA5D;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC;YACzB,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAC;QACxD,CAAK,CAAC;QAEF,CAAJ,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAuD,CAAC;QAEpD,CAAJ,EAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE;YACpB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAqD,EAArD,EAAyD,CAAzD,CAA2D;QACvD;QAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B;IACxB;IAEQ,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAC7B,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2C,EAD3C;QAGI,CAAJ,CAAA,CAAA,CAAA,EAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC;QAClC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAA6B;QAEtE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC;QAClC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAA,CAAA,CAAyD,CAAC;QACtD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA+B,CAAC;QAC5B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqC;YAC/B,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD,CAAC,CAAzD,CAAA,CAAA,CAAA,CAA8D,CAAC,CAAC;YAC1D,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B;QAC1B,CAAK;QACD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC;QAEpC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB;IACjB;;AAGF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CACtB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,EAC/B,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmC,EAFnC;IAIE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAE,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC;;AAExB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC;AAE/D,CAAA,CAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,EAAE;QACvB;AACJ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAwD,EAAxD,CAAA,CAAA,CAAA,CAAA,CAAA,CACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC+B,EAAE,EAAE,CADnC,EACsC,EAAE,CADxC,CAAA,CAEI,EAFJ,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAGA,CAAA,CAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAmB,CAAC,CAAC,CAAC,EAAE,CAAxB,EAA2B,GAA3B,CAAA,CAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAC,CAAC,CAAxD,CAA0D,CAAC,CAAC,CAA5D,CAAA,CAAA,CAAgE,CAAC,CAAjE,CAAA,CAAA,CAAqE,CAAC;AACtE;QACE,EAAE,CAAJ,CACA,CADA,CACE;AACF;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAA2C,EAA3C;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2C,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CACnE,CAAC,EAAE,CADP,CAAA,CAAA,CACW,EAAE,CADb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAC2B,EAAE,EAAE,CAD/B,CAAA,CAAA,CACmC,EAAE,CADrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CACoD,EAAE,EAAE,CADxD,CAAA,CAAA,CAC4D,EAAE,CAD9D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CACwE,CAAC,EACrE,CAFJ,CAAA,CAAA,CAAA,CAES,CACN;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAkE;IAEhE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAC9C,CAAC,EAAE,CADP,CAAA,CAAA,CACW,EAAE,CADb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,EAAE,CADvB,CAAA,CAAA,CAAA,CAAA,CAC6B,EAAE,CAD/B,CAAA,CAAA,EAAA,CACqC,CAAC,EAClC,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEoC,CACjC;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAGG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;AACpB;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAA6D,EAA7D;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAkC,CAAC;IAEjC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CAAC,CADL,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC2B,CAAC,EAD5B,EACgC,OAE5B,CAHJ,CAAA,CAAA,CAGQ,CAAC,CAHT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAIG;AACH;;;;;;AAGA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CACrC,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyC,EADzC;IAGE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CACnB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,EACd,CAAC,CAAC,EAAE,CAFR,CAAA,CAAA,CAAA,CAEa,EAAE,CAFf,EAAA;QAGM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,EAAE;YACzB,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC;QACrB;QAEA,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,EAAE;YAC/B,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B;QACrB;QAEA,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAkB;IACd,CAAC,EACD,CAAC,CACF;AACH;AAMA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAyC,EAAE,CAA3C,CAAA,CAAA,CAAA,EAAA,EAAmD,CAAC,EAApD;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,EAAE;QAC7B,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAgB;IACd;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;IACzC,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAkC,CAAC;IAEjC,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CACnB,CADJ,CAAA,CAAA,CAAA,CACS,EACL,CAAC,CAAC,EAAE,CAFR,CAAA,CAAA,CAAA,CAEa,EAAE,CAFf,EAAA;QAGM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoB,CAApB,CAAA,CAAA,EAAyB,CAAC,EAAE;YACpB,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;QAChB;QAEA,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAA4B,CAAC,EAAE;YACvB,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAoB;QACd;QAEA,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAkB;IACd,CAAC,EACD,CADJ,CAAA,CAAA,CAAA,CACS;QAET,CAAK,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,EAAE,CAAjD,CAAA,CAAoD,CAAC,EAAE,CAAvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoE;QACpE,CAAK,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAA4C,CAAC,EAAE,CAA/C,CAAA,CAAkD,CAAC,EAAE,CAArD,CAAA,CAAA,CAAA,CAA0D,CAAC;AAC3D;"}