{"version": 3, "file": "mockSubscriptionLink.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockSubscriptionLink.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAUjD,MAAM,OAAO,oBAAqB,SAAQ,UAAU;IAC3C,aAAa,GAAU,EAAE,CAAC;IAC1B,MAAM,GAAU,EAAE,CAAC;IACnB,SAAS,CAAwB;IAEhC,SAAS,GAAU,EAAE,CAAC;IAE9B;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAEM,OAAO,CAAC,SAA+B;QAC5C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,OAAO,IAAI,UAAU,CAAoB,CAAC,QAAQ,EAAE,EAAE;YACpD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9B,OAAO,GAAG,EAAE;gBACV,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACzC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,MAAmC,EAAE,QAAQ,GAAG,KAAK;QACzE,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,MAAM;gBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YACjE,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC7B,IAAI,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI;oBAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACjE,IAAI,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;oBAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACjE,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ;oBAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;IACxB,CAAC;IAEM,gBAAgB;QACrB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACjE,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC7B,IAAI,QAAQ,CAAC,QAAQ;gBAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,OAAO,CAAC,QAAa;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEM,aAAa,CAAC,QAAa;QAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7D,CAAC;CACF", "sourcesContent": ["import { Observable } from \"rxjs\";\n\nimport { ApolloLink } from \"@apollo/client/link\";\n\nexport declare namespace MockSubscriptionLink {\n  export interface Result {\n    result?: ApolloLink.Result;\n    error?: Error;\n    delay?: number;\n  }\n}\n\nexport class MockSubscriptionLink extends ApolloLink {\n  public unsubscribers: any[] = [];\n  public setups: any[] = [];\n  public operation?: ApolloLink.Operation;\n\n  private observers: any[] = [];\n\n  constructor() {\n    super();\n  }\n\n  public request(operation: ApolloLink.Operation) {\n    this.operation = operation;\n    return new Observable<ApolloLink.Result>((observer) => {\n      this.setups.forEach((x) => x());\n      this.observers.push(observer);\n      return () => {\n        this.unsubscribers.forEach((x) => x());\n      };\n    });\n  }\n\n  public simulateResult(result: MockSubscriptionLink.Result, complete = false) {\n    setTimeout(() => {\n      const { observers } = this;\n      if (!observers.length) throw new Error(\"subscription torn down\");\n      observers.forEach((observer) => {\n        if (result.result && observer.next) observer.next(result.result);\n        if (result.error && observer.error) observer.error(result.error);\n        if (complete && observer.complete) observer.complete();\n      });\n    }, result.delay || 0);\n  }\n\n  public simulateComplete() {\n    const { observers } = this;\n    if (!observers.length) throw new Error(\"subscription torn down\");\n    observers.forEach((observer) => {\n      if (observer.complete) observer.complete();\n    });\n  }\n\n  public onSetup(listener: any): void {\n    this.setups = this.setups.concat([listener]);\n  }\n\n  public onUnsubscribe(listener: any): void {\n    this.unsubscribers = this.unsubscribers.concat([listener]);\n  }\n}\n"]}