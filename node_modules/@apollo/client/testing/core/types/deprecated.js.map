{"version": 3, "file": "deprecated.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/types/deprecated.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { OperationVariables } from \"@apollo/client\";\n\nimport type { MockLink } from \"../mocking/mockLink.js\";\n\n/** @deprecated Use `MockLink.MockedRequest` instead */\nexport type MockedRequest<\n  TVariables extends OperationVariables = Record<string, any>,\n> = MockLink.MockedRequest<TVariables>;\n\n/** @deprecated Use `MockLink.MockedResponse` instead */\nexport type MockedResponse<\n  TData = Record<string, any>,\n  TVariables extends OperationVariables = Record<string, any>,\n> = MockLink.MockedResponse<TData, TVariables>;\n\n/** @deprecated Use `MockLink.Options` instead */\nexport type MockLinkOptions = MockLink.Options;\n\n/** @deprecated Use `MockLink.ResultFunction` instead */\nexport type ResultFunction<\n  T,\n  V = Record<string, any>,\n> = MockLink.ResultFunction<T, V>;\n"]}