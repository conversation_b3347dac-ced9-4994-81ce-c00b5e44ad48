{"version": 3, "file": "MockedProvider.js", "sourceRoot": "", "sources": ["../../../src/testing/react/MockedProvider.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAG9D,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAwBlD,MAAM,OAAO,cAAe,SAAQ,KAAK,CAAC,SAGzC;IACC,YAAY,KAA0B;QACpC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEb,MAAM,EACJ,KAAK,EACL,cAAc,EACd,KAAK,EACL,UAAU,EACV,IAAI,EACJ,YAAY,EACZ,sBAAsB,EACtB,QAAQ,GACT,GAAG,IAAI,CAAC,KAAK,CAAC;QACf,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC;YAC9B,KAAK,EAAE,KAAK,IAAI,IAAI,KAAK,EAAE;YAC3B,cAAc;YACd,IAAI,EACF,IAAI;gBACJ,IAAI,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE;oBACxB,YAAY;oBACZ,cAAc,EAAE,sBAAsB;iBACvC,CAAC;YACJ,UAAU;YACV,QAAQ;SACT,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG;YACX,MAAM;SACP,CAAC;IACJ,CAAC;IAEM,MAAM;QACX,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5C,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QAE9B,OAAO,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnC,oBAAC,cAAc,IAAC,MAAM,EAAE,MAAM,IAC3B,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,CAAC,CACtD;YACnB,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAEM,oBAAoB;QACzB,oEAAoE;QACpE,mDAAmD;QACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["import * as React from \"react\";\n\nimport { Apollo<PERSON>lient } from \"@apollo/client\";\nimport type { ApolloCache } from \"@apollo/client/cache\";\nimport { InMemoryCache as Cache } from \"@apollo/client/cache\";\nimport type { ApolloLink } from \"@apollo/client/link\";\nimport type { LocalState } from \"@apollo/client/local-state\";\nimport { ApolloProvider } from \"@apollo/client/react\";\nimport { MockLink } from \"@apollo/client/testing\";\n\nexport interface MockedProviderProps {\n  mocks?: ReadonlyArray<MockLink.MockedResponse<any, any>>;\n  defaultOptions?: ApolloClient.DefaultOptions;\n  cache?: ApolloCache;\n  localState?: LocalState;\n  childProps?: object;\n  children?: any;\n  link?: ApolloLink;\n  showWarnings?: boolean;\n  mockLinkDefaultOptions?: MockLink.DefaultOptions;\n  /**\n   * Configuration used by the [Apollo Client Devtools extension](https://www.apollographql.com/docs/react/development-testing/developer-tooling/#apollo-client-devtools) for this client.\n   *\n   * @since 3.14.0\n   */\n  devtools?: ApolloClient.Options[\"devtools\"];\n}\n\ninterface MockedProviderState {\n  client: ApolloClient;\n}\n\nexport class MockedProvider extends React.Component<\n  MockedProviderProps,\n  MockedProviderState\n> {\n  constructor(props: MockedProviderProps) {\n    super(props);\n\n    const {\n      mocks,\n      defaultOptions,\n      cache,\n      localState,\n      link,\n      showWarnings,\n      mockLinkDefaultOptions,\n      devtools,\n    } = this.props;\n    const client = new ApolloClient({\n      cache: cache || new Cache(),\n      defaultOptions,\n      link:\n        link ||\n        new MockLink(mocks || [], {\n          showWarnings,\n          defaultOptions: mockLinkDefaultOptions,\n        }),\n      localState,\n      devtools,\n    });\n\n    this.state = {\n      client,\n    };\n  }\n\n  public render() {\n    const { children, childProps } = this.props;\n    const { client } = this.state;\n\n    return React.isValidElement(children) ?\n        <ApolloProvider client={client}>\n          {React.cloneElement(React.Children.only(children), { ...childProps })}\n        </ApolloProvider>\n      : null;\n  }\n\n  public componentWillUnmount() {\n    // Since this.state.client was created in the constructor, it's this\n    // MockedProvider's responsibility to terminate it.\n    this.state.client.stop();\n  }\n}\n"]}