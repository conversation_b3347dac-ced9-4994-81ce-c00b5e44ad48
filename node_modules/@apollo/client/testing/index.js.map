{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/testing/index.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC", "sourcesContent": ["export type {\n  MockedRequest,\n  MockedResponse,\n  MockLinkOptions,\n  ResultFunction,\n} from \"./core/types/deprecated.js\";\nexport { MockLink, realisticDelay } from \"./core/mocking/mockLink.js\";\nexport { MockSubscriptionLink } from \"./core/mocking/mockSubscriptionLink.js\";\n"]}