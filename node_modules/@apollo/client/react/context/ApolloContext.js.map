{"version": 3, "file": "ApolloContext.js", "sources": ["../../../src/react/context/ApolloContext.ts"], "sourcesContent": ["import type * as ReactTypes from \"react\";\nimport * as React from \"react\";\n\nimport type { ApolloClient } from \"@apollo/client\";\nimport type {\n  HookWrappers,\n  wrapperSymbol,\n} from \"@apollo/client/react/internal\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nexport interface ApolloContextValue {\n  client?: ApolloClient;\n  [wrapperSymbol]?: HookWrappers;\n}\n\n// To make sure Apollo Client doesn't create more than one React context\n// (which can lead to problems like having an Apollo Client instance added\n// in one context, then attempting to retrieve it from another different\n// context), a single Apollo context is created and tracked in global state.\nconst contextKey = Symbol.for(\"__APOLLO_CONTEXT__\");\n\nexport function getApolloContext(): ReactTypes.Context<ApolloContextValue> {\n  invariant(\n    \"createContext\" in React,\n    \"Invoking `getApolloContext` in an environment where `React.createContext` is not available.\\n\" +\n      \"The Apollo Client functionality you are trying to use is only available in React Client Components.\\n\" +\n      'Please make sure to add \"use client\" at the top of your file.\\n' +\n      // TODO: change to React documentation once React documentation contains information about Client Components\n      \"For more information, see https://nextjs.org/docs/getting-started/react-essentials#client-components\"\n  );\n\n  let context = (React.createContext as any)[\n    contextKey\n  ] as React.Context<ApolloContextValue>;\n  if (!context) {\n    Object.defineProperty(React.createContext, contextKey, {\n      value: (context = React.createContext<ApolloContextValue>({})),\n      enumerable: false,\n      writable: false,\n      configurable: true,\n    });\n    context.displayName = \"ApolloContext\";\n  }\n  return context;\n}\n"], "names": [], "mappings": "AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAP,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAO9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAO9D,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC;AAEnD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,EAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EACuB,CADvB,CAAA,CAAA,CAAA,MAOG;IAED,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CACxC,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc,CAC0B;IACtC,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE;QACZ,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,EAAE;YACrD,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAqB,CAAhE,CAAkE,CAAC,CAAC;YAC9D,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE,CAAlB,CAAA,CAAA,CAAA,CAAuB;YACjB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAqB;YACf,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAwB;QACxB,CAAK,CAAC;QACF,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC;IACvC;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;AAChB;"}