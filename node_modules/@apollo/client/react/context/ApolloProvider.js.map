{"version": 3, "file": "ApolloProvider.js", "sources": ["../../../src/react/context/ApolloProvider.tsx"], "sourcesContent": ["import type * as ReactTypes from \"react\";\nimport * as React from \"react\";\n\nimport type { ApolloClient } from \"@apollo/client\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nimport { getApolloContext } from \"./ApolloContext.js\";\n\nexport declare namespace ApolloProvider {\n  interface Props {\n    client: ApolloClient;\n    children: ReactTypes.ReactNode | ReactTypes.ReactNode[] | null;\n  }\n}\n\nexport const ApolloProvider: ReactTypes.FC<ApolloProvider.Props> = ({\n  client,\n  children,\n}) => {\n  const ApolloContext = getApolloContext();\n  const parentContext = React.useContext(ApolloContext);\n\n  const context = React.useMemo(() => {\n    return {\n      ...parentContext,\n      client: client || parentContext.client,\n    };\n  }, [parentContext, client]);\n\n  invariant(\n    context.client,\n    \"ApolloProvider was not passed a client instance. Make \" +\n      'sure you pass in your client via the \"client\" prop.'\n  );\n\n  return (\n    <ApolloContext.Provider value={context}>{children}</ApolloContext.Provider>\n  );\n};\n"], "names": [], "mappings": "AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAP,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAG9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD;AASrD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmE,CAAC,EAClE,CADF,CAAA,CAAA,CAAA,CAAA,CACQ,EACN,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEU,EAFV,CAGC,EAAE,CAHH,EAAA;IAIE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAA0C;IACxC,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC;IAErD,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,EAAmC,CAAnC,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAChB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAA4C;QAC5C,CAAK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAAC;IAE3B,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CACW,CAAC,CADZ,CAAA,CAAA,CAAA,CAAA,MAIG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CACL,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACK,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB,CAAC,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,EAD3B,EAC4B,CAD5B,CAAA,CAAA,CAAA,CACiC,EAAE,CADnC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC6C,CAD7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqD,CAA0B,CAC5E;AACH,CAAC;"}