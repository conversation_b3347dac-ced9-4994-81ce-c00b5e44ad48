{"version": 3, "file": "useApolloClient.js", "sources": ["../../../src/react/hooks/useApolloClient.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type { ApolloClient } from \"@apollo/client\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nimport { getApolloContext } from \"../context/ApolloContext.js\";\n\n/**\n * @example\n *\n * ```jsx\n * import { useApolloClient } from \"@apollo/client\";\n *\n * function SomeComponent() {\n *   const client = useApolloClient();\n *   // `client` is now set to the `ApolloClient` instance being used by the\n *   // application (that was configured using something like `ApolloProvider`)\n * }\n * ```\n *\n * @returns The `ApolloClient` instance being used by the application.\n */\nexport function useApolloClient(override?: ApolloClient): ApolloClient {\n  const context = React.useContext(getApolloContext());\n  const client = override || context.client;\n  invariant(\n    !!client,\n    'Could not find \"client\" in the context or passed in as an option. ' +\n      \"Wrap the root component in an <ApolloProvider>, or pass an ApolloClient \" +\n      \"instance in via options.\"\n  );\n\n  return client;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAP,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAG9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA;;;;;;;;;;;;;;CAcA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD,EAAvD;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAnD,CAAqD,CAAC;IACpD,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAA2C;IACzC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACI,CAAC,CAAC,CADN,CAAA,CAAA,CAAA,CAAA,MAKG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe;AACf;"}