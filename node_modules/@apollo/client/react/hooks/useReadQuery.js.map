{"version": 3, "file": "useReadQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useReadQuery.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAY/B,OAAO,EACL,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,GACtB,MAAM,+BAA+B,CAAC;AAGvC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAqCjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,UAAU,YAAY,CAI1B,QAAuC;IAEvC,aAAa,CAAC;IACd,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,gBAAgB,GAAG,eAAe,CACtC,SAAS,CAAC,CAAC;QACT,0EAA0E;QAC1E,iFAAiF;QAChF,SAAS,CAAC,YAAY,CAAS;QAClC,CAAC,CAAC,SAAS,CAC6B,CAAC;IAE3C,OAAO,QAAQ,CACb,cAAc;IACd,yDAAyD;IACzD,aAAa,EACb,gBAAgB,CACjB,CAAC,QAAQ,CAAC,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CACpB,QAAuC;IAEvC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAChC,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CACpC,GAAG,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC9B,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAClC,GAAG,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EACjC,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC9B,gBAAgB,CAAC,YAAY,EAAE,CAAC;QAChC,qBAAqB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAErE,MAAM,OAAO,GAAG,oBAAoB,CAClC,KAAK,CAAC,WAAW,CACf,CAAC,WAAW,EAAE,EAAE;QACd,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACzC,WAAW,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,EACD,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAC7B,EACD,UAAU,EACV,UAAU,CACX,CAAC;IAEF,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IAE9B,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxB,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,KAAK,EAAE,MAAM,CAAC,KAAK;SACmB,CAAC;IAC3C,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACf,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  <PERSON>rror<PERSON>ike,\n  GetDataState,\n  NetworkStatus,\n  ObservableQuery,\n} from \"@apollo/client\";\nimport type { MaybeMasked } from \"@apollo/client/masking\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport {\n  assertWrappedQueryRef,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n} from \"@apollo/client/react/internal\";\nimport type { DocumentationTypes as UtilityDocumentationTypes } from \"@apollo/client/utilities/internal\";\n\nimport { __use, wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useReadQuery {\n  export namespace Base {\n    export interface Result<TData = unknown> {\n      /**\n       * {@inheritDoc @apollo/client!QueryResultDocumentation#error:member}\n       *\n       * This property can be ignored when using the default `errorPolicy` or an\n       * `errorPolicy` of `none`. The hook will throw the error instead of setting\n       * this property.\n       */\n      error: ErrorLike | undefined;\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n    }\n  }\n  export type Result<\n    TData = unknown,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData> & GetDataState<MaybeMasked<TData>, TStates>;\n\n  export namespace DocumentationTypes {\n    namespace useReadQuery {\n      export interface Result<TData = unknown>\n        extends Base.Result<TData>,\n          UtilityDocumentationTypes.DataState<TData> {}\n    }\n\n    /** {@inheritDoc @apollo/client/react!useReadQuery:function(1)} */\n    export function useReadQuery<TData>(\n      queryRef: QueryRef<TData>\n    ): useReadQuery.Result<TData>;\n  }\n}\n\n/**\n * For a detailed explanation of `useReadQuery`, see the [fetching with Suspense reference](https://www.apollographql.com/docs/react/data/suspense#avoiding-request-waterfalls).\n *\n * @param queryRef - The `QueryRef` that was generated via `useBackgroundQuery`.\n * @returns An object containing the query result data, error, and network status.\n *\n * @example\n *\n * ```jsx\n * import { Suspense } from \"react\";\n * import { useBackgroundQuery, useReadQuery } from \"@apollo/client\";\n *\n * function Parent() {\n *   const [queryRef] = useBackgroundQuery(query);\n *\n *   return (\n *     <Suspense fallback={<div>Loading...</div>}>\n *       <Child queryRef={queryRef} />\n *     </Suspense>\n *   );\n * }\n *\n * function Child({ queryRef }) {\n *   const { data } = useReadQuery(queryRef);\n *\n *   return <div>{data.name}</div>;\n * }\n * ```\n */\nexport function useReadQuery<\n  TData,\n  TStates extends DataState<TData>[\"dataState\"],\n>(\n  queryRef: QueryRef<TData, any, TStates>\n): useReadQuery.Result<TData, TStates> {\n  \"use no memo\";\n  const unwrapped = unwrapQueryRef(queryRef);\n  const clientOrObsQuery = useApolloClient(\n    unwrapped ?\n      // passing an `ObservableQuery` is not supported by the types, but it will\n      // return any truthy value that is passed in as an override so we cast the result\n      (unwrapped[\"observable\"] as any)\n    : undefined\n  ) as ApolloClient | ObservableQuery<TData>;\n\n  return wrapHook(\n    \"useReadQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useReadQuery_,\n    clientOrObsQuery\n  )(queryRef);\n}\n\nfunction useReadQuery_<TData, TStates extends DataState<TData>[\"dataState\"]>(\n  queryRef: QueryRef<TData, any, TStates>\n): useReadQuery.Result<TData, TStates> {\n  assertWrappedQueryRef(queryRef);\n  const internalQueryRef = React.useMemo(\n    () => unwrapQueryRef(queryRef),\n    [queryRef]\n  );\n\n  const getPromise = React.useCallback(\n    () => getWrappedPromise(queryRef),\n    [queryRef]\n  );\n\n  if (internalQueryRef.disposed) {\n    internalQueryRef.reinitialize();\n    updateWrappedQueryRef(queryRef, internalQueryRef.promise);\n  }\n\n  React.useEffect(() => internalQueryRef.retain(), [internalQueryRef]);\n\n  const promise = useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        return internalQueryRef.listen((promise) => {\n          updateWrappedQueryRef(queryRef, promise);\n          forceUpdate();\n        });\n      },\n      [internalQueryRef, queryRef]\n    ),\n    getPromise,\n    getPromise\n  );\n\n  const result = __use(promise);\n\n  return React.useMemo(() => {\n    return {\n      data: result.data,\n      dataState: result.dataState,\n      networkStatus: result.networkStatus,\n      error: result.error,\n    } as useReadQuery.Result<TData, TStates>;\n  }, [result]);\n}\n"]}