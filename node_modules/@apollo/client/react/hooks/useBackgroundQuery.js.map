{"version": 3, "file": "useBackgroundQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useBackgroundQuery.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAc/B,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAO1D,OAAO,EACL,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACrB,YAAY,GACb,MAAM,+BAA+B,CAAC;AAQvC,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AA4X7D,MAAM,UAAU,kBAAkB,CAIhC,KAA0D,EAC1D,OAAqE;IAKrE,aAAa,CAAC;IACd,OAAO,QAAQ,CACb,oBAAoB;IACpB,yDAAyD;IACzD,mBAAmB,EACnB,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,KAAK,EAAE,OAAO,IAAK,EAAU,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,mBAAmB,CAK1B,KAA0D,EAC1D,OAEmD;IAKnD,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IAC3E,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,iBAAiB,CAAC;IACrD,MAAM,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IAElC,yEAAyE;IACzE,iEAAiE;IACjE,wEAAwE;IACxE,wEAAwE;IACxE,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC;IAC/D,cAAc,CAAC,OAAO,KAAK,WAAW,KAAK,SAAS,CAAC;IAErD,MAAM,QAAQ,GAAa;QACzB,KAAK;QACL,kBAAkB,CAAC,SAAS,CAAC;QAC7B,GAAI,EAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;KAClC,CAAC;IAEF,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAiB,QAAQ,EAAE,GAAG,EAAE,CACxE,MAAM,CAAC,UAAU,CACf,iBAA6D,CAC9D,CACF,CAAC;IAEF,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAC1D,YAAY,CAAC,QAAQ,CAAC,CACvB,CAAC;IACF,IAAI,cAAc,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE,CAAC;QACjD,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACjD,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACzD,qBAAqB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,sEAAsE;IACtE,2EAA2E;IAC3E,0EAA0E;IAC1E,6EAA6E;IAC7E,yDAAyD;IACzD,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,2EAA2E;QAC3E,iEAAiE;QACjE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE;YACzB,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAC9B,uEAAuE;QACvE,kEAAkE;IACpE,CAAC,CAAC,CAAC;IAEH,MAAM,SAAS,GAAyC,KAAK,CAAC,WAAW,CACvE,CAAC,OAAO,EAAE,EAAE;QACV,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE5C,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,MAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,CAAC,SAAS,EAAE,EAAE;QACZ,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5C,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEzD,OAAO;QACL,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD;YACE,SAAS;YACT,OAAO;YACP,kGAAkG;YAClG,eAAe,EAAE,QAAQ,CAAC,UAAU;iBACjC,eAA6D;SACjE;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorPolicy,\n  OperationVariables,\n  RefetchWritePolicy,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON>,\n  FetchMoreFunction,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport {\n  getSuspenseCache,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"@apollo/client/react/internal\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\n\nimport type { SkipToken } from \"./constants.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useWatchQueryOptions } from \"./useSuspenseQuery.js\";\n\nexport declare namespace useBackgroundQuery {\n  import _self = useBackgroundQuery;\n  export type FetchPolicy = Extract<\n    WatchQueryFetchPolicy,\n    \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n  >;\n\n  export namespace Base {\n    export interface Options {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy_suspense:member} */\n      refetchWritePolicy?: RefetchWritePolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n      returnPartialData?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: FetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n      queryKey?: string | number | any[];\n\n      /**\n       * {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip_deprecated:member}\n       *\n       * @example Recommended usage of `skipToken`:\n       *\n       * ```ts\n       * import { skipToken, useBackgroundQuery } from \"@apollo/client\";\n       *\n       * const [queryRef] = useBackgroundQuery(\n       *   query,\n       *   id ? { variables: { id } } : skipToken\n       * );\n       * ```\n       */\n      skip?: boolean;\n    }\n  }\n\n  export type Options<\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useBackgroundQuery {\n      export interface Options<\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export interface Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n    subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n\n    /**\n     * {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)}\n     *\n     * @remarks\n     * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n     */\n    fetchMore: FetchMoreFunction<TData, TVariables>;\n\n    /**\n     * {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member}\n     *\n     * @remarks\n     * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n     */\n    refetch: RefetchFunction<TData, TVariables>;\n  }\n\n  namespace DocumentationTypes {\n    namespace useBackgroundQuery {\n      export interface Result<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends _self.Result<TData, TVariables> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\n    export function useBackgroundQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options: SkipToken | useBackgroundQuery.Options<TVariables>\n    ): [\n      QueryRef<TData, TVariables> | undefined,\n      useBackgroundQuery.Result<TData, TVariables>,\n    ];\n  }\n}\n\n/**\n * For a detailed explanation of useBackgroundQuery, see the [fetching with Suspense reference](https://www.apollographql.com/docs/react/data/suspense).\n *\n * @returns A tuple containing:\n *\n * 1.  A `QueryRef` that can be passed to `useReadQuery` to read the query result. The `queryRef` is `undefined` if the query is skipped.\n * 2.  An object containing helper functions for the query:\n *     - `refetch`: A function to re-execute the query\n *     - `fetchMore`: A function to fetch more results for pagination\n *     - `subscribeToMore`: A function to subscribe to updates\n *\n * @example\n *\n * ```jsx\n * import { Suspense } from \"react\";\n * import { ApolloClient, InMemoryCache, HttpLink } from \"@apollo/client\";\n * import { useBackgroundQuery, useReadQuery } from \"@apollo/client/react\";\n *\n * const query = gql`\n *   foo {\n *     bar\n *   }\n * `;\n *\n * const client = new ApolloClient({\n *   link: new HttpLink({ uri: \"http://localhost:4000/graphql\" }),\n *   cache: new InMemoryCache(),\n * });\n *\n * function SuspenseFallback() {\n *   return <div>Loading...</div>;\n * }\n *\n * function Child({ queryRef }) {\n *   const { data } = useReadQuery(queryRef);\n *\n *   return <div>{data.foo.bar}</div>;\n * }\n *\n * function Parent() {\n *   const [queryRef] = useBackgroundQuery(query);\n *\n *   return (\n *     <Suspense fallback={<SuspenseFallback />}>\n *       <Child queryRef={queryRef} />\n *     </Suspense>\n *   );\n * }\n *\n * function App() {\n *   return (\n *     <ApolloProvider client={client}>\n *       <Parent />\n *     </ApolloProvider>\n *   );\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - An optional object containing options for the query. Instead of passing a `useBackgroundQuery.Options` object into the hook, you can also pass a [`skipToken`](#skiptoken) to prevent the `useBackgroundQuery` hook from executing the query or suspending.\n */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    /** @deprecated `returnPartialData` has no effect on `no-cache` queries */\n    returnPartialData: boolean;\n    fetchPolicy: \"no-cache\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: false;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"empty\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\" | \"empty\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"empty\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: false;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: boolean;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: false;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken\n): [undefined, useBackgroundQuery.Result<TData, TVariables>];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (useBackgroundQuery.Options<NoInfer<TVariables>> & {\n        returnPartialData: false;\n      })\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (useBackgroundQuery.Options<NoInfer<TVariables>> & {\n        returnPartialData: boolean;\n      })\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useBackgroundQuery.Options<NoInfer<TVariables>>]\n  : [options: useBackgroundQuery.Options<NoInfer<TVariables>>]\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>]\n  : [options: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>]\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>\n): [\n  QueryRef<TData, TVariables, DataState<TData>[\"dataState\"]> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n] {\n  \"use no memo\";\n  return wrapHook(\n    \"useBackgroundQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useBackgroundQuery_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(query, options ?? ({} as any));\n}\n\nfunction useBackgroundQuery_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n  TStates extends DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken & Partial<useBackgroundQuery.Options<NoInfer<TVariables>>>)\n    | useBackgroundQuery.Options<NoInfer<TVariables>>\n): [\n  QueryRef<TData, TVariables, TStates> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n] {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions({ client, query, options });\n  const { fetchPolicy, variables } = watchQueryOptions;\n  const { queryKey = [] } = options;\n\n  // This ref tracks the first time query execution is enabled to determine\n  // whether to return a query ref or `undefined`. When initialized\n  // in a skipped state (either via `skip: true` or `skipToken`) we return\n  // `undefined` for the `queryRef` until the query has been enabled. Once\n  // enabled, a query ref is always returned regardless of whether the query is\n  // skipped again later.\n  const didFetchResult = React.useRef(fetchPolicy !== \"standby\");\n  didFetchResult.current ||= fetchPolicy !== \"standby\";\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n\n  const queryRef = suspenseCache.getQueryRef<TData, TStates>(cacheKey, () =>\n    client.watchQuery(\n      watchQueryOptions as ApolloClient.WatchQueryOptions<any, any>\n    )\n  );\n\n  const [wrappedQueryRef, setWrappedQueryRef] = React.useState(\n    wrapQueryRef(queryRef)\n  );\n  if (unwrapQueryRef(wrappedQueryRef) !== queryRef) {\n    setWrappedQueryRef(wrapQueryRef(queryRef));\n  }\n  if (queryRef.didChangeOptions(watchQueryOptions)) {\n    const promise = queryRef.applyOptions(watchQueryOptions);\n    updateWrappedQueryRef(wrappedQueryRef, promise);\n  }\n\n  // This prevents issues where rerendering useBackgroundQuery after the\n  // queryRef has been disposed would cause the hook to return a new queryRef\n  // instance since disposal also removes it from the suspense cache. We add\n  // the queryRef back in the suspense cache so that the next render will reuse\n  // this queryRef rather than initializing a new instance.\n  React.useEffect(() => {\n    // Since the queryRef is disposed async via `setTimeout`, we have to wait a\n    // tick before checking it and adding back to the suspense cache.\n    const id = setTimeout(() => {\n      if (queryRef.disposed) {\n        suspenseCache.add(cacheKey, queryRef);\n      }\n    });\n\n    return () => clearTimeout(id);\n    // Omitting the deps is intentional. This avoids stale closures and the\n    // conditional ensures we aren't running the logic on each render.\n  });\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      const promise = queryRef.fetchMore(options);\n\n      setWrappedQueryRef(wrapQueryRef(queryRef));\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = queryRef.refetch(variables);\n\n      setWrappedQueryRef(wrapQueryRef(queryRef));\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  React.useEffect(() => queryRef.softRetain(), [queryRef]);\n\n  return [\n    didFetchResult.current ? wrappedQueryRef : void 0,\n    {\n      fetchMore,\n      refetch,\n      // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n      subscribeToMore: queryRef.observable\n        .subscribeToMore as SubscribeToMoreFunction<TData, TVariables>,\n    },\n  ];\n}\n"]}