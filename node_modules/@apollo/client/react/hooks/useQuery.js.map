{"version": 3, "file": "useQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useQuery.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AACH,OAAO;AACP,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAmBhD,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAO/C,OAAO,EACL,eAAe,EACf,YAAY,GACb,MAAM,mCAAmC,CAAC;AAE3C,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AA6JjE,MAAM,gBAAgB,GAAG,MAAM,EAAE,CAAC;AAwGlC,MAAM,UAAU,QAAQ,CAItB,KAA0D,EAC1D,GAAG,CAAC,OAAO,CAEuD;IAElE,aAAa,CAAC;IACd,OAAO,QAAQ,CACb,UAAU;IACV,yDAAyD;IACzD,SAAS,EACT,eAAe,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAC3C,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,SAAS,CAChB,KAA0D,EAC1D,UAGI,EAAyC;IAE7C,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IAEvC,MAAM,iBAAiB,GACrB,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,UAAiB,EAAE,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IAE5E,IAAI,IAAI,EAAE,CAAC;QACT,mEAAmE;QACnE,uEAAuE;QACvE,yDAAyD;QACzD,iBAAiB,CAAC,kBAAkB;YAClC,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,WAAW,CAAC;QACpD,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;IAC5C,CAAC;IAED,SAAS,WAAW,CAClB,QAA2C;QAE3C,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAExD,OAAO;YACL,MAAM;YACN,KAAK;YACL,UAAU;YACV,UAAU,EAAE;gBACV,OAAO,EAAE,UAAU,CAAC,gBAAgB,EAAE;gBACtC,qEAAqE;gBACrE,uEAAuE;gBACvE,YAAY,EAAE,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,IAAa;gBACxD,SAAS,EAAE,UAAU,CAAC,SAAS;aAChC;SACF,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAEpD,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;QACrD,8EAA8E;QAC9E,8EAA8E;QAC9E,oDAAoD;QACpD,0EAA0E;QAC1E,2EAA2E;QAC3E,gCAAgC;QAChC,QAAQ,CAAC,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;IAEzC,gCAAgC,CAC9B,iBAAiB,EACjB,UAAU,CACX,CAAC;IAEF,yBAAyB,CACvB,UAAU,EAAE,kCAAkC;IAC9C,UAAU,EAAE,kCAAkC;IAC9C,iBAAiB,CAClB,CAAC;IAEF,MAAM,MAAM,GAAG,SAAS,CACtB,UAAU,EACV,UAAU,EACV,OAAO,CAAC,GAAG,CACZ,CAAC;IAEF,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAClC,GAAG,EAAE,CAAC,CAAC;QACL,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAC5C,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;QAChD,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,YAAY,EAAE,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;QACtD,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;KAC7D,CAAC,EACF,CAAC,UAAU,CAAC,CACb,CAAC;IAEF,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;IAC7C,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAEpC,OAAO;YACL,GAAG,IAAI;YACP,MAAM;YACN,UAAU;YACV,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,YAAY;YACZ,GAAG,cAAc;SAClB,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,gCAAgC,CAIvC,iBAAoE,EACpE,UAA+C;IAE/C,aAAa,CAAC;IACd,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;QACnC,iBAAiB,CAAC,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC;IACxE,CAAC;AACH,CAAC;AAED,SAAS,SAAS,CAChB,UAA+C,EAC/C,UAAiC,EACjC,GAAwB;IAExB,aAAa,CAAC;IACd,OAAO,oBAAoB,CACzB,KAAK,CAAC,WAAW,CACf,CAAC,iBAAiB,EAAE,EAAE;QACpB,MAAM,YAAY,GAAG,UAAU;YAC7B,iEAAiE;YACjE,qEAAqE;YACrE,kEAAkE;YAClE,sEAAsE;YACtE,qEAAqE;YACrE,aAAa;aACZ,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;aAC9B,SAAS,CAAC,CAAC,MAAM,EAAE,EAAE;YACpB,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;YAEpC;YACE,8CAA8C;YAC9C,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;gBACvB,4DAA4D;gBAC5D,iEAAiE;gBACjE,+DAA+D;gBAC/D,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,EACjD,CAAC;gBACD,OAAO;YACT,CAAC;YAED,yDAAyD;YACzD,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;YAE5C,IAAI,QAAQ,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxD,UAAU,CAAC,YAAY,GAAG,QAAQ,CAAC,IAAa,CAAC;YACnD,CAAC;YAED,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC;YAC5B,iBAAiB,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEL,2CAA2C;QAC3C,yEAAyE;QACzE,0EAA0E;QAC1E,kCAAkC;QAClC,OAAO,GAAG,EAAE;YACV,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;QAC/C,CAAC,CAAC;IACJ,CAAC,EAED,CAAC,UAAU,EAAE,UAAU,CAAC,CACzB,EACD,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,EACxB,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,8FAA8F;AAC9F,4EAA4E;AAC5E,SAAS,yBAAyB;AAIhC,uDAAuD;AACvD,UAAiC;AACjC,uDAAuD;AACvD,UAA+C,EAC/C,iBAA8E;IAE9E,aAAa,CAAC;IACd,IACE,UAAU,CAAC,gBAAgB,CAAC;QAC5B,CAAC,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,iBAAiB,CAAC,EACvD,CAAC;QACD,qEAAqE;QACrE,qEAAqE;QACrE,mEAAmE;QACnE,sEAAsE;QACtE,kEAAkE;QAClE,oEAAoE;QACpE,mEAAmE;QACnE,+DAA+D;QAC/D,IAAI,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,iBAAiB,CAAC,EAAE,CAAC;YACrE,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;QAED,uEAAuE;QACvE,sEAAsE;QACtE,gBAAgB;QAChB,MAAM,MAAM,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAE7C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACjD,UAAU,CAAC,YAAY,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;gBAC/C,UAAU,CAAC,YAAsB,CAAU,CAAC;QACjD,CAAC;QACD,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC;QAC5B,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;IAC9C,CAAC;IACD,UAAU,CAAC,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;AACnD,CAAC;AAED,SAAS,eAAe,CACtB,eAA4E,EAC5E,OAAoE;IAEpE,OAAO,CACL,eAAe,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK;QACvC,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;QACpD,CAAC,eAAe,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW;YAClD,CAAC,OAAO,CAAC,WAAW,KAAK,SAAS;gBAChC,eAAe,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAChD,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,iBAAiB,GAAG,eAAe,CAAC;IAC3C,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,KAAK,CAAQ;IACnB,SAAS,EAAE,OAAO;IAClB,KAAK,EAAE,KAAK,CAAC;IACb,aAAa,EAAE,aAAa,CAAC,OAAO;IACpC,OAAO,EAAE,IAAI;CACd,CAAsE,CAAC", "sourcesContent": ["/**\n * Function parameters in this file try to follow a common order for the sake of\n * readability and consistency. The order is as follows:\n *\n * resultData\n * observable\n * client\n * query\n * options\n * watchQueryOptions\n * makeWatchQueryOptions\n */\n/**  */\nimport { equal } from \"@wry/equality\";\nimport * as React from \"react\";\nimport { asapScheduler, observeOn } from \"rxjs\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorLike,\n  ErrorPolicy,\n  GetDataState,\n  InternalTypes,\n  ObservableQuery,\n  OperationVariables,\n  RefetchWritePolicy,\n  SubscribeToMoreFunction,\n  TypedDocumentNode,\n  UpdateQueryMapFn,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport { NetworkStatus } from \"@apollo/client\";\nimport type { MaybeMasked } from \"@apollo/client/masking\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\nimport {\n  maybeDeepFreeze,\n  mergeOptions,\n} from \"@apollo/client/utilities/internal\";\n\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useQuery {\n  import _self = useQuery;\n  export namespace Base {\n    export interface Options<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: WatchQueryFetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#nextFetchPolicy:member} */\n      nextFetchPolicy?:\n        | WatchQueryFetchPolicy\n        | ((\n            this: ApolloClient.WatchQueryOptions<TData, TVariables>,\n            currentFetchPolicy: WatchQueryFetchPolicy,\n            context: InternalTypes.NextFetchPolicyContext<TData, TVariables>\n          ) => WatchQueryFetchPolicy);\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#initialFetchPolicy:member} */\n\n      initialFetchPolicy?: WatchQueryFetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n      refetchWritePolicy?: RefetchWritePolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#pollInterval:member} */\n      pollInterval?: number;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n      notifyOnNetworkStatusChange?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n      returnPartialData?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skipPollAttempt:member} */\n      skipPollAttempt?: () => boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#ssr:member} */\n      ssr?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip:member} */\n      skip?: boolean;\n    }\n  }\n  export type Options<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options<TData, TVariables> & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useQuery {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TData, TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export namespace Base {\n    export interface Result<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#client:member} */\n      client: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#observable:member} */\n      observable: ObservableQuery<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#previousData:member} */\n      previousData?: MaybeMasked<TData>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n      error?: ErrorLike;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#loading:member} */\n      loading: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#startPolling:member} */\n      startPolling: (pollInterval: number) => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#stopPolling:member} */\n      stopPolling: () => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#subscribeToMore:member} */\n      subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#updateQuery:member} */\n      updateQuery: (mapFn: UpdateQueryMapFn<TData, TVariables>) => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member} */\n      refetch: (\n        variables?: Partial<TVariables>\n      ) => Promise<ApolloClient.QueryResult<MaybeMasked<TData>>>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#variables:member} */\n      variables: TVariables;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member} */\n      fetchMore: <\n        TFetchData = TData,\n        TFetchVars extends OperationVariables = TVariables,\n      >(\n        fetchMoreOptions: ObservableQuery.FetchMoreOptions<\n          TData,\n          TVariables,\n          TFetchData,\n          TFetchVars\n        >\n      ) => Promise<ApolloClient.QueryResult<MaybeMasked<TFetchData>>>;\n    }\n  }\n  export type Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData, TVariables> &\n    GetDataState<MaybeMasked<TData>, TStates>;\n\n  export namespace DocumentationTypes {\n    namespace useQuery {\n      export interface Result<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Result<TData, TVariables>,\n          UtilityDocumentationTypes.DataState<TData> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useQuery:function(1)} */\n    export function useQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options: useQuery.Options<TData, TVariables>\n    ): useQuery.Result<TData, TVariables>;\n  }\n}\n\nconst lastWatchOptions = Symbol();\n\ninterface ObsQueryWithMeta<TData, TVariables extends OperationVariables>\n  extends ObservableQuery<TData, TVariables> {\n  [lastWatchOptions]?: Readonly<\n    ApolloClient.WatchQueryOptions<TData, TVariables>\n  >;\n}\n\ninterface InternalResult<TData> {\n  // These members are populated by getCurrentResult and setResult, and it's\n  // okay/normal for them to be initially undefined.\n  current: ObservableQuery.Result<TData>;\n  previousData?: undefined | MaybeMasked<TData>;\n\n  // Track current variables separately in case a call to e.g. `refetch(newVars)`\n  // causes an emit that is deeply equal to the current result. This lets us\n  // compare if we should force rerender due to changed variables\n  variables: OperationVariables;\n}\n\ninterface InternalState<TData, TVariables extends OperationVariables> {\n  client: ReturnType<typeof useApolloClient>;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  observable: ObsQueryWithMeta<TData, TVariables>;\n  resultData: InternalResult<TData>;\n}\n\n/**\n * A hook for executing queries in an Apollo application.\n *\n * To run a query within a React component, call `useQuery` and pass it a GraphQL query document.\n *\n * When your component renders, `useQuery` returns an object from Apollo Client that contains `loading`, `error`, `dataState`, and `data` properties you can use to render your UI.\n *\n * > Refer to the [Queries](https://www.apollographql.com/docs/react/data/queries) section for a more in-depth overview of `useQuery`.\n *\n * @example\n *\n * ```jsx\n * import { gql } from \"@apollo/client\";\n * import { useQuery } from \"@apollo/client/react\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const { loading, error, data } = useQuery(GET_GREETING, {\n *     variables: { language: \"english\" },\n *   });\n *   if (loading) return <p>Loading ...</p>;\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns Query result object\n */\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: true;\n  }\n): useQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useQuery:function(1)} */\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n  }\n): useQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useQuery:function(1)} */\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n  : [options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n): useQuery.Result<TData, TVariables, \"empty\" | \"complete\" | \"streaming\">;\n\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n  : [options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n): useQuery.Result<TData, TVariables> {\n  \"use no memo\";\n  return wrapHook(\n    \"useQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useQuery_,\n    useApolloClient(options && options.client)\n  )(query, options);\n}\n\nfunction useQuery_<TData, TVariables extends OperationVariables>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useQuery.Options<\n    NoInfer<TData>,\n    NoInfer<TVariables>\n  > = {} as useQuery.Options<TData, TVariables>\n): useQuery.Result<TData, TVariables> {\n  const client = useApolloClient(options.client);\n  const { skip, ssr, ...opts } = options;\n\n  const watchQueryOptions: ApolloClient.WatchQueryOptions<TData, TVariables> =\n    mergeOptions(client.defaultOptions.watchQuery as any, { ...opts, query });\n\n  if (skip) {\n    // When skipping, we set watchQueryOptions.fetchPolicy initially to\n    // \"standby\", but we also need/want to preserve the initial non-standby\n    // fetchPolicy that would have been used if not skipping.\n    watchQueryOptions.initialFetchPolicy =\n      options.initialFetchPolicy || options.fetchPolicy;\n    watchQueryOptions.fetchPolicy = \"standby\";\n  }\n\n  function createState(\n    previous?: InternalState<TData, TVariables>\n  ): InternalState<TData, TVariables> {\n    const observable = client.watchQuery(watchQueryOptions);\n\n    return {\n      client,\n      query,\n      observable,\n      resultData: {\n        current: observable.getCurrentResult(),\n        // Reuse previousData from previous InternalState (if any) to provide\n        // continuity of previousData even if/when the query or client changes.\n        previousData: previous?.resultData.current.data as TData,\n        variables: observable.variables,\n      },\n    };\n  }\n\n  let [state, setState] = React.useState(createState);\n\n  if (client !== state.client || query !== state.query) {\n    // If the client or query have changed, we need to create a new InternalState.\n    // This will trigger a re-render with the new state, but it will also continue\n    // to run the current render function to completion.\n    // Since we sometimes trigger some side-effects in the render function, we\n    // re-assign `state` to the new state to ensure that those side-effects are\n    // triggered with the new state.\n    setState((state = createState(state)));\n  }\n\n  const { observable, resultData } = state;\n\n  useInitialFetchPolicyIfNecessary<TData, TVariables>(\n    watchQueryOptions,\n    observable\n  );\n\n  useResubscribeIfNecessary<TData, TVariables>(\n    resultData, // might get mutated during render\n    observable, // might get mutated during render\n    watchQueryOptions\n  );\n\n  const result = useResult<TData, TVariables>(\n    observable,\n    resultData,\n    options.ssr\n  );\n\n  const obsQueryFields = React.useMemo(\n    () => ({\n      refetch: observable.refetch.bind(observable),\n      fetchMore: observable.fetchMore.bind(observable),\n      updateQuery: observable.updateQuery.bind(observable),\n      startPolling: observable.startPolling.bind(observable),\n      stopPolling: observable.stopPolling.bind(observable),\n      subscribeToMore: observable.subscribeToMore.bind(observable),\n    }),\n    [observable]\n  );\n\n  const previousData = resultData.previousData;\n  return React.useMemo(() => {\n    const { partial, ...rest } = result;\n\n    return {\n      ...rest,\n      client,\n      observable,\n      variables: observable.variables,\n      previousData,\n      ...obsQueryFields,\n    };\n  }, [result, client, observable, previousData, obsQueryFields]);\n}\n\nfunction useInitialFetchPolicyIfNecessary<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  watchQueryOptions: ApolloClient.WatchQueryOptions<TData, TVariables>,\n  observable: ObsQueryWithMeta<TData, TVariables>\n) {\n  \"use no memo\";\n  if (!watchQueryOptions.fetchPolicy) {\n    watchQueryOptions.fetchPolicy = observable.options.initialFetchPolicy;\n  }\n}\n\nfunction useResult<TData, TVariables extends OperationVariables>(\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  resultData: InternalResult<TData>,\n  ssr: boolean | undefined\n) {\n  \"use no memo\";\n  return useSyncExternalStore(\n    React.useCallback(\n      (handleStoreChange) => {\n        const subscription = observable\n          // We use the asapScheduler here to prevent issues with trying to\n          // update in the middle of a render. `reobserve` is kicked off in the\n          // middle of a render and because RxJS emits values synchronously,\n          // its possible for this `handleStoreChange` to be called in that same\n          // render. This allows the render to complete before trying to emit a\n          // new value.\n          .pipe(observeOn(asapScheduler))\n          .subscribe((result) => {\n            const previous = resultData.current;\n\n            if (\n              // Avoid rerendering if the result is the same\n              equal(previous, result) &&\n              // Force rerender if the value was emitted because variables\n              // changed, such as when calling `refetch(newVars)` which returns\n              // the same data when `notifyOnNetworkStatusChange` is `false`.\n              equal(resultData.variables, observable.variables)\n            ) {\n              return;\n            }\n\n            // eslint-disable-next-line react-compiler/react-compiler\n            resultData.variables = observable.variables;\n\n            if (previous.data && !equal(previous.data, result.data)) {\n              resultData.previousData = previous.data as TData;\n            }\n\n            resultData.current = result;\n            handleStoreChange();\n          });\n\n        // Do the \"unsubscribe\" with a short delay.\n        // This way, an existing subscription can be reused without an additional\n        // request if \"unsubscribe\"  and \"resubscribe\" to the same ObservableQuery\n        // happen in very fast succession.\n        return () => {\n          setTimeout(() => subscription.unsubscribe());\n        };\n      },\n\n      [observable, resultData]\n    ),\n    () => resultData.current,\n    () => (ssr === false ? useQuery.ssrDisabledResult : resultData.current)\n  );\n}\n\n// this hook is not compatible with any rules of React, and there's no good way to rewrite it.\n// it should stay a separate hook that will not be optimized by the compiler\nfunction useResubscribeIfNecessary<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  /** this hook will mutate properties on `resultData` */\n  resultData: InternalResult<TData>,\n  /** this hook will mutate properties on `observable` */\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  watchQueryOptions: Readonly<ApolloClient.WatchQueryOptions<TData, TVariables>>\n) {\n  \"use no memo\";\n  if (\n    observable[lastWatchOptions] &&\n    !equal(observable[lastWatchOptions], watchQueryOptions)\n  ) {\n    // Though it might be tempting to postpone this reobserve call to the\n    // useEffect block, we need getCurrentResult to return an appropriate\n    // loading:true result synchronously (later within the same call to\n    // useQuery). Since we already have this.observable here (not true for\n    // the very first call to useQuery), we are not initiating any new\n    // subscriptions, though it does feel less than ideal that reobserve\n    // (potentially) kicks off a network request (for example, when the\n    // variables have changed), which is technically a side-effect.\n    if (shouldReobserve(observable[lastWatchOptions], watchQueryOptions)) {\n      observable.reobserve(watchQueryOptions);\n    } else {\n      observable.applyOptions(watchQueryOptions);\n    }\n\n    // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n    // but save the current data as this.previousData, just like setResult\n    // usually does.\n    const result = observable.getCurrentResult();\n\n    if (!equal(result.data, resultData.current.data)) {\n      resultData.previousData = (resultData.current.data ||\n        (resultData.previousData as TData)) as TData;\n    }\n    resultData.current = result;\n    resultData.variables = observable.variables;\n  }\n  observable[lastWatchOptions] = watchQueryOptions;\n}\n\nfunction shouldReobserve<TData, TVariables extends OperationVariables>(\n  previousOptions: Readonly<ApolloClient.WatchQueryOptions<TData, TVariables>>,\n  options: Readonly<ApolloClient.WatchQueryOptions<TData, TVariables>>\n) {\n  return (\n    previousOptions.query !== options.query ||\n    !equal(previousOptions.variables, options.variables) ||\n    (previousOptions.fetchPolicy !== options.fetchPolicy &&\n      (options.fetchPolicy === \"standby\" ||\n        previousOptions.fetchPolicy === \"standby\"))\n  );\n}\n\nuseQuery.ssrDisabledResult = maybeDeepFreeze({\n  loading: true,\n  data: void 0 as any,\n  dataState: \"empty\",\n  error: void 0,\n  networkStatus: NetworkStatus.loading,\n  partial: true,\n}) satisfies ObservableQuery.Result<any> as ObservableQuery.Result<any>;\n"]}