{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/index.ts"], "names": [], "mappings": "AAAA,+EAA+E;AAC/E,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC", "sourcesContent": ["// These hooks are used internally and are not exported publicly by the library\nexport { useDeepMemo } from \"./useDeepMemo.js\";\nexport { useRenderGuard } from \"./useRenderGuard.js\";\nexport { __use } from \"./__use.js\";\nexport { wrapHook } from \"./wrapHook.js\";\n"]}