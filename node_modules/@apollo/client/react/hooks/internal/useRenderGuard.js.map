{"version": 3, "file": "useRenderGuard.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/useRenderGuard.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,IAAI,GAAwB,CAAC;AAE7B,SAAS,IAAI,KAAI,CAAC;AAClB,MAAM,UAAU,cAAc;IAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,8FAA8F;QAC9F,GAAG,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,OAAO,KAAK,CAAC,WAAW;IACtB;;OAEG,CAAC,GAAG,EAAE;QACP,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YAErB;;;;;;;;;;;;;;eAcG;YACH,KAAK,CAAC,YAAY,CAAC,+BAA+B,CAAC,CAAC,GAAG,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;QACvB,CAAC;IACH,CAAC,EACD,EAAE,CACH,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nlet Ctx: React.Context<null>;\n\nfunction noop() {}\nexport function useRenderGuard() {\n  if (!Ctx) {\n    // we want the intialization to be lazy because `createContext` would error on import in a RSC\n    Ctx = React.createContext(null);\n  }\n\n  return React.useCallback(\n    /**\n     * @returns true if the hook was called during render\n     */ () => {\n      const orig = console.error;\n      try {\n        console.error = noop;\n\n        /**\n         * `useContext` can be called conditionally during render, so this is safe.\n         * (Also, during render we would want to throw as a reaction to this anyways, so it\n         * wouldn't even matter if we got the order of hooks mixed up...)\n         *\n         * They cannot however be called outside of Render, and that's what we're testing here.\n         *\n         * Different versions of React have different behaviour on an invalid hook call:\n         *\n         * React 16.8 - 17: throws an error\n         * https://github.com/facebook/react/blob/2b93d686e359c7afa299e2ec5cf63160a32a1155/packages/react/src/ReactHooks.js#L18-L26\n         *\n         * React 18 & 19: `console.error` in development, then `resolveDispatcher` returns `null` and a member access on `null` throws.\n         * https://github.com/facebook/react/blob/58e8304483ebfadd02a295339b5e9a989ac98c6e/packages/react/src/ReactHooks.js#L28-L35\n         */\n        React[\"useContext\" /* hide this from the linter */](Ctx);\n        return true;\n      } catch (e) {\n        return false;\n      } finally {\n        console.error = orig;\n      }\n    },\n    []\n  );\n}\n"]}