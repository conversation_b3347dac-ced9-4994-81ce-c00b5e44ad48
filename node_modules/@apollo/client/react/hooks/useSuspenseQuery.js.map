{"version": 3, "file": "useSuspenseQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useSuspenseQuery.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAkB/B,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAO1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,OAAO,EAAE,MAAM,sCAAsC,CAAC;AAQ/D,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AACnE,OAAO,EAAE,2BAA2B,EAAE,MAAM,2CAA2C,CAAC;AACxF,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AA2SvD,MAAM,UAAU,gBAAgB,CAI9B,KAA0D,EAC1D,OAAmE;IAMnE,aAAa,CAAC;IACd,OAAO,QAAQ,CACb,kBAAkB;IAClB,yDAAyD;IACzD,iBAAiB,EACjB,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,KAAK,EAAE,OAAO,IAAK,EAAU,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,iBAAiB,CAIxB,KAA0D,EAC1D,OAEwC;IAMxC,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,iBAAiB,GAAG,oBAAoB,CAAW;QACvD,MAAM;QACN,KAAK;QACL,OAAO;KACR,CAAC,CAAC;IACH,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,iBAAiB,CAAC;IACrD,MAAM,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IAElC,MAAM,QAAQ,GAAa;QACzB,KAAK;QACL,kBAAkB,CAAC,SAAS,CAAC;QAC7B,GAAI,EAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;KAClC,CAAC;IAEF,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,EAAE,CACxD,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CACrC,CAAC;IAEF,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC,QAAQ,CAExC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAEpC,+EAA+E;IAC/E,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE,CAAC;QAChC,yDAAyD;QACzD,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;QAC1B,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;IAChC,CAAC;IACD,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACjD,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAElC,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YACjD,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE;YACV,cAAc,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAgC,GAAG,EAAE;QACnE,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;QACpC,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;QAExC,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC1B,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,SAAS;YACpC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK;YAChE,KAAK;YACL,QAAQ;YACR,OAAO,EAAE,CAAC,QAAQ;SACnB,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEtB,MAAM,MAAM,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAEvE,MAAM,SAAS,GAAG,KAAK,CAAC,WAAW,CAGjC,CAAC,OAAO,EAAE,EAAE;QACV,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5C,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAE7C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACyC,CAAC;IAEtD,MAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,CAAC,SAAS,EAAE,EAAE;QACZ,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5C,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAE7C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,kGAAkG;IAClG,MAAM,eAAe,GAAG,QAAQ,CAAC,UAAU;SACxC,eAAyE,CAAC;IAE7E,OAAO,KAAK,CAAC,OAAO,CAElB,GAAG,EAAE;QACL,OAAO;YACL,MAAM;YACN,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,SAAS;YACT,OAAO;YACP,eAAe;SAKhB,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;AAC5D,CAAC;AAWD,MAAM,UAAU,oBAAoB,CAGlC,EACA,MAAM,EACN,KAAK,EACL,OAAO,GAIR;IACC,OAAO,WAAW,CAAoD,GAAG,EAAE;QACzE,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO;gBACL,KAAK;gBACL,WAAW,EAAE,SAAS;aAC8B,CAAC;QACzD,CAAC;QAED,MAAM,WAAW,GACf,OAAO,CAAC,WAAW;YACnB,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,WAAW;YAC7C,aAAa,CAAC;QAEhB,MAAM,iBAAiB,GACrB;YACE,GAAG,OAAO;YACV,WAAW;YACX,KAAK;YACL,2BAA2B,EAAE,KAAK;YAClC,eAAe,EAAE,KAAK,CAAC;SACxB,CAAC;QAEJ,IAAI,OAAO,EAAE,CAAC;YACZ,2BAA2B,CAAC,iBAAiB,CAAC,CAAC;QACjD,CAAC;QAED,0EAA0E;QAC1E,qEAAqE;QACrE,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;QAC5C,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorLike,\n  ErrorPolicy,\n  GetDataState,\n  MaybeMasked,\n  ObservableQuery,\n  OperationVariables,\n  RefetchWritePolicy,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport { NetworkStatus } from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON>,\n  FetchMoreFunction,\n  QueryKey,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport { getSuspenseCache } from \"@apollo/client/react/internal\";\nimport { __DEV__ } from \"@apollo/client/utilities/environment\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\n\nimport type { SkipToken } from \"./constants.js\";\nimport { skipToken } from \"./constants.js\";\nimport { __use, useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport { validateSuspenseHookOptions } from \"./internal/validateSuspenseHookOptions.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\nexport declare namespace useSuspenseQuery {\n  export type FetchPolicy = Extract<\n    WatchQueryFetchPolicy,\n    \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n  >;\n\n  export namespace Base {\n    export interface Options<\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n      returnPartialData?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy_suspense:member} */\n      refetchWritePolicy?: RefetchWritePolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: FetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n      queryKey?: string | number | any[];\n\n      /**\n       * {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip_deprecated:member}\n       *\n       * @example Recommended usage of `skipToken`:\n       *\n       * ```ts\n       * import { skipToken, useSuspenseQuery } from \"@apollo/client\";\n       *\n       * const { data } = useSuspenseQuery(\n       *   query,\n       *   id ? { variables: { id } } : skipToken\n       * );\n       * ```\n       */\n      skip?: boolean;\n    }\n  }\n  export type Options<\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options<TVariables> & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useSuspenseQuery {\n      export interface Options<\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export namespace Base {\n    export interface Result<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n      error: ErrorLike | undefined;\n\n      /**\n       * {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member}\n       *\n       * @remarks\n       * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n       */\n      fetchMore: FetchMoreFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n\n      /**\n       * {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member}\n       *\n       * @remarks\n       * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n       */\n      refetch: RefetchFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#subscribeToMore:member} */\n      subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n    }\n  }\n  export type Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData, TVariables> &\n    GetDataState<MaybeMasked<TData>, TStates>;\n\n  export namespace DocumentationTypes {\n    namespace useSuspenseQuery {\n      export interface Result<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Result<TData, TVariables>,\n          UtilityDocumentationTypes.DataState<TData> {}\n    }\n  }\n  export namespace DocumentationTypes {\n    /** Test {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\n    export function useSuspenseQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options?: useSuspenseQuery.Options<TVariables>\n    ): useSuspenseQuery.Result<TData, TVariables>;\n  }\n}\n\n/**\n * For a detailed explanation of `useSuspenseQuery`, see the [fetching with Suspense reference](https://www.apollographql.com/docs/react/data/suspense).\n *\n * @example\n *\n * ```jsx\n * import { Suspense } from \"react\";\n * import { useSuspenseQuery } from \"@apollo/client\";\n *\n * const listQuery = gql`\n *   query {\n *     list {\n *       id\n *     }\n *   }\n * `;\n *\n * function App() {\n *   return (\n *     <Suspense fallback={<Spinner />}>\n *       <List />\n *     </Suspense>\n *   );\n * }\n *\n * function List() {\n *   const { data } = useSuspenseQuery(listQuery);\n *\n *   return (\n *     <ol>\n *       {data.list.map((item) => (\n *         <Item key={item.id} id={item.id} />\n *       ))}\n *     </ol>\n *   );\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - An optional object containing options for the query. Instead of passing a `useSuspenseQuery.Options` object into the hook, you can also pass a [`skipToken`](#skiptoken) to prevent the `useSuspenseQuery` hook from executing the query or suspending.\n */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: true;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"partial\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: true;\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"empty\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: true;\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"partial\" | \"streaming\" | \"complete\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (useSuspenseQuery.Options<NoInfer<TVariables>> & {\n        returnPartialData: true;\n      })\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"streaming\" | \"complete\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useSuspenseQuery.Options<NoInfer<TVariables>>]\n  : [options: useSuspenseQuery.Options<NoInfer<TVariables>>]\n): useSuspenseQuery.Result<TData, TVariables, \"complete\" | \"streaming\">;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>]\n  : [options: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>]\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"streaming\" | \"complete\" | \"partial\"\n> {\n  \"use no memo\";\n  return wrapHook(\n    \"useSuspenseQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseQuery_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(query, options ?? ({} as any));\n}\n\nfunction useSuspenseQuery_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken & Partial<useSuspenseQuery.Options<TVariables>>)\n    | useSuspenseQuery.Options<TVariables>\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"partial\" | \"complete\" | \"streaming\" | \"empty\"\n> {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions<any, any>({\n    client,\n    query,\n    options,\n  });\n  const { fetchPolicy, variables } = watchQueryOptions;\n  const { queryKey = [] } = options;\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n\n  const queryRef = suspenseCache.getQueryRef(cacheKey, () =>\n    client.watchQuery(watchQueryOptions)\n  );\n\n  let [current, setPromise] = React.useState<\n    [QueryKey, Promise<ObservableQuery.Result<any>>]\n  >([queryRef.key, queryRef.promise]);\n\n  // This saves us a re-execution of the render function when a variable changed.\n  if (current[0] !== queryRef.key) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    current[0] = queryRef.key;\n    current[1] = queryRef.promise;\n  }\n  let promise = current[1];\n\n  if (queryRef.didChangeOptions(watchQueryOptions)) {\n    current[1] = promise = queryRef.applyOptions(watchQueryOptions);\n  }\n\n  React.useEffect(() => {\n    const dispose = queryRef.retain();\n\n    const removeListener = queryRef.listen((promise) => {\n      setPromise([queryRef.key, promise]);\n    });\n\n    return () => {\n      removeListener();\n      dispose();\n    };\n  }, [queryRef]);\n\n  const skipResult = React.useMemo<ObservableQuery.Result<TData>>(() => {\n    const error = queryRef.result.error;\n    const complete = !!queryRef.result.data;\n\n    return {\n      loading: false,\n      data: queryRef.result.data,\n      dataState: queryRef.result.dataState,\n      networkStatus: error ? NetworkStatus.error : NetworkStatus.ready,\n      error,\n      complete,\n      partial: !complete,\n    };\n  }, [queryRef.result]);\n\n  const result = fetchPolicy === \"standby\" ? skipResult : __use(promise);\n\n  const fetchMore = React.useCallback<\n    FetchMoreFunction<unknown, OperationVariables>\n  >(\n    (options) => {\n      const promise = queryRef.fetchMore(options);\n      setPromise([queryRef.key, queryRef.promise]);\n\n      return promise;\n    },\n    [queryRef]\n  ) as FetchMoreFunction<TData | undefined, TVariables>;\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = queryRef.refetch(variables);\n      setPromise([queryRef.key, queryRef.promise]);\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n  const subscribeToMore = queryRef.observable\n    .subscribeToMore as SubscribeToMoreFunction<TData | undefined, TVariables>;\n\n  return React.useMemo<\n    useSuspenseQuery.Result<TData, TVariables, DataState<TData>[\"dataState\"]>\n  >(() => {\n    return {\n      client,\n      data: result.data,\n      dataState: result.dataState,\n      error: result.error,\n      networkStatus: result.networkStatus,\n      fetchMore,\n      refetch,\n      subscribeToMore,\n    } as useSuspenseQuery.Result<\n      TData,\n      TVariables,\n      DataState<TData>[\"dataState\"]\n    >;\n  }, [client, fetchMore, refetch, result, subscribeToMore]);\n}\n\ninterface UseWatchQueryOptionsHookOptions<\n  TData,\n  TVariables extends OperationVariables,\n> {\n  client: ApolloClient;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  options: SkipToken | useSuspenseQuery.Options<TVariables>;\n}\n\nexport function useWatchQueryOptions<\n  TData,\n  TVariables extends OperationVariables,\n>({\n  client,\n  query,\n  options,\n}: UseWatchQueryOptionsHookOptions<\n  TData,\n  TVariables\n>): ApolloClient.WatchQueryOptions<TData, TVariables> {\n  return useDeepMemo<ApolloClient.WatchQueryOptions<TData, TVariables>>(() => {\n    if (options === skipToken) {\n      return {\n        query,\n        fetchPolicy: \"standby\",\n      } as ApolloClient.WatchQueryOptions<TData, TVariables>;\n    }\n\n    const fetchPolicy =\n      options.fetchPolicy ||\n      client.defaultOptions.watchQuery?.fetchPolicy ||\n      \"cache-first\";\n\n    const watchQueryOptions: ApolloClient.WatchQueryOptions<TData, TVariables> =\n      {\n        ...options,\n        fetchPolicy,\n        query,\n        notifyOnNetworkStatusChange: false,\n        nextFetchPolicy: void 0,\n      };\n\n    if (__DEV__) {\n      validateSuspenseHookOptions(watchQueryOptions);\n    }\n\n    // Assign the updated fetch policy after our validation since `standby` is\n    // not a supported fetch policy on its own without the use of `skip`.\n    if (options.skip) {\n      watchQueryOptions.fetchPolicy = \"standby\";\n    }\n\n    return watchQueryOptions;\n  }, [client, options, query]);\n}\n"]}