{"version": 3, "file": "useQueryRefHandlers.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useQueryRefHandlers.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAW/B,OAAO,EACL,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,EACrB,YAAY,GACb,MAAM,+BAA+B,CAAC;AAEvC,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AA0BvD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,mBAAmB,CAIjC,QAAoE;IAEpE,aAAa,CAAC;IACd,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,gBAAgB,GAAG,eAAe,CACtC,SAAS,CAAC,CAAC;QACT,0EAA0E;QAC1E,iFAAiF;QAChF,SAAS,CAAC,YAAY,CAAS;QAClC,CAAC,CAAC,SAAS,CAC6B,CAAC;IAE3C,OAAO,QAAQ,CACb,qBAAqB;IACrB,yDAAyD;IACzD,oBAAoB,EACpB,gBAAgB,CACjB,CAAC,QAAQ,CAAC,CAAC;AACd,CAAC;AAED,SAAS,oBAAoB,CAI3B,QAAoE;IAEpE,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAChC,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzE,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACvE,MAAM,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAElD,4EAA4E;IAC5E,2EAA2E;IAC3E,gEAAgE;IAChE,IAAI,gBAAgB,KAAK,QAAQ,EAAE,CAAC;QAClC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC9B,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,qBAAqB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,CAAC,SAAS,EAAE,EAAE;QACZ,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEpD,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAEnD,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF,MAAM,SAAS,GAAyC,KAAK,CAAC,WAAW,CACvE,CAAC,OAAO,EAAE,EAAE;QACV,MAAM,OAAO,GAAG,gBAAgB,CAAC,SAAS,CACxC,OAAqD,CACtD,CAAC;QAEF,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAEnD,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF,OAAO;QACL,OAAO;QACP,SAAS;QACT,kGAAkG;QAClG,eAAe,EAAE,gBAAgB,CAAC,UAAU;aACzC,eAA6D;KACjE,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport type { DataState, OperationVariables } from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport type { ApolloClient } from \"@apollo/client\";\nimport type { ObservableQuery } from \"@apollo/client\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport type {\n  FetchMoreFunction,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport {\n  assertWrappedQueryRef,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"@apollo/client/react/internal\";\n\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\nexport declare namespace useQueryRefHandlers {\n  export interface Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    /** {@inheritDoc @apollo/client!ObservableQuery#refetch:member(1)} */\n    refetch: RefetchFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)} */\n    fetchMore: FetchMoreFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n    subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useQueryRefHandlers:function(1)} */\n    export function useQueryRefHandlers<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      queryRef: QueryRef<TData, TVariables>\n    ): useQueryRefHandlers.Result<TData, TVariables>;\n  }\n}\n\n/**\n * A React hook that returns a `refetch` and `fetchMore` function for a given\n * `queryRef`.\n *\n * This is useful to get access to handlers for a `queryRef` that was created by\n * `createQueryPreloader` or when the handlers for a `queryRef` produced in\n * a different component are inaccessible.\n *\n * @example\n *\n * ```tsx\n * const MyComponent({ queryRef }) {\n *   const { refetch, fetchMore } = useQueryRefHandlers(queryRef);\n *\n *   // ...\n * }\n * ```\n *\n * @param queryRef - A `QueryRef` returned from `useBackgroundQuery`, `useLoadableQuery`, or `createQueryPreloader`.\n */\nexport function useQueryRefHandlers<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  queryRef: QueryRef<TData, TVariables, DataState<TData>[\"dataState\"]>\n): useQueryRefHandlers.Result<TData, TVariables> {\n  \"use no memo\";\n  const unwrapped = unwrapQueryRef(queryRef);\n  const clientOrObsQuery = useApolloClient(\n    unwrapped ?\n      // passing an `ObservableQuery` is not supported by the types, but it will\n      // return any truthy value that is passed in as an override so we cast the result\n      (unwrapped[\"observable\"] as any)\n    : undefined\n  ) as ApolloClient | ObservableQuery<TData>;\n\n  return wrapHook(\n    \"useQueryRefHandlers\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useQueryRefHandlers_,\n    clientOrObsQuery\n  )(queryRef);\n}\n\nfunction useQueryRefHandlers_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  queryRef: QueryRef<TData, TVariables, DataState<TData>[\"dataState\"]>\n): useQueryRefHandlers.Result<TData, TVariables> {\n  assertWrappedQueryRef(queryRef);\n  const [previousQueryRef, setPreviousQueryRef] = React.useState(queryRef);\n  const [wrappedQueryRef, setWrappedQueryRef] = React.useState(queryRef);\n  const internalQueryRef = unwrapQueryRef(queryRef);\n\n  // To ensure we can support React transitions, this hook needs to manage the\n  // queryRef state and apply React's state value immediately to the existing\n  // queryRef since this hook doesn't return the queryRef directly\n  if (previousQueryRef !== queryRef) {\n    setPreviousQueryRef(queryRef);\n    setWrappedQueryRef(queryRef);\n  } else {\n    updateWrappedQueryRef(queryRef, getWrappedPromise(wrappedQueryRef));\n  }\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = internalQueryRef.refetch(variables);\n\n      setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      const promise = internalQueryRef.fetchMore(\n        options as ObservableQuery.FetchMoreOptions<any, any>\n      );\n\n      setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  return {\n    refetch,\n    fetchMore,\n    // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n    subscribeToMore: internalQueryRef.observable\n      .subscribeToMore as SubscribeToMoreFunction<TData, TVariables>,\n  };\n}\n"]}