{"version": 3, "file": "useFragment.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useFragment.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,eAAe,CAAC;AAClC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAmB/B,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AA4GjE;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CAGzB,OAA+C;IAC/C,aAAa,CAAC;IACd,OAAO,QAAQ,CACb,aAAa;IACb,yDAAyD;IACzD,YAAY,EACZ,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAChC,CAAC,OAAO,CAAC,CAAC;AACb,CAAC;AAED,SAAS,YAAY,CACnB,OAA+C;IAE/C,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;IACzB,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IAElC,6EAA6E;IAC7E,kEAAkE;IAClE,0EAA0E;IAC1E,8EAA8E;IAC9E,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CACtB,GAAG,EAAE,CACH,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI;QAC/B,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI;YACtB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EACxB,CAAC,KAAK,EAAE,IAAI,CAAC,CACd,CAAC;IAEF,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,EAAG,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IAE9E,qDAAqD;IACrD,gEAAgE;IAChE,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,aAAa,CAAC;QAE1E,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO;gBACL,MAAM,EAAE,YAAY,CAAC;oBACnB,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,KAAK;iBACW,CAAC;aAC9B,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACzB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAoB;YACzC,GAAG,aAAa;YAChB,iBAAiB,EAAE,IAAI;YACvB,EAAE,EAAE,IAAI;YACR,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAC5B,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,EAC7B,YAAY,CACb;YACD,UAAU;SACX,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,YAAY,CAAQ;gBAC1B,GAAG,IAAI;gBACP,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC;oBAC1C,QAAQ;oBACR,YAAY;oBACZ,sEAAsE;oBACtE,qBAAqB;oBACrB,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;iBAC9C,CAAQ;aACV,CAAC;SACH,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;IAE5B,kDAAkD;IAClD,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEjE,OAAO,oBAAoB,CACzB,KAAK,CAAC,WAAW,CACf,CAAC,WAAW,EAAE,EAAE;QACd,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,MAAM,YAAY,GAChB,aAAa,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YAC3B,IAAI;YACN,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC;gBAC5C,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE;oBACf,mEAAmE;oBACnE,sDAAsD;oBACtD,iBAAiB;oBACjB,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC;wBAAE,OAAO;oBACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;oBACrB,iEAAiE;oBACjE,kEAAkE;oBAClE,6DAA6D;oBAC7D,qEAAqE;oBACrE,YAAY,CAAC,WAAW,CAAC,CAAC;oBAC1B,WAAW,GAAG,UAAU,CAAC,WAAW,CAAQ,CAAC;gBAC/C,CAAC;aACF,CAAC,CAAC;QACP,OAAO,GAAG,EAAE;YACV,YAAY,EAAE,WAAW,EAAE,CAAC;YAC5B,YAAY,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EACD,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,CAC9B,EACD,WAAW,EACX,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CACnB,IAA6B;IAE7B,MAAM,MAAM,GAAG;QACb,IAAI,EAAE,IAAI,CAAC,MAAM;QACjB,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;QACzB,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;KACrB,CAAC,CAAC,uDAAuD;IAEvF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IACxC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import equal from \"@wry/equality\";\nimport * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataValue,\n  DocumentNode,\n  GetDataState,\n  OperationVariables,\n  TypedDocumentNode,\n} from \"@apollo/client\";\nimport type {\n  Cache,\n  MissingTree,\n  Reference,\n  StoreObject,\n} from \"@apollo/client/cache\";\nimport type { FragmentType, MaybeMasked } from \"@apollo/client/masking\";\nimport type { NoInfer } from \"@apollo/client/utilities/internal\";\n\nimport { useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useFragment {\n  import _self = useFragment;\n  export interface Options<TData, TVariables extends OperationVariables> {\n    /**\n     * A GraphQL document created using the `gql` template string tag from\n     * `graphql-tag` with one or more fragments which will be used to determine\n     * the shape of data to read. If you provide more than one fragment in this\n     * document then you must also specify `fragmentName` to select a single.\n     */\n    fragment: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n    /**\n     * The name of the fragment in your GraphQL document to be used. If you do\n     * not provide a `fragmentName` and there is only one fragment in your\n     * `fragment` document then that fragment will be used.\n     */\n    fragmentName?: string;\n\n    /**\n     * Any variables that the GraphQL query may depend on.\n     */\n    variables?: NoInfer<TVariables>;\n\n    /**\n     * An object containing a `__typename` and primary key fields (such as `id`) identifying the entity object from which the fragment will be retrieved, or a `{ __ref: \"...\" }` reference, or a `string` ID (uncommon).\n     */\n    from:\n      | StoreObject\n      | Reference\n      | FragmentType<NoInfer<TData>>\n      | string\n      | null;\n\n    /**\n     * Whether to read from optimistic or non-optimistic cache data. If\n     * this named option is provided, the optimistic parameter of the\n     * readQuery method can be omitted.\n     *\n     * @defaultValue true\n     */\n    optimistic?: boolean;\n\n    /**\n     * The instance of `ApolloClient` to use to look up the fragment.\n     *\n     * By default, the instance that's passed down via context is used, but you\n     * can provide a different instance here.\n     *\n     * @docGroup 1. Operation options\n     */\n    client?: ApolloClient;\n  }\n\n  namespace DocumentationTypes {\n    namespace useFragment {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends _self.Options<TData, TVariables> {}\n    }\n  }\n\n  // TODO: Update this to return `null` when there is no data returned from the\n  // fragment.\n  export type Result<TData> =\n    | ({\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#complete:member} */\n        complete: true;\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#missing:member} */\n        missing?: never;\n      } & GetDataState<MaybeMasked<TData>, \"complete\">)\n    | ({\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#complete:member} */\n        complete: false;\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#missing:member} */\n        missing?: MissingTree;\n      } & GetDataState<MaybeMasked<TData>, \"partial\">);\n\n  export namespace DocumentationTypes {\n    namespace useFragment {\n      export interface Result<TData> {\n        data: MaybeMasked<TData> | DataValue.Partial<MaybeMasked<TData>>;\n        complete: boolean;\n        /**\n         * A tree of all `MissingFieldError` messages reported during fragment reading, where the branches of the tree indicate the paths of the errors within the query result.\n         */\n        missing?: MissingTree;\n      }\n    }\n  }\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useFragment:function(1)} */\n    export function useFragment<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >({\n      fragment,\n      from,\n      fragmentName,\n      variables,\n      optimistic,\n      client,\n    }: useFragment.Options<TData, TVariables>): useFragment.Result<TData>;\n  }\n}\n\n/**\n * `useFragment` represents a lightweight live binding into the Apollo Client Cache and enables Apollo Client to broadcast very specific fragment results to individual components. This hook returns an always-up-to-date view of whatever data the cache currently contains for a given fragment. `useFragment` never triggers network requests of its own.\n *\n * Note that the `useQuery` hook remains the primary hook responsible for querying and populating data in the cache ([see the API reference](./hooks#usequery)). As a result, the component reading the fragment data via `useFragment` is still subscribed to all changes in the query data, but receives updates only when that fragment's specific data change.\n *\n * To view a `useFragment` example, see the [Fragments](https://www.apollographql.com/docs/react/data/fragments#usefragment) page.\n */\nexport function useFragment<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(options: useFragment.Options<TData, TVariables>): useFragment.Result<TData> {\n  \"use no memo\";\n  return wrapHook(\n    \"useFragment\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useFragment_,\n    useApolloClient(options.client)\n  )(options);\n}\n\nfunction useFragment_<TData, TVariables extends OperationVariables>(\n  options: useFragment.Options<TData, TVariables>\n): useFragment.Result<TData> {\n  const client = useApolloClient(options.client);\n  const { cache } = client;\n  const { from, ...rest } = options;\n\n  // We calculate the cache id seperately from `stableOptions` because we don't\n  // want changes to non key fields in the `from` property to affect\n  // `stableOptions` and retrigger our subscription. If the cache identifier\n  // stays the same between renders, we want to reuse the existing subscription.\n  const id = React.useMemo(\n    () =>\n      typeof from === \"string\" ? from\n      : from === null ? null\n      : cache.identify(from),\n    [cache, from]\n  );\n\n  const stableOptions = useDeepMemo(() => ({ ...rest, from: id! }), [rest, id]);\n\n  // Since .next is async, we need to make sure that we\n  // get the correct diff on the next render given new diffOptions\n  const diff = React.useMemo(() => {\n    const { fragment, fragmentName, from, optimistic = true } = stableOptions;\n\n    if (from === null) {\n      return {\n        result: diffToResult({\n          result: {},\n          complete: false,\n        } as Cache.DiffResult<TData>),\n      };\n    }\n\n    const { cache } = client;\n    const diff = cache.diff<TData, TVariables>({\n      ...stableOptions,\n      returnPartialData: true,\n      id: from,\n      query: cache[\"getFragmentDoc\"](\n        client[\"transform\"](fragment),\n        fragmentName\n      ),\n      optimistic,\n    });\n\n    return {\n      result: diffToResult<TData>({\n        ...diff,\n        result: client[\"queryManager\"].maskFragment({\n          fragment,\n          fragmentName,\n          // TODO: Revert to `diff.result` once `useFragment` supports `null` as\n          // valid return value\n          data: diff.result === null ? {} : diff.result,\n        }) as any,\n      }),\n    };\n  }, [client, stableOptions]);\n\n  // Used for both getSnapshot and getServerSnapshot\n  const getSnapshot = React.useCallback(() => diff.result, [diff]);\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        let lastTimeout = 0;\n\n        const subscription =\n          stableOptions.from === null ?\n            null\n          : client.watchFragment(stableOptions).subscribe({\n              next: (result) => {\n                // Avoid unnecessarily rerendering this hook for the initial result\n                // emitted from watchFragment which should be equal to\n                // `diff.result`.\n                if (equal(result, diff.result)) return;\n                diff.result = result;\n                // If we get another update before we've re-rendered, bail out of\n                // the update and try again. This ensures that the relative timing\n                // between useQuery and useFragment stays roughly the same as\n                // fixed in https://github.com/apollographql/apollo-client/pull/11083\n                clearTimeout(lastTimeout);\n                lastTimeout = setTimeout(forceUpdate) as any;\n              },\n            });\n        return () => {\n          subscription?.unsubscribe();\n          clearTimeout(lastTimeout);\n        };\n      },\n      [client, stableOptions, diff]\n    ),\n    getSnapshot,\n    getSnapshot\n  );\n}\n\nfunction diffToResult<TData>(\n  diff: Cache.DiffResult<TData>\n): useFragment.Result<TData> {\n  const result = {\n    data: diff.result,\n    complete: !!diff.complete,\n    dataState: diff.complete ? \"complete\" : \"partial\",\n  } as useFragment.Result<TData>; // TODO: Remove assertion once useFragment returns null\n\n  if (diff.missing) {\n    result.missing = diff.missing.missing;\n  }\n\n  return result;\n}\n"]}