{"version": 3, "file": "useMutation.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useMutation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAqB/B,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AAEjE,OAAO,EAAE,yBAAyB,EAAE,MAAM,yCAAyC,CAAC;AACpF,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAmJvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,MAAM,UAAU,WAAW,CAMzB,QAA6D,EAC7D,OASC;IAMD,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAChD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,CAExC,GAAG,EAAE,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;IAErC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,MAAM;QACN,UAAU,EAAE,CAAC;QACb,SAAS,EAAE,IAAI;QACf,MAAM;QACN,QAAQ;QACR,OAAO;KACR,CAAC,CAAC;IAEH,yBAAyB,CAAC,GAAG,EAAE;QAC7B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,CAC/B,CACE,iBAII,EAAoE,EACxE,EAAE;QACF,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;QAC1C,MAAM,WAAW,GAAG,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;QAE3D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACzD,SAAS,CACP,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG;gBACpB,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,IAAI;gBACZ,MAAM;aACP,CAAC,CACH,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;QAC5C,MAAM,aAAa,GAAG,YAAY,CAAC,WAAW,EAAE,cAAqB,CAAC,CAAC;QAEvE,OAAO,MAAM;aACV,MAAM,CACL,aAAsE,CACvE;aACA,IAAI,CACH,CAAC,QAAQ,EAAE,EAAE;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC;YAEjC,MAAM,OAAO,GACX,cAAc,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;YAEzD,IAAI,KAAK,IAAI,OAAO,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YAChC,CAAC;YAED,IAAI,UAAU,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC1C,MAAM,MAAM,GAAG;oBACb,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,KAAK;oBACd,IAAI;oBACJ,KAAK;oBACL,MAAM;iBACP,CAAC;gBAEF,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;oBAChE,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,MAAM,WAAW,GACf,cAAc,CAAC,WAAW,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC;YAEjE,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAK,EAAE,aAAa,CAAC,CAAC;YAC/C,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IACE,UAAU,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU;gBACrC,GAAG,CAAC,OAAO,CAAC,SAAS,EACrB,CAAC;gBACD,MAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,KAAK;oBACL,IAAI,EAAE,KAAK,CAAC;oBACZ,MAAM,EAAE,IAAI;oBACZ,MAAM;iBACP,CAAC;gBAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;oBACvC,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GACX,cAAc,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;YAEzD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YAChC,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC,CACF,CAAC;IACN,CAAC,EACD,EAAE,CACH,CAAC;IAEF,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QACnC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YACtD,SAAS,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC5B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAEzB,OAAO,GAAG,EAAE;YACV,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAC,OAAc,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAoB;IAC/C,OAAO;QACL,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,KAAK;QACd,MAAM;KACP,CAAC;AACJ,CAAC", "sourcesContent": ["import type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport { equal } from \"@wry/equality\";\nimport * as React from \"react\";\n\nimport type {\n  ApolloCache,\n  ApolloClient,\n  DefaultContext,\n  DocumentNode,\n  ErrorLike,\n  ErrorPolicy,\n  InternalRefetchQueriesInclude,\n  MaybeMasked,\n  MutationFetchPolicy,\n  MutationQueryReducersMap,\n  MutationUpdaterFunction,\n  NormalizedExecutionResult,\n  OnQueryUpdated,\n  OperationVariables,\n  Unmasked,\n} from \"@apollo/client\";\nimport type { IgnoreModifier } from \"@apollo/client/cache\";\nimport type { NoInfer, Prettify } from \"@apollo/client/utilities/internal\";\nimport { mergeOptions } from \"@apollo/client/utilities/internal\";\n\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\ntype MakeRequiredVariablesOptional<\n  TVariables extends OperationVariables,\n  TConfiguredVariables extends Partial<TVariables>,\n> = Prettify<\n  {\n    [K in keyof TVariables as K extends keyof TConfiguredVariables ? K\n    : never]?: TVariables[K];\n  } & Omit<TVariables, keyof TConfiguredVariables>\n>;\n\nexport declare namespace useMutation {\n  export interface Options<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n    TConfiguredVariables extends Partial<TVariables> = Partial<TVariables>,\n  > {\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#optimisticResponse:member} */\n    optimisticResponse?:\n      | Unmasked<NoInfer<TData>>\n      | ((\n          vars: TVariables,\n          { IGNORE }: { IGNORE: IgnoreModifier }\n        ) => Unmasked<NoInfer<TData>> | IgnoreModifier);\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#updateQueries:member} */\n    updateQueries?: MutationQueryReducersMap<TData>;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#refetchQueries:member} */\n    refetchQueries?:\n      | ((\n          result: NormalizedExecutionResult<Unmasked<TData>>\n        ) => InternalRefetchQueriesInclude)\n      | InternalRefetchQueriesInclude;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#awaitRefetchQueries:member} */\n    awaitRefetchQueries?: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#update:member} */\n    update?: MutationUpdaterFunction<TData, TVariables, TCache>;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onQueryUpdated:member} */\n    onQueryUpdated?: OnQueryUpdated<any>;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#errorPolicy:member} */\n    errorPolicy?: ErrorPolicy;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n    variables?: TConfiguredVariables;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#context:member} */\n    context?: DefaultContext;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#fetchPolicy:member} */\n    fetchPolicy?: MutationFetchPolicy;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#keepRootFields:member} */\n    keepRootFields?: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#client:member} */\n    client?: ApolloClient;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n    notifyOnNetworkStatusChange?: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onCompleted:member} */\n    onCompleted?: (\n      data: MaybeMasked<TData>,\n      clientOptions?: Options<TData, TVariables, TCache>\n    ) => void;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onError:member} */\n    onError?: (\n      error: ErrorLike,\n      clientOptions?: Options<TData, TVariables, TCache>\n    ) => void;\n  }\n\n  export interface Result<TData = unknown> {\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#data:member} */\n    data: MaybeMasked<TData> | null | undefined;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#error:member} */\n    error: ErrorLike | undefined;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#loading:member} */\n    loading: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#called:member} */\n    called: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#client:member} */\n    client: ApolloClient;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#reset:member} */\n    reset: () => void;\n  }\n\n  export type ResultTuple<\n    TData,\n    TVariables extends OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n  > = [\n    mutate: MutationFunction<TData, TVariables, TCache>,\n    result: Result<TData>,\n  ];\n\n  export type MutationFunction<\n    TData,\n    TVariables extends OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n  > = (\n    ...[options]: {} extends TVariables ?\n      [\n        options?: MutationFunctionOptions<TData, TVariables, TCache> & {\n          /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n          variables?: TVariables;\n        },\n      ]\n    : [\n        options: MutationFunctionOptions<TData, TVariables, TCache> & {\n          /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n          variables: TVariables;\n        },\n      ]\n  ) => Promise<ApolloClient.MutateResult<MaybeMasked<TData>>>;\n\n  export type MutationFunctionOptions<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n  > = Options<TData, TVariables, TCache>;\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useMutation:function(1)} */\n    export function useMutation<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options?: useMutation.Options<TData, TVariables>\n    ): useMutation.ResultTuple<TData, TVariables>;\n  }\n}\n\n/**\n * > Refer to the [Mutations](https://www.apollographql.com/docs/react/data/mutations/) section for a more in-depth overview of `useMutation`.\n *\n * @example\n *\n * ```jsx\n * import { gql, useMutation } from \"@apollo/client\";\n *\n * const ADD_TODO = gql`\n *   mutation AddTodo($type: String!) {\n *     addTodo(type: $type) {\n *       id\n *       type\n *     }\n *   }\n * `;\n *\n * function AddTodo() {\n *   let input;\n *   const [addTodo, { data }] = useMutation(ADD_TODO);\n *\n *   return (\n *     <div>\n *       <form\n *         onSubmit={(e) => {\n *           e.preventDefault();\n *           addTodo({ variables: { type: input.value } });\n *           input.value = \"\";\n *         }}\n *       >\n *         <input\n *           ref={(node) => {\n *             input = node;\n *           }}\n *         />\n *         <button type=\"submit\">Add Todo</button>\n *       </form>\n *     </div>\n *   );\n * }\n * ```\n *\n * @param mutation - A GraphQL mutation document parsed into an AST by `gql`.\n * @param options - Options to control how the mutation is executed.\n * @returns A tuple in the form of `[mutate, result]`\n */\nexport function useMutation<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n  TCache extends ApolloCache = ApolloCache,\n  TConfiguredVariables extends Partial<TVariables> = {},\n>(\n  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: useMutation.Options<\n    NoInfer<TData>,\n    NoInfer<TVariables>,\n    TCache,\n    {\n      [K in keyof TConfiguredVariables]: K extends keyof TVariables ?\n        TConfiguredVariables[K]\n      : never;\n    }\n  >\n): useMutation.ResultTuple<\n  TData,\n  MakeRequiredVariablesOptional<TVariables, TConfiguredVariables>,\n  TCache\n> {\n  const client = useApolloClient(options?.client);\n  const [result, setResult] = React.useState<\n    Omit<useMutation.Result<TData>, \"reset\">\n  >(() => createInitialResult(client));\n\n  const ref = React.useRef({\n    result,\n    mutationId: 0,\n    isMounted: true,\n    client,\n    mutation,\n    options,\n  });\n\n  useIsomorphicLayoutEffect(() => {\n    Object.assign(ref.current, { client, options, mutation });\n  });\n\n  const execute = React.useCallback(\n    (\n      executeOptions: useMutation.MutationFunctionOptions<\n        TData,\n        TVariables,\n        TCache\n      > = {} as useMutation.MutationFunctionOptions<TData, TVariables, TCache>\n    ) => {\n      const { options, mutation } = ref.current;\n      const baseOptions = { ...options, mutation };\n      const client = executeOptions.client || ref.current.client;\n\n      if (!ref.current.result.loading && ref.current.isMounted) {\n        setResult(\n          (ref.current.result = {\n            loading: true,\n            error: undefined,\n            data: undefined,\n            called: true,\n            client,\n          })\n        );\n      }\n\n      const mutationId = ++ref.current.mutationId;\n      const clientOptions = mergeOptions(baseOptions, executeOptions as any);\n\n      return client\n        .mutate(\n          clientOptions as ApolloClient.MutateOptions<TData, OperationVariables>\n        )\n        .then(\n          (response) => {\n            const { data, error } = response;\n\n            const onError =\n              executeOptions.onError || ref.current.options?.onError;\n\n            if (error && onError) {\n              onError(error, clientOptions);\n            }\n\n            if (mutationId === ref.current.mutationId) {\n              const result = {\n                called: true,\n                loading: false,\n                data,\n                error,\n                client,\n              };\n\n              if (ref.current.isMounted && !equal(ref.current.result, result)) {\n                setResult((ref.current.result = result));\n              }\n            }\n\n            const onCompleted =\n              executeOptions.onCompleted || ref.current.options?.onCompleted;\n\n            if (!error) {\n              onCompleted?.(response.data!, clientOptions);\n            }\n\n            return response;\n          },\n          (error) => {\n            if (\n              mutationId === ref.current.mutationId &&\n              ref.current.isMounted\n            ) {\n              const result = {\n                loading: false,\n                error,\n                data: void 0,\n                called: true,\n                client,\n              };\n\n              if (!equal(ref.current.result, result)) {\n                setResult((ref.current.result = result));\n              }\n            }\n\n            const onError =\n              executeOptions.onError || ref.current.options?.onError;\n\n            if (onError) {\n              onError(error, clientOptions);\n            }\n\n            throw error;\n          }\n        );\n    },\n    []\n  );\n\n  const reset = React.useCallback(() => {\n    if (ref.current.isMounted) {\n      const result = createInitialResult(ref.current.client);\n      Object.assign(ref.current, { mutationId: 0, result });\n      setResult(result);\n    }\n  }, []);\n\n  React.useEffect(() => {\n    const current = ref.current;\n    current.isMounted = true;\n\n    return () => {\n      current.isMounted = false;\n    };\n  }, []);\n\n  return [execute as any, { reset, ...result }];\n}\n\nfunction createInitialResult(client: ApolloClient) {\n  return {\n    data: undefined,\n    error: undefined,\n    called: false,\n    loading: false,\n    client,\n  };\n}\n"]}