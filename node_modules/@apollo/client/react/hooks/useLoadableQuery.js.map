{"version": 3, "file": "useLoadableQuery.js", "sources": ["../../../src/react/hooks/useLoadableQuery.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorPolicy,\n  ObservableQuery,\n  OperationVariables,\n  RefetchWritePolicy,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON>,\n  FetchMoreFunction,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport {\n  assertWrappedQueryRef,\n  getSuspenseCache,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"@apollo/client/react/internal\";\nimport { __DEV__ } from \"@apollo/client/utilities/environment\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nimport { __use, useDeepMemo, useRenderGuard } from \"./internal/index.js\";\nimport { validateSuspenseHookOptions } from \"./internal/validateSuspenseHookOptions.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\ntype ResetFunction = () => void;\n\nexport declare namespace useLoadableQuery {\n  export type LoadQueryFunction<TVariables extends OperationVariables> = (\n    // Use variadic args to handle cases where TVariables is type `never`, in\n    // which case we don't want to allow a variables argument. In other\n    // words, we don't want to allow variables to be passed as an argument to this\n    // function if the query does not expect variables in the document.\n    ...args: {} extends TVariables ? [variables?: TVariables]\n    : [variables: TVariables]\n  ) => void;\n\n  export type Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = [\n    loadQuery: LoadQueryFunction<TVariables>,\n    queryRef: QueryRef<TData, TVariables, TStates> | null,\n    handlers: Handlers<TData, TVariables>,\n  ];\n  export interface Handlers<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member} */\n    fetchMore: FetchMoreFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member} */\n    refetch: RefetchFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n    subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n    /**\n     * A function that resets the `queryRef` back to `null`.\n     */\n    reset: ResetFunction;\n  }\n\n  export type FetchPolicy = Extract<\n    WatchQueryFetchPolicy,\n    \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n  >;\n\n  export interface Options {\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n    client?: ApolloClient;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n    context?: DefaultContext;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n    errorPolicy?: ErrorPolicy;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n    fetchPolicy?: FetchPolicy;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n    queryKey?: string | number | any[];\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n    refetchWritePolicy?: RefetchWritePolicy;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n    returnPartialData?: boolean;\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useLoadableQuery:function(1)} */\n    export function useLoadableQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options: useLoadableQuery.Options\n    ): useLoadableQuery.Result<TData, TVariables>;\n  }\n}\n\n/**\n * A hook for imperatively loading a query, such as responding to a user\n * interaction.\n *\n * > Refer to the [Suspense - Fetching in response to user interaction](https://www.apollographql.com/docs/react/data/suspense#fetching-in-response-to-user-interaction) section for a more in-depth overview of `useLoadableQuery`.\n *\n * @example\n *\n * ```jsx\n * import { gql, useLoadableQuery } from \"@apollo/client\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function App() {\n *   const [loadGreeting, queryRef] = useLoadableQuery(GET_GREETING);\n *\n *   return (\n *     <>\n *       <button onClick={() => loadGreeting({ language: \"english\" })}>\n *         Load greeting\n *       </button>\n *       <Suspense fallback={<div>Loading...</div>}>\n *         {queryRef && <Hello queryRef={queryRef} />}\n *       </Suspense>\n *     </>\n *   );\n * }\n *\n * function Hello({ queryRef }) {\n *   const { data } = useReadQuery(queryRef);\n *\n *   return <div>{data.greeting.message}</div>;\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns A tuple in the form of `[loadQuery, queryRef, handlers]`\n */\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useLoadableQuery.Options & {\n    returnPartialData: true;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): useLoadableQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"partial\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useLoadableQuery:function(1)} */\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useLoadableQuery.Options & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): useLoadableQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useLoadableQuery:function(1)} */\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useLoadableQuery.Options & {\n    returnPartialData: true;\n  }\n): useLoadableQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useLoadableQuery:function(1)} */\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: useLoadableQuery.Options\n): useLoadableQuery.Result<TData, TVariables, \"complete\" | \"streaming\">;\n\nexport function useLoadableQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useLoadableQuery.Options = {}\n): useLoadableQuery.Result<TData, TVariables> {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions({ client, query, options });\n  const { queryKey = [] } = options;\n\n  const [queryRef, setQueryRef] = React.useState<QueryRef<\n    TData,\n    TVariables,\n    DataState<TData>[\"dataState\"]\n  > | null>(null);\n\n  assertWrappedQueryRef(queryRef);\n\n  const internalQueryRef = queryRef && unwrapQueryRef(queryRef);\n\n  if (queryRef && internalQueryRef?.didChangeOptions(watchQueryOptions)) {\n    const promise = internalQueryRef.applyOptions(watchQueryOptions);\n    updateWrappedQueryRef(queryRef, promise);\n  }\n\n  const calledDuringRender = useRenderGuard();\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      if (!internalQueryRef) {\n        throw new Error(\n          \"The query has not been loaded. Please load the query.\"\n        );\n      }\n\n      const promise = internalQueryRef.fetchMore(options);\n\n      setQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      if (!internalQueryRef) {\n        throw new Error(\n          \"The query has not been loaded. Please load the query.\"\n        );\n      }\n\n      const promise = internalQueryRef.refetch(options);\n\n      setQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const loadQuery: useLoadableQuery.LoadQueryFunction<TVariables> =\n    React.useCallback(\n      (...args) => {\n        invariant(\n          !calledDuringRender(),\n          \"useLoadableQuery: 'loadQuery' should not be called during render. To start a query during render, use the 'useBackgroundQuery' hook.\"\n        );\n\n        const [variables] = args;\n\n        const cacheKey: CacheKey = [\n          query,\n          canonicalStringify(variables),\n          ...([] as any[]).concat(queryKey),\n        ];\n\n        const queryRef = suspenseCache.getQueryRef(cacheKey, () =>\n          client.watchQuery({\n            ...watchQueryOptions,\n            variables,\n          } as ApolloClient.WatchQueryOptions<any, any>)\n        );\n\n        setQueryRef(wrapQueryRef(queryRef));\n      },\n      [\n        query,\n        queryKey,\n        suspenseCache,\n        watchQueryOptions,\n        calledDuringRender,\n        client,\n      ]\n    );\n\n  const subscribeToMore: SubscribeToMoreFunction<TData, TVariables> =\n    React.useCallback(\n      (options) => {\n        invariant(\n          internalQueryRef,\n          \"The query has not been loaded. Please load the query.\"\n        );\n\n        return internalQueryRef.observable.subscribeToMore(\n          // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n          options as any as ObservableQuery.SubscribeToMoreOptions<\n            TData,\n            OperationVariables\n          >\n        );\n      },\n      [internalQueryRef]\n    );\n\n  const reset: ResetFunction = React.useCallback(() => {\n    setQueryRef(null);\n  }, []);\n\n  return [loadQuery, queryRef, { fetchMore, refetch, reset, subscribeToMore }];\n}\n\nfunction useWatchQueryOptions<TData, TVariables extends OperationVariables>({\n  client,\n  query,\n  options,\n}: {\n  client: ApolloClient;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  options: useLoadableQuery.Options;\n}): ApolloClient.WatchQueryOptions<TData, TVariables> {\n  return useDeepMemo<ApolloClient.WatchQueryOptions<TData, TVariables>>(() => {\n    const fetchPolicy =\n      options.fetchPolicy ||\n      client.defaultOptions.watchQuery?.fetchPolicy ||\n      \"cache-first\";\n\n    const watchQueryOptions = {\n      ...options,\n      fetchPolicy,\n      query,\n      notifyOnNetworkStatusChange: false,\n      nextFetchPolicy: void 0,\n    };\n\n    if (__DEV__) {\n      validateSuspenseHookOptions(watchQueryOptions as any);\n    }\n\n    return watchQueryOptions as ApolloClient.WatchQueryOptions<\n      TData,\n      TVariables\n    >;\n  }, [client, options, query]);\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAP,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAe9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD;AAOzD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EACL,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuB,EACrB,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEkB,EAChB,CAHF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGgB,EACd,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIuB,EACrB,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKc,EALd,EAAA,CAAA,CAAA,CAAA,EAMO,CANP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAMsC;AACtC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAC9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAE9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAS,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAmD,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwE;AACxE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuF;AACvF,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;AAmLtD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAI9B,CAJF,CAAA,CAAA,CAAA,CAI4D,EAC1D,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAKsC,CALtC,CAKwC,EALxC;IAOE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,EAAE,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAyD,EAAE,CAA3D,CAAA,CAAA,CAAA,CAAgE,EAAE,CAAlE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAA2E,CAAC;IAC1E,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,EAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;IAEjC,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,EAA9B,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAIpC,CAJZ,CAAA,CAAA,CAIgB,CAAC;IAEf,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC;IAE/B,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAuC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC;IAE7D,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE,CAAC,EAAE;QACrE,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmE,CAAC;QAChE,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC;IAC1C;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAA3C,CAA6C;IAE3C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0D,CAA1D,CAAA,CAAA,CAAA,CAA+D,CAAC,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CACvE,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CACY,EAAE,CADd,EAAA;QAEM,CAAN,EAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE;YACrB,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CACb,CADV,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiE,CACxD;QACH;QAEA,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD,CAAC;QAEnD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,CAAC;QAE3C,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAChB,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CAAC,CACnB;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsD,CAAtD,CAAA,CAAA,CAAA,CAA2D,CAAC,CAA5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CACnE,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CACY,EAAE,CADd,EAAA;QAEM,CAAN,EAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE;YACrB,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CACb,CADV,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiE,CACxD;QACH;QAEA,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC;QAEjD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,CAAC;QAE3C,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAChB,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CAAC,CACnB;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAEU,CAFV,CAAA,CAAA,CAEc,EAAE,CAFhB,EAAA;QAGQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACU,CAAC,CADX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC6B,CAD7B,MAGS;QAED,CAAR,CAAA,CAAA,CAAA,EAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAxB,EAA4B,CAA5B,CAAA,CAAA,CAAgC;QAExB,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmC;YACzB,CAAV,CAAA,CAAA,CAAA,CAAe;YACL,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC;YAC7B,CAAV,CAAA,CAAc,CAAd,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;QAC3C,CAAS;QAED,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,EAAE,CAA7D,EAAgE,CAAhE,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CACgB,CAAC,CADjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CAAC;YAChB,CAAZ,CAAA,CAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC;YACpB,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;QACrB,CAAuD,CAAC,CAC/C;QAED,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAAC;IACrC,CAAC,EACD;QACE,CAAR,CAAA,CAAA,CAAA,CAAa;QACL,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;QACR,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;QACb,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB;QACjB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B;QAClB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc;IACd,CAAO,CACF;IAEH,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAEc,EAAE,CAFhB,EAAA;QAGQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAGS;QAED,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D;QAChD,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAGW,CACF;IACH,CAAC,EACD,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuB,CAAC,CACnB;IAEH,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,EAAoD,CAApD,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAoB,CAAC;IACnB,CAAC,EAAE,CAAL,CAAO,CAAC;IAEN,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE,EAAE,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,EAAE,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,EAAE,CAArD,CAAA,CAAA,CAAA,CAA0D,EAAE,CAA5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAA6E,CAAC;AAC9E;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAA+C,EAC1E,CADF,CAAA,CAAA,CAAA,CAAA,CACQ,EACN,CAFF,CAAA,CAAA,CAAA,CAEO,EACL,CAHF,CAAA,CAAA,CAAA,CAAA,CAAA,CAGS,EAHT,CAQC,EARD;IASE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAoD,CAAxE,EAA2E,CAA3E,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CACa,CAAC,CADd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YAEM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAtC,CAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB;QAEf,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B;YACxB,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YACV,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;YACX,CAAN,CAAA,CAAA,CAAA,CAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,EAAE,CAAnC,CAAA,CAAA,CAAA,CAAwC;YAClC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,EAA4B,CAAC;QAC7B,CAAK;QAED,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE;YACX,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC;QACvD;QAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAA4B,CAAC,CAAC;AAC9B;"}