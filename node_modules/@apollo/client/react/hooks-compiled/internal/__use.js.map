{"version": 3, "file": "__use.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/__use.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,eAAe,EAAE,MAAM,mCAAmC,CAAC;AAGpE,sEAAsE;AACtE,6DAA6D;AAC7D,gEAAgE;AAChE,MAAM,MAAM,GAAG,KAA2B,CAAC;AAC3C,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAoB,CAAC;AAElD,kFAAkF;AAClF,4CAA4C;AAC5C,MAAM,CAAC,MAAM,KAAK,GAChB,QAAQ;IACR,SAAS,KAAK,CAAS,OAAwB;QAC7C,MAAM,gBAAgB,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;QAElD,QAAQ,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAChC,KAAK,SAAS;gBACZ,MAAM,gBAAgB,CAAC;YACzB,KAAK,UAAU;gBACb,MAAM,gBAAgB,CAAC,MAAM,CAAC;YAChC,KAAK,WAAW;gBACd,OAAO,gBAAgB,CAAC,KAAK,CAAC;QAClC,CAAC;IACH,CAAC,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport { decoratePromise } from \"@apollo/client/utilities/internal\";\n\ntype Use = <T>(promise: Promise<T>) => T;\n// Prevent webpack from complaining about our feature detection of the\n// use property of the React namespace, which is expected not\n// to exist when using current stable versions, and that's fine.\nconst useKey = \"use\" as keyof typeof React;\nconst realHook = React[useKey] as Use | undefined;\n\n// This is named with two underscores to allow this hook to evade typical rules of\n// hooks (i.e. it can be used conditionally)\nexport const __use =\n  realHook ||\n  function __use<TValue>(promise: Promise<TValue>) {\n    const decoratedPromise = decoratePromise(promise);\n\n    switch (decoratedPromise.status) {\n      case \"pending\":\n        throw decoratedPromise;\n      case \"rejected\":\n        throw decoratedPromise.reason;\n      case \"fulfilled\":\n        return decoratedPromise.value;\n    }\n  };\n"]}