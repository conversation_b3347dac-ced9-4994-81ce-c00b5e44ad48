{"version": 3, "file": "useDeepMemo.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/useDeepMemo.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,MAAM,UAAU,WAAW,CACzB,MAAoB,EACpB,IAAoB;IAEpB,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAA0C,KAAK,CAAC,CAAC,CAAC;IAC1E,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;QACnD,GAAG,CAAC,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC;IAC1C,CAAC;IACD,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;AAC3B,CAAC", "sourcesContent": ["import { equal } from \"@wry/equality\";\nimport type { DependencyList } from \"react\";\nimport * as React from \"react\";\n\nexport function useDeepMemo<TValue>(\n  memoFn: () => TValue,\n  deps: DependencyList\n) {\n  const ref = React.useRef<{ deps: DependencyList; value: TValue }>(void 0);\n  if (!ref.current || !equal(ref.current.deps, deps)) {\n    ref.current = { value: memoFn(), deps };\n  }\n  return ref.current.value;\n}\n"]}