{"version": 3, "file": "useIsomorphicLayoutEffect.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/useIsomorphicLayoutEffect.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,SAAS,EAAE,MAAM,mCAAmC,CAAC;AAE9D,8EAA8E;AAC9E,+EAA+E;AAC/E,yEAAyE;AACzE,+EAA+E;AAC/E,SAAS;AACT,MAAM,CAAC,MAAM,yBAAyB,GACpC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport { canUseDOM } from \"@apollo/client/utilities/internal\";\n\n// use canUseDOM here instead of canUseLayoutEffect because we want to be able\n// to use useLayoutEffect in our jest tests. useLayoutEffect seems to work fine\n// in useSuspenseQuery tests, but to honor the original comment about the\n// warnings for useSyncExternalStore implementation, canUseLayoutEffect is left\n// alone.\nexport const useIsomorphicLayoutEffect =\n  canUseDOM ? React.useLayoutEffect : React.useEffect;\n"]}