{"version": 3, "file": "wrapHook.js", "sources": ["../../../../src/react/hooks/internal/wrapHook.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type { ApolloClient, InternalTypes } from \"@apollo/client\";\nimport type { ObservableQuery } from \"@apollo/client\";\nimport type { createQueryPreloader } from \"@apollo/client/react\";\nimport type {\n  useBackgroundQuery,\n  useFragment,\n  useQuery,\n  useQueryRefHandlers,\n  useReadQuery,\n  useSuspenseFragment,\n  useSuspenseQuery,\n} from \"@apollo/client/react\";\n\n// direct import to avoid circular dependency\nimport { getApolloContext } from \"../../context/ApolloContext.js\";\n\nexport const wrapperSymbol = Symbol.for(\"apollo.hook.wrappers\");\n\ntype FunctionSignature<T> =\n  T extends (...args: infer A) => infer R ? (...args: A) => R : never;\n\ninterface WrappableHooks {\n  createQueryPreloader: FunctionSignature<typeof createQueryPreloader>;\n  useQuery: FunctionSignature<typeof useQuery>;\n  useSuspenseQuery: FunctionSignature<typeof useSuspenseQuery>;\n  useSuspenseFragment: FunctionSignature<typeof useSuspenseFragment>;\n  useBackgroundQuery: FunctionSignature<typeof useBackgroundQuery>;\n  useReadQuery: FunctionSignature<typeof useReadQuery>;\n  useFragment: FunctionSignature<typeof useFragment>;\n  useQueryRefHandlers: FunctionSignature<typeof useQueryRefHandlers>;\n}\n\n/**\n * @internal\n * Can be used to correctly type the [Symbol.for(\"apollo.hook.wrappers\")] property of\n * `QueryManager`, to override/wrap hook functionality.\n */\nexport type HookWrappers = {\n  [K in keyof WrappableHooks]?: (\n    originalHook: WrappableHooks[K]\n  ) => WrappableHooks[K];\n};\n\ninterface QueryManagerWithWrappers extends InternalTypes.QueryManager {\n  [wrapperSymbol]?: HookWrappers;\n}\n\n/**\n * @internal\n *\n * Makes an Apollo Client hook \"wrappable\".\n * That means that the Apollo Client instance can expose a \"wrapper\" that will be\n * used to wrap the original hook implementation with additional logic.\n * @example\n *\n * ```tsx\n * // this is already done in `@apollo/client` for all wrappable hooks (see `WrappableHooks`)\n * // following this pattern\n * function useQuery() {\n *   return wrapHook('useQuery', _useQuery, options.client)(query, options);\n * }\n * function _useQuery(query, options) {\n *   // original implementation\n * }\n *\n * // this is what a library like `@apollo/client-react-streaming` would do\n * class ApolloClientWithStreaming extends ApolloClient {\n *   constructor(options) {\n *     super(options);\n *     this.queryManager[Symbol.for(\"apollo.hook.wrappers\")] = {\n *       useQuery: (original) => (query, options) => {\n *         console.log(\"useQuery was called with options\", options);\n *         return original(query, options);\n *       }\n *     }\n *   }\n * }\n *\n * // this will now log the options and then call the original `useQuery`\n * const client = new ApolloClientWithStreaming({ ... });\n * useQuery(query, { client });\n * ```\n */\nexport function wrapHook<Hook extends (...args: any[]) => any>(\n  hookName: keyof WrappableHooks,\n  useHook: Hook,\n  clientOrObsQuery: ObservableQuery<any> | ApolloClient\n): Hook {\n  // Priority-wise, the later entries in this array wrap\n  // previous entries and could prevent them (and in the end,\n  // even the original hook) from running\n  const wrapperSources = [\n    (\n      clientOrObsQuery as unknown as {\n        // both `ApolloClient` and `ObservableQuery` have a `queryManager` property\n        // but they're both `private`, so we have to cast around for a bit here.\n        queryManager: QueryManagerWithWrappers;\n      }\n    )[\"queryManager\"],\n    // if we are a hook (not `preloadQuery`), we are guaranteed to be inside of\n    // a React render and can use context\n    hookName.startsWith(\"use\") ?\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      React.useContext(getApolloContext())\n    : undefined,\n  ];\n\n  let wrapped = useHook;\n  for (const source of wrapperSources) {\n    const wrapper = source?.[wrapperSymbol]?.[hookName];\n    if (wrapper) {\n      wrapped = wrapper(wrapped) as Hook;\n    }\n  }\n\n  return wrapped;\n}\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAP,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAe9B,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE;AAEjE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmE/D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CACtB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgC,EAC9B,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAEe,EACb,CAHF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGuD,EAHvD;IAKE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB;QAEnB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKK,CAAC,CALN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKoB,CAAC;QACjB,CAAJ,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAJ,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAA6B,EAAE;YACzB,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAvC,CAAyC;YACrC,EAAE,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACf,CAAG;IAED,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IACrB,CAAF,CAAA,EAAA,CAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,EAAE;QACnC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B,CAA1B,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAA6C,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC;QACnD,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE;YACX,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAS;QACpC;IACF;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;AAChB;"}