{"version": 3, "file": "useSuspenseFragment.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useSuspenseFragment.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAW/B,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAG1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAOjE,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AA4EvD,MAAM,gBAAgB,GAAG,EAGxB,CAAC;AAwCF,MAAM,UAAU,mBAAmB,CAIjC,OAAuD;IAEvD,aAAa,CAAC;IACd,OAAO,QAAQ,CACb,qBAAqB;IACrB,yDAAyD;IACzD,oBAAoB,EACpB,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,OAAO,CAAC,CAAC;AACb,CAAC;AAED,SAAS,oBAAoB,CAI3B,OAAuD;IAEvD,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;IAEzB,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CACtB,GAAG,EAAE,CACH,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI;QAC/B,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI;YACtB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EACxB,CAAC,KAAK,EAAE,IAAI,CAAC,CACG,CAAC;IAEnB,MAAM,WAAW,GACf,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CACnB,gBAAgB,CAAC,MAAM,CAAC,CAAC,cAAc,CACrC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,EACrD,MAAM,EACN,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,SAAuB,EAAE,IAAI,EAAE,EAAE,EAAE,CAC7D,CACF,CAAC;IAEJ,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC,QAAQ,CAGxC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CACxC,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,CACvC,CACF,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;QACrC,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YACpD,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE;YACV,OAAO,EAAE,CAAC;YACV,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACxB,CAAC;IAED,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,GAAG,EAAE,CAAC;QACnC,yDAAyD;QACzD,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC;QAC7B,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;IACnC,CAAC;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAE/B,OAAO,EAAE,IAAI,EAAE,CAAC;AAClB,CAAC", "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataValue,\n  DocumentNode,\n  OperationVariables,\n  Reference,\n  StoreObject,\n  TypedDocumentNode,\n} from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type { FragmentType, MaybeMasked } from \"@apollo/client/masking\";\nimport type { FragmentKey } from \"@apollo/client/react/internal\";\nimport { getSuspenseCache } from \"@apollo/client/react/internal\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\n\nimport { __use } from \"./internal/__use.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\ntype From<TData> =\n  | StoreObject\n  | Reference\n  | FragmentType<NoInfer<TData>>\n  | string\n  | null;\n\nexport declare namespace useSuspenseFragment {\n  import _self = useSuspenseFragment;\n  export namespace Base {\n    export type Options<TData, TVariables extends OperationVariables> = {\n      /**\n       * A GraphQL document created using the `gql` template string tag from\n       * `graphql-tag` with one or more fragments which will be used to determine\n       * the shape of data to read. If you provide more than one fragment in this\n       * document then you must also specify `fragmentName` to select a single.\n       */\n      fragment: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n      /**\n       * The name of the fragment in your GraphQL document to be used. If you do\n       * not provide a `fragmentName` and there is only one fragment in your\n       * `fragment` document then that fragment will be used.\n       */\n      fragmentName?: string;\n      from: From<TData>;\n      // Override this field to make it optional (default: true).\n      optimistic?: boolean;\n      /**\n       * The instance of `ApolloClient` to use to look up the fragment.\n       *\n       * By default, the instance that's passed down via context is used, but you\n       * can provide a different instance here.\n       *\n       * @docGroup 1. Operation options\n       */\n      client?: ApolloClient;\n    };\n  }\n  export type Options<\n    TData,\n    TVariables extends OperationVariables,\n  > = Base.Options<TData, TVariables> & VariablesOption<NoInfer<TVariables>>;\n\n  export namespace DocumentationTypes {\n    export namespace useSuspenseFragment {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TData, TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export interface Result<TData> {\n    data: DataValue.Complete<MaybeMasked<TData>>;\n  }\n  export namespace DocumentationTypes {\n    export namespace useSuspenseFragment {\n      export interface Result<TData = unknown> extends _self.Result<TData> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\n    export function useSuspenseFragment<\n      TData,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      options: useSuspenseFragment.Options<TData, TVariables>\n    ): useSuspenseFragment.Result<TData>;\n  }\n}\n\nconst NULL_PLACEHOLDER = [] as unknown as [\n  FragmentKey,\n  Promise<MaybeMasked<any> | null>,\n];\n\n/** #TODO documentation */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables> & {\n    from: NonNullable<From<TData>>;\n  }\n): useSuspenseFragment.Result<TData>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables> & {\n    from: null;\n  }\n): useSuspenseFragment.Result<null>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables> & {\n    from: From<TData>;\n  }\n): useSuspenseFragment.Result<TData | null>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables>\n): useSuspenseFragment.Result<TData>;\n\nexport function useSuspenseFragment<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables>\n): useSuspenseFragment.Result<TData | null> {\n  \"use no memo\";\n  return wrapHook(\n    \"useSuspenseFragment\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseFragment_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(options);\n}\n\nfunction useSuspenseFragment_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables>\n): useSuspenseFragment.Result<TData | null> {\n  const client = useApolloClient(options.client);\n  const { from, variables } = options;\n  const { cache } = client;\n\n  const id = React.useMemo(\n    () =>\n      typeof from === \"string\" ? from\n      : from === null ? null\n      : cache.identify(from),\n    [cache, from]\n  ) as string | null;\n\n  const fragmentRef =\n    id === null ? null : (\n      getSuspenseCache(client).getFragmentRef(\n        [id, options.fragment, canonicalStringify(variables)],\n        client,\n        { ...options, variables: variables as TVariables, from: id }\n      )\n    );\n\n  let [current, setPromise] = React.useState<\n    [FragmentKey, Promise<MaybeMasked<TData> | null>]\n  >(\n    fragmentRef === null ? NULL_PLACEHOLDER : (\n      [fragmentRef.key, fragmentRef.promise]\n    )\n  );\n\n  React.useEffect(() => {\n    if (fragmentRef === null) {\n      return;\n    }\n\n    const dispose = fragmentRef.retain();\n    const removeListener = fragmentRef.listen((promise) => {\n      setPromise([fragmentRef.key, promise]);\n    });\n\n    return () => {\n      dispose();\n      removeListener();\n    };\n  }, [fragmentRef]);\n\n  if (fragmentRef === null) {\n    return { data: null };\n  }\n\n  if (current[0] !== fragmentRef.key) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    current[0] = fragmentRef.key;\n    current[1] = fragmentRef.promise;\n  }\n\n  const data = __use(current[1]);\n\n  return { data };\n}\n"]}