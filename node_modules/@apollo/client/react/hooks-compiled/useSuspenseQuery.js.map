{"version": 3, "file": "useSuspenseQuery.js", "sources": ["../../../src/react/hooks/useSuspenseQuery.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorLike,\n  ErrorPolicy,\n  GetDataState,\n  MaybeMasked,\n  ObservableQuery,\n  OperationVariables,\n  RefetchWritePolicy,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport { NetworkStatus } from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON>,\n  FetchMoreFunction,\n  QueryKey,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport { getSuspenseCache } from \"@apollo/client/react/internal\";\nimport { __DEV__ } from \"@apollo/client/utilities/environment\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\n\nimport type { SkipToken } from \"./constants.js\";\nimport { skipToken } from \"./constants.js\";\nimport { __use, useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport { validateSuspenseHookOptions } from \"./internal/validateSuspenseHookOptions.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\nexport declare namespace useSuspenseQuery {\n  export type FetchPolicy = Extract<\n    WatchQueryFetchPolicy,\n    \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n  >;\n\n  export namespace Base {\n    export interface Options<\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n      returnPartialData?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy_suspense:member} */\n      refetchWritePolicy?: RefetchWritePolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: FetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n      queryKey?: string | number | any[];\n\n      /**\n       * {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip_deprecated:member}\n       *\n       * @example Recommended usage of `skipToken`:\n       *\n       * ```ts\n       * import { skipToken, useSuspenseQuery } from \"@apollo/client\";\n       *\n       * const { data } = useSuspenseQuery(\n       *   query,\n       *   id ? { variables: { id } } : skipToken\n       * );\n       * ```\n       */\n      skip?: boolean;\n    }\n  }\n  export type Options<\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options<TVariables> & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useSuspenseQuery {\n      export interface Options<\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export namespace Base {\n    export interface Result<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n      error: ErrorLike | undefined;\n\n      /**\n       * {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member}\n       *\n       * @remarks\n       * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n       */\n      fetchMore: FetchMoreFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n\n      /**\n       * {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member}\n       *\n       * @remarks\n       * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n       */\n      refetch: RefetchFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#subscribeToMore:member} */\n      subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n    }\n  }\n  export type Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData, TVariables> &\n    GetDataState<MaybeMasked<TData>, TStates>;\n\n  export namespace DocumentationTypes {\n    namespace useSuspenseQuery {\n      export interface Result<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Result<TData, TVariables>,\n          UtilityDocumentationTypes.DataState<TData> {}\n    }\n  }\n  export namespace DocumentationTypes {\n    /** Test {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\n    export function useSuspenseQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options?: useSuspenseQuery.Options<TVariables>\n    ): useSuspenseQuery.Result<TData, TVariables>;\n  }\n}\n\n/**\n * For a detailed explanation of `useSuspenseQuery`, see the [fetching with Suspense reference](https://www.apollographql.com/docs/react/data/suspense).\n *\n * @example\n *\n * ```jsx\n * import { Suspense } from \"react\";\n * import { useSuspenseQuery } from \"@apollo/client\";\n *\n * const listQuery = gql`\n *   query {\n *     list {\n *       id\n *     }\n *   }\n * `;\n *\n * function App() {\n *   return (\n *     <Suspense fallback={<Spinner />}>\n *       <List />\n *     </Suspense>\n *   );\n * }\n *\n * function List() {\n *   const { data } = useSuspenseQuery(listQuery);\n *\n *   return (\n *     <ol>\n *       {data.list.map((item) => (\n *         <Item key={item.id} id={item.id} />\n *       ))}\n *     </ol>\n *   );\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - An optional object containing options for the query. Instead of passing a `useSuspenseQuery.Options` object into the hook, you can also pass a [`skipToken`](#skiptoken) to prevent the `useSuspenseQuery` hook from executing the query or suspending.\n */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: true;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"partial\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: true;\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"empty\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: true;\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"partial\" | \"streaming\" | \"complete\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useSuspenseQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n  }\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (useSuspenseQuery.Options<NoInfer<TVariables>> & {\n        returnPartialData: true;\n      })\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"streaming\" | \"complete\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useSuspenseQuery.Options<NoInfer<TVariables>>]\n  : [options: useSuspenseQuery.Options<NoInfer<TVariables>>]\n): useSuspenseQuery.Result<TData, TVariables, \"complete\" | \"streaming\">;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>]\n  : [options: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>]\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseQuery:function(1)} */\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"complete\" | \"streaming\" | \"empty\"\n>;\n\nexport function useSuspenseQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SkipToken | useSuspenseQuery.Options<NoInfer<TVariables>>\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"streaming\" | \"complete\" | \"partial\"\n> {\n  \"use no memo\";\n  return wrapHook(\n    \"useSuspenseQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseQuery_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(query, options ?? ({} as any));\n}\n\nfunction useSuspenseQuery_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken & Partial<useSuspenseQuery.Options<TVariables>>)\n    | useSuspenseQuery.Options<TVariables>\n): useSuspenseQuery.Result<\n  TData,\n  TVariables,\n  \"partial\" | \"complete\" | \"streaming\" | \"empty\"\n> {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions<any, any>({\n    client,\n    query,\n    options,\n  });\n  const { fetchPolicy, variables } = watchQueryOptions;\n  const { queryKey = [] } = options;\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n\n  const queryRef = suspenseCache.getQueryRef(cacheKey, () =>\n    client.watchQuery(watchQueryOptions)\n  );\n\n  let [current, setPromise] = React.useState<\n    [QueryKey, Promise<ObservableQuery.Result<any>>]\n  >([queryRef.key, queryRef.promise]);\n\n  // This saves us a re-execution of the render function when a variable changed.\n  if (current[0] !== queryRef.key) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    current[0] = queryRef.key;\n    current[1] = queryRef.promise;\n  }\n  let promise = current[1];\n\n  if (queryRef.didChangeOptions(watchQueryOptions)) {\n    current[1] = promise = queryRef.applyOptions(watchQueryOptions);\n  }\n\n  React.useEffect(() => {\n    const dispose = queryRef.retain();\n\n    const removeListener = queryRef.listen((promise) => {\n      setPromise([queryRef.key, promise]);\n    });\n\n    return () => {\n      removeListener();\n      dispose();\n    };\n  }, [queryRef]);\n\n  const skipResult = React.useMemo<ObservableQuery.Result<TData>>(() => {\n    const error = queryRef.result.error;\n    const complete = !!queryRef.result.data;\n\n    return {\n      loading: false,\n      data: queryRef.result.data,\n      dataState: queryRef.result.dataState,\n      networkStatus: error ? NetworkStatus.error : NetworkStatus.ready,\n      error,\n      complete,\n      partial: !complete,\n    };\n  }, [queryRef.result]);\n\n  const result = fetchPolicy === \"standby\" ? skipResult : __use(promise);\n\n  const fetchMore = React.useCallback<\n    FetchMoreFunction<unknown, OperationVariables>\n  >(\n    (options) => {\n      const promise = queryRef.fetchMore(options);\n      setPromise([queryRef.key, queryRef.promise]);\n\n      return promise;\n    },\n    [queryRef]\n  ) as FetchMoreFunction<TData | undefined, TVariables>;\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = queryRef.refetch(variables);\n      setPromise([queryRef.key, queryRef.promise]);\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n  const subscribeToMore = queryRef.observable\n    .subscribeToMore as SubscribeToMoreFunction<TData | undefined, TVariables>;\n\n  return React.useMemo<\n    useSuspenseQuery.Result<TData, TVariables, DataState<TData>[\"dataState\"]>\n  >(() => {\n    return {\n      client,\n      data: result.data,\n      dataState: result.dataState,\n      error: result.error,\n      networkStatus: result.networkStatus,\n      fetchMore,\n      refetch,\n      subscribeToMore,\n    } as useSuspenseQuery.Result<\n      TData,\n      TVariables,\n      DataState<TData>[\"dataState\"]\n    >;\n  }, [client, fetchMore, refetch, result, subscribeToMore]);\n}\n\ninterface UseWatchQueryOptionsHookOptions<\n  TData,\n  TVariables extends OperationVariables,\n> {\n  client: ApolloClient;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  options: SkipToken | useSuspenseQuery.Options<TVariables>;\n}\n\nexport function useWatchQueryOptions<\n  TData,\n  TVariables extends OperationVariables,\n>({\n  client,\n  query,\n  options,\n}: UseWatchQueryOptionsHookOptions<\n  TData,\n  TVariables\n>): ApolloClient.WatchQueryOptions<TData, TVariables> {\n  return useDeepMemo<ApolloClient.WatchQueryOptions<TData, TVariables>>(() => {\n    if (options === skipToken) {\n      return {\n        query,\n        fetchPolicy: \"standby\",\n      } as ApolloClient.WatchQueryOptions<TData, TVariables>;\n    }\n\n    const fetchPolicy =\n      options.fetchPolicy ||\n      client.defaultOptions.watchQuery?.fetchPolicy ||\n      \"cache-first\";\n\n    const watchQueryOptions: ApolloClient.WatchQueryOptions<TData, TVariables> =\n      {\n        ...options,\n        fetchPolicy,\n        query,\n        notifyOnNetworkStatusChange: false,\n        nextFetchPolicy: void 0,\n      };\n\n    if (__DEV__) {\n      validateSuspenseHookOptions(watchQueryOptions);\n    }\n\n    // Assign the updated fetch policy after our validation since `standby` is\n    // not a supported fetch policy on its own without the use of `skip`.\n    if (options.skip) {\n      watchQueryOptions.fetchPolicy = \"standby\";\n    }\n\n    return watchQueryOptions;\n  }, [client, options, query]);\n}\n"], "names": [], "mappings": ";AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAP,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;AAkB9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C;AAC9C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD;AAOzD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgE;AAChE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D;AAQ9D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C;AAC1C,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA6C,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE;AAClE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuF;AACvF,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;AA2StD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAI9B,CAJF,CAAA,CAAA,CAAA,CAI4D,EAC1D,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAKqE,EALrE;IAWE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CACb,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACsB;IAClB,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EACjB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CAAA,CAAA,EAC2B,CAD3B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EACuC,CADvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACgD,EAAE,CADlD,CAAA,CAAA,CAAA,CAAA,CAAA,CACyD,CAAC,CAD1D,CAAA,CAAA,CAAA,CAAA,EACiE,EAAE,CADnE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4E,CAAC,CAC1E,CAAC,CAFJ,CAAA,CAAA,CAAA,CAES,EAAE,CAFX,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAEuB,CAFvB,CAEiC,CAAC;AAClC;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAIxB,CAJF,CAAA,CAAA,CAAA,CAI4D,EAC1D,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAO0C,EAP1C;IAaE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAW;QACvD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU;QACN,CAAJ,CAAA,CAAA,CAAA,CAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;IACX,CAAG,CAAC;IACF,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAqC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;IACpD,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,EAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;IAEjC,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B;QACzB,CAAJ,CAAA,CAAA,CAAA,CAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC;QAC7B,CAAJ,CAAA,CAAQ,CAAR,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC;IACrC,CAAG;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,EAAE,CAAvD,EAA0D,CAA1D,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CACU,CAAC,CADX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CAAC,CADtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuC,CAAC,CACrC;IAED,CAAF,CAAA,EAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAA1B,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAExC,CAAC,CAFL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEa,CAAC,CAFd,CAAA,CAEiB,EAAE,CAFnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE2B,CAAC,CAF5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmC,CAAC,CAAC;IAEnC,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAC,EAAf,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAiC,EAAE;QAC/B,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAC,EAAb,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAA6B;QACzB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAC,EAAb,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC;IAC/B;IACA,CAAF,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAC,CAAC;IAExB,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,EAAE;QAChD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAC,EAAb,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE,CAAC;IACjE;IAEA,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAnC,CAAqC;QAEjC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,UAAQ,EAAE,CAArD,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAA8B,WAAS,CAAC,CAAC;QACrC,CAAC,CAAC;QAEF,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,EAAc,CAAd,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAApB,CAAsB;YAChB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAe;QACX,CAAC;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAC;IAEd,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAgC,CAAlE,EAAqE,CAArE,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAuC;QACnC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAAC,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAA2C;QAEvC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,CAAoB;YACd,CAAN,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAgC;YAC1B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C;YACpC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,EAA2B,EAAE,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,EAAiD,EAAE,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgE,CAAC,CAAjE,CAAA,CAAA,CAAA,CAAsE;YAChE,CAAN,CAAA,CAAA,CAAA,CAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc;YACR,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB;QACxB,CAAK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAC;IAErB,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA2C,EAAE,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAwD,EAAE,CAA1D,CAAA,CAAA,CAAA,CAA+D,CAAC,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CAAC;IAEtE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAGjC,UAAQ,EAAE,CAHd,EAAA;QAIM,CAAN,CAAA,CAAA,CAAA,YAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,UAAQ,CAAC;QAC3C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAA8B,EAAE,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAC;;IAG9C,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACa,CAAC,CACyC;IAErD,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsD,CAAtD,CAAA,CAAA,CAAA,CAA2D,CAAC,CAA5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CACnE,YAAU,EAAE,CADhB,EAAA;QAEM,CAAN,CAAA,CAAA,CAAA,YAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,YAAU,CAAC;QAC3C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAA8B,EAAE,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAC;;IAG9C,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACa,CAAC,CACX;IAED,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAK,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8E;IAE5E,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAElB,CAFJ,EAEO,CAFP,EAAA;QAGI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY;YACN,CAAN,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAuB;YACjB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC;YAC3B,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAyB;YACnB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC;YACnC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;YACT,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;YACP,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;QAKrB,CAAK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAE,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAwC,EAAE,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC,CAAC;AAC3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}