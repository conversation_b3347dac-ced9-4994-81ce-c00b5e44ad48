{"version": 3, "file": "version.js", "sources": ["../src/version.ts"], "sourcesContent": ["export const version = \"local\" as string;\nexport const build = \"source\" as \"source\" | \"esm\" | \"cjs\";\n"], "names": [], "mappings": "AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,SAAwC;AACxC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,EAAA,OAAyD;"}