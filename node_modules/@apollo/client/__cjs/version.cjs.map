{"version": 3, "file": "version.cjs", "sources": ["../../src/version.ts"], "sourcesContent": ["export const version = \"local\" as string;\nexport const build = \"source\" as \"source\" | \"esm\" | \"cjs\";\n"], "names": [], "mappings": ";;;AAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,SAAwC;AAC3B,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,EAAA,OAAyD;"}