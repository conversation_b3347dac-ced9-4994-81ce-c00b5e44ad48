{"version": 3, "file": "split.cjs", "sources": ["../../../../src/link/core/split.ts"], "sourcesContent": ["import { ApolloLink } from \"./ApolloLink.js\";\n\n/**\n * @deprecated Use `ApolloLink.split` instead. `split` will be removed in a\n * future major version.\n */\nexport const split = ApolloLink.split;\n"], "names": [], "mappings": ";;;AAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mBAAA,CAAA;AAEA,CAAA,CAAA;;;CAGA,CAAA;AACa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAqC;"}