{"version": 3, "file": "invariantErrorCodes.cjs", "sources": ["../../src/invariantErrorCodes.ts"], "sourcesContent": ["export interface ErrorCodes {\n  [key: number]: { file: string; condition?: string; message?: string };\n}\n\nexport const errorCodes: ErrorCodes = {};\nexport const devDebug: ErrorCodes = {};\nexport const devLog: ErrorCodes = {};\nexport const devWarn: ErrorCodes = {};\nexport const devError: ErrorCodes = {};\n"], "names": [], "mappings": ";;;AAIa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsC,CAAtC,CAAwC;AAC3B,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAsC;AACzB,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkC,CAAlC,CAAoC;AACvB,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmC,CAAnC,CAAqC;AACxB,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAsC;"}