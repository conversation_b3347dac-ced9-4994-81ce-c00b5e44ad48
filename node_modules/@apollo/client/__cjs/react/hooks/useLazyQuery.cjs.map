{"version": 3, "file": "useLazyQuery.cjs", "sources": ["../../../../src/react/hooks/useLazyQuery.ts"], "sourcesContent": ["import type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport { equal } from \"@wry/equality\";\nimport type { DocumentNode } from \"graphql\";\nimport * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  ErrorLike,\n  ErrorPolicy,\n  GetDataState,\n  InternalTypes,\n  MaybeMasked,\n  ObservableQuery,\n  OperationVariables,\n  RefetchWritePolicy,\n  SubscribeToMoreFunction,\n  UpdateQueryMapFn,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport { NetworkStatus } from \"@apollo/client\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\nimport { maybeDeepFreeze } from \"@apollo/client/utilities/internal\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nimport { useRenderGuard } from \"./internal/index.js\";\nimport { useDeepMemo } from \"./internal/useDeepMemo.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nexport declare namespace useLazyQuery {\n  import _self = useLazyQuery;\n  export interface Options<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n    fetchPolicy?: WatchQueryFetchPolicy;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#nextFetchPolicy:member} */\n    nextFetchPolicy?:\n      | WatchQueryFetchPolicy\n      | ((\n          this: ApolloClient.WatchQueryOptions<TData, TVariables>,\n          currentFetchPolicy: WatchQueryFetchPolicy,\n          context: InternalTypes.NextFetchPolicyContext<TData, TVariables>\n        ) => WatchQueryFetchPolicy);\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n    refetchWritePolicy?: RefetchWritePolicy;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n    errorPolicy?: ErrorPolicy;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#pollInterval:member} */\n    pollInterval?: number;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n    notifyOnNetworkStatusChange?: boolean;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n    returnPartialData?: boolean;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skipPollAttempt:member} */\n    skipPollAttempt?: () => boolean;\n\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n    client?: ApolloClient;\n  }\n  namespace DocumentationTypes {\n    namespace useLazyQuery {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends _self.Options<TData, TVariables> {}\n    }\n  }\n\n  namespace Base {\n    export interface Result<TData, TVariables extends OperationVariables> {\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#startPolling:member} */\n      startPolling: (pollInterval: number) => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#stopPolling:member} */\n      stopPolling: () => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#subscribeToMore:member} */\n      subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#updateQuery:member} */\n      updateQuery: (mapFn: UpdateQueryMapFn<TData, TVariables>) => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member} */\n      refetch: (\n        variables?: Partial<TVariables>\n      ) => Promise<ApolloClient.QueryResult<MaybeMasked<TData>>>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member} */\n      fetchMore: <\n        TFetchData = TData,\n        TFetchVars extends OperationVariables = TVariables,\n      >(\n        fetchMoreOptions: ObservableQuery.FetchMoreOptions<\n          TData,\n          TVariables,\n          TFetchData,\n          TFetchVars\n        >\n      ) => Promise<ApolloClient.QueryResult<MaybeMasked<TFetchData>>>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#client:member} */\n      client: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#observable:member} */\n      observable: ObservableQuery<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#previousData:member} */\n      previousData?: MaybeMasked<TData>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n      error?: ErrorLike;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#loading:member} */\n      loading: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n    }\n  }\n\n  export type Result<\n    TData,\n    TVariables extends OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData, TVariables> &\n    (\n      | ({\n          /**\n           * If `true`, the associated lazy query has been executed.\n           *\n           * @docGroup 2. Network info\n           */\n          called: true;\n\n          /** {@inheritDoc @apollo/client!QueryResultDocumentation#variables:member} */\n          variables: TVariables;\n        } & GetDataState<MaybeMasked<TData>, TStates>)\n      | {\n          /**\n           * If `true`, the associated lazy query has been executed.\n           *\n           * @docGroup 2. Network info\n           */\n          called: false;\n\n          /** {@inheritDoc @apollo/client!QueryResultDocumentation#variables:member} */\n          variables: Partial<TVariables>;\n\n          /** {@inheritDoc @apollo/client!QueryResultDocumentation#data:member} */\n          data: undefined;\n          /** {@inheritDoc @apollo/client!QueryResultDocumentation#dataState:member} */\n          dataState: \"empty\";\n        }\n    );\n\n  namespace DocumentationTypes {\n    namespace useLazyQuery {\n      export interface Result<TData, TVariables extends OperationVariables>\n        extends Base.Result<TData, TVariables>,\n          UtilityDocumentationTypes.DataState<TData>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {\n        /**\n         * If `true`, the associated lazy query has been executed.\n         *\n         * @docGroup 2. Network info\n         */\n        called: boolean;\n      }\n    }\n  }\n\n  export type ExecOptions<\n    TVariables extends OperationVariables = OperationVariables,\n  > = {\n    /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n    context?: DefaultContext;\n  } & VariablesOption<TVariables>;\n\n  namespace DocumentationTypes {\n    namespace useLazyQuery {\n      export interface ExecOptions<TVariables extends OperationVariables>\n        extends UtilityDocumentationTypes.VariableOptions<TVariables> {\n        /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n        context?: DefaultContext;\n      }\n    }\n  }\n\n  export type ResultTuple<\n    TData,\n    TVariables extends OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = [\n    execute: ExecFunction<TData, TVariables>,\n    result: useLazyQuery.Result<TData, TVariables, TStates>,\n  ];\n\n  export type ExecFunction<TData, TVariables extends OperationVariables> = (\n    ...args: {} extends TVariables ?\n      [options?: useLazyQuery.ExecOptions<TVariables>]\n    : [options: useLazyQuery.ExecOptions<TVariables>]\n  ) => ObservableQuery.ResultPromise<ApolloClient.QueryResult<TData>>;\n\n  namespace DocumentationTypes {\n    namespace useLazyQuery {\n      export import ResultTuple = _self.ResultTuple;\n    }\n  }\n\n  namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useLazyQuery:function(1)} */\n    export function useLazyQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options: useLazyQuery.Options<TData, TVariables>\n    ): useLazyQuery.ResultTuple<TData, TVariables>;\n  }\n}\n\n// The following methods, when called will execute the query, regardless of\n// whether the useLazyQuery execute function was called before.\nconst EAGER_METHODS = [\n  \"refetch\",\n  \"fetchMore\",\n  \"updateQuery\",\n  \"startPolling\",\n  \"stopPolling\",\n  \"subscribeToMore\",\n] as const;\n\n/**\n * A hook for imperatively executing queries in an Apollo application, e.g. in response to user interaction.\n *\n * > Refer to the [Queries - Manual execution with useLazyQuery](https://www.apollographql.com/docs/react/data/queries#manual-execution-with-uselazyquery) section for a more in-depth overview of `useLazyQuery`.\n *\n * @example\n *\n * ```jsx\n * import { gql } from \"@apollo/client\";\n * import { useLazyQuery } from \"@apollo/client/react\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const [loadGreeting, { called, loading, data }] = useLazyQuery(GET_GREETING, {\n *     variables: { language: \"english\" },\n *   });\n *   if (called && loading) return <p>Loading ...</p>;\n *   if (!called) {\n *     return <button onClick={() => loadGreeting()}>Load greeting</button>;\n *   }\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Default options to control how the query is executed.\n * @returns A tuple in the form of `[execute, result]`\n */\nexport function useLazyQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useLazyQuery.Options<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: true;\n  }\n): useLazyQuery.ResultTuple<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useLazyQuery:function(1)} */\nexport function useLazyQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useLazyQuery.Options<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n  }\n): useLazyQuery.ResultTuple<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useLazyQuery:function(1)} */\nexport function useLazyQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: useLazyQuery.Options<NoInfer<TData>, NoInfer<TVariables>>\n): useLazyQuery.ResultTuple<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\"\n>;\n\nexport function useLazyQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n  TStates extends DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: useLazyQuery.Options<NoInfer<TData>, NoInfer<TVariables>>\n): useLazyQuery.ResultTuple<TData, TVariables, TStates> {\n  const client = useApolloClient(options?.client);\n  const previousDataRef = React.useRef<TData>(undefined);\n  const resultRef = React.useRef<ObservableQuery.Result<TData>>(undefined);\n  const stableOptions = useDeepMemo(() => options, [options]);\n  const calledDuringRender = useRenderGuard();\n\n  function createObservable() {\n    return client.watchQuery({\n      ...options,\n      query,\n      initialFetchPolicy: options?.fetchPolicy,\n      fetchPolicy: \"standby\",\n    } as ApolloClient.WatchQueryOptions<TData, TVariables>);\n  }\n\n  const [currentClient, setCurrentClient] = React.useState(client);\n  const [observable, setObservable] = React.useState(createObservable);\n\n  if (currentClient !== client) {\n    setCurrentClient(client);\n    setObservable(createObservable());\n  }\n\n  // TODO: Revisit after we have RxJS in place. We should be able to use\n  // observable.getCurrentResult() (or equivalent) to get these values which\n  // will hopefully alleviate the need for us to use refs to track these values.\n  const updateResult = React.useCallback(\n    (result: ObservableQuery.Result<TData>, forceUpdate: () => void) => {\n      const previousData = resultRef.current?.data;\n\n      if (previousData && !equal(previousData, result.data)) {\n        previousDataRef.current = previousData as TData;\n      }\n\n      resultRef.current = result;\n\n      forceUpdate();\n    },\n    []\n  );\n\n  const observableResult = useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        const subscription = observable.subscribe((result) => {\n          if (!equal(resultRef.current, result)) {\n            updateResult(result, forceUpdate);\n          }\n        });\n\n        return () => {\n          subscription.unsubscribe();\n        };\n      },\n      [observable, updateResult]\n    ),\n    () => resultRef.current || initialResult,\n    () => initialResult\n  );\n\n  // We use useMemo here to make sure the eager methods have a stable identity.\n  const eagerMethods = React.useMemo(() => {\n    const eagerMethods: Record<string, any> = {};\n    for (const key of EAGER_METHODS) {\n      eagerMethods[key] = function (...args: any[]) {\n        invariant(\n          resultRef.current,\n          \"useLazyQuery: '%s' cannot be called before executing the query.\",\n          key\n        );\n\n        // @ts-expect-error this is just to generic to type\n        return observable[key](...args);\n      };\n    }\n\n    return eagerMethods as Pick<\n      useLazyQuery.Result<TData, TVariables>,\n      (typeof EAGER_METHODS)[number]\n    >;\n  }, [observable]);\n\n  React.useEffect(() => {\n    const updatedOptions: Partial<ObservableQuery.Options<TData, TVariables>> =\n      {\n        query,\n        errorPolicy: stableOptions?.errorPolicy,\n        refetchWritePolicy: stableOptions?.refetchWritePolicy,\n        returnPartialData: stableOptions?.returnPartialData,\n        notifyOnNetworkStatusChange: stableOptions?.notifyOnNetworkStatusChange,\n        nextFetchPolicy: options?.nextFetchPolicy,\n        skipPollAttempt: options?.skipPollAttempt,\n      };\n\n    // Wait to apply the changed fetch policy until after the execute\n    // function has been called. The execute function will handle setting the\n    // the fetch policy away from standby for us when called for the first time.\n    if (\n      observable.options.fetchPolicy !== \"standby\" &&\n      stableOptions?.fetchPolicy\n    ) {\n      updatedOptions.fetchPolicy = stableOptions.fetchPolicy;\n    }\n\n    observable.applyOptions(updatedOptions);\n  }, [\n    query,\n    observable,\n    stableOptions,\n    // Ensure inline functions don't suffer from stale closures by checking for\n    // these deps separately. @wry/equality doesn't compare function identity\n    // so `stableOptions` isn't updated when using inline functions.\n    options?.nextFetchPolicy,\n    options?.skipPollAttempt,\n  ]);\n\n  const execute: useLazyQuery.ExecFunction<TData, TVariables> =\n    React.useCallback(\n      (...args) => {\n        invariant(\n          !calledDuringRender(),\n          \"useLazyQuery: 'execute' should not be called during render. To start a query during render, use the 'useQuery' hook.\"\n        );\n\n        const [executeOptions] = args;\n\n        let fetchPolicy = observable.options.fetchPolicy;\n\n        if (fetchPolicy === \"standby\") {\n          fetchPolicy = observable.options.initialFetchPolicy;\n        }\n\n        return observable.reobserve({\n          fetchPolicy,\n          // If `variables` is not given, reset back to empty variables by\n          // ensuring the key exists in options\n          variables: executeOptions?.variables,\n          context: executeOptions?.context ?? {},\n        });\n      },\n      [observable, calledDuringRender]\n    );\n\n  const executeRef = React.useRef(execute);\n  useIsomorphicLayoutEffect(() => {\n    executeRef.current = execute;\n  });\n\n  const stableExecute = React.useCallback<typeof execute>(\n    (...args) => executeRef.current(...args),\n    []\n  );\n\n  const result = React.useMemo(() => {\n    const { partial, ...result } = observableResult;\n\n    return {\n      ...eagerMethods,\n      ...result,\n      client,\n      previousData: previousDataRef.current,\n      variables: observable.variables,\n      observable,\n      called: !!resultRef.current,\n    };\n  }, [client, observableResult, eagerMethods, observable]);\n\n  return [stableExecute, result as any];\n}\n\nconst initialResult: ObservableQuery.Result<any> = maybeDeepFreeze({\n  data: undefined,\n  dataState: \"empty\",\n  loading: false,\n  networkStatus: NetworkStatus.ready,\n  partial: true,\n});\n"], "names": [], "mappings": ";;AAsUA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AArUA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAkBA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAMA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AA4MA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB;IACpB,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;IACT,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;IACX,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;IACd,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB;AACnB,CAAU;AA+EV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAK1B,CALF,CAAA,CAAA,CAAA,CAK4D,EAC1D,CANF,CAAA,CAAA,CAAA,CAAA,CAAA,CAMqE,EANrE;IAQE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC;IAC/C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAQ,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD,CAAC;IACtD,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAgC,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE,CAAC;IACxE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAnC,CAAoC,CAApC,EAAuC,CAAvC,EAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,EAAE,CAAC,CAApD,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAC,CAAC;IAC3D,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAA3C,CAAA,CAA6C;IAE3C,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC;YACvB,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YACV,CAAN,CAAA,CAAA,CAAA,CAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAjC,CAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C;YACxC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;QAC5B,CAA0D,CAAC;IACzD;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,EAAxC,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC,CAA3D,CAAA,CAAA,CAAA,CAAA,CAAiE,CAAC;IAChE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAlC,EAAsC,CAAtC,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqE,CAAC;IAEpE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAA8B,EAAE;QAC5B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC;QACxB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CAAoC,CAAC;IACnC;IAEA,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CACpC,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAC0C,EAAE,CAD5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmE,EAAE,CADrE,EAAA;QAEM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAA5C,CAA8C,CAA9C,CAAA,CAAA,CAAkD;QAE5C,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA0B,CAAC,CAA3B,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAA0D,CAAC,EAAE;YACrD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD;QACjD;QAEA,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAgC;QAE1B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CAAmB;IACf,CAAC,EACD,CADJ,CACM,CACH;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAA/C,CACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEkB,EAAE,CAFpB,EAAA;QAGQ,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAC,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAyD,EAAE,CAA3D,EAAA;YACU,CAAV,EAAA,CAAc,CAAC,CAAf,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAApB,CAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,EAAE,CAAxC,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,EAAE;gBACrC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B,EAAE,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC;YACnC;QACF,CAAC,CAAC;QAEF,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,EAAkB,CAAlB,EAAA;YACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CAAoC;QAC5B,CAAC;IACH,CAAC,EACD,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB,EAAE,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+B,CAAC,CAC3B,EACD,CAHJ,EAGO,CAHP,EAGU,CAHV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGmB,CAAC,CAHpB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAG+B,CAH/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAG4C,EACxC,CAJJ,EAIO,CAJP,EAIU,CAJV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIuB,CACpB;IAED,CAAF,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,EAAwC,CAAxC,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8C,CAA9C,CAAgD;QAC5C,CAAJ,CAAA,EAAA,CAAS,CAAT,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,EAAA,CAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EAAE;YAC/B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAsB,EAAtB,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoC,CAApC,CAAA,CAAuC,CAAvC,CAAA,CAAA,CAAkD,EAAlD;iBACA,GAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CAAA,CAAA,CAAA,OAGU,CAHV,CAAA,EAIS;gBAED,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;gBACQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAA6B,CAAC,CAAC,CAA/B,CAAA,CAAkC,CAAlC,CAAA,CAAA,CAAsC,CAAC;YACjC,CAAC;QACH;QAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAC;IAEhB,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACM;YACE,CAAR,CAAA,CAAA,CAAA,CAAa;YACL,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C;YACvC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAAE,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAzC,CAA2C,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6D;YACrD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D;YACnD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EAAE,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAlD,CAAoD,CAApD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+E;YACvE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD;YACzC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD;QACjD,CAAO;QAEH,CAAJ,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CACM,CADN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgB,CAAC,CADjB,CAAA,CAAA,CAAA,CAAA,CAAA,CACwB,CAAC,CADzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EACyC,CADzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YAEM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAnB,CAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAC1B;YACA,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D;QACxD;QAEA,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;IACzC,CAAC,EAAE;QACD,CAAJ,CAAA,CAAA,CAAA,CAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc;QACV,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;QACb,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAX,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;QACxB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAX,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;IAC5B,CAAG,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAEU,CAFV,CAAA,CAAA,CAEc,EAAE,CAFhB,EAAA;SAGA,GAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GACU,CAAC,CADX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC6B,CAD7B,MAGS;QAED,CAAR,CAAA,CAAA,CAAA,EAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAA7B,EAAiC,CAAjC,CAAA,CAAA,CAAqC;QAE7B,CAAR,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD;QAEhD,CAAR,EAAA,CAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,EAAE;YAC7B,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6D;QACrD;QAEA,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC;YAC1B,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;YACX,CAAV,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAnC,CAAqC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C;YACpC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAjC,CAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA8C,CAA9C,CAAgD;QAChD,CAAS,CAAC;IACJ,CAAC,EACD,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB,EAAE,CADnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqC,CAAC,CACjC;IAEH,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC;IACxC,CAAF,CAAA,EAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAA4B,CAA5B,EAA+B,CAA/B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC;IAC9B,CAAC,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CACrC,CAAC,CADL,CAAA,CACQ,CADR,CAAA,CAAA,CACY,EAAE,CADd,EACiB,CADjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CAAC,CAD5B,CAAA,CAAA,CAAA,CAAA,CAAA,CACmC,CAAC,CADpC,CAAA,CACuC,CADvC,CAAA,CAAA,CAC2C,CAAC,EACxC,CAFJ,CAEM,CACH;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,EAAkC,CAAlC,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD;QAE/C,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB;YACf,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe;YACT,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY;YACN,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C;YACrC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;YAC/B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YACV,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAC,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC;QACjC,CAAK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,EAAE,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,EAAE,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD,CAAC,CAAC;IAExD,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC;AACvC;AAEA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmD,CAAnD,CAAA,EAAmD,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE,CAAlE,CAAmE;IACjE,CAAF,CAAA,CAAA,CAAM,EAAE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;IACf,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAClB,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAA,CAAgB;IACd,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAoC;IAClC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAe;AACf,CAAC,CAAC;"}