{"version": 3, "file": "useSubscription.cjs", "sources": ["../../../../src/react/hooks/useSubscription.ts"], "sourcesContent": ["import type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport { equal } from \"@wry/equality\";\nimport type { DocumentNode } from \"graphql\";\nimport * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DefaultContext,\n  ErrorLike,\n  ErrorPolicy,\n  FetchPolicy,\n  OperationVariables,\n} from \"@apollo/client\";\nimport type { MaybeMasked } from \"@apollo/client/masking\";\nimport type { DocumentationTypes as UtilityDocumentationTypes } from \"@apollo/client/utilities/internal\";\nimport type {\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\nimport { invariant } from \"@apollo/client/utilities/invariant\";\n\nimport { useDeepMemo } from \"./internal/useDeepMemo.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useSubscription {\n  import _self = useSubscription;\n  export namespace Base {\n    export interface Options<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: FetchPolicy;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#shouldResubscribe:member} */\n      shouldResubscribe?:\n        | boolean\n        | ((options: Options<TData, TVariables>) => boolean);\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#skip:member} */\n      skip?: boolean;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#extensions:member} */\n      extensions?: Record<string, any>;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onComplete:member} */\n      onComplete?: () => void;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onData:member} */\n      onData?: (options: OnDataOptions<TData>) => any;\n\n      /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#onError:member} */\n      onError?: (error: ErrorLike) => void;\n\n      /**\n       * {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#ignoreResults:member}\n       * @defaultValue `false`\n       */\n      ignoreResults?: boolean;\n    }\n  }\n\n  export type Options<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options<TData, TVariables> & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useSubscription {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TData, TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export interface Result<TData = unknown> {\n    /** {@inheritDoc @apollo/client!SubscriptionResultDocumentation#loading:member} */\n    loading: boolean;\n\n    /** {@inheritDoc @apollo/client!SubscriptionResultDocumentation#data:member} */\n    data?: MaybeMasked<TData>;\n\n    /** {@inheritDoc @apollo/client!SubscriptionResultDocumentation#error:member} */\n    error?: ErrorLike;\n\n    /**\n     * A function that when called will disconnect and reconnect the connection\n     * to the subscription. If the subscription is deduplicated, this will\n     * restart the connection for all deduplicated subscriptions.\n     */\n    restart: () => void;\n  }\n\n  export namespace DocumentationTypes {\n    namespace useSubscription {\n      export interface Result<TData = unknown> extends _self.Result<TData> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useSubscription:function(1)} */\n    export function useSubscription<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      options?: useSubscription.Options<TData, TVariables>\n    ): useSubscription.Result<TData>;\n  }\n\n  export type OnDataResult<TData = unknown> = Omit<Result<TData>, \"restart\">;\n\n  export interface OnDataOptions<TData = unknown> {\n    client: ApolloClient;\n    data: OnDataResult<TData>;\n  }\n\n  export interface OnSubscriptionDataOptions<TData = unknown> {\n    client: ApolloClient;\n    subscriptionData: OnDataResult<TData>;\n  }\n}\n\n/**\n * > Refer to the [Subscriptions](https://www.apollographql.com/docs/react/data/subscriptions/) section for a more in-depth overview of `useSubscription`.\n *\n * @example\n *\n * ```jsx\n * const COMMENTS_SUBSCRIPTION = gql`\n *   subscription OnCommentAdded($repoFullName: String!) {\n *     commentAdded(repoFullName: $repoFullName) {\n *       id\n *       content\n *     }\n *   }\n * `;\n *\n * function DontReadTheComments({ repoFullName }) {\n *   const {\n *     data: { commentAdded },\n *     loading,\n *   } = useSubscription(COMMENTS_SUBSCRIPTION, { variables: { repoFullName } });\n *   return <h4>New comment: {!loading && commentAdded.content}</h4>;\n * }\n * ```\n *\n * @remarks\n *\n * #### Consider using `onData` instead of `useEffect`\n *\n * If you want to react to incoming data, please use the `onData` option instead of `useEffect`.\n * State updates you make inside a `useEffect` hook might cause additional rerenders, and `useEffect` is mostly meant for side effects of rendering, not as an event handler.\n * State updates made in an event handler like `onData` might - depending on the React version - be batched and cause only a single rerender.\n *\n * Consider the following component:\n *\n * ```jsx\n * export function Subscriptions() {\n *   const { data, error, loading } = useSubscription(query);\n *   const [accumulatedData, setAccumulatedData] = useState([]);\n *\n *   useEffect(() => {\n *     setAccumulatedData((prev) => [...prev, data]);\n *   }, [data]);\n *\n *   return (\n *     <>\n *       {loading && <p>Loading...</p>}\n *       {JSON.stringify(accumulatedData, undefined, 2)}\n *     </>\n *   );\n * }\n * ```\n *\n * Instead of using `useEffect` here, we can re-write this component to use the `onData` callback function accepted in `useSubscription`'s `options` object:\n *\n * ```jsx\n * export function Subscriptions() {\n *   const [accumulatedData, setAccumulatedData] = useState([]);\n *   const { data, error, loading } = useSubscription(query, {\n *     onData({ data }) {\n *       setAccumulatedData((prev) => [...prev, data]);\n *     },\n *   });\n *\n *   return (\n *     <>\n *       {loading && <p>Loading...</p>}\n *       {JSON.stringify(accumulatedData, undefined, 2)}\n *     </>\n *   );\n * }\n * ```\n *\n * > ⚠️ **Note:** The `useSubscription` option `onData` is available in Apollo Client >= 3.7. In previous versions, the equivalent option is named `onSubscriptionData`.\n *\n * Now, the first message will be added to the `accumulatedData` array since `onData` is called _before_ the component re-renders. React 18 automatic batching is still in effect and results in a single re-render, but with `onData` we can guarantee each message received after the component mounts is added to `accumulatedData`.\n *\n * @param subscription - A GraphQL subscription document parsed into an AST by `gql`.\n * @param options - Options to control how the subscription is executed.\n * @returns Query result object\n */\nexport function useSubscription<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  subscription: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options = {} as useSubscription.Options<TData, TVariables>]: {} extends (\n    TVariables\n  ) ?\n    [options?: useSubscription.Options<NoInfer<TData>, NoInfer<TVariables>>]\n  : [options: useSubscription.Options<NoInfer<TData>, NoInfer<TVariables>>]\n): useSubscription.Result<TData> {\n  const client = useApolloClient(options.client);\n\n  const {\n    skip,\n    fetchPolicy,\n    errorPolicy,\n    shouldResubscribe,\n    context,\n    extensions,\n    ignoreResults,\n  } = options;\n  const variables = useDeepMemo(() => options.variables, [options.variables]);\n\n  const recreate = () =>\n    createSubscription(\n      client,\n      subscription,\n      variables,\n      fetchPolicy,\n      errorPolicy,\n      context,\n      extensions\n    );\n\n  let [observable, setObservable] = React.useState(\n    options.skip ? null : recreate\n  );\n\n  const recreateRef = React.useRef(recreate);\n  useIsomorphicLayoutEffect(() => {\n    recreateRef.current = recreate;\n  });\n\n  if (skip) {\n    if (observable) {\n      setObservable((observable = null));\n    }\n  } else if (\n    !observable ||\n    ((client !== observable.__.client ||\n      subscription !== observable.__.query ||\n      fetchPolicy !== observable.__.fetchPolicy ||\n      errorPolicy !== observable.__.errorPolicy ||\n      !equal(variables, observable.__.variables)) &&\n      (typeof shouldResubscribe === \"function\" ?\n        !!shouldResubscribe(options!)\n      : shouldResubscribe) !== false)\n  ) {\n    setObservable((observable = recreate()));\n  }\n\n  const optionsRef = React.useRef(options);\n  React.useEffect(() => {\n    optionsRef.current = options;\n  });\n\n  const fallbackLoading = !skip && !ignoreResults;\n  const fallbackResult = React.useMemo(\n    () => ({\n      loading: fallbackLoading,\n      error: void 0,\n      data: void 0,\n    }),\n    [fallbackLoading]\n  );\n\n  const ignoreResultsRef = React.useRef(ignoreResults);\n  useIsomorphicLayoutEffect(() => {\n    // We cannot reference `ignoreResults` directly in the effect below\n    // it would add a dependency to the `useEffect` deps array, which means the\n    // subscription would be recreated if `ignoreResults` changes\n    // As a result, on resubscription, the last result would be re-delivered,\n    // rendering the component one additional time, and re-triggering `onData`.\n    // The same applies to `fetchPolicy`, which results in a new `observable`\n    // being created. We cannot really avoid it in that case, but we can at least\n    // avoid it for `ignoreResults`.\n    ignoreResultsRef.current = ignoreResults;\n  });\n\n  const ret = useSyncExternalStore(\n    React.useCallback(\n      (update) => {\n        if (!observable) {\n          return () => {};\n        }\n\n        let subscriptionStopped = false;\n        const client = observable.__.client;\n        const subscription = observable.subscribe({\n          next(value) {\n            if (subscriptionStopped) {\n              return;\n            }\n\n            const result = {\n              loading: false,\n              data: value.data,\n              error: value.error,\n            };\n\n            observable.__.setResult(result);\n            if (!ignoreResultsRef.current) update();\n\n            if (result.error) {\n              optionsRef.current.onError?.(result.error);\n            } else if (optionsRef.current.onData) {\n              optionsRef.current.onData({\n                client,\n                data: result,\n              });\n            }\n          },\n          complete() {\n            observable.__.completed = true;\n            if (!subscriptionStopped && optionsRef.current.onComplete) {\n              optionsRef.current.onComplete();\n            }\n          },\n        });\n\n        return () => {\n          // immediately stop receiving subscription values, but do not unsubscribe\n          // until after a short delay in case another useSubscription hook is\n          // reusing the same underlying observable and is about to subscribe\n          subscriptionStopped = true;\n\n          setTimeout(() => subscription.unsubscribe());\n        };\n      },\n      [observable]\n    ),\n    () =>\n      observable && !skip && !ignoreResults ?\n        observable.__.result\n      : fallbackResult,\n    () => fallbackResult\n  );\n\n  const restart = React.useCallback(() => {\n    invariant(\n      !optionsRef.current.skip,\n      \"A subscription that is skipped cannot be restarted.\"\n    );\n    if (observable?.__.completed) {\n      setObservable(recreateRef.current());\n    } else {\n      observable?.restart();\n    }\n  }, [optionsRef, recreateRef, observable]);\n\n  return React.useMemo(() => ({ ...ret, restart }), [ret, restart]);\n}\n\ntype SubscriptionResult<TData> = Omit<useSubscription.Result<TData>, \"restart\">;\n\nfunction createSubscription<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  client: ApolloClient,\n  query: TypedDocumentNode<TData, TVariables>,\n  variables: TVariables | undefined,\n  fetchPolicy: FetchPolicy | undefined,\n  errorPolicy: ErrorPolicy | undefined,\n  context: DefaultContext | undefined,\n  extensions: Record<string, any> | undefined\n) {\n  const options = {\n    query,\n    variables,\n    fetchPolicy,\n    errorPolicy,\n    context,\n    extensions,\n  } as ApolloClient.SubscribeOptions<TData, TVariables>;\n  const __ = {\n    ...options,\n    client,\n    completed: false,\n    result: {\n      loading: true,\n      data: void 0,\n      error: void 0,\n    } as SubscriptionResult<TData>,\n    setResult(result: SubscriptionResult<TData>) {\n      __.result = result;\n    },\n  };\n\n  return Object.assign(client.subscribe(options), {\n    /**\n     * A tracking object to store details about the observable and the latest result of the subscription.\n     */\n    __,\n  });\n}\n"], "names": [], "mappings": ";;AAuNA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAtNA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAgBA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AA+GA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+EA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAI7B,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAImE,EACjE,CALF,CAAA,CAKK,CAAC,CALN,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAKgB,CALhB,CAKgE,CAIW,EAT3E;IAWE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAE9C,CAAF,CAAA,CAAA,CAAA,EAAQ,EACJ,CADJ,CAAA,CAAA,CACQ,EACJ,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEe,EACX,CAHJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGe,EACX,CAJJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIqB,EACjB,CALJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAKW,EACP,CANJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAMc,EACV,CAPJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAOiB,EAPjB,EAAA,EAQM,CARN,CAAA,CAAA,CAAA,CAAA,CAAA,CAQa;IACX,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAA/B,CAAgC,CAAhC,EAAmC,CAAnC,EAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD,EAAE,CAAC,CAA1D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE,CAAC,CAAlE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CAAC,CAAC;IAE3E,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,EAAsB,CAAtB,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACsB,CAChB,CAFN,CAAA,CAAA,CAAA,CAAA,CAEY,EACN,CAHN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGkB,EACZ,CAJN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIe,EACT,CALN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKiB,EACX,CANN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAMiB,EACX,CAPN,CAAA,CAAA,CAAA,CAAA,CAAA,CAOa,EACP,CARN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAQgB,CACX;IAEH,CAAF,CAAA,EAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAhC,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAC9C,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CACW,CAAC,CADZ,CAAA,CAAA,EACiB,EAAE,CADnB,CAAA,CAAA,EACwB,EAAE,CAD1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkC,CAC/B;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC;IAC1C,CAAF,CAAA,EAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAA4B,CAA5B,EAA+B,CAA/B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC;IAChC,CAAC,CAAC;IAEF,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAU,EAAE;QACR,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE;YACd,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkC,CAAlC,CAAA,CAAA,CAAsC,CAAC,CAAC;QACpC;IACF;IAAF,CAAA,CAAA,CAAA,EAAS,CAAT,EAAA,CACI,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QAEI,CAAC,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAC,CAAP,CAAA,EAAO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAZ,CAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,EAAhD,CAAA;YACM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA+C;gBACvC,CAAC,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC;gBAC9B,EAAE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAzB,CAAA,CAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAoC,CAAC,EACjC;QACA,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAA0C,CAAC,CAAC;IAC1C;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC;IACxC,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC;IAC9B,CAAC,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAAC,CAA3B,CAAA,CAAA,EAAA,CAAA,EAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD;IAC/C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAClC,CADJ,EACO,CADP,EACU,CAAC;QACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B;QACxB,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,EAAkB,CAAC;QACb,CAAN,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,EAAiB,CAAC;IAClB,CAAK,CAAC,EACF,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoB,CAAC,CAClB;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC;IACpD,CAAF,CAAA,EAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAA4B,CAA5B,EAA+B,CAA/B,EAAA;QACI,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;IAC1C,CAAC,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,EAAA,EAAc,CAAd,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAEa,EAAE,CAFf,EAAA;QAGQ,CAAR,EAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE;YACf,CAAV,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAjB,EAAoB,CAApB,EAAA,EAAwB,CAAC;QACjB;QAEA,CAAR,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAuC;QAC/B,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAA2C;QACnC,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC;YACxC,CAAV,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAoB,EAApB;gBACY,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EAAE;oBACvB,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA;gBACY;gBAEA,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B;oBACb,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAA4B;oBACd,CAAd,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAA8B;oBAChB,CAAd,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAgC;gBAChC,CAAa;gBAED,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;gBAC/B,CAAZ,EAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC;oBAAE,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAjD,CAAmD;gBAEvC,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAA4B,EAAE;oBAChB,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAuD,CAAC;gBAC5C;gBAAZ,CAAA,CAAA,CAAA,EAAmB,CAAnB,EAAA,CAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAgD,EAAE;oBACpC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC;wBACxB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB;wBACN,CAAhB,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAA4B;oBAC5B,CAAe,CAAC;gBACJ;YACF,CAAC;YACD,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAlB,EAAA;gBACY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsC,CAAtC,CAAA,CAAA,CAA0C;gBAC9B,CAAZ,EAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC,CAA3D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqE,EAAE;oBACzD,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAA3C,CAA6C;gBACjC;YACF,CAAC;QACX,CAAS,CAAC;QAEF,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,EAAkB,CAAlB,EAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAhC,CAAA,CAAA,CAAoC;YAE1B,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,EAAwB,CAAxB,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAnD,CAAqD,CAAC;QAC9C,CAAC;IACH,CAAC,EACD,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB,CAAC,CACb,EACD,CAHJ,EAGO,CAHP,EAIM,CAJN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAIoB,CAAC,CAJrB,CAAA,CAAA,EAAA,CAAA,EAI6B,CAAC,CAJ9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAI4C;QACpC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA;QACM,EAAE,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAClB,CADJ,EACO,CADP,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACwB,CACrB;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,EAAuC,CAAvC,EAAA;SACA,GAAI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GACM,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB,CAAC,CADlB,CAAA,CAAA,CAAA,CAAA,CAAA,CACyB,CAAC,CAD1B,CAAA,CAAA,MAGK;QACD,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAlB,CAAoB,CAApB,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAE;YAC5B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAvC,CAAyC,CAAC;QACtC;QAAJ,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAhB,CAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA2B;QACvB;IACF,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAAC;IAEzC,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,EAA0B,CAA1B,EAA6B,CAAC,EAAE,CAAhC,CAAA,CAAmC,CAAnC,CAAA,CAAsC,EAAE,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAiD,CAAC,EAAE,CAAC,CAArD,CAAA,CAAwD,EAAE,CAA1D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE,CAAC,CAAC;AACnE;AAIA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAIzB,CAJF,CAAA,CAAA,CAAA,CAAA,CAIsB,EACpB,CALF,CAAA,CAAA,CAAA,CAK6C,EAC3C,CANF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAMmC,EACjC,CAPF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAOsC,EACpC,CARF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAQsC,EACpC,CATF,CAAA,CAAA,CAAA,CAAA,CAAA,CASqC,EACnC,CAVF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAU6C,EAV7C;IAYE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB;QACd,CAAJ,CAAA,CAAA,CAAA,CAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;QACT,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;QACX,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;QACX,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QACP,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc;IACd,CAAuD;IACrD,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,EAAA,EAAa;QACT,CAAJ,CAAA,CAAO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc;QACV,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU;QACN,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,CAAoB;QAChB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,EAAE;YACN,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAmB;YACb,CAAN,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,EAAiB,CAAC;YACZ,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,EAAkB,CAAC;QACnB,CAAkC;QAC9B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAA+C,EAA/C;YACM,CAAN,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAwB;QACpB,CAAC;IACL,CAAG;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC,EAAE;QAC9C,CAAJ,CAAA;;SAEA,CAAA;QACI,CAAJ,CAAM;IACN,CAAG,CAAC;AACJ;"}