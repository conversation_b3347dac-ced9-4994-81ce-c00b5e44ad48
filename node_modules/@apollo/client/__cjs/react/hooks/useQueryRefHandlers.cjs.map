{"version": 3, "file": "useQueryRefHandlers.cjs", "sources": ["../../../../src/react/hooks/useQueryRefHandlers.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type { DataState, OperationVariables } from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport type { ApolloClient } from \"@apollo/client\";\nimport type { ObservableQuery } from \"@apollo/client\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport type {\n  FetchMoreFunction,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport {\n  assertWrappedQueryRef,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"@apollo/client/react/internal\";\n\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\nexport declare namespace useQueryRefHandlers {\n  export interface Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    /** {@inheritDoc @apollo/client!ObservableQuery#refetch:member(1)} */\n    refetch: RefetchFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)} */\n    fetchMore: FetchMoreFunction<TData, TVariables>;\n    /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n    subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useQueryRefHandlers:function(1)} */\n    export function useQueryRefHandlers<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      queryRef: QueryRef<TData, TVariables>\n    ): useQueryRefHandlers.Result<TData, TVariables>;\n  }\n}\n\n/**\n * A React hook that returns a `refetch` and `fetchMore` function for a given\n * `queryRef`.\n *\n * This is useful to get access to handlers for a `queryRef` that was created by\n * `createQueryPreloader` or when the handlers for a `queryRef` produced in\n * a different component are inaccessible.\n *\n * @example\n *\n * ```tsx\n * const MyComponent({ queryRef }) {\n *   const { refetch, fetchMore } = useQueryRefHandlers(queryRef);\n *\n *   // ...\n * }\n * ```\n *\n * @param queryRef - A `QueryRef` returned from `useBackgroundQuery`, `useLoadableQuery`, or `createQueryPreloader`.\n */\nexport function useQueryRefHandlers<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  queryRef: QueryRef<TData, TVariables, DataState<TData>[\"dataState\"]>\n): useQueryRefHandlers.Result<TData, TVariables> {\n  \"use no memo\";\n  const unwrapped = unwrapQueryRef(queryRef);\n  const clientOrObsQuery = useApolloClient(\n    unwrapped ?\n      // passing an `ObservableQuery` is not supported by the types, but it will\n      // return any truthy value that is passed in as an override so we cast the result\n      (unwrapped[\"observable\"] as any)\n    : undefined\n  ) as ApolloClient | ObservableQuery<TData>;\n\n  return wrapHook(\n    \"useQueryRefHandlers\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useQueryRefHandlers_,\n    clientOrObsQuery\n  )(queryRef);\n}\n\nfunction useQueryRefHandlers_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  queryRef: QueryRef<TData, TVariables, DataState<TData>[\"dataState\"]>\n): useQueryRefHandlers.Result<TData, TVariables> {\n  assertWrappedQueryRef(queryRef);\n  const [previousQueryRef, setPreviousQueryRef] = React.useState(queryRef);\n  const [wrappedQueryRef, setWrappedQueryRef] = React.useState(queryRef);\n  const internalQueryRef = unwrapQueryRef(queryRef);\n\n  // To ensure we can support React transitions, this hook needs to manage the\n  // queryRef state and apply React's state value immediately to the existing\n  // queryRef since this hook doesn't return the queryRef directly\n  if (previousQueryRef !== queryRef) {\n    setPreviousQueryRef(queryRef);\n    setWrappedQueryRef(queryRef);\n  } else {\n    updateWrappedQueryRef(queryRef, getWrappedPromise(wrappedQueryRef));\n  }\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = internalQueryRef.refetch(variables);\n\n      setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      const promise = internalQueryRef.fetchMore(\n        options as ObservableQuery.FetchMoreOptions<any, any>\n      );\n\n      setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n\n      return promise;\n    },\n    [internalQueryRef]\n  );\n\n  return {\n    refetch,\n    fetchMore,\n    // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n    subscribeToMore: internalQueryRef.observable\n      .subscribeToMore as SubscribeToMoreFunction<TData, TVariables>,\n  };\n}\n"], "names": [], "mappings": ";;AAkEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAlEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAWA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AA0BA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;CAmBA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAIjC,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIsE,EAJtE;IAME,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC;IAC1C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAA1C,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACc;QACR,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACM,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B;QACzB,EAAE,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAC6B;IAE1C,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyB;IACrB,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EACpB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoB,CACjB,CAAC,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEY,CAAC;AACb;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAI3B,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIsE,EAJtE;IAME,CAAF,CAAA,EAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAvB,CAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC;IAC/B,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,EAA9C,EAAkD,CAAlD,CAAA,CAAA,CAAA,CAAuD,CAAC,CAAxD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgE,CAAC,CAAjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE,CAAC;IACxE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,EAA5C,EAAgD,CAAhD,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC,CAA/D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CAAC;IACtE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAzC,CAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC;IAEjD,CAAF,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EAAE;QACjC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC;QAC7B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC;IAC9B;IAAF,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,EAAI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE,CAApC,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAArD,CAAsD,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqE,CAAC,CAAC;IACrE;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsD,CAAtD,CAAA,CAAA,CAAA,CAA2D,CAAC,CAA5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CACnE,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc,EAAE,CADhB,EAAA;QAEM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD,CAAC;QAEnD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAArC,CAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAC;QAElD,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAChB,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CAAC,CACnB;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0D,CAA1D,CAAA,CAAA,CAAA,CAA+D,CAAC,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CACvE,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CACY,EAAE,CADd,EAAA;QAEM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CACxC,CADR,CAAA,CAAA,CAAA,CAAA,CAAA,CAC6D,CACtD;QAED,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAArC,CAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAC;QAElD,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAChB,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CAAC,CACnB;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QACP,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;QACT,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoE;IACpE,CAAG;AACH;"}