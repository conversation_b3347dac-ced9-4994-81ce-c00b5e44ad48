{"version": 3, "file": "useSuspenseFragment.cjs", "sources": ["../../../../src/react/hooks/useSuspenseFragment.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataValue,\n  DocumentNode,\n  OperationVariables,\n  Reference,\n  StoreObject,\n  TypedDocumentNode,\n} from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type { FragmentType, MaybeMasked } from \"@apollo/client/masking\";\nimport type { FragmentKey } from \"@apollo/client/react/internal\";\nimport { getSuspenseCache } from \"@apollo/client/react/internal\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\n\nimport { __use } from \"./internal/__use.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\ntype From<TData> =\n  | StoreObject\n  | Reference\n  | FragmentType<NoInfer<TData>>\n  | string\n  | null;\n\nexport declare namespace useSuspenseFragment {\n  import _self = useSuspenseFragment;\n  export namespace Base {\n    export type Options<TData, TVariables extends OperationVariables> = {\n      /**\n       * A GraphQL document created using the `gql` template string tag from\n       * `graphql-tag` with one or more fragments which will be used to determine\n       * the shape of data to read. If you provide more than one fragment in this\n       * document then you must also specify `fragmentName` to select a single.\n       */\n      fragment: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n      /**\n       * The name of the fragment in your GraphQL document to be used. If you do\n       * not provide a `fragmentName` and there is only one fragment in your\n       * `fragment` document then that fragment will be used.\n       */\n      fragmentName?: string;\n      from: From<TData>;\n      // Override this field to make it optional (default: true).\n      optimistic?: boolean;\n      /**\n       * The instance of `ApolloClient` to use to look up the fragment.\n       *\n       * By default, the instance that's passed down via context is used, but you\n       * can provide a different instance here.\n       *\n       * @docGroup 1. Operation options\n       */\n      client?: ApolloClient;\n    };\n  }\n  export type Options<\n    TData,\n    TVariables extends OperationVariables,\n  > = Base.Options<TData, TVariables> & VariablesOption<NoInfer<TVariables>>;\n\n  export namespace DocumentationTypes {\n    export namespace useSuspenseFragment {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TData, TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export interface Result<TData> {\n    data: DataValue.Complete<MaybeMasked<TData>>;\n  }\n  export namespace DocumentationTypes {\n    export namespace useSuspenseFragment {\n      export interface Result<TData = unknown> extends _self.Result<TData> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\n    export function useSuspenseFragment<\n      TData,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      options: useSuspenseFragment.Options<TData, TVariables>\n    ): useSuspenseFragment.Result<TData>;\n  }\n}\n\nconst NULL_PLACEHOLDER = [] as unknown as [\n  FragmentKey,\n  Promise<MaybeMasked<any> | null>,\n];\n\n/** #TODO documentation */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables> & {\n    from: NonNullable<From<TData>>;\n  }\n): useSuspenseFragment.Result<TData>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables> & {\n    from: null;\n  }\n): useSuspenseFragment.Result<null>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables> & {\n    from: From<TData>;\n  }\n): useSuspenseFragment.Result<TData | null>;\n\n/** {@inheritDoc @apollo/client/react!useSuspenseFragment:function(1)} */\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables>\n): useSuspenseFragment.Result<TData>;\n\nexport function useSuspenseFragment<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables>\n): useSuspenseFragment.Result<TData | null> {\n  \"use no memo\";\n  return wrapHook(\n    \"useSuspenseFragment\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseFragment_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(options);\n}\n\nfunction useSuspenseFragment_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: useSuspenseFragment.Options<TData, TVariables>\n): useSuspenseFragment.Result<TData | null> {\n  const client = useApolloClient(options.client);\n  const { from, variables } = options;\n  const { cache } = client;\n\n  const id = React.useMemo(\n    () =>\n      typeof from === \"string\" ? from\n      : from === null ? null\n      : cache.identify(from),\n    [cache, from]\n  ) as string | null;\n\n  const fragmentRef =\n    id === null ? null : (\n      getSuspenseCache(client).getFragmentRef(\n        [id, options.fragment, canonicalStringify(variables)],\n        client,\n        { ...options, variables: variables as TVariables, from: id }\n      )\n    );\n\n  let [current, setPromise] = React.useState<\n    [FragmentKey, Promise<MaybeMasked<TData> | null>]\n  >(\n    fragmentRef === null ? NULL_PLACEHOLDER : (\n      [fragmentRef.key, fragmentRef.promise]\n    )\n  );\n\n  React.useEffect(() => {\n    if (fragmentRef === null) {\n      return;\n    }\n\n    const dispose = fragmentRef.retain();\n    const removeListener = fragmentRef.listen((promise) => {\n      setPromise([fragmentRef.key, promise]);\n    });\n\n    return () => {\n      dispose();\n      removeListener();\n    };\n  }, [fragmentRef]);\n\n  if (fragmentRef === null) {\n    return { data: null };\n  }\n\n  if (current[0] !== fragmentRef.key) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    current[0] = fragmentRef.key;\n    current[1] = fragmentRef.promise;\n  }\n\n  const data = __use(current[1]);\n\n  return { data };\n}\n"], "names": [], "mappings": ";;AA8IA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AA9IA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAWA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAGA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAOA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AA4EA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAGC;AAwCD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAIjC,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAIyD,EAJzD;IAME,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyB;IACrB,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EACpB,CADJ,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CADnB,CACoB,CADpB,CAAA,CAAA,CAAA,CAAA,EAC2B,CAD3B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EACuC,CADvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACgD,EAAE,CADlD,CAAA,CAAA,CAAA,CAAA,CAAA,CACyD,CAAC,CAD1D,CAAA,CAAA,CAAA,CAAA,EACiE,EAAE,CADnE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4E,CAAC,CAC1E,CAAC,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAEW,CAAC;AACZ;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAI3B,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAIyD,EAJzD;IAME,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;IACnC,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,EAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B;IAExB,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,EAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CACtB,CADJ,EACO,CADP,EAEM,CAFN,CAAA,CAAA,CAAA,CAAA,EAEa,CAFb,CAAA,CAAA,EAAA,CAAA,CAAA,EAEsB,CAFtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAE+B,EAAE,CAFjC,CAAA,CAAA;QAGM,EAAE,CAAR,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,EAAsB,EAAE,CAAxB,CAAA,CAAA;YACM,EAAE,CAAR,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAA2B,CAAC,EACxB,CAAC,CADL,CAAA,CAAA,CAAA,CACU,EAAE,CADZ,CAAA,CAAA,CACgB,CAAC,CACG;IAElB,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACI,CADJ,EAAA,CAAA,CAAA,EACW,CADX,CAAA,CAAA,EACgB,EAAE,CADlB,CAAA,CAAA,EACuB,EAAE,CACnB,CAFN,CAAA,EAEM,CAFN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEsB,CAFtB,CAEuB,CAFvB,CAAA,CAAA,CAAA,CAAA,CAE6B,CAAC,CAAC,CAF/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE6C,CACrC,CAAC,CAHT,CAGW,EAAE,CAHb,CAAA,CAAA,CAAA,CAAA,CAAA,CAGoB,CAAC,CAHrB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAG6B,EAAE,CAH/B,CAAA,EAG+B,CAH/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGiD,CAHjD,CAGkD,CAHlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAG2D,CAAC,CAAC,EACrD,CAJR,CAAA,CAAA,CAAA,CAAA,CAIc,EACN,EAAE,CALV,CAAA,CAKa,CALb,CAAA,CAAA,CAAA,CAAA,CAAA,CAKoB,EAAE,CALtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAK+B,EAAE,CALjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKwD,EAAE,CAL1D,CAAA,CAAA,CAK8D,EAAE,CALhE,EAAA,CAKoE,CAC7D,CACF;IAEH,CAAF,CAAA,EAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAA1B,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAGxC,CAHJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAGoB,CAHpB,CAAA,CAAA,EAGyB,EAAE,CAH3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAG4C,EAAE,CACxC,CAAC,CAJP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIkB,CAAC,CAJnB,CAAA,CAIsB,EAAE,CAJxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAImC,CAAC,CAJpC,CAAA,CAAA,CAAA,CAAA,CAAA,CAI2C,CAAC,CACvC,CACF;IAED,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAA4B,EAAE;YACxB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA;QACI;QAEA,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAtC,CAAwC;QACpC,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,EAAE,CAAxD,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAiC,EAAE,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAAC;QACxC,CAAC,CAAC;QAEF,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,EAAc,CAAd,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAb,CAAe;YACT,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAApB,CAAsB;QAClB,CAAC;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAC;IAEjB,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAsB,CAAtB,CAAA,CAAA,CAA0B,EAAE;QACxB,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,EAAE,CAAb,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,EAAA,CAAyB;IACvB;IAEA,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAC,EAAf,CAAA,CAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAoC,EAAE;QAClC,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAC,EAAb,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAgC;QAC5B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAC,EAAb,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC;IAClC;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,EAAA,EAAe,CAAf,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAApB,CAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAAC,CAAC,CAAC;IAE9B,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,EAAE,CAAX,CAAA,CAAA,EAAA,CAAiB;AACjB;"}