{"version": 3, "file": "useFragment.cjs", "sources": ["../../../../src/react/hooks/useFragment.ts"], "sourcesContent": ["import equal from \"@wry/equality\";\nimport * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataValue,\n  DocumentNode,\n  GetDataState,\n  OperationVariables,\n  TypedDocumentNode,\n} from \"@apollo/client\";\nimport type {\n  Cache,\n  MissingTree,\n  Reference,\n  StoreObject,\n} from \"@apollo/client/cache\";\nimport type { FragmentType, MaybeMasked } from \"@apollo/client/masking\";\nimport type { NoInfer } from \"@apollo/client/utilities/internal\";\n\nimport { useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useFragment {\n  import _self = useFragment;\n  export interface Options<TData, TVariables extends OperationVariables> {\n    /**\n     * A GraphQL document created using the `gql` template string tag from\n     * `graphql-tag` with one or more fragments which will be used to determine\n     * the shape of data to read. If you provide more than one fragment in this\n     * document then you must also specify `fragmentName` to select a single.\n     */\n    fragment: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n    /**\n     * The name of the fragment in your GraphQL document to be used. If you do\n     * not provide a `fragmentName` and there is only one fragment in your\n     * `fragment` document then that fragment will be used.\n     */\n    fragmentName?: string;\n\n    /**\n     * Any variables that the GraphQL query may depend on.\n     */\n    variables?: NoInfer<TVariables>;\n\n    /**\n     * An object containing a `__typename` and primary key fields (such as `id`) identifying the entity object from which the fragment will be retrieved, or a `{ __ref: \"...\" }` reference, or a `string` ID (uncommon).\n     */\n    from:\n      | StoreObject\n      | Reference\n      | FragmentType<NoInfer<TData>>\n      | string\n      | null;\n\n    /**\n     * Whether to read from optimistic or non-optimistic cache data. If\n     * this named option is provided, the optimistic parameter of the\n     * readQuery method can be omitted.\n     *\n     * @defaultValue true\n     */\n    optimistic?: boolean;\n\n    /**\n     * The instance of `ApolloClient` to use to look up the fragment.\n     *\n     * By default, the instance that's passed down via context is used, but you\n     * can provide a different instance here.\n     *\n     * @docGroup 1. Operation options\n     */\n    client?: ApolloClient;\n  }\n\n  namespace DocumentationTypes {\n    namespace useFragment {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends _self.Options<TData, TVariables> {}\n    }\n  }\n\n  // TODO: Update this to return `null` when there is no data returned from the\n  // fragment.\n  export type Result<TData> =\n    | ({\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#complete:member} */\n        complete: true;\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#missing:member} */\n        missing?: never;\n      } & GetDataState<MaybeMasked<TData>, \"complete\">)\n    | ({\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#complete:member} */\n        complete: false;\n        /** {@inheritDoc @apollo/client/react!useFragment.DocumentationTypes.useFragment.Result#missing:member} */\n        missing?: MissingTree;\n      } & GetDataState<MaybeMasked<TData>, \"partial\">);\n\n  export namespace DocumentationTypes {\n    namespace useFragment {\n      export interface Result<TData> {\n        data: MaybeMasked<TData> | DataValue.Partial<MaybeMasked<TData>>;\n        complete: boolean;\n        /**\n         * A tree of all `MissingFieldError` messages reported during fragment reading, where the branches of the tree indicate the paths of the errors within the query result.\n         */\n        missing?: MissingTree;\n      }\n    }\n  }\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useFragment:function(1)} */\n    export function useFragment<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >({\n      fragment,\n      from,\n      fragmentName,\n      variables,\n      optimistic,\n      client,\n    }: useFragment.Options<TData, TVariables>): useFragment.Result<TData>;\n  }\n}\n\n/**\n * `useFragment` represents a lightweight live binding into the Apollo Client Cache and enables Apollo Client to broadcast very specific fragment results to individual components. This hook returns an always-up-to-date view of whatever data the cache currently contains for a given fragment. `useFragment` never triggers network requests of its own.\n *\n * Note that the `useQuery` hook remains the primary hook responsible for querying and populating data in the cache ([see the API reference](./hooks#usequery)). As a result, the component reading the fragment data via `useFragment` is still subscribed to all changes in the query data, but receives updates only when that fragment's specific data change.\n *\n * To view a `useFragment` example, see the [Fragments](https://www.apollographql.com/docs/react/data/fragments#usefragment) page.\n */\nexport function useFragment<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(options: useFragment.Options<TData, TVariables>): useFragment.Result<TData> {\n  \"use no memo\";\n  return wrapHook(\n    \"useFragment\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useFragment_,\n    useApolloClient(options.client)\n  )(options);\n}\n\nfunction useFragment_<TData, TVariables extends OperationVariables>(\n  options: useFragment.Options<TData, TVariables>\n): useFragment.Result<TData> {\n  const client = useApolloClient(options.client);\n  const { cache } = client;\n  const { from, ...rest } = options;\n\n  // We calculate the cache id seperately from `stableOptions` because we don't\n  // want changes to non key fields in the `from` property to affect\n  // `stableOptions` and retrigger our subscription. If the cache identifier\n  // stays the same between renders, we want to reuse the existing subscription.\n  const id = React.useMemo(\n    () =>\n      typeof from === \"string\" ? from\n      : from === null ? null\n      : cache.identify(from),\n    [cache, from]\n  );\n\n  const stableOptions = useDeepMemo(() => ({ ...rest, from: id! }), [rest, id]);\n\n  // Since .next is async, we need to make sure that we\n  // get the correct diff on the next render given new diffOptions\n  const diff = React.useMemo(() => {\n    const { fragment, fragmentName, from, optimistic = true } = stableOptions;\n\n    if (from === null) {\n      return {\n        result: diffToResult({\n          result: {},\n          complete: false,\n        } as Cache.DiffResult<TData>),\n      };\n    }\n\n    const { cache } = client;\n    const diff = cache.diff<TData, TVariables>({\n      ...stableOptions,\n      returnPartialData: true,\n      id: from,\n      query: cache[\"getFragmentDoc\"](\n        client[\"transform\"](fragment),\n        fragmentName\n      ),\n      optimistic,\n    });\n\n    return {\n      result: diffToResult<TData>({\n        ...diff,\n        result: client[\"queryManager\"].maskFragment({\n          fragment,\n          fragmentName,\n          // TODO: Revert to `diff.result` once `useFragment` supports `null` as\n          // valid return value\n          data: diff.result === null ? {} : diff.result,\n        }) as any,\n      }),\n    };\n  }, [client, stableOptions]);\n\n  // Used for both getSnapshot and getServerSnapshot\n  const getSnapshot = React.useCallback(() => diff.result, [diff]);\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        let lastTimeout = 0;\n\n        const subscription =\n          stableOptions.from === null ?\n            null\n          : client.watchFragment(stableOptions).subscribe({\n              next: (result) => {\n                // Avoid unnecessarily rerendering this hook for the initial result\n                // emitted from watchFragment which should be equal to\n                // `diff.result`.\n                if (equal(result, diff.result)) return;\n                diff.result = result;\n                // If we get another update before we've re-rendered, bail out of\n                // the update and try again. This ensures that the relative timing\n                // between useQuery and useFragment stays roughly the same as\n                // fixed in https://github.com/apollographql/apollo-client/pull/11083\n                clearTimeout(lastTimeout);\n                lastTimeout = setTimeout(forceUpdate) as any;\n              },\n            });\n        return () => {\n          subscription?.unsubscribe();\n          clearTimeout(lastTimeout);\n        };\n      },\n      [client, stableOptions, diff]\n    ),\n    getSnapshot,\n    getSnapshot\n  );\n}\n\nfunction diffToResult<TData>(\n  diff: Cache.DiffResult<TData>\n): useFragment.Result<TData> {\n  const result = {\n    data: diff.result,\n    complete: !!diff.complete,\n    dataState: diff.complete ? \"complete\" : \"partial\",\n  } as useFragment.Result<TData>; // TODO: Remove assertion once useFragment returns null\n\n  if (diff.missing) {\n    result.missing = diff.missing.missing;\n  }\n\n  return result;\n}\n"], "names": [], "mappings": ";;AAyIA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAzIA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAmBA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6BAAA,CAAA;AA4GA,CAAA,CAAA;;;;;;CAMA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAGzB,CAHF,CAAA,CAAA,CAAA,CAAA,CAAA,CAGiD,EAHjD;IAIE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiB;IACb,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EACZ,CADJ,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CADnB,CACoB,CADpB,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CAAC,CAD5B,CAAA,CAAA,CAAA,CAAA,CACkC,CAAC,CAChC,CAAC,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAEW,CAAC;AACZ;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CACnB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CACiD,EADjD;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,EAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B;IACxB,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAmB,CAAnB,CAAA,CAAA,EAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;IAEjC,CAAF,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,EAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CACtB,CADJ,EACO,CADP,EAEM,CAFN,CAAA,CAAA,CAAA,CAAA,EAEa,CAFb,CAAA,CAAA,EAAA,CAAA,CAAA,EAEsB,CAFtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAE+B,EAAE,CAFjC,CAAA,CAAA;QAGM,EAAE,CAAR,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,EAAsB,EAAE,CAAxB,CAAA,CAAA;YACM,EAAE,CAAR,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAA2B,CAAC,EACxB,CAAC,CADL,CAAA,CAAA,CAAA,CACU,EAAE,CADZ,CAAA,CAAA,CACgB,CAAC,CACd;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAnC,CAAoC,CAApC,EAAuC,CAAvC,EAA0C,CAAC,EAAE,CAA7C,CAAA,CAAgD,CAAhD,CAAA,CAAA,CAAoD,EAAE,CAAtD,CAAA,CAAA,CAA0D,EAAE,CAA5D,EAAA,CAAiE,CAAC,EAAE,CAAC,CAArE,CAAA,CAAA,CAAyE,EAAE,CAA3E,CAA6E,CAAC,CAAC;IAE7E,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,EAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,EAAgC,CAAhC,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE,CAApC,CAAA,CAAA,CAAwC,EAAE,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuD,CAAvD,CAAA,CAAA,EAAA,EAAA,EAAgE,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6E;QAEzE,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAqB,EAAE;YACjB,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa;gBACL,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC;oBACnB,CAAV,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE,CAAlB,CAAoB;oBACV,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAA,CAAyB;gBACzB,CAAoC,CAAC;YACrC,CAAO;QACH;QAEA,CAAJ,CAAA,CAAA,CAAA,EAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,EAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAA4B;QACxB,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAA2B,CAAoB;YACzC,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAChB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAA6B;YACvB,CAAN,CAAQ,EAAE,CAAV,CAAA,CAAA,CAAc;YACR,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAC5B,CADR,CAAA,CAAA,CAAA,CAAA,CACc,CAAC,CADf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC0B,CAAC,CAAC,CAD5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoC,CAAC,EAC7B,CAFR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEoB,CACb;YACD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;QAChB,CAAK,CAAC;QAEF,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAQ;gBAC1B,CAAR,CAAA,CAAW,CAAX,CAAA,CAAA,CAAe;gBACP,CAAR,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC;oBAC1C,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;oBACR,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;oBACZ,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;oBACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;oBACU,CAAV,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAgC,CAAhC,CAAA,CAAA,EAAqC,EAAE,CAAvC,EAA0C,EAAE,CAA5C,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAuD;gBACvD,CAAS,CAAQ;YACjB,CAAO,CAAC;QACR,CAAK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAAC;IAE3B,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,CAAA,CAAA,CAAkD,CAAC,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAyD,EAAE,CAAC,CAA5D,CAAA,CAAA,CAAgE,CAAC,CAAC;IAEhE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAA7B,CACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEkB,EAAE,CAFpB,EAAA;QAGQ,CAAR,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAAC;QAEnB,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuB,CAAC,CADxB,CAAA,CAAA,EAAA,CAAA,CAAA,EACiC,CADjC,CAAA,CAAA,EACsC;YAC1B,CAAZ,CAAA,CAAA;YACU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAAC,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC;gBAC5C,CAAd,CAAA,CAAA,CAAkB,EAAE,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE,CAA7B,EAAA;oBACgB,CAAhB,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACgB,CAAhB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;oBACgB,CAAhB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACgB,CAAhB,EAAA,CAAoB,CAApB,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAE,CAAlC,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC;wBAAE,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAA;oBACgB,CAAhB,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAoC;oBACpB,CAAhB,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;oBACgB,CAAhB,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACgB,CAAhB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;oBACgB,CAAhB,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;oBACgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC;oBACzB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAQ;gBAC9C,CAAC;YACf,CAAa,CAAC;QACN,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,EAAkB,CAAlB,EAAA;YACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAtB,CAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAnC,CAAqC;YAC3B,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC;QAC3B,CAAC;IACH,CAAC,EACD,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CACa,EAAE,CADf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4B,EAAE,CAD9B,CAAA,CAAA,CACkC,CAAC,CAC9B,EACD,CAHJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGe,EACX,CAJJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIe,CACZ;AACH;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CACnB,CADF,CAAA,CAAA,CAC+B,EAD/B;IAGE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB;QACb,CAAJ,CAAA,CAAA,CAAQ,EAAE,CAAV,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAqB;QACjB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAC,CAAC,CAAhB,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B;QACzB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA6B,EAAE,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA0C,EAAE,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD;IACrD,CAAgC,EAAE,CAAlC,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IAEE,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE;QAChB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC;IACvC;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe;AACf;"}