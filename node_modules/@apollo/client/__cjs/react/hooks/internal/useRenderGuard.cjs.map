{"version": 3, "file": "useRenderGuard.cjs", "sources": ["../../../../../src/react/hooks/internal/useRenderGuard.ts"], "sourcesContent": ["import * as React from \"react\";\n\nlet Ctx: React.Context<null>;\n\nfunction noop() {}\nexport function useRenderGuard() {\n  if (!Ctx) {\n    // we want the intialization to be lazy because `createContext` would error on import in a RSC\n    Ctx = React.createContext(null);\n  }\n\n  return React.useCallback(\n    /**\n     * @returns true if the hook was called during render\n     */ () => {\n      const orig = console.error;\n      try {\n        console.error = noop;\n\n        /**\n         * `useContext` can be called conditionally during render, so this is safe.\n         * (Also, during render we would want to throw as a reaction to this anyways, so it\n         * wouldn't even matter if we got the order of hooks mixed up...)\n         *\n         * They cannot however be called outside of Render, and that's what we're testing here.\n         *\n         * Different versions of React have different behaviour on an invalid hook call:\n         *\n         * React 16.8 - 17: throws an error\n         * https://github.com/facebook/react/blob/2b93d686e359c7afa299e2ec5cf63160a32a1155/packages/react/src/ReactHooks.js#L18-L26\n         *\n         * React 18 & 19: `console.error` in development, then `resolveDispatcher` returns `null` and a member access on `null` throws.\n         * https://github.com/facebook/react/blob/58e8304483ebfadd02a295339b5e9a989ac98c6e/packages/react/src/ReactHooks.js#L28-L35\n         */\n        React[\"useContext\" /* hide this from the linter */](Ctx);\n        return true;\n      } catch (e) {\n        return false;\n      } finally {\n        console.error = orig;\n      }\n    },\n    []\n  );\n}\n"], "names": [], "mappings": ";;AAKA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AALA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,EAAI,CAAJ,CAAA,CAA4B;AAE5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAa,CAAb,EAAA,EAAiB;AACjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAA9B,EAAA;IACE,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAU,EAAE;QACR,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;QACI,CAAJ,CAAA,EAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAkC,CAAC;IACjC;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B;IACtB,CAAJ,CAAA;;KAEA,CAAA,EAAQ,CAAR,EAAW,CAAX,EAAA;QACM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAgC;QAC1B,CAAN,CAAA,EAAU;YACF,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAA4B;YAEpB,CAAR,CAAA;;;;;;;;;;;;;;aAcA,CAAA;YACQ,CAAR,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA2B,CAA3B,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAA0D,CAAC,CAAC,CAA5D,CAAA,CAA+D,CAAC;YACxD,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAmB;QACb;QAAE,CAAR,CAAA,CAAA,CAAA,EAAA,CAAe,CAAC,EAAE;YACV,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAoB;QACd;QAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB;YACR,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAA4B;QACtB;IACF,CAAC,EACD,CADJ,CACM,CACH;AACH;"}