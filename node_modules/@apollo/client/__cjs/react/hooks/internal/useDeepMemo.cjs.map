{"version": 3, "file": "useDeepMemo.cjs", "sources": ["../../../../../src/react/hooks/internal/useDeepMemo.ts"], "sourcesContent": ["import { equal } from \"@wry/equality\";\nimport type { DependencyList } from \"react\";\nimport * as React from \"react\";\n\nexport function useDeepMemo<TValue>(\n  memoFn: () => TValue,\n  deps: DependencyList\n) {\n  const ref = React.useRef<{ deps: DependencyList; value: TValue }>(void 0);\n  if (!ref.current || !equal(ref.current.deps, deps)) {\n    ref.current = { value: memoFn(), deps };\n  }\n  return ref.current.value;\n}\n"], "names": [], "mappings": ";;AAIA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAJA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CACzB,CADF,CAAA,CAAA,CAAA,CAAA,CACsB,EACpB,CAFF,CAAA,CAAA,CAEsB,EAFtB;IAIE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,EAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B,CAA0C,CAApE,CAAA,CAAA,EAAyE,CAAC,CAAC;IACzE,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAsB,CAAC,CAAvB,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAA5B,CAA6B,CAA7B,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAA6C,EAAE,CAA/C,CAAA,CAAA,CAAmD,CAAC,EAAE;QAClD,CAAJ,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,EAAE,CAApB,CAAA,CAAA,CAAA,CAAyB,EAAE,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAjC,CAAmC,EAAE,CAArC,CAAA,CAAA,EAAA,CAA2C;IACzC;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAA0B;AAC1B;"}