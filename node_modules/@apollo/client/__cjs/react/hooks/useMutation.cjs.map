{"version": 3, "file": "useMutation.cjs", "sources": ["../../../../src/react/hooks/useMutation.ts"], "sourcesContent": ["import type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport { equal } from \"@wry/equality\";\nimport * as React from \"react\";\n\nimport type {\n  ApolloCache,\n  ApolloClient,\n  DefaultContext,\n  DocumentNode,\n  ErrorLike,\n  ErrorPolicy,\n  InternalRefetchQueriesInclude,\n  MaybeMasked,\n  MutationFetchPolicy,\n  MutationQueryReducersMap,\n  MutationUpdaterFunction,\n  NormalizedExecutionResult,\n  OnQueryUpdated,\n  OperationVariables,\n  Unmasked,\n} from \"@apollo/client\";\nimport type { IgnoreModifier } from \"@apollo/client/cache\";\nimport type { NoInfer, Prettify } from \"@apollo/client/utilities/internal\";\nimport { mergeOptions } from \"@apollo/client/utilities/internal\";\n\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\n\ntype MakeRequiredVariablesOptional<\n  TVariables extends OperationVariables,\n  TConfiguredVariables extends Partial<TVariables>,\n> = Prettify<\n  {\n    [K in keyof TVariables as K extends keyof TConfiguredVariables ? K\n    : never]?: TVariables[K];\n  } & Omit<TVariables, keyof TConfiguredVariables>\n>;\n\nexport declare namespace useMutation {\n  export interface Options<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n    TConfiguredVariables extends Partial<TVariables> = Partial<TVariables>,\n  > {\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#optimisticResponse:member} */\n    optimisticResponse?:\n      | Unmasked<NoInfer<TData>>\n      | ((\n          vars: TVariables,\n          { IGNORE }: { IGNORE: IgnoreModifier }\n        ) => Unmasked<NoInfer<TData>> | IgnoreModifier);\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#updateQueries:member} */\n    updateQueries?: MutationQueryReducersMap<TData>;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#refetchQueries:member} */\n    refetchQueries?:\n      | ((\n          result: NormalizedExecutionResult<Unmasked<TData>>\n        ) => InternalRefetchQueriesInclude)\n      | InternalRefetchQueriesInclude;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#awaitRefetchQueries:member} */\n    awaitRefetchQueries?: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#update:member} */\n    update?: MutationUpdaterFunction<TData, TVariables, TCache>;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onQueryUpdated:member} */\n    onQueryUpdated?: OnQueryUpdated<any>;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#errorPolicy:member} */\n    errorPolicy?: ErrorPolicy;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n    variables?: TConfiguredVariables;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#context:member} */\n    context?: DefaultContext;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#fetchPolicy:member} */\n    fetchPolicy?: MutationFetchPolicy;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#keepRootFields:member} */\n    keepRootFields?: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#client:member} */\n    client?: ApolloClient;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n    notifyOnNetworkStatusChange?: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onCompleted:member} */\n    onCompleted?: (\n      data: MaybeMasked<TData>,\n      clientOptions?: Options<TData, TVariables, TCache>\n    ) => void;\n\n    /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#onError:member} */\n    onError?: (\n      error: ErrorLike,\n      clientOptions?: Options<TData, TVariables, TCache>\n    ) => void;\n  }\n\n  export interface Result<TData = unknown> {\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#data:member} */\n    data: MaybeMasked<TData> | null | undefined;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#error:member} */\n    error: ErrorLike | undefined;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#loading:member} */\n    loading: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#called:member} */\n    called: boolean;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#client:member} */\n    client: ApolloClient;\n\n    /** {@inheritDoc @apollo/client!MutationResultDocumentation#reset:member} */\n    reset: () => void;\n  }\n\n  export type ResultTuple<\n    TData,\n    TVariables extends OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n  > = [\n    mutate: MutationFunction<TData, TVariables, TCache>,\n    result: Result<TData>,\n  ];\n\n  export type MutationFunction<\n    TData,\n    TVariables extends OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n  > = (\n    ...[options]: {} extends TVariables ?\n      [\n        options?: MutationFunctionOptions<TData, TVariables, TCache> & {\n          /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n          variables?: TVariables;\n        },\n      ]\n    : [\n        options: MutationFunctionOptions<TData, TVariables, TCache> & {\n          /** {@inheritDoc @apollo/client!MutationOptionsDocumentation#variables:member} */\n          variables: TVariables;\n        },\n      ]\n  ) => Promise<ApolloClient.MutateResult<MaybeMasked<TData>>>;\n\n  export type MutationFunctionOptions<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TCache extends ApolloCache = ApolloCache,\n  > = Options<TData, TVariables, TCache>;\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useMutation:function(1)} */\n    export function useMutation<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options?: useMutation.Options<TData, TVariables>\n    ): useMutation.ResultTuple<TData, TVariables>;\n  }\n}\n\n/**\n * > Refer to the [Mutations](https://www.apollographql.com/docs/react/data/mutations/) section for a more in-depth overview of `useMutation`.\n *\n * @example\n *\n * ```jsx\n * import { gql, useMutation } from \"@apollo/client\";\n *\n * const ADD_TODO = gql`\n *   mutation AddTodo($type: String!) {\n *     addTodo(type: $type) {\n *       id\n *       type\n *     }\n *   }\n * `;\n *\n * function AddTodo() {\n *   let input;\n *   const [addTodo, { data }] = useMutation(ADD_TODO);\n *\n *   return (\n *     <div>\n *       <form\n *         onSubmit={(e) => {\n *           e.preventDefault();\n *           addTodo({ variables: { type: input.value } });\n *           input.value = \"\";\n *         }}\n *       >\n *         <input\n *           ref={(node) => {\n *             input = node;\n *           }}\n *         />\n *         <button type=\"submit\">Add Todo</button>\n *       </form>\n *     </div>\n *   );\n * }\n * ```\n *\n * @param mutation - A GraphQL mutation document parsed into an AST by `gql`.\n * @param options - Options to control how the mutation is executed.\n * @returns A tuple in the form of `[mutate, result]`\n */\nexport function useMutation<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n  TCache extends ApolloCache = ApolloCache,\n  TConfiguredVariables extends Partial<TVariables> = {},\n>(\n  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: useMutation.Options<\n    NoInfer<TData>,\n    NoInfer<TVariables>,\n    TCache,\n    {\n      [K in keyof TConfiguredVariables]: K extends keyof TVariables ?\n        TConfiguredVariables[K]\n      : never;\n    }\n  >\n): useMutation.ResultTuple<\n  TData,\n  MakeRequiredVariablesOptional<TVariables, TConfiguredVariables>,\n  TCache\n> {\n  const client = useApolloClient(options?.client);\n  const [result, setResult] = React.useState<\n    Omit<useMutation.Result<TData>, \"reset\">\n  >(() => createInitialResult(client));\n\n  const ref = React.useRef({\n    result,\n    mutationId: 0,\n    isMounted: true,\n    client,\n    mutation,\n    options,\n  });\n\n  useIsomorphicLayoutEffect(() => {\n    Object.assign(ref.current, { client, options, mutation });\n  });\n\n  const execute = React.useCallback(\n    (\n      executeOptions: useMutation.MutationFunctionOptions<\n        TData,\n        TVariables,\n        TCache\n      > = {} as useMutation.MutationFunctionOptions<TData, TVariables, TCache>\n    ) => {\n      const { options, mutation } = ref.current;\n      const baseOptions = { ...options, mutation };\n      const client = executeOptions.client || ref.current.client;\n\n      if (!ref.current.result.loading && ref.current.isMounted) {\n        setResult(\n          (ref.current.result = {\n            loading: true,\n            error: undefined,\n            data: undefined,\n            called: true,\n            client,\n          })\n        );\n      }\n\n      const mutationId = ++ref.current.mutationId;\n      const clientOptions = mergeOptions(baseOptions, executeOptions as any);\n\n      return client\n        .mutate(\n          clientOptions as ApolloClient.MutateOptions<TData, OperationVariables>\n        )\n        .then(\n          (response) => {\n            const { data, error } = response;\n\n            const onError =\n              executeOptions.onError || ref.current.options?.onError;\n\n            if (error && onError) {\n              onError(error, clientOptions);\n            }\n\n            if (mutationId === ref.current.mutationId) {\n              const result = {\n                called: true,\n                loading: false,\n                data,\n                error,\n                client,\n              };\n\n              if (ref.current.isMounted && !equal(ref.current.result, result)) {\n                setResult((ref.current.result = result));\n              }\n            }\n\n            const onCompleted =\n              executeOptions.onCompleted || ref.current.options?.onCompleted;\n\n            if (!error) {\n              onCompleted?.(response.data!, clientOptions);\n            }\n\n            return response;\n          },\n          (error) => {\n            if (\n              mutationId === ref.current.mutationId &&\n              ref.current.isMounted\n            ) {\n              const result = {\n                loading: false,\n                error,\n                data: void 0,\n                called: true,\n                client,\n              };\n\n              if (!equal(ref.current.result, result)) {\n                setResult((ref.current.result = result));\n              }\n            }\n\n            const onError =\n              executeOptions.onError || ref.current.options?.onError;\n\n            if (onError) {\n              onError(error, clientOptions);\n            }\n\n            throw error;\n          }\n        );\n    },\n    []\n  );\n\n  const reset = React.useCallback(() => {\n    if (ref.current.isMounted) {\n      const result = createInitialResult(ref.current.client);\n      Object.assign(ref.current, { mutationId: 0, result });\n      setResult(result);\n    }\n  }, []);\n\n  React.useEffect(() => {\n    const current = ref.current;\n    current.isMounted = true;\n\n    return () => {\n      current.isMounted = false;\n    };\n  }, []);\n\n  return [execute as any, { reset, ...result }];\n}\n\nfunction createInitialResult(client: ApolloClient) {\n  return {\n    data: undefined,\n    error: undefined,\n    called: false,\n    loading: false,\n    client,\n  };\n}\n"], "names": [], "mappings": ";;AA2NA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AA1NA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAqBA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AAmJA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6CA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAMzB,CANF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAM+D,EAC7D,CAPF,CAAA,CAAA,CAAA,CAAA,CAAA,CAgBG,EAhBH;IAsBE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC;IAC/C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,EAA1B,EAA8B,CAA9B,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAExC,CAFJ,EAEO,CAFP,EAEU,CAFV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE6B,CAAC,CAF9B,CAAA,CAAA,CAAA,CAAA,CAEoC,CAAC,CAAC;IAEpC,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,EAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC;QACvB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU;QACN,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAC;QACb,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAmB;QACf,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU;QACN,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY;QACR,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;IACX,CAAG,CAAC;IAEF,CAAF,CAAA,EAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAA3B,CAA4B,CAA5B,EAA+B,CAA/B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAX,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE,EAAE,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC,EAAE,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,EAAE,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAA4D,CAAC;IAC3D,CAAC,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAC/B,CACE,CAFN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAMU,CANV,CAM8E,EACxE,CAPN,EAAA;QAQM,CAAN,CAAA,CAAA,CAAA,EAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAoC,CAApC,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C;QACzC,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,EAAE,CAA5B,CAAA,CAA+B,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,EAAE,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAkD;QAC5C,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAApC,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA8C,CAA9C,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC,CAA1D,CAAA,CAAA,CAAA,CAAA,CAAgE;QAE1D,CAAN,EAAA,CAAU,CAAC,CAAX,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAyC,CAAzC,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,EAAE;YACxD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CACP,CAAC,CADX,CAAA,CACc,CAAC,CADf,CAAA,CAAA,CAAA,CAAA,CAAA,CACsB,CAAC,CADvB,CAAA,CAAA,CAAA,CAAA,EAAA,EACgC;gBACpB,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAyB;gBACb,CAAZ,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B;gBAChB,CAAZ,CAAA,CAAA,CAAgB,EAAE,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B;gBACf,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAwB;gBACZ,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB;YAClB,CAAW,CAAC,CACH;QACH;QAEA,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAA2B,CAA3B,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD;QAC3C,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA4B,CAA5B,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAAyC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,EAAE,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CAAC;QAEtE,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA;YACA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAe,CACL,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgF;YAEhF,CAAS,CAAT,CAAA,CAAA,CAAa,CACH,CAAC,CADX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,EAAE,CADrB,EAAA;YAEY,CAAZ,CAAA,CAAA,CAAA,EAAkB,EAAE,CAApB,CAAA,CAAA,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAAA,EAAA,EAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C;YAEhC,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACc,CADd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4B,CAAC,CAD7B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EACwC,CADxC,CAAA,CAC2C,CAAC,CAD5C,CAAA,CAAA,CAAA,CAAA,CAAA,CACmD,CAAC,CADpD,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2D,CAD3D,CAC6D,CAD7D,CAAA,CAAA,CAAA,CAAA,CAAA,CACoE;YAExD,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,EAAA,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAE;gBACpB,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAA2B,EAAE,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;YAC/B;YAEA,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA+B,CAA/B,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,EAAE;gBACzC,CAAd,CAAA,CAAA,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B;oBACb,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE,CAAxB,CAAA,CAAA,CAA4B;oBACZ,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAAA,CAA8B;oBACd,CAAhB,CAAA,CAAA,CAAoB;oBACJ,CAAhB,CAAA,CAAA,CAAA,CAAqB;oBACL,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB;gBACtB,CAAe;gBAED,CAAd,EAAA,CAAkB,CAAlB,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAA2C,CAAC,CAA5C,CAAA,EAA4C,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAjD,CAAkD,CAAlD,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6D,CAAC,CAA9D,CAAA,CAAA,CAAA,CAAA,CAAoE,EAAE,CAAtE,CAAA,CAAA,CAAA,CAAA,CAA4E,CAAC,EAAE;oBAC/D,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAAC,CAA3B,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgD,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAC;gBAC1C;YACF;YAEA,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACc,CADd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4B,CAAC,CAD7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC4C,CAD5C,CAAA,CAC+C,CAAC,CADhD,CAAA,CAAA,CAAA,CAAA,CAAA,CACuD,CAAC,CADxD,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+D,CAD/D,CACiE,CADjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4E;YAEhE,CAAZ,EAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAsB,EAAE;gBACV,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAA0C,EAAE,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC;YAC9C;YAEA,CAAZ,CAAA,CAAA,CAAA,CAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B;QACjB,CAAC,EACD,CAAC,CADX,CAAA,CAAA,CAAA,CACgB,EAAE,CADlB,EAAA;YAEY,CAAZ,EAAA,CACc,CADd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC6B,CAD7B,CAAA,CACgC,CAAC,CADjC,CAAA,CAAA,CAAA,CAAA,CAAA,CACwC,CAAC,CADzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;gBAEc,CAAd,CAAA,CAAiB,CAAC,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,EACrB;gBACA,CAAd,CAAA,CAAA,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B;oBACb,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE,CAAzB,CAAA,CAAA,CAAA,CAA8B;oBACd,CAAhB,CAAA,CAAA,CAAA,CAAqB;oBACL,CAAhB,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,EAA2B,CAAC;oBACZ,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAE,CAAxB,CAAA,CAAA,CAA4B;oBACZ,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB;gBACtB,CAAe;gBAED,CAAd,EAAA,CAAkB,CAAC,CAAnB,CAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAxB,CAAyB,CAAzB,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC,CAArC,CAAA,CAAA,CAAA,CAAA,CAA2C,EAAE,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,EAAE;oBACtC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAAC,CAA3B,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgD,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAC;gBAC1C;YACF;YAEA,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACc,CADd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4B,CAAC,CAD7B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EACwC,CADxC,CAAA,CAC2C,CAAC,CAD5C,CAAA,CAAA,CAAA,CAAA,CAAA,CACmD,CAAC,CADpD,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2D,CAD3D,CAC6D,CAD7D,CAAA,CAAA,CAAA,CAAA,CAAA,CACoE;YAExD,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAE;gBACX,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAA2B,EAAE,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC;YAC/B;YAEA,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAuB;QACb,CAAC,CACF;IACL,CAAC,EACD,CADJ,CACM,CACH;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,EAAqC,CAArC,EAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAAE;YACzB,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAC;YACtD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,EAAE,EAAE,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,EAAE,CAAC,EAAE,CAAlD,CAAA,CAAA,CAAA,CAAA,EAAA,CAA0D,CAAC;YACrD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC;QACnB;IACF,CAAC,EAAE,CAAL,CAAO,CAAC;IAEN,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;QAC3B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,CAAA,CAA4B;QAExB,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,EAAc,CAAd,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAA+B;QAC3B,CAAC;IACH,CAAC,EAAE,CAAL,CAAO,CAAC;IAEN,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,EAAE,CAA5B,CAAA,CAAA,CAAA,CAAiC,EAAE,CAAnC,CAAA,CAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,EAAA,CAA8C,CAAC;AAC/C;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAiD,EAAjD;IACE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAA,CAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB;QACf,CAAJ,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;QAChB,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAiB;QACb,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAkB;QACd,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAU;IACV,CAAG;AACH;"}