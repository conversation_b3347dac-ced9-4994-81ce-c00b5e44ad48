{"version": 3, "file": "useQuery.cjs", "sources": ["../../../../src/react/hooks/useQuery.ts"], "sourcesContent": ["/**\n * Function parameters in this file try to follow a common order for the sake of\n * readability and consistency. The order is as follows:\n *\n * resultData\n * observable\n * client\n * query\n * options\n * watchQueryOptions\n * makeWatchQueryOptions\n */\n/**  */\nimport { equal } from \"@wry/equality\";\nimport * as React from \"react\";\nimport { asapScheduler, observeOn } from \"rxjs\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorLike,\n  ErrorPolicy,\n  GetDataState,\n  InternalTypes,\n  ObservableQuery,\n  OperationVariables,\n  RefetchWritePolicy,\n  SubscribeToMoreFunction,\n  TypedDocumentNode,\n  UpdateQueryMapFn,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport { NetworkStatus } from \"@apollo/client\";\nimport type { MaybeMasked } from \"@apollo/client/masking\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\nimport {\n  maybeDeepFreeze,\n  mergeOptions,\n} from \"@apollo/client/utilities/internal\";\n\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useQuery {\n  import _self = useQuery;\n  export namespace Base {\n    export interface Options<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: WatchQueryFetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#nextFetchPolicy:member} */\n      nextFetchPolicy?:\n        | WatchQueryFetchPolicy\n        | ((\n            this: ApolloClient.WatchQueryOptions<TData, TVariables>,\n            currentFetchPolicy: WatchQueryFetchPolicy,\n            context: InternalTypes.NextFetchPolicyContext<TData, TVariables>\n          ) => WatchQueryFetchPolicy);\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#initialFetchPolicy:member} */\n\n      initialFetchPolicy?: WatchQueryFetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy:member} */\n      refetchWritePolicy?: RefetchWritePolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#pollInterval:member} */\n      pollInterval?: number;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#notifyOnNetworkStatusChange:member} */\n      notifyOnNetworkStatusChange?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n      returnPartialData?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skipPollAttempt:member} */\n      skipPollAttempt?: () => boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#ssr:member} */\n      ssr?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip:member} */\n      skip?: boolean;\n    }\n  }\n  export type Options<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options<TData, TVariables> & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useQuery {\n      export interface Options<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options<TData, TVariables>,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export namespace Base {\n    export interface Result<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    > {\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#client:member} */\n      client: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#observable:member} */\n      observable: ObservableQuery<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#previousData:member} */\n      previousData?: MaybeMasked<TData>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#error:member} */\n      error?: ErrorLike;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#loading:member} */\n      loading: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#startPolling:member} */\n      startPolling: (pollInterval: number) => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#stopPolling:member} */\n      stopPolling: () => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#subscribeToMore:member} */\n      subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#updateQuery:member} */\n      updateQuery: (mapFn: UpdateQueryMapFn<TData, TVariables>) => void;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member} */\n      refetch: (\n        variables?: Partial<TVariables>\n      ) => Promise<ApolloClient.QueryResult<MaybeMasked<TData>>>;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#variables:member} */\n      variables: TVariables;\n\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#fetchMore:member} */\n      fetchMore: <\n        TFetchData = TData,\n        TFetchVars extends OperationVariables = TVariables,\n      >(\n        fetchMoreOptions: ObservableQuery.FetchMoreOptions<\n          TData,\n          TVariables,\n          TFetchData,\n          TFetchVars\n        >\n      ) => Promise<ApolloClient.QueryResult<MaybeMasked<TFetchData>>>;\n    }\n  }\n  export type Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData, TVariables> &\n    GetDataState<MaybeMasked<TData>, TStates>;\n\n  export namespace DocumentationTypes {\n    namespace useQuery {\n      export interface Result<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Result<TData, TVariables>,\n          UtilityDocumentationTypes.DataState<TData> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useQuery:function(1)} */\n    export function useQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options: useQuery.Options<TData, TVariables>\n    ): useQuery.Result<TData, TVariables>;\n  }\n}\n\nconst lastWatchOptions = Symbol();\n\ninterface ObsQueryWithMeta<TData, TVariables extends OperationVariables>\n  extends ObservableQuery<TData, TVariables> {\n  [lastWatchOptions]?: Readonly<\n    ApolloClient.WatchQueryOptions<TData, TVariables>\n  >;\n}\n\ninterface InternalResult<TData> {\n  // These members are populated by getCurrentResult and setResult, and it's\n  // okay/normal for them to be initially undefined.\n  current: ObservableQuery.Result<TData>;\n  previousData?: undefined | MaybeMasked<TData>;\n\n  // Track current variables separately in case a call to e.g. `refetch(newVars)`\n  // causes an emit that is deeply equal to the current result. This lets us\n  // compare if we should force rerender due to changed variables\n  variables: OperationVariables;\n}\n\ninterface InternalState<TData, TVariables extends OperationVariables> {\n  client: ReturnType<typeof useApolloClient>;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  observable: ObsQueryWithMeta<TData, TVariables>;\n  resultData: InternalResult<TData>;\n}\n\n/**\n * A hook for executing queries in an Apollo application.\n *\n * To run a query within a React component, call `useQuery` and pass it a GraphQL query document.\n *\n * When your component renders, `useQuery` returns an object from Apollo Client that contains `loading`, `error`, `dataState`, and `data` properties you can use to render your UI.\n *\n * > Refer to the [Queries](https://www.apollographql.com/docs/react/data/queries) section for a more in-depth overview of `useQuery`.\n *\n * @example\n *\n * ```jsx\n * import { gql } from \"@apollo/client\";\n * import { useQuery } from \"@apollo/client/react\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const { loading, error, data } = useQuery(GET_GREETING, {\n *     variables: { language: \"english\" },\n *   });\n *   if (loading) return <p>Loading ...</p>;\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns Query result object\n */\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: true;\n  }\n): useQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useQuery:function(1)} */\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n  }\n): useQuery.Result<\n  TData,\n  TVariables,\n  \"empty\" | \"complete\" | \"streaming\" | \"partial\"\n>;\n\n/** {@inheritDoc @apollo/client/react!useQuery:function(1)} */\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n  : [options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n): useQuery.Result<TData, TVariables, \"empty\" | \"complete\" | \"streaming\">;\n\nexport function useQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n  : [options: useQuery.Options<NoInfer<TData>, NoInfer<TVariables>>]\n): useQuery.Result<TData, TVariables> {\n  \"use no memo\";\n  return wrapHook(\n    \"useQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useQuery_,\n    useApolloClient(options && options.client)\n  )(query, options);\n}\n\nfunction useQuery_<TData, TVariables extends OperationVariables>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useQuery.Options<\n    NoInfer<TData>,\n    NoInfer<TVariables>\n  > = {} as useQuery.Options<TData, TVariables>\n): useQuery.Result<TData, TVariables> {\n  const client = useApolloClient(options.client);\n  const { skip, ssr, ...opts } = options;\n\n  const watchQueryOptions: ApolloClient.WatchQueryOptions<TData, TVariables> =\n    mergeOptions(client.defaultOptions.watchQuery as any, { ...opts, query });\n\n  if (skip) {\n    // When skipping, we set watchQueryOptions.fetchPolicy initially to\n    // \"standby\", but we also need/want to preserve the initial non-standby\n    // fetchPolicy that would have been used if not skipping.\n    watchQueryOptions.initialFetchPolicy =\n      options.initialFetchPolicy || options.fetchPolicy;\n    watchQueryOptions.fetchPolicy = \"standby\";\n  }\n\n  function createState(\n    previous?: InternalState<TData, TVariables>\n  ): InternalState<TData, TVariables> {\n    const observable = client.watchQuery(watchQueryOptions);\n\n    return {\n      client,\n      query,\n      observable,\n      resultData: {\n        current: observable.getCurrentResult(),\n        // Reuse previousData from previous InternalState (if any) to provide\n        // continuity of previousData even if/when the query or client changes.\n        previousData: previous?.resultData.current.data as TData,\n        variables: observable.variables,\n      },\n    };\n  }\n\n  let [state, setState] = React.useState(createState);\n\n  if (client !== state.client || query !== state.query) {\n    // If the client or query have changed, we need to create a new InternalState.\n    // This will trigger a re-render with the new state, but it will also continue\n    // to run the current render function to completion.\n    // Since we sometimes trigger some side-effects in the render function, we\n    // re-assign `state` to the new state to ensure that those side-effects are\n    // triggered with the new state.\n    setState((state = createState(state)));\n  }\n\n  const { observable, resultData } = state;\n\n  useInitialFetchPolicyIfNecessary<TData, TVariables>(\n    watchQueryOptions,\n    observable\n  );\n\n  useResubscribeIfNecessary<TData, TVariables>(\n    resultData, // might get mutated during render\n    observable, // might get mutated during render\n    watchQueryOptions\n  );\n\n  const result = useResult<TData, TVariables>(\n    observable,\n    resultData,\n    options.ssr\n  );\n\n  const obsQueryFields = React.useMemo(\n    () => ({\n      refetch: observable.refetch.bind(observable),\n      fetchMore: observable.fetchMore.bind(observable),\n      updateQuery: observable.updateQuery.bind(observable),\n      startPolling: observable.startPolling.bind(observable),\n      stopPolling: observable.stopPolling.bind(observable),\n      subscribeToMore: observable.subscribeToMore.bind(observable),\n    }),\n    [observable]\n  );\n\n  const previousData = resultData.previousData;\n  return React.useMemo(() => {\n    const { partial, ...rest } = result;\n\n    return {\n      ...rest,\n      client,\n      observable,\n      variables: observable.variables,\n      previousData,\n      ...obsQueryFields,\n    };\n  }, [result, client, observable, previousData, obsQueryFields]);\n}\n\nfunction useInitialFetchPolicyIfNecessary<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  watchQueryOptions: ApolloClient.WatchQueryOptions<TData, TVariables>,\n  observable: ObsQueryWithMeta<TData, TVariables>\n) {\n  \"use no memo\";\n  if (!watchQueryOptions.fetchPolicy) {\n    watchQueryOptions.fetchPolicy = observable.options.initialFetchPolicy;\n  }\n}\n\nfunction useResult<TData, TVariables extends OperationVariables>(\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  resultData: InternalResult<TData>,\n  ssr: boolean | undefined\n) {\n  \"use no memo\";\n  return useSyncExternalStore(\n    React.useCallback(\n      (handleStoreChange) => {\n        const subscription = observable\n          // We use the asapScheduler here to prevent issues with trying to\n          // update in the middle of a render. `reobserve` is kicked off in the\n          // middle of a render and because RxJS emits values synchronously,\n          // its possible for this `handleStoreChange` to be called in that same\n          // render. This allows the render to complete before trying to emit a\n          // new value.\n          .pipe(observeOn(asapScheduler))\n          .subscribe((result) => {\n            const previous = resultData.current;\n\n            if (\n              // Avoid rerendering if the result is the same\n              equal(previous, result) &&\n              // Force rerender if the value was emitted because variables\n              // changed, such as when calling `refetch(newVars)` which returns\n              // the same data when `notifyOnNetworkStatusChange` is `false`.\n              equal(resultData.variables, observable.variables)\n            ) {\n              return;\n            }\n\n            // eslint-disable-next-line react-compiler/react-compiler\n            resultData.variables = observable.variables;\n\n            if (previous.data && !equal(previous.data, result.data)) {\n              resultData.previousData = previous.data as TData;\n            }\n\n            resultData.current = result;\n            handleStoreChange();\n          });\n\n        // Do the \"unsubscribe\" with a short delay.\n        // This way, an existing subscription can be reused without an additional\n        // request if \"unsubscribe\"  and \"resubscribe\" to the same ObservableQuery\n        // happen in very fast succession.\n        return () => {\n          setTimeout(() => subscription.unsubscribe());\n        };\n      },\n\n      [observable, resultData]\n    ),\n    () => resultData.current,\n    () => (ssr === false ? useQuery.ssrDisabledResult : resultData.current)\n  );\n}\n\n// this hook is not compatible with any rules of React, and there's no good way to rewrite it.\n// it should stay a separate hook that will not be optimized by the compiler\nfunction useResubscribeIfNecessary<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  /** this hook will mutate properties on `resultData` */\n  resultData: InternalResult<TData>,\n  /** this hook will mutate properties on `observable` */\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  watchQueryOptions: Readonly<ApolloClient.WatchQueryOptions<TData, TVariables>>\n) {\n  \"use no memo\";\n  if (\n    observable[lastWatchOptions] &&\n    !equal(observable[lastWatchOptions], watchQueryOptions)\n  ) {\n    // Though it might be tempting to postpone this reobserve call to the\n    // useEffect block, we need getCurrentResult to return an appropriate\n    // loading:true result synchronously (later within the same call to\n    // useQuery). Since we already have this.observable here (not true for\n    // the very first call to useQuery), we are not initiating any new\n    // subscriptions, though it does feel less than ideal that reobserve\n    // (potentially) kicks off a network request (for example, when the\n    // variables have changed), which is technically a side-effect.\n    if (shouldReobserve(observable[lastWatchOptions], watchQueryOptions)) {\n      observable.reobserve(watchQueryOptions);\n    } else {\n      observable.applyOptions(watchQueryOptions);\n    }\n\n    // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n    // but save the current data as this.previousData, just like setResult\n    // usually does.\n    const result = observable.getCurrentResult();\n\n    if (!equal(result.data, resultData.current.data)) {\n      resultData.previousData = (resultData.current.data ||\n        (resultData.previousData as TData)) as TData;\n    }\n    resultData.current = result;\n    resultData.variables = observable.variables;\n  }\n  observable[lastWatchOptions] = watchQueryOptions;\n}\n\nfunction shouldReobserve<TData, TVariables extends OperationVariables>(\n  previousOptions: Readonly<ApolloClient.WatchQueryOptions<TData, TVariables>>,\n  options: Readonly<ApolloClient.WatchQueryOptions<TData, TVariables>>\n) {\n  return (\n    previousOptions.query !== options.query ||\n    !equal(previousOptions.variables, options.variables) ||\n    (previousOptions.fetchPolicy !== options.fetchPolicy &&\n      (options.fetchPolicy === \"standby\" ||\n        previousOptions.fetchPolicy === \"standby\"))\n  );\n}\n\nuseQuery.ssrDisabledResult = maybeDeepFreeze({\n  loading: true,\n  data: void 0 as any,\n  dataState: \"empty\",\n  error: void 0,\n  networkStatus: NetworkStatus.loading,\n  partial: true,\n}) satisfies ObservableQuery.Result<any> as ObservableQuery.Result<any>;\n"], "names": [], "mappings": ";;AAqTA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AArTA,CAAA,CAAA;;;;;;;;;;;CAWA,CAAA;AACA,CAAA,CAAA,GAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAmBA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAOA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAKA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6BAAA,CAAA;AA6JA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B,CAA/B,CAAiC;AAwGjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAItB,CAJF,CAAA,CAAA,CAAA,CAI4D,EAC1D,CALF,CAAA,CAKK,CAAC,CALN,CAAA,CAAA,CAAA,CAAA,CAAA,CAKa,CAEuD,EAPpE;IASE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc;IACV,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EACT,CADJ,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CADnB,CACoB,CADpB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAC+B,CAD/B,CAAA,CAAA,CAAA,CAAA,CAAA,CACsC,CAAC,CADvC,CAAA,CAAA,CAAA,CAAA,CAC6C,CAAC,CAC3C,CAAC,CAFJ,CAAA,CAAA,CAAA,CAES,EAAE,CAFX,CAAA,CAAA,CAAA,CAAA,CAAA,CAEkB,CAAC;AACnB;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAChB,CADF,CAAA,CAAA,CAAA,CAC4D,EAC1D,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAKM,CALN,CAK+C,EAL/C;IAOE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAc,EAAE,CAAhB,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAwB,CAAxB,CAAA,CAAA,EAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC;IAEtC,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACI,CADJ,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgB,CADhB,CACiB,CADjB,CAAA,CAAA,CAAA,CAAA,CACuB,CAAC,CADxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACsC,CAAC,CADvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACwD,EAAE,EAAE,CAD5D,CAAA,CAC+D,CAD/D,CAAA,CAAA,CACmE,EAAE,CADrE,CAAA,CAAA,CAAA,EAAA,CAC4E,CAAC;IAE3E,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAU,EAAE;QACR,CAAJ,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD;QACnD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C;IAC3C;IAEA,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAClB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+C,EAD/C;QAGI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC;QAEvD,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY;YACN,CAAN,CAAA,CAAA,CAAA,CAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YACV,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAE;gBACV,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAA5C,CAA8C;gBACtC,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACQ,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAA9B,CAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC,CAAnD,CAAA,CAAA,CAAgE;gBACxD,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC;YACvC,CAAO;QACP,CAAK;IACH;IAEA,CAAF,CAAA,EAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAtB,EAA0B,CAA1B,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC;IAEnD,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA2C,CAA3C,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAsD,EAAE;QACpD,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAC,CAAd,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAC,CAAC;IACxC;IAEA,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAqC,CAArC,CAAA,CAAA,CAAA,CAA0C;IAExC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAC9B,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,EACjB,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEc,CACX;IAED,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CACvB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc,EAAE,CADhB,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAhB,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAClB;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CACtB,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc,EACV,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEc,EACV,CAHJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAGW,CAAC,CAHZ,CAAA,CAGe,CACZ;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAClC,CADJ,EACO,CADP,EACU,CAAC;QACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC;QAC5C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAA0C,CAAC,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAC;QAChD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC;QACpD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAC;QACtD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyD,CAAC;QACpD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAlD,CAAA,CAAA,CAAsD,CAAC,CAAvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE,CAAC;IAClE,CAAK,CAAC,EACF,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACe,CAAC,CACb;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C;IAC5C,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,EAA0B,CAA1B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAwB,CAAxB,CAAA,CAAA,EAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC;QAEnC,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAa;YACP,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY;YACN,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YACV,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC;YAC/B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;YACZ,CAAN,CAAA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;QACvB,CAAK;IACH,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,EAAE,CAAd,CAAA,CAAA,CAAA,CAAA,CAAoB,EAAE,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,EAAE,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,EAAE,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC,CAAC;AAChE;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAIvC,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIsE,EACpE,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKiD,EALjD;IAOE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,EAAA,CAAM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,EAAE;QAClC,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD,CAAC,CAAvD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE;IACvE;AACF;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAChB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiD,EAC/C,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmC,EACjC,CAHF,CAAA,CAG0B,EAH1B;IAKE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAA7B,CACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEwB,EAAE,CAF1B,EAAA;QAGQ,CAAR,CAAA,CAAA,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACU,CAAV,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACU,CAAV,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA;YACU,CAAV,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAW,CAAX,CAAA,CAAA,CAAe,CAAC,CAAhB,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC;YACxC,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAA4B,EAAE,CAA9B,EAAA;YACY,CAAZ,CAAA,CAAA,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C;YAEnC,CAAZ,EAAA;YACc,CAAd,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACc,CAAd,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAnB,CAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,EAAE,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAoC,EAApC,CAAA;gBACc,CAAd,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACc,CAAd,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACc,CAAd,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACc,CAAd,CAAA,EAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAnB,CAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,EAAE,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAAC,EACjD;gBACA,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA;YACY;YAEA,CAAZ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD;YAE3C,CAAZ,EAAA,CAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,EAAA,CAAA,EAAiC,CAAC,CAAlC,CAAA,EAAkC,CAAlC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAvC,CAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAAqD,EAAE,CAAvD,CAAA,CAAA,CAAA,CAAA,CAA6D,CAAC,CAA9D,CAAA,CAAA,CAAkE,CAAC,EAAE;gBACvD,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC,CAAjD,CAAA,CAAA,CAA8D;YAClD;YAEA,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAuC;YAC3B,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAA7B,CAA+B;QACrB,CAAC,CAAC;QAEJ,CAAR,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAR,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAR,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,EAAkB,CAAlB,EAAA;YACU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,EAAwB,CAAxB,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAnD,CAAqD,CAAC;QAC9C,CAAC;IACH,CAAC,EAED,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEiB,EAAE,CAFnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAE6B,CAAC,CACzB,EACD,CAJJ,EAIO,CAJP,EAIU,CAJV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIoB,CAAC,CAJrB,CAAA,CAAA,CAAA,CAAA,CAAA,CAI4B,EACxB,CALJ,EAKO,CALP,EAKU,CAAC,CALX,CAAA,EAAA,CAAA,CAAA,EAKmB,CALnB,CAAA,CAAA,CAAA,EAKyB,EAAE,CAL3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKmC,CAAC,CALpC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAKsD,EAAE,CALxD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAKkE,CAAC,CALnE,CAAA,CAAA,CAAA,CAAA,CAAA,CAK0E,CAAC,CACxE;AACH;AAEA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC;AAIhC,CAAF,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;AACjC,CAAF,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,EAC/C,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgF,EADhF;IAGE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,EAAA,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc,CAAC,CADf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC+B,EAD/B,CAAA;QAEI,CAAC,CAAL,CAAA,EAAK,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAV,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,EAAE,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC,EACvD;QACA,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,EAAE,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CAAC,EAAE;YACpE,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC;QACzC;QAAJ,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;QAC5C;QAEA,CAAJ,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAA9C,CAAgD;QAE5C,CAAJ,EAAA,CAAQ,CAAC,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAd,CAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAA,CAA0B,EAAE,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAA/C,CAAA,CAAA,CAAmD,CAAC,EAAE;YAChD,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC,CAA5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmD,CAAC,CAApD,CAAA,CAAA,EAAA,CAAA;gBACS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAAU;QAChD;QACA,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAA+B;QAC3B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C;IAC7C;IACA,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,EAA7B,EAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD;AAClD;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CACtB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC8E,EAC5E,CAFF,CAAA,CAAA,CAAA,CAAA,CAAA,CAEsE,EAFtE;IAIE,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CACL,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CAAC,CADpB,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAC8B,CAD9B,CAAA,CAAA,CAAA,CAAA,CAAA,CACqC,CAAC,CADtC,CAAA,CAAA,CAAA,EAAA,CAAA;QAEI,CAAC,CAAL,CAAA,EAAK,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAV,CAAW,CAAX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,EAAE,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAC,CAA9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuD,EAAvD,CAAA;QACI,CAAC,CAAL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAqC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;YACM,CAAC,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA+B,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;gBACQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAwC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,CAAC,CAChD;AACH;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B,CAA7B,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAA5C,CAA6C;IAC3C,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAM,EAAE,CAAR,CAAA,CAAA,EAAa,CAAQ;IACnB,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAClB,CAAF,CAAA,CAAA,CAAA,CAAO,EAAE,CAAT,CAAA,CAAA,EAAc,CAAC;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC;IACpC,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAX,CAAA,CAAA,CAAe;AACf,CAAC,CAAsE;"}