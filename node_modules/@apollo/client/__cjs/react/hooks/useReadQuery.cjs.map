{"version": 3, "file": "useReadQuery.cjs", "sources": ["../../../../src/react/hooks/useReadQuery.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  <PERSON>rror<PERSON>ike,\n  GetDataState,\n  NetworkStatus,\n  ObservableQuery,\n} from \"@apollo/client\";\nimport type { MaybeMasked } from \"@apollo/client/masking\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport {\n  assertWrappedQueryRef,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n} from \"@apollo/client/react/internal\";\nimport type { DocumentationTypes as UtilityDocumentationTypes } from \"@apollo/client/utilities/internal\";\n\nimport { __use, wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\nexport declare namespace useReadQuery {\n  export namespace Base {\n    export interface Result<TData = unknown> {\n      /**\n       * {@inheritDoc @apollo/client!QueryResultDocumentation#error:member}\n       *\n       * This property can be ignored when using the default `errorPolicy` or an\n       * `errorPolicy` of `none`. The hook will throw the error instead of setting\n       * this property.\n       */\n      error: ErrorLike | undefined;\n      /** {@inheritDoc @apollo/client!QueryResultDocumentation#networkStatus:member} */\n      networkStatus: NetworkStatus;\n    }\n  }\n  export type Result<\n    TData = unknown,\n    TStates extends\n      DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n  > = Base.Result<TData> & GetDataState<MaybeMasked<TData>, TStates>;\n\n  export namespace DocumentationTypes {\n    namespace useReadQuery {\n      export interface Result<TData = unknown>\n        extends Base.Result<TData>,\n          UtilityDocumentationTypes.DataState<TData> {}\n    }\n\n    /** {@inheritDoc @apollo/client/react!useReadQuery:function(1)} */\n    export function useReadQuery<TData>(\n      queryRef: QueryRef<TData>\n    ): useReadQuery.Result<TData>;\n  }\n}\n\n/**\n * For a detailed explanation of `useReadQuery`, see the [fetching with Suspense reference](https://www.apollographql.com/docs/react/data/suspense#avoiding-request-waterfalls).\n *\n * @param queryRef - The `QueryRef` that was generated via `useBackgroundQuery`.\n * @returns An object containing the query result data, error, and network status.\n *\n * @example\n *\n * ```jsx\n * import { Suspense } from \"react\";\n * import { useBackgroundQuery, useReadQuery } from \"@apollo/client\";\n *\n * function Parent() {\n *   const [queryRef] = useBackgroundQuery(query);\n *\n *   return (\n *     <Suspense fallback={<div>Loading...</div>}>\n *       <Child queryRef={queryRef} />\n *     </Suspense>\n *   );\n * }\n *\n * function Child({ queryRef }) {\n *   const { data } = useReadQuery(queryRef);\n *\n *   return <div>{data.name}</div>;\n * }\n * ```\n */\nexport function useReadQuery<\n  TData,\n  TStates extends DataState<TData>[\"dataState\"],\n>(\n  queryRef: QueryRef<TData, any, TStates>\n): useReadQuery.Result<TData, TStates> {\n  \"use no memo\";\n  const unwrapped = unwrapQueryRef(queryRef);\n  const clientOrObsQuery = useApolloClient(\n    unwrapped ?\n      // passing an `ObservableQuery` is not supported by the types, but it will\n      // return any truthy value that is passed in as an override so we cast the result\n      (unwrapped[\"observable\"] as any)\n    : undefined\n  ) as ApolloClient | ObservableQuery<TData>;\n\n  return wrapHook(\n    \"useReadQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useReadQuery_,\n    clientOrObsQuery\n  )(queryRef);\n}\n\nfunction useReadQuery_<TData, TStates extends DataState<TData>[\"dataState\"]>(\n  queryRef: QueryRef<TData, any, TStates>\n): useReadQuery.Result<TData, TStates> {\n  assertWrappedQueryRef(queryRef);\n  const internalQueryRef = React.useMemo(\n    () => unwrapQueryRef(queryRef),\n    [queryRef]\n  );\n\n  const getPromise = React.useCallback(\n    () => getWrappedPromise(queryRef),\n    [queryRef]\n  );\n\n  if (internalQueryRef.disposed) {\n    internalQueryRef.reinitialize();\n    updateWrappedQueryRef(queryRef, internalQueryRef.promise);\n  }\n\n  React.useEffect(() => internalQueryRef.retain(), [internalQueryRef]);\n\n  const promise = useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        return internalQueryRef.listen((promise) => {\n          updateWrappedQueryRef(queryRef, promise);\n          forceUpdate();\n        });\n      },\n      [internalQueryRef, queryRef]\n    ),\n    getPromise,\n    getPromise\n  );\n\n  const result = __use(promise);\n\n  return React.useMemo(() => {\n    return {\n      data: result.data,\n      dataState: result.dataState,\n      networkStatus: result.networkStatus,\n      error: result.error,\n    } as useReadQuery.Result<TData, TStates>;\n  }, [result]);\n}\n"], "names": [], "mappings": ";;AAwFA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAxFA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAYA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6BAAA,CAAA;AAqCA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAI1B,CAJF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIyC,EAJzC;IAME,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAlC,CAAmC,CAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAAC;IAC1C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0C,CAA1C,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACc;QACR,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;QACM,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACO,CAAP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B;QACzB,EAAE,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAC6B;IAE1C,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACkB;IACd,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EACb,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoB,CACjB,CAAC,CAFJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEY,CAAC;AACb;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CACpB,CADF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyC,EADzC;IAGE,CAAF,CAAA,EAAE,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAvB,CAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC;IAC/B,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,CAA3B,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CACpC,CADJ,EACO,CADP,EACU,CADV,CAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACwB,CADxB,CACyB,CADzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,CAAC,EAC9B,CAAC,CAFL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEa,CAAC,CACX;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,CAAA,CAAA,CAAA,CAA0B,CAAC,CAA3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAClC,CADJ,EACO,CADP,EACU,CADV,CAAA,EACU,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC2B,CAD3B,CAC4B,CAD5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACoC,CAAC,EACjC,CAAC,CAFL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEa,CAAC,CACX;IAED,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,EAAE;QAC7B,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAjC,CAAmC;QAC/B,CAAJ,CAAA,EAAI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAC,CAArD,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAC;IAC3D;IAEA,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAA/C,CAAiD,EAAE,CAAC,CAApD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoE,CAAC,CAAC;IAEpE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAlB,CAAA,EAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAtC,CACI,CADJ,CAAA,CAAA,CAAA,CACS,CAAC,CADV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAAC,CAFP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEkB,EAAE,CAFpB,EAAA;QAGQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAC,CAAxC,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+C,EAAE,CAAjD,EAAA;YACU,CAAV,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAA/B,CAAgC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,EAAE,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC;YACxC,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAArB,CAAuB;QACf,CAAC,CAAC;IACJ,CAAC,EACD,CAAC,CADP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACuB,EAAE,CADzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACiC,CAAC,CAC7B,EACD,CAHJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGc,EACV,CAJJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAIc,CACX;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAtB,CAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC;IAE7B,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,EAA0B,CAA1B,EAAA;QACI,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW;YACL,CAAN,CAAA,CAAA,CAAU,EAAE,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAuB;YACjB,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC;YAC3B,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,EAAE,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC;YACnC,CAAN,CAAA,CAAA,CAAA,CAAW,EAAE,CAAb,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAApB,CAAA,CAAA,CAAA,CAAyB;QACzB,CAA4C;IAC1C,CAAC,EAAE,CAAC,CAAN,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAC;AACd;"}