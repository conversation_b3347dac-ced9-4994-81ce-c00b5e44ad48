{"version": 3, "file": "useBackgroundQuery.cjs", "sources": ["../../../../src/react/hooks/useBackgroundQuery.ts"], "sourcesContent": ["import * as React from \"react\";\n\nimport type {\n  ApolloClient,\n  DataState,\n  DefaultContext,\n  DocumentNode,\n  ErrorPolicy,\n  OperationVariables,\n  RefetchWritePolicy,\n  TypedDocumentNode,\n  WatchQueryFetchPolicy,\n} from \"@apollo/client\";\nimport type { SubscribeToMoreFunction } from \"@apollo/client\";\nimport { canonicalStringify } from \"@apollo/client/cache\";\nimport type { QueryRef } from \"@apollo/client/react\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON>,\n  FetchMoreFunction,\n  RefetchFunction,\n} from \"@apollo/client/react/internal\";\nimport {\n  getSuspenseCache,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"@apollo/client/react/internal\";\nimport type {\n  DocumentationTypes as UtilityDocumentationTypes,\n  NoInfer,\n  VariablesOption,\n} from \"@apollo/client/utilities/internal\";\n\nimport type { SkipToken } from \"./constants.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useWatchQueryOptions } from \"./useSuspenseQuery.js\";\n\nexport declare namespace useBackgroundQuery {\n  import _self = useBackgroundQuery;\n  export type FetchPolicy = Extract<\n    WatchQueryFetchPolicy,\n    \"cache-first\" | \"network-only\" | \"no-cache\" | \"cache-and-network\"\n  >;\n\n  export namespace Base {\n    export interface Options {\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#client:member} */\n      client?: ApolloClient;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#refetchWritePolicy_suspense:member} */\n      refetchWritePolicy?: RefetchWritePolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#errorPolicy:member} */\n      errorPolicy?: ErrorPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#context:member} */\n      context?: DefaultContext;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#returnPartialData:member} */\n      returnPartialData?: boolean;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#fetchPolicy:member} */\n      fetchPolicy?: FetchPolicy;\n\n      /** {@inheritDoc @apollo/client!QueryOptionsDocumentation#queryKey:member} */\n      queryKey?: string | number | any[];\n\n      /**\n       * {@inheritDoc @apollo/client!QueryOptionsDocumentation#skip_deprecated:member}\n       *\n       * @example Recommended usage of `skipToken`:\n       *\n       * ```ts\n       * import { skipToken, useBackgroundQuery } from \"@apollo/client\";\n       *\n       * const [queryRef] = useBackgroundQuery(\n       *   query,\n       *   id ? { variables: { id } } : skipToken\n       * );\n       * ```\n       */\n      skip?: boolean;\n    }\n  }\n\n  export type Options<\n    TVariables extends OperationVariables = OperationVariables,\n  > = Base.Options & VariablesOption<TVariables>;\n\n  export namespace DocumentationTypes {\n    namespace useBackgroundQuery {\n      export interface Options<\n        TVariables extends OperationVariables = OperationVariables,\n      > extends Base.Options,\n          UtilityDocumentationTypes.VariableOptions<TVariables> {}\n    }\n  }\n\n  export interface Result<\n    TData = unknown,\n    TVariables extends OperationVariables = OperationVariables,\n  > {\n    /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n    subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n\n    /**\n     * {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)}\n     *\n     * @remarks\n     * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n     */\n    fetchMore: FetchMoreFunction<TData, TVariables>;\n\n    /**\n     * {@inheritDoc @apollo/client!QueryResultDocumentation#refetch:member}\n     *\n     * @remarks\n     * Calling this function will cause the component to re-suspend, unless the call site is wrapped in [`startTransition`](https://react.dev/reference/react/startTransition).\n     */\n    refetch: RefetchFunction<TData, TVariables>;\n  }\n\n  namespace DocumentationTypes {\n    namespace useBackgroundQuery {\n      export interface Result<\n        TData = unknown,\n        TVariables extends OperationVariables = OperationVariables,\n      > extends _self.Result<TData, TVariables> {}\n    }\n  }\n\n  export namespace DocumentationTypes {\n    /** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\n    export function useBackgroundQuery<\n      TData = unknown,\n      TVariables extends OperationVariables = OperationVariables,\n    >(\n      query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n      options: SkipToken | useBackgroundQuery.Options<TVariables>\n    ): [\n      QueryRef<TData, TVariables> | undefined,\n      useBackgroundQuery.Result<TData, TVariables>,\n    ];\n  }\n}\n\n/**\n * For a detailed explanation of useBackgroundQuery, see the [fetching with Suspense reference](https://www.apollographql.com/docs/react/data/suspense).\n *\n * @returns A tuple containing:\n *\n * 1.  A `QueryRef` that can be passed to `useReadQuery` to read the query result. The `queryRef` is `undefined` if the query is skipped.\n * 2.  An object containing helper functions for the query:\n *     - `refetch`: A function to re-execute the query\n *     - `fetchMore`: A function to fetch more results for pagination\n *     - `subscribeToMore`: A function to subscribe to updates\n *\n * @example\n *\n * ```jsx\n * import { Suspense } from \"react\";\n * import { ApolloClient, InMemoryCache, HttpLink } from \"@apollo/client\";\n * import { useBackgroundQuery, useReadQuery } from \"@apollo/client/react\";\n *\n * const query = gql`\n *   foo {\n *     bar\n *   }\n * `;\n *\n * const client = new ApolloClient({\n *   link: new HttpLink({ uri: \"http://localhost:4000/graphql\" }),\n *   cache: new InMemoryCache(),\n * });\n *\n * function SuspenseFallback() {\n *   return <div>Loading...</div>;\n * }\n *\n * function Child({ queryRef }) {\n *   const { data } = useReadQuery(queryRef);\n *\n *   return <div>{data.foo.bar}</div>;\n * }\n *\n * function Parent() {\n *   const [queryRef] = useBackgroundQuery(query);\n *\n *   return (\n *     <Suspense fallback={<SuspenseFallback />}>\n *       <Child queryRef={queryRef} />\n *     </Suspense>\n *   );\n * }\n *\n * function App() {\n *   return (\n *     <ApolloProvider client={client}>\n *       <Parent />\n *     </ApolloProvider>\n *   );\n * }\n * ```\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - An optional object containing options for the query. Instead of passing a `useBackgroundQuery.Options` object into the hook, you can also pass a [`skipToken`](#skiptoken) to prevent the `useBackgroundQuery` hook from executing the query or suspending.\n */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    /** @deprecated `returnPartialData` has no effect on `no-cache` queries */\n    returnPartialData: boolean;\n    fetchPolicy: \"no-cache\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: false;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"empty\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\" | \"empty\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"empty\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: false;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n    returnPartialData: boolean;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: false;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    returnPartialData: boolean;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: useBackgroundQuery.Options<NoInfer<TVariables>> & {\n    skip: boolean;\n  }\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken\n): [undefined, useBackgroundQuery.Result<TData, TVariables>];\n\n/** {@inheritDoc @apollo/client/react!useBackgroundQuery:function(1)} */\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (useBackgroundQuery.Options<NoInfer<TVariables>> & {\n        returnPartialData: false;\n      })\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (useBackgroundQuery.Options<NoInfer<TVariables>> & {\n        returnPartialData: boolean;\n      })\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\" | \"partial\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: useBackgroundQuery.Options<NoInfer<TVariables>>]\n  : [options: useBackgroundQuery.Options<NoInfer<TVariables>>]\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\">,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  ...[options]: {} extends TVariables ?\n    [options?: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>]\n  : [options: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>]\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>\n): [\n  QueryRef<TData, TVariables, \"complete\" | \"streaming\"> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SkipToken | useBackgroundQuery.Options<NoInfer<TVariables>>\n): [\n  QueryRef<TData, TVariables, DataState<TData>[\"dataState\"]> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n] {\n  \"use no memo\";\n  return wrapHook(\n    \"useBackgroundQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useBackgroundQuery_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(query, options ?? ({} as any));\n}\n\nfunction useBackgroundQuery_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n  TStates extends DataState<TData>[\"dataState\"] = DataState<TData>[\"dataState\"],\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken & Partial<useBackgroundQuery.Options<NoInfer<TVariables>>>)\n    | useBackgroundQuery.Options<NoInfer<TVariables>>\n): [\n  QueryRef<TData, TVariables, TStates> | undefined,\n  useBackgroundQuery.Result<TData, TVariables>,\n] {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions({ client, query, options });\n  const { fetchPolicy, variables } = watchQueryOptions;\n  const { queryKey = [] } = options;\n\n  // This ref tracks the first time query execution is enabled to determine\n  // whether to return a query ref or `undefined`. When initialized\n  // in a skipped state (either via `skip: true` or `skipToken`) we return\n  // `undefined` for the `queryRef` until the query has been enabled. Once\n  // enabled, a query ref is always returned regardless of whether the query is\n  // skipped again later.\n  const didFetchResult = React.useRef(fetchPolicy !== \"standby\");\n  didFetchResult.current ||= fetchPolicy !== \"standby\";\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n\n  const queryRef = suspenseCache.getQueryRef<TData, TStates>(cacheKey, () =>\n    client.watchQuery(\n      watchQueryOptions as ApolloClient.WatchQueryOptions<any, any>\n    )\n  );\n\n  const [wrappedQueryRef, setWrappedQueryRef] = React.useState(\n    wrapQueryRef(queryRef)\n  );\n  if (unwrapQueryRef(wrappedQueryRef) !== queryRef) {\n    setWrappedQueryRef(wrapQueryRef(queryRef));\n  }\n  if (queryRef.didChangeOptions(watchQueryOptions)) {\n    const promise = queryRef.applyOptions(watchQueryOptions);\n    updateWrappedQueryRef(wrappedQueryRef, promise);\n  }\n\n  // This prevents issues where rerendering useBackgroundQuery after the\n  // queryRef has been disposed would cause the hook to return a new queryRef\n  // instance since disposal also removes it from the suspense cache. We add\n  // the queryRef back in the suspense cache so that the next render will reuse\n  // this queryRef rather than initializing a new instance.\n  React.useEffect(() => {\n    // Since the queryRef is disposed async via `setTimeout`, we have to wait a\n    // tick before checking it and adding back to the suspense cache.\n    const id = setTimeout(() => {\n      if (queryRef.disposed) {\n        suspenseCache.add(cacheKey, queryRef);\n      }\n    });\n\n    return () => clearTimeout(id);\n    // Omitting the deps is intentional. This avoids stale closures and the\n    // conditional ensures we aren't running the logic on each render.\n  });\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      const promise = queryRef.fetchMore(options);\n\n      setWrappedQueryRef(wrapQueryRef(queryRef));\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = queryRef.refetch(variables);\n\n      setWrappedQueryRef(wrapQueryRef(queryRef));\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  React.useEffect(() => queryRef.softRetain(), [queryRef]);\n\n  return [\n    didFetchResult.current ? wrappedQueryRef : void 0,\n    {\n      fetchMore,\n      refetch,\n      // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n      subscribeToMore: queryRef.observable\n        .subscribeToMore as SubscribeToMoreFunction<TData, TVariables>,\n    },\n  ];\n}\n"], "names": [], "mappings": ";;AAgaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAhaA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAcA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAOA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAaA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wBAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yBAAA,CAAA;AA4XA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAhB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAIhC,CAJF,CAAA,CAAA,CAAA,CAI4D,EAC1D,CALF,CAAA,CAAA,CAAA,CAAA,CAAA,CAKuE,EALvE;IAUE,CAAF,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAe;IACb,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAjB,CACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACwB;IACpB,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EACnB,CADJ,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACmB,CADnB,CACoB,CADpB,CAAA,CAAA,CAAA,CAAA,EAC2B,CAD3B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EACuC,CADvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACgD,EAAE,CADlD,CAAA,CAAA,CAAA,CAAA,CAAA,CACyD,CAAC,CAD1D,CAAA,CAAA,CAAA,CAAA,EACiE,EAAE,CADnE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAC4E,CAAC,CAC1E,CAAC,CAFJ,CAAA,CAAA,CAAA,CAES,EAAE,CAFX,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAEuB,CAFvB,CAEiC,CAAC;AAClC;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAK1B,CALF,CAAA,CAAA,CAAA,CAK4D,EAC1D,CANF,CAAA,CAAA,CAAA,CAAA,CAAA,CAQqD,EARrD;IAaE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,EAAA,EAAiB,CAAjB,CAAA,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAhC,CAAiC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAwB,CAAxB,CAAA,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAxC,CAAyC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAA+C,CAAC;IAC9C,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA4B,CAA5B,CAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAhD,CAAiD,EAAE,CAAnD,CAAA,CAAA,CAAA,CAAA,CAAyD,EAAE,CAA3D,CAAA,CAAA,CAAA,CAAgE,EAAE,CAAlE,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAA2E,CAAC;IAC1E,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAqC,CAArC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;IACpD,CAAF,CAAA,CAAA,CAAA,EAAQ,EAAE,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAqB,CAArB,EAAA,EAAA,EAA4B,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC;IAEjC,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAqC,CAAC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAsD,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+D,CAAC;IAC9D,CAAF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA6B,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA6C,CAA7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsD;IAEpD,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA6B;QACzB,CAAJ,CAAA,CAAA,CAAA,CAAS;QACL,CAAJ,CAAA,EAAI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAtB,CAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC;QAC7B,CAAJ,CAAA,CAAQ,CAAR,CAAoB,CAAC,CAArB,CAAA,CAAA,CAAA,CAAA,CAA2B,CAAC,CAA5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,CAAC;IACrC,CAAG;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAiB,CAA7D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqE,EAAE,CAAvE,EAA0E,CAA1E,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CACU,CAAC,CADX,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACqB,CACf,CAFN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEmE,CAC9D,CACF;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAC,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,EAA5C,EAAgD,CAAhD,CAAA,CAAA,CAAA,CAAqD,CAAC,CAAtD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8D,CAC1D,CADJ,CAAA,EACI,CADJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACgB,CADhB,CACiB,CADjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACyB,CAAC,CACvB;IACD,CAAF,EAAA,CAAM,CAAN,CAAA,EAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAApB,CAAqB,CAArB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoC,EAApC,CAAA,CAAA,EAA0C,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,EAAE;QAChD,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,CAAC,CAAvB,CAAA,EAAuB,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAnC,CAAoC,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC,CAAC;IAC5C;IACA,CAAF,EAAA,CAAM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiD,CAAC,EAAE;QAChD,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAoB,CAApB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4B,CAAC,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,CAAC,CAA1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAC;QACxD,CAAJ,CAAA,EAAI,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAzB,CAA0B,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyC,EAAE,CAA3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkD,CAAC;IACjD;IAEA,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACI,CAAJ,CAAA,CAAA,CAAA,EAAU,CAAV,EAAA,EAAe,CAAf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAA;YACM,CAAN,EAAA,CAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2B,EAAE;gBACrB,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAtB,CAAA,CAAyB,CAAC,CAA1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,EAAE,CAApC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,CAAC;YACvC;QACF,CAAC,CAAC;QAEF,CAAJ,CAAA,CAAA,CAAA,CAAA,EAAW,CAAX,EAAc,CAAd,EAAiB,CAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAC,CAA9B,CAAgC,CAAC;QAC7B,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;QACI,CAAJ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACE,CAAC,CAAC;IAEF,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA0D,CAA1D,CAAA,CAAA,CAAA,CAA+D,CAAC,CAAhE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2E,CACvE,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CACY,EAAE,CADd,EAAA;QAEM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC,CAAC,CAAzC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC;QAE3C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAArC,CAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAAC;QAE1C,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAChB,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACa,CAAC,CACX;IAED,CAAF,CAAA,CAAA,CAAA,EAAQ,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsD,CAAtD,CAAA,CAAA,CAAA,CAA2D,CAAC,CAA5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CACnE,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACc,EAAE,CADhB,EAAA;QAEM,CAAN,CAAA,CAAA,CAAA,EAAY,CAAZ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAsB,CAAtB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAA/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAC,CAAvC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgD,CAAC;QAE3C,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,CAAC,CAAzB,CAAA,EAAyB,CAAzB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqC,CAArC,CAAsC,CAAtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8C,CAAC,CAAC;QAE1C,CAAN,CAAA,CAAA,CAAA,CAAA,EAAa,CAAb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB;IAChB,CAAC,EACD,CAAC,CADL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACa,CAAC,CACX;IAED,CAAF,CAAA,CAAA,CAAA,CAAO,CAAC,CAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2C,CAA3C,CAA6C,EAAE,CAAC,CAAhD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwD,CAAC,CAAC;IAExD,CAAF,CAAA,CAAA,CAAA,CAAA,EAAS;QACL,CAAJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAnB,CAAA,CAAA,CAAA,CAAA,CAAA,EAA2B,EAAE,CAA7B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA6C,EAAE,CAA/C,CAAA,CAAA,EAAoD,CAAC;QACjD;YACE,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe;YACT,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa;YACP,CAAN,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;YACM,CAAN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE,CAAvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;gBACA,CAAS,CAAT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE;QACtE,CAAK;IACL,CAAG;AACH;"}