/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { registerApolloClient } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const registerApolloClient: typeof import("@apollo/client-integration-nextjs").registerApolloClient;
export { registerApolloClient };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { ApolloNextAppProvider } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const ApolloNextAppProvider: typeof import("@apollo/client-integration-nextjs").ApolloNextAppProvider;
export { ApolloNextAppProvider };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { DebounceMultipartResponsesLink } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const DebounceMultipartResponsesLink: typeof import("@apollo/client-integration-nextjs").DebounceMultipartResponsesLink;
export { DebounceMultipartResponsesLink };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { ApolloClient } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const ApolloClient: typeof import("@apollo/client-integration-nextjs").ApolloClient;
export { ApolloClient };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { InMemoryCache } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const InMemoryCache: typeof import("@apollo/client-integration-nextjs").InMemoryCache;
export { InMemoryCache };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { RemoveMultipartDirectivesLink } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const RemoveMultipartDirectivesLink: typeof import("@apollo/client-integration-nextjs").RemoveMultipartDirectivesLink;
export { RemoveMultipartDirectivesLink };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { SSRMultipartLink } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const SSRMultipartLink: typeof import("@apollo/client-integration-nextjs").SSRMultipartLink;
export { SSRMultipartLink };

/**
 * @deprecated
 * This import has moved to `"@apollo/client-integration-nextjs"`.
 *
 * Please update your import to
 * ```ts
 * import { resetApolloClientSingletons } from "@apollo/client-integration-nextjs";
 * ```
 */
declare const resetApolloClientSingletons: typeof import("@apollo/client-integration-nextjs").resetApolloClientSingletons;
export { resetApolloClientSingletons };
