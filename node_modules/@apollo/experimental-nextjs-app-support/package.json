{"name": "@apollo/experimental-nextjs-app-support", "version": "0.13.0", "repository": {"url": "git+https://github.com/apollographql/apollo-client-integrations.git"}, "keywords": ["apollo", "nextjs", "apollo-client", "graphql", "ssr", "rsc", "app-router", "app"], "type": "module", "exports": {".": {"require": {"types": "./redirect/index.d.cts", "default": "./redirect/index.cjs"}, "import": {"types": "./redirect/index.d.ts", "default": "./redirect/index.js"}}, "./rsc": {"require": {"types": "./redirect/rsc.d.cts", "react-server": "./redirect/rsc.cjs", "default": "./redirect/empty.cjs"}, "import": {"types": "./redirect/rsc.d.ts", "react-server": "./redirect/rsc.js", "default": "./redirect/empty.js"}}, "./ssr": {"require": {"types": "./redirect/ssr.d.cts", "default": "./redirect/ssr.cjs"}, "import": {"types": "./redirect/ssr.d.ts", "default": "./redirect/ssr.js"}}, "./package.json": "./package.json"}, "typesVersions": {"*": {"ssr": ["./redirect/ssr.d.ts"], "rsc": ["./redirect/rsc.d.ts"]}}, "typings": "./redirect/index.d.ts", "author": "<EMAIL>", "license": "MIT", "files": ["redirect/", "package.json", "LICENSE.md", "README.md"], "scripts": {"build": "true", "test": "true", "test-bundle": "yarn test-bundle:attw && yarn test-bundle:package && yarn test-bundle:publint && yarn test-bundle:shape", "test-bundle:attw": "attw --pack .", "test-bundle:package": "yarn workspace monorepo verify-package-json $PWD/package.json", "test-bundle:publint": "publint --strict", "test-bundle:shape": "yarn workspace monorepo verify-package-shape $PWD/package-shape.json", "bundle-info": "yarn test-bundle --format json | jq '.analysis.entrypoints|to_entries|map({key:.key,value:.value.resolutions|to_entries|map({key:.key,value:.value.resolution.fileName })|from_entries})|from_entries'"}, "devDependencies": {"@apollo/client": "^4.0.0", "@arethetypeswrong/cli": "0.15.3", "@tsconfig/recommended": "1.0.6", "@types/react": "*", "@types/react-dom": "*", "graphql": "*", "publint": "0.2.7", "react": "*", "react-dom": "*", "rxjs": "^7.8.2", "typescript": "^5.7.3"}, "peerDependencies": {"@apollo/client": "^4.0.0", "next": "^15.2.3", "react": "^19", "rxjs": "^7.3.0"}, "dependencies": {"@apollo/client-integration-nextjs": "0.13.0"}}