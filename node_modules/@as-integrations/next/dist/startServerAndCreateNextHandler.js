"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startServerAndCreateNextHandler = startServerAndCreateNextHandler;
var getBody_1 = require("./lib/getBody");
var getHeaders_1 = require("./lib/getHeaders");
var isNextApiRequest_1 = require("./lib/isNextApiRequest");
var stream_1 = require("stream");
var url_1 = require("url");
// eslint-disable-next-line @typescript-eslint/no-explicit-any
var defaultContext = function () { return __awaiter(void 0, void 0, void 0, function () { return __generator(this, function (_a) {
    return [2 /*return*/, ({})];
}); }); };
function startServerAndCreateNextHandler(server, options) {
    server.startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests();
    var contextFunction = (options === null || options === void 0 ? void 0 : options.context) || defaultContext;
    function handler(req, res) {
        return __awaiter(this, void 0, void 0, function () {
            var httpGraphQLResponse, _a, _b, _c, _d, _e, key, value, headers, _f, _g, _h, key, value;
            var _j, _k, e_1, _l, e_2, _m;
            return __generator(this, function (_o) {
                switch (_o.label) {
                    case 0:
                        _b = (_a = server).executeHTTPGraphQLRequest;
                        _j = {
                            context: function () { return contextFunction(req, res); }
                        };
                        _k = {};
                        return [4 /*yield*/, (0, getBody_1.getBody)(req)];
                    case 1: return [4 /*yield*/, _b.apply(_a, [(_j.httpGraphQLRequest = (_k.body = _o.sent(),
                                _k.headers = (0, getHeaders_1.getHeaders)(req),
                                _k.method = req.method || 'POST',
                                _k.search = req.url ? (0, url_1.parse)(req.url).search || '' : '',
                                _k),
                                _j)])];
                    case 2:
                        httpGraphQLResponse = _o.sent();
                        if ((0, isNextApiRequest_1.isNextApiRequest)(req)) {
                            if (!res) {
                                throw new Error('API Routes require you to pass both the req and res object.');
                            }
                            try {
                                for (_c = __values(httpGraphQLResponse.headers), _d = _c.next(); !_d.done; _d = _c.next()) {
                                    _e = __read(_d.value, 2), key = _e[0], value = _e[1];
                                    res.setHeader(key, value);
                                }
                            }
                            catch (e_1_1) { e_1 = { error: e_1_1 }; }
                            finally {
                                try {
                                    if (_d && !_d.done && (_l = _c.return)) _l.call(_c);
                                }
                                finally { if (e_1) throw e_1.error; }
                            }
                            res.statusCode = httpGraphQLResponse.status || 200;
                            if (httpGraphQLResponse.body.kind === 'complete') {
                                res.send(httpGraphQLResponse.body.string);
                                res.end();
                            }
                            else {
                                res.send(stream_1.Readable.from(httpGraphQLResponse.body.asyncIterator));
                            }
                            return [2 /*return*/];
                        }
                        headers = {};
                        try {
                            for (_f = __values(httpGraphQLResponse.headers), _g = _f.next(); !_g.done; _g = _f.next()) {
                                _h = __read(_g.value, 2), key = _h[0], value = _h[1];
                                headers[key] = value;
                            }
                        }
                        catch (e_2_1) { e_2 = { error: e_2_1 }; }
                        finally {
                            try {
                                if (_g && !_g.done && (_m = _f.return)) _m.call(_f);
                            }
                            finally { if (e_2) throw e_2.error; }
                        }
                        // eslint-disable-next-line consistent-return
                        return [2 /*return*/, new Response(httpGraphQLResponse.body.kind === 'complete'
                                ? httpGraphQLResponse.body.string
                                : new ReadableStream({
                                    pull: function (controller) {
                                        return __awaiter(this, void 0, void 0, function () {
                                            var _a, value, done;
                                            return __generator(this, function (_b) {
                                                switch (_b.label) {
                                                    case 0:
                                                        if (!(httpGraphQLResponse.body.kind === 'chunked')) return [3 /*break*/, 2];
                                                        return [4 /*yield*/, httpGraphQLResponse.body.asyncIterator.next()];
                                                    case 1:
                                                        _a = _b.sent(), value = _a.value, done = _a.done;
                                                        if (done) {
                                                            controller.close();
                                                        }
                                                        else {
                                                            controller.enqueue(value);
                                                        }
                                                        _b.label = 2;
                                                    case 2: return [2 /*return*/];
                                                }
                                            });
                                        });
                                    },
                                }), { headers: headers, status: httpGraphQLResponse.status || 200 })];
                }
            });
        });
    }
    return handler;
}
