{"name": "supplysight", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@apollo/client": "^4.0.0", "@apollo/experimental-nextjs-app-support": "^0.13.0", "@apollo/server": "^5.0.0", "@as-integrations/next": "^4.0.0", "graphql": "^16.11.0", "lucide-react": "^0.541.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}