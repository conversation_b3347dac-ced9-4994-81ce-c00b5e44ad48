module.exports = [
"[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/getEmbeddedHTML.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getEmbeddedExplorerHTML",
    ()=>getEmbeddedExplorerHTML,
    "getEmbeddedSandboxHTML",
    ()=>getEmbeddedSandboxHTML
]);
function getConfigStringForHtml(config) {
    return JSON.stringify(config).replace('<', '\\u003c').replace('>', '\\u003e').replace('&', '\\u0026').replace("'", '\\u0027');
}
const getEmbeddedExplorerHTML = (explorerCdnVersion, config, apolloServerVersion, nonce)=>{
    const productionLandingPageEmbedConfigOrDefault = {
        displayOptions: {},
        persistExplorerState: false,
        runTelemetry: true,
        ...typeof config.embed === 'boolean' ? {} : config.embed
    };
    const embeddedExplorerParams = {
        graphRef: config.graphRef,
        target: '#embeddableExplorer',
        initialState: {
            ...'document' in config || 'headers' in config || 'variables' in config ? {
                document: config.document,
                headers: config.headers,
                variables: config.variables
            } : {},
            ...'collectionId' in config ? {
                collectionId: config.collectionId,
                operationId: config.operationId
            } : {},
            displayOptions: {
                ...productionLandingPageEmbedConfigOrDefault.displayOptions
            }
        },
        persistExplorerState: productionLandingPageEmbedConfigOrDefault.persistExplorerState,
        includeCookies: config.includeCookies,
        runtime: apolloServerVersion,
        runTelemetry: productionLandingPageEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Explorer cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableExplorer {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableExplorer"></div>
<script nonce="${nonce}" src="https://embeddable-explorer.cdn.apollographql.com/${encodeURIComponent(explorerCdnVersion)}/embeddable-explorer.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var endpointUrl = window.location.href;
  var embeddedExplorerConfig = ${getConfigStringForHtml(embeddedExplorerParams)};
  new window.EmbeddedExplorer({
    ...embeddedExplorerConfig,
    endpointUrl,
  });
</script>
`;
};
const getEmbeddedSandboxHTML = (sandboxCdnVersion, config, apolloServerVersion, nonce)=>{
    const localDevelopmentEmbedConfigOrDefault = {
        runTelemetry: true,
        endpointIsEditable: false,
        initialState: {},
        ...typeof config.embed === 'boolean' ? {} : config.embed ?? {}
    };
    const embeddedSandboxConfig = {
        target: '#embeddableSandbox',
        initialState: {
            ...'document' in config || 'headers' in config || 'variables' in config ? {
                document: config.document,
                variables: config.variables,
                headers: config.headers
            } : {},
            ...'collectionId' in config ? {
                collectionId: config.collectionId,
                operationId: config.operationId
            } : {},
            includeCookies: config.includeCookies,
            ...localDevelopmentEmbedConfigOrDefault.initialState
        },
        hideCookieToggle: false,
        endpointIsEditable: localDevelopmentEmbedConfigOrDefault.endpointIsEditable,
        runtime: apolloServerVersion,
        runTelemetry: localDevelopmentEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Sandbox cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableSandbox {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableSandbox"></div>
<script nonce="${nonce}" src="https://embeddable-sandbox.cdn.apollographql.com/${encodeURIComponent(sandboxCdnVersion)}/embeddable-sandbox.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var initialEndpoint = window.location.href;
  var embeddedSandboxConfig = ${getConfigStringForHtml(embeddedSandboxConfig)};
  new window.EmbeddedSandbox(
    {
      ...embeddedSandboxConfig,
      initialEndpoint,
    }
  );
</script>
`;
}; //# sourceMappingURL=getEmbeddedHTML.js.map
}),
"[project]/node_modules/@apollo/server/dist/esm/generated/packageVersion.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "packageVersion",
    ()=>packageVersion
]);
const packageVersion = "5.0.0"; //# sourceMappingURL=packageVersion.js.map
}),
"[project]/node_modules/uuid/dist/esm/native.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
const __TURBOPACK__default__export__ = {
    randomUUID: __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["randomUUID"]
};
}),
"[project]/node_modules/uuid/dist/esm/rng.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>rng
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
const rnds8Pool = new Uint8Array(256);
let poolPtr = rnds8Pool.length;
function rng() {
    if (poolPtr > rnds8Pool.length - 16) {
        (0, __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["randomFillSync"])(rnds8Pool);
        poolPtr = 0;
    }
    return rnds8Pool.slice(poolPtr, poolPtr += 16);
}
}),
"[project]/node_modules/uuid/dist/esm/regex.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
const __TURBOPACK__default__export__ = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;
}),
"[project]/node_modules/uuid/dist/esm/validate.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$regex$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/regex.js [app-route] (ecmascript)");
;
function validate(uuid) {
    return typeof uuid === 'string' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$regex$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].test(uuid);
}
const __TURBOPACK__default__export__ = validate;
}),
"[project]/node_modules/uuid/dist/esm/stringify.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "unsafeStringify",
    ()=>unsafeStringify
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/validate.js [app-route] (ecmascript)");
;
const byteToHex = [];
for(let i = 0; i < 256; ++i){
    byteToHex.push((i + 0x100).toString(16).slice(1));
}
function unsafeStringify(arr, offset = 0) {
    return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
}
function stringify(arr, offset = 0) {
    const uuid = unsafeStringify(arr, offset);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(uuid)) {
        throw TypeError('Stringified UUID is invalid');
    }
    return uuid;
}
const __TURBOPACK__default__export__ = stringify;
}),
"[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$native$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/native.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$rng$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/rng.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$stringify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/stringify.js [app-route] (ecmascript)");
;
;
;
function v4(options, buf, offset) {
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$native$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].randomUUID && !buf && !options) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$native$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].randomUUID();
    }
    options = options || {};
    const rnds = options.random ?? options.rng?.() ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$rng$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
    if (rnds.length < 16) {
        throw new Error('Random bytes length must be >= 16');
    }
    rnds[6] = rnds[6] & 0x0f | 0x40;
    rnds[8] = rnds[8] & 0x3f | 0x80;
    if (buf) {
        offset = offset || 0;
        if (offset < 0 || offset + 16 > buf.length) {
            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);
        }
        for(let i = 0; i < 16; ++i){
            buf[offset + i] = rnds[i];
        }
        return buf;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$stringify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unsafeStringify"])(rnds);
}
const __TURBOPACK__default__export__ = v4;
}),
"[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "v4",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript)");
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ApolloServerPluginLandingPageLocalDefault",
    ()=>ApolloServerPluginLandingPageLocalDefault,
    "ApolloServerPluginLandingPageProductionDefault",
    ()=>ApolloServerPluginLandingPageProductionDefault,
    "DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION",
    ()=>DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION,
    "DEFAULT_EMBEDDED_EXPLORER_VERSION",
    ()=>DEFAULT_EMBEDDED_EXPLORER_VERSION,
    "DEFAULT_EMBEDDED_SANDBOX_VERSION",
    ()=>DEFAULT_EMBEDDED_SANDBOX_VERSION
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$plugin$2f$landingPage$2f$default$2f$getEmbeddedHTML$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/getEmbeddedHTML.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$generated$2f$packageVersion$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/generated/packageVersion.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
;
function ApolloServerPluginLandingPageLocalDefault(options = {}) {
    const { version, __internal_apolloStudioEnv__, ...rest } = {
        embed: true,
        ...options
    };
    return ApolloServerPluginLandingPageDefault(version, {
        isProd: false,
        apolloStudioEnv: __internal_apolloStudioEnv__,
        ...rest
    });
}
function ApolloServerPluginLandingPageProductionDefault(options = {}) {
    const { version, __internal_apolloStudioEnv__, ...rest } = options;
    return ApolloServerPluginLandingPageDefault(version, {
        isProd: true,
        apolloStudioEnv: __internal_apolloStudioEnv__,
        ...rest
    });
}
function encodeConfig(config) {
    return JSON.stringify(encodeURIComponent(JSON.stringify(config)));
}
const getNonEmbeddedLandingPageHTML = (cdnVersion, config, apolloServerVersion, nonce)=>{
    const encodedConfig = encodeConfig(config);
    return `
 <div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>The full landing page cannot be loaded; it appears that you might be offline.</p>
</div>
<script nonce="${nonce}">window.landingPage = ${encodedConfig};</script>
<script nonce="${nonce}" src="https://apollo-server-landing-page.cdn.apollographql.com/${encodeURIComponent(cdnVersion)}/static/js/main.js?runtime=${apolloServerVersion}"></script>`;
};
const DEFAULT_EMBEDDED_EXPLORER_VERSION = 'v3';
const DEFAULT_EMBEDDED_SANDBOX_VERSION = 'v2';
const DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION = '_latest';
function ApolloServerPluginLandingPageDefault(maybeVersion, config) {
    const explorerVersion = maybeVersion ?? DEFAULT_EMBEDDED_EXPLORER_VERSION;
    const sandboxVersion = maybeVersion ?? DEFAULT_EMBEDDED_SANDBOX_VERSION;
    const apolloServerLandingPageVersion = maybeVersion ?? DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION;
    const apolloServerVersion = `@apollo/server@${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$generated$2f$packageVersion$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["packageVersion"]}`;
    const scriptSafeList = [
        'https://apollo-server-landing-page.cdn.apollographql.com',
        'https://embeddable-sandbox.cdn.apollographql.com',
        'https://embeddable-explorer.cdn.apollographql.com'
    ].join(' ');
    const styleSafeList = [
        'https://apollo-server-landing-page.cdn.apollographql.com',
        'https://embeddable-sandbox.cdn.apollographql.com',
        'https://embeddable-explorer.cdn.apollographql.com',
        'https://fonts.googleapis.com'
    ].join(' ');
    const iframeSafeList = [
        'https://explorer.embed.apollographql.com',
        'https://sandbox.embed.apollographql.com',
        'https://embed.apollo.local:3000'
    ].join(' ');
    return {
        __internal_installed_implicitly__: false,
        async serverWillStart () {
            return {
                async renderLandingPage () {
                    const encodedASLandingPageVersion = encodeURIComponent(apolloServerLandingPageVersion);
                    async function html() {
                        const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha256').update((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])()).digest('hex');
                        const scriptCsp = `script-src 'self' 'nonce-${nonce}' ${scriptSafeList}`;
                        const styleCsp = `style-src 'nonce-${nonce}' ${styleSafeList}`;
                        const imageCsp = `img-src https://apollo-server-landing-page.cdn.apollographql.com`;
                        const manifestCsp = `manifest-src https://apollo-server-landing-page.cdn.apollographql.com`;
                        const frameCsp = `frame-src ${iframeSafeList}`;
                        return `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Security-Policy" content="${scriptCsp}; ${styleCsp}; ${imageCsp}; ${manifestCsp}; ${frameCsp}" />
    <link
      rel="icon"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/assets/favicon.png"
    />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro&display=swap"
      rel="stylesheet"
    />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Apollo server landing page" />
    <link
      rel="apple-touch-icon"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/assets/favicon.png"
    />
    <link
      rel="manifest"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/manifest.json"
    />
    <title>Apollo Server</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="react-root">
      <style nonce=${nonce}>
        body {
          margin: 0;
          overflow-x: hidden;
          overflow-y: hidden;
        }
        .fallback {
          opacity: 0;
          animation: fadeIn 1s 1s;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
          padding: 1em;
        }
        @keyframes fadeIn {
          0% {opacity:0;}
          100% {opacity:1; }
        }
      </style>
    ${config.embed ? 'graphRef' in config && config.graphRef ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$plugin$2f$landingPage$2f$default$2f$getEmbeddedHTML$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEmbeddedExplorerHTML"])(explorerVersion, config, apolloServerVersion, nonce) : !('graphRef' in config) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$plugin$2f$landingPage$2f$default$2f$getEmbeddedHTML$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEmbeddedSandboxHTML"])(sandboxVersion, config, apolloServerVersion, nonce) : getNonEmbeddedLandingPageHTML(apolloServerLandingPageVersion, config, apolloServerVersion, nonce) : getNonEmbeddedLandingPageHTML(apolloServerLandingPageVersion, config, apolloServerVersion, nonce)}
    </div>
  </body>
</html>
          `;
                    }
                    return {
                        html
                    };
                }
            };
        }
    };
} //# sourceMappingURL=index.js.map
}),
];

//# sourceMappingURL=node_modules_ecc3b0cf._.js.map