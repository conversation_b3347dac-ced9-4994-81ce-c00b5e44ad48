module.exports = [
"[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "asArray",
    ()=>asArray,
    "assertSome",
    ()=>assertSome,
    "compareNodes",
    ()=>compareNodes,
    "compareStrings",
    ()=>compareStrings,
    "isDocumentString",
    ()=>isDocumentString,
    "isSome",
    ()=>isSome,
    "isUrl",
    ()=>isUrl,
    "isValidPath",
    ()=>isValidPath,
    "nodeToString",
    ()=>nodeToString
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/parser.mjs [app-route] (ecmascript)");
;
const URL_REGEXP = /^(https?|wss?|file):\/\//;
function isUrl(str) {
    if (typeof str !== 'string') {
        return false;
    }
    if (!URL_REGEXP.test(str)) {
        return false;
    }
    if (URL.canParse) {
        return URL.canParse(str);
    }
    try {
        const url = new URL(str);
        return !!url;
    } catch (e) {
        return false;
    }
}
const asArray = (fns)=>Array.isArray(fns) ? fns : fns ? [
        fns
    ] : [];
const invalidDocRegex = /\.[a-z0-9]+$/i;
function isDocumentString(str) {
    if (typeof str !== 'string') {
        return false;
    }
    // XXX: is-valid-path or is-glob treat SDL as a valid path
    // (`scalar Date` for example)
    // this why checking the extension is fast enough
    // and prevent from parsing the string in order to find out
    // if the string is a SDL
    if (invalidDocRegex.test(str) || isUrl(str)) {
        return false;
    }
    try {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(str);
        return true;
    } catch (e) {
        if (!e.message.includes('EOF') && str.replace(/(\#[^*]*)/g, '').trim() !== '' && str.includes(' ')) {
            throw new Error(`Failed to parse the GraphQL document. ${e.message}\n${str}`);
        }
    }
    return false;
}
const invalidPathRegex = /[‘“!%^<>`\n]/;
function isValidPath(str) {
    return typeof str === 'string' && !invalidPathRegex.test(str);
}
function compareStrings(a, b) {
    if (String(a) < String(b)) {
        return -1;
    }
    if (String(a) > String(b)) {
        return 1;
    }
    return 0;
}
function nodeToString(a) {
    let name;
    if ('alias' in a) {
        name = a.alias?.value;
    }
    if (name == null && 'name' in a) {
        name = a.name?.value;
    }
    if (name == null) {
        name = a.kind;
    }
    return name;
}
function compareNodes(a, b, customFn) {
    const aStr = nodeToString(a);
    const bStr = nodeToString(b);
    if (typeof customFn === 'function') {
        return customFn(aStr, bStr);
    }
    return compareStrings(aStr, bStr);
}
function isSome(input) {
    return input != null;
}
function assertSome(input, message = 'Value should be something') {
    if (input == null) {
        throw new Error(message);
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/mergeDeep.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "mergeDeep",
    ()=>mergeDeep
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
function mergeDeep(sources, respectPrototype = false, respectArrays = false, respectArrayLength = false) {
    let expectedLength;
    let allArrays = true;
    const areArraysInTheSameLength = sources.every((source)=>{
        if (Array.isArray(source)) {
            if (expectedLength === undefined) {
                expectedLength = source.length;
                return true;
            } else if (expectedLength === source.length) {
                return true;
            }
        } else {
            allArrays = false;
        }
        return false;
    });
    if (respectArrayLength && areArraysInTheSameLength) {
        return new Array(expectedLength).fill(null).map((_, index)=>mergeDeep(sources.map((source)=>source[index]), respectPrototype, respectArrays, respectArrayLength));
    }
    if (allArrays) {
        return sources.flat(1);
    }
    let output;
    let firstObjectSource;
    if (respectPrototype) {
        firstObjectSource = sources.find((source)=>isObject(source));
        if (output == null) {
            output = {};
        }
        if (firstObjectSource) {
            Object.setPrototypeOf(output, Object.create(Object.getPrototypeOf(firstObjectSource)));
        }
    }
    for (const source of sources){
        if (isObject(source)) {
            if (firstObjectSource) {
                const outputPrototype = Object.getPrototypeOf(output);
                const sourcePrototype = Object.getPrototypeOf(source);
                if (sourcePrototype) {
                    for (const key of Object.getOwnPropertyNames(sourcePrototype)){
                        const descriptor = Object.getOwnPropertyDescriptor(sourcePrototype, key);
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSome"])(descriptor)) {
                            Object.defineProperty(outputPrototype, key, descriptor);
                        }
                    }
                }
            }
            for(const key in source){
                if (output == null) {
                    output = {};
                }
                if (key in output) {
                    output[key] = mergeDeep([
                        output[key],
                        source[key]
                    ], respectPrototype, respectArrays, respectArrayLength);
                } else {
                    output[key] = source[key];
                }
            }
        } else if (Array.isArray(source)) {
            if (!Array.isArray(output)) {
                output = source;
            } else {
                output = mergeDeep([
                    output,
                    source
                ], respectPrototype, respectArrays, respectArrayLength);
            }
        } else {
            output = source;
        }
    }
    return output;
}
function isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/astFromType.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "astFromType",
    ()=>astFromType
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cross-inspect/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
;
;
function astFromType(type) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(type)) {
        const innerType = astFromType(type.ofType);
        if (innerType.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NON_NULL_TYPE) {
            throw new Error(`Invalid type node ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inspect"])(type)}. Inner type of non-null type cannot be a non-null type.`);
        }
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NON_NULL_TYPE,
            type: innerType
        };
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListType"])(type)) {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST_TYPE,
            type: astFromType(type.ofType)
        };
    }
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE,
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        }
    };
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/astFromValueUntyped.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "astFromValueUntyped",
    ()=>astFromValueUntyped
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
;
function astFromValueUntyped(value) {
    // only explicit null, not undefined, NaN
    if (value === null) {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NULL
        };
    }
    // undefined
    if (value === undefined) {
        return null;
    }
    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but
    // the value is not an array, convert the value using the list's item type.
    if (Array.isArray(value)) {
        const valuesNodes = [];
        for (const item of value){
            const itemNode = astFromValueUntyped(item);
            if (itemNode != null) {
                valuesNodes.push(itemNode);
            }
        }
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST,
            values: valuesNodes
        };
    }
    if (typeof value === 'object') {
        if (value?.toJSON) {
            return astFromValueUntyped(value.toJSON());
        }
        const fieldNodes = [];
        for(const fieldName in value){
            const fieldValue = value[fieldName];
            const ast = astFromValueUntyped(fieldValue);
            if (ast) {
                fieldNodes.push({
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_FIELD,
                    name: {
                        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
                        value: fieldName
                    },
                    value: ast
                });
            }
        }
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT,
            fields: fieldNodes
        };
    }
    // Others serialize based on their corresponding JavaScript scalar types.
    if (typeof value === 'boolean') {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].BOOLEAN,
            value
        };
    }
    if (typeof value === 'bigint') {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INT,
            value: String(value)
        };
    }
    // JavaScript numbers can be Int or Float values.
    if (typeof value === 'number' && isFinite(value)) {
        const stringNum = String(value);
        return integerStringRegExp.test(stringNum) ? {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INT,
            value: stringNum
        } : {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].FLOAT,
            value: stringNum
        };
    }
    if (typeof value === 'string') {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].STRING,
            value
        };
    }
    throw new TypeError(`Cannot convert value to AST: ${value}.`);
}
/**
 * IntValue:
 *   - NegativeSign? 0
 *   - NegativeSign? NonZeroDigit ( Digit+ )?
 */ const integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;
}),
"[project]/node_modules/@graphql-tools/utils/esm/jsutils.js [app-route] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "hasOwnProperty",
    ()=>hasOwnProperty,
    "isIterableObject",
    ()=>isIterableObject,
    "isObjectLike",
    ()=>isObjectLike,
    "promiseReduce",
    ()=>promiseReduce
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$whatwg$2d$node$2f$promise$2d$helpers$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@whatwg-node/promise-helpers/esm/index.js [app-route] (ecmascript)");
;
function isIterableObject(value) {
    return value != null && typeof value === 'object' && Symbol.iterator in value;
}
function isObjectLike(value) {
    return typeof value === 'object' && value !== null;
}
;
function promiseReduce(values, callbackFn, initialValue) {
    let accumulator = initialValue;
    for (const value of values){
        accumulator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$whatwg$2d$node$2f$promise$2d$helpers$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleMaybePromise"])(()=>accumulator, (resolved)=>callbackFn(resolved, value));
    }
    return accumulator;
}
function hasOwnProperty(obj, prop) {
    return Object.prototype.hasOwnProperty.call(obj, prop);
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/astFromValue.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "astFromValue",
    ()=>astFromValue
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cross-inspect/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValueUntyped$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/astFromValueUntyped.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$jsutils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/jsutils.js [app-route] (ecmascript) <locals>");
;
;
;
;
function astFromValue(value, type) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(type)) {
        const astValue = astFromValue(value, type.ofType);
        if (astValue?.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NULL) {
            return null;
        }
        return astValue;
    }
    // only explicit null, not undefined, NaN
    if (value === null) {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NULL
        };
    }
    // undefined
    if (value === undefined) {
        return null;
    }
    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but
    // the value is not an array, convert the value using the list's item type.
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListType"])(type)) {
        const itemType = type.ofType;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$jsutils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isIterableObject"])(value)) {
            const valuesNodes = [];
            for (const item of value){
                const itemNode = astFromValue(item, itemType);
                if (itemNode != null) {
                    valuesNodes.push(itemNode);
                }
            }
            return {
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST,
                values: valuesNodes
            };
        }
        return astFromValue(value, itemType);
    }
    // Populate the fields of the input object by creating ASTs from each value
    // in the JavaScript object according to the fields in the input type.
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$jsutils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isObjectLike"])(value)) {
            return null;
        }
        const fieldNodes = [];
        for (const field of Object.values(type.getFields())){
            const fieldValue = astFromValue(value[field.name], field.type);
            if (fieldValue) {
                fieldNodes.push({
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_FIELD,
                    name: {
                        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
                        value: field.name
                    },
                    value: fieldValue
                });
            }
        }
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT,
            fields: fieldNodes
        };
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLeafType"])(type)) {
        // Since value is an internally represented value, it must be serialized
        // to an externally represented value before converting into an AST.
        const serialized = type.serialize(value);
        if (serialized == null) {
            return null;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            return {
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM,
                value: serialized
            };
        }
        // ID types can use Int literals.
        if (type.name === 'ID' && typeof serialized === 'string' && integerStringRegExp.test(serialized)) {
            return {
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INT,
                value: serialized
            };
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValueUntyped$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromValueUntyped"])(serialized);
    }
    /* c8 ignore next 3 */ // Not reachable, all possible types have been considered.
    console.assert(false, 'Unexpected input type: ' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inspect"])(type));
}
/**
 * IntValue:
 *   - NegativeSign? 0
 *   - NegativeSign? NonZeroDigit ( Digit+ )?
 */ const integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;
}),
"[project]/node_modules/@graphql-tools/utils/esm/descriptionFromObject.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getDescriptionNode",
    ()=>getDescriptionNode
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
;
function getDescriptionNode(obj) {
    if (obj.astNode?.description) {
        return {
            ...obj.astNode.description,
            block: true
        };
    }
    if (obj.description) {
        return {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].STRING,
            value: obj.description,
            block: true
        };
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/errors.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "createGraphQLError",
    ()=>createGraphQLError,
    "relocatedError",
    ()=>relocatedError
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$version$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/version.mjs [app-route] (ecmascript)");
;
const possibleGraphQLErrorProperties = [
    'message',
    'locations',
    'path',
    'nodes',
    'source',
    'positions',
    'originalError',
    'name',
    'stack',
    'extensions'
];
function isGraphQLErrorLike(error) {
    return error != null && typeof error === 'object' && Object.keys(error).every((key)=>possibleGraphQLErrorProperties.includes(key));
}
function createGraphQLError(message, options) {
    if (options?.originalError && !(options.originalError instanceof Error) && isGraphQLErrorLike(options.originalError)) {
        options.originalError = createGraphQLError(options.originalError.message, options.originalError);
    }
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$version$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["versionInfo"].major >= 17) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"](message, options);
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"](message, options?.nodes, options?.source, options?.positions, options?.path, options?.originalError, options?.extensions);
}
function relocatedError(originalError, path) {
    return createGraphQLError(originalError.message, {
        nodes: originalError.nodes,
        source: originalError.source,
        positions: originalError.positions,
        path: path == null ? originalError.path : path,
        originalError,
        extensions: originalError.extensions
    });
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/getArgumentValues.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getArgumentValues",
    ()=>getArgumentValues
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cross-inspect/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/printer.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$valueFromAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/valueFromAST.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/errors.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$jsutils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/jsutils.js [app-route] (ecmascript) <locals>");
;
;
;
;
function getArgumentValues(def, node, variableValues = {}) {
    const coercedValues = {};
    const argumentNodes = node.arguments ?? [];
    const argNodeMap = argumentNodes.reduce((prev, arg)=>({
            ...prev,
            [arg.name.value]: arg
        }), {});
    for (const { name, type: argType, defaultValue } of def.args){
        const argumentNode = argNodeMap[name];
        if (!argumentNode) {
            if (defaultValue !== undefined) {
                coercedValues[name] = defaultValue;
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(argType)) {
                throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGraphQLError"])(`Argument "${name}" of required type "${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inspect"])(argType)}" ` + 'was not provided.', {
                    nodes: [
                        node
                    ]
                });
            }
            continue;
        }
        const valueNode = argumentNode.value;
        let isNull = valueNode.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NULL;
        if (valueNode.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].VARIABLE) {
            const variableName = valueNode.name.value;
            if (variableValues == null || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$jsutils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasOwnProperty"])(variableValues, variableName)) {
                if (defaultValue !== undefined) {
                    coercedValues[name] = defaultValue;
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(argType)) {
                    throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGraphQLError"])(`Argument "${name}" of required type "${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inspect"])(argType)}" ` + `was provided the variable "$${variableName}" which was not provided a runtime value.`, {
                        nodes: [
                            valueNode
                        ]
                    });
                }
                continue;
            }
            isNull = variableValues[variableName] == null;
        }
        if (isNull && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(argType)) {
            throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGraphQLError"])(`Argument "${name}" of non-null type "${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cross$2d$inspect$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["inspect"])(argType)}" ` + 'must not be null.', {
                nodes: [
                    valueNode
                ]
            });
        }
        const coercedValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$valueFromAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["valueFromAST"])(valueNode, argType, variableValues);
        if (coercedValue === undefined) {
            // Note: ValuesOfCorrectTypeRule validation should catch this before
            // execution. This is a runtime check to ensure execution does not
            // continue with an invalid argument value.
            throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGraphQLError"])(`Argument "${name}" has invalid value ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["print"])(valueNode)}.`, {
                nodes: [
                    valueNode
                ]
            });
        }
        coercedValues[name] = coercedValue;
    }
    return coercedValues;
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/memoize.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "memoize1",
    ()=>memoize1,
    "memoize2",
    ()=>memoize2,
    "memoize2of4",
    ()=>memoize2of4,
    "memoize2of5",
    ()=>memoize2of5,
    "memoize3",
    ()=>memoize3,
    "memoize4",
    ()=>memoize4,
    "memoize5",
    ()=>memoize5
]);
function memoize1(fn) {
    const memoize1cache = new WeakMap();
    return function memoized(a1) {
        const cachedValue = memoize1cache.get(a1);
        if (cachedValue === undefined) {
            const newValue = fn(a1);
            memoize1cache.set(a1, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize2(fn) {
    const memoize2cache = new WeakMap();
    return function memoized(a1, a2) {
        let cache2 = memoize2cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize2cache.set(a1, cache2);
            const newValue = fn(a1, a2);
            cache2.set(a2, newValue);
            return newValue;
        }
        const cachedValue = cache2.get(a2);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2);
            cache2.set(a2, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize3(fn) {
    const memoize3Cache = new WeakMap();
    return function memoized(a1, a2, a3) {
        let cache2 = memoize3Cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize3Cache.set(a1, cache2);
            const cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const newValue = fn(a1, a2, a3);
            cache3.set(a3, newValue);
            return newValue;
        }
        let cache3 = cache2.get(a2);
        if (!cache3) {
            cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const newValue = fn(a1, a2, a3);
            cache3.set(a3, newValue);
            return newValue;
        }
        const cachedValue = cache3.get(a3);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3);
            cache3.set(a3, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize4(fn) {
    const memoize4Cache = new WeakMap();
    return function memoized(a1, a2, a3, a4) {
        let cache2 = memoize4Cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize4Cache.set(a1, cache2);
            const cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        let cache3 = cache2.get(a2);
        if (!cache3) {
            cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        const cache4 = cache3.get(a3);
        if (!cache4) {
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        const cachedValue = cache4.get(a4);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize5(fn) {
    const memoize5Cache = new WeakMap();
    return function memoized(a1, a2, a3, a4, a5) {
        let cache2 = memoize5Cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize5Cache.set(a1, cache2);
            const cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        let cache3 = cache2.get(a2);
        if (!cache3) {
            cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        let cache4 = cache3.get(a3);
        if (!cache4) {
            cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        let cache5 = cache4.get(a4);
        if (!cache5) {
            cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        const cachedValue = cache5.get(a5);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize2of4(fn) {
    const memoize2of4cache = new WeakMap();
    return function memoized(a1, a2, a3, a4) {
        let cache2 = memoize2of4cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize2of4cache.set(a1, cache2);
            const newValue = fn(a1, a2, a3, a4);
            cache2.set(a2, newValue);
            return newValue;
        }
        const cachedValue = cache2.get(a2);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4);
            cache2.set(a2, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize2of5(fn) {
    const memoize2of4cache = new WeakMap();
    return function memoized(a1, a2, a3, a4, a5) {
        let cache2 = memoize2of4cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize2of4cache.set(a1, cache2);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache2.set(a2, newValue);
            return newValue;
        }
        const cachedValue = cache2.get(a2);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4, a5);
            cache2.set(a2, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/getDirectiveExtensions.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getDirectiveExtensions",
    ()=>getDirectiveExtensions
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$valueFromAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/valueFromAST.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$valueFromASTUntyped$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/valueFromASTUntyped.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getArgumentValues$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/getArgumentValues.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$memoize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/memoize.js [app-route] (ecmascript)");
;
;
;
function getDirectiveExtensions(directableObj, schema, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = {};
    if (directableObj.extensions) {
        let directivesInExtensions = directableObj.extensions;
        for (const pathSegment of pathToDirectivesInExtensions){
            directivesInExtensions = directivesInExtensions?.[pathSegment];
        }
        if (directivesInExtensions != null) {
            for(const directiveNameProp in directivesInExtensions){
                const directiveObjs = directivesInExtensions[directiveNameProp];
                const directiveName = directiveNameProp;
                if (Array.isArray(directiveObjs)) {
                    for (const directiveObj of directiveObjs){
                        let existingDirectiveExtensions = directiveExtensions[directiveName];
                        if (!existingDirectiveExtensions) {
                            existingDirectiveExtensions = [];
                            directiveExtensions[directiveName] = existingDirectiveExtensions;
                        }
                        existingDirectiveExtensions.push(directiveObj);
                    }
                } else {
                    let existingDirectiveExtensions = directiveExtensions[directiveName];
                    if (!existingDirectiveExtensions) {
                        existingDirectiveExtensions = [];
                        directiveExtensions[directiveName] = existingDirectiveExtensions;
                    }
                    existingDirectiveExtensions.push(directiveObjs);
                }
            }
        }
    }
    const memoizedStringify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$memoize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["memoize1"])((obj)=>JSON.stringify(obj));
    const astNodes = [];
    if (directableObj.astNode) {
        astNodes.push(directableObj.astNode);
    }
    if (directableObj.extensionASTNodes) {
        astNodes.push(...directableObj.extensionASTNodes);
    }
    for (const astNode of astNodes){
        if (astNode.directives?.length) {
            for (const directive of astNode.directives){
                const directiveName = directive.name.value;
                let existingDirectiveExtensions = directiveExtensions[directiveName];
                if (!existingDirectiveExtensions) {
                    existingDirectiveExtensions = [];
                    directiveExtensions[directiveName] = existingDirectiveExtensions;
                }
                const directiveInSchema = schema?.getDirective(directiveName);
                let value = {};
                if (directiveInSchema) {
                    value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getArgumentValues$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getArgumentValues"])(directiveInSchema, directive);
                }
                if (directive.arguments) {
                    for (const argNode of directive.arguments){
                        const argName = argNode.name.value;
                        if (value[argName] == null) {
                            const argInDirective = directiveInSchema?.args.find((arg)=>arg.name === argName);
                            if (argInDirective) {
                                value[argName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$valueFromAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["valueFromAST"])(argNode.value, argInDirective.type);
                            }
                        }
                        if (value[argName] == null) {
                            value[argName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$valueFromASTUntyped$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["valueFromASTUntyped"])(argNode.value);
                        }
                    }
                }
                if (astNodes.length > 0 && existingDirectiveExtensions.length > 0) {
                    const valStr = memoizedStringify(value);
                    if (existingDirectiveExtensions.some((val)=>memoizedStringify(val) === valStr)) {
                        continue;
                    }
                }
                existingDirectiveExtensions.push(value);
            }
        }
    }
    return directiveExtensions;
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/get-directives.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getDirective",
    ()=>getDirective,
    "getDirectiveInExtensions",
    ()=>getDirectiveInExtensions,
    "getDirectives",
    ()=>getDirectives,
    "getDirectivesInExtensions",
    ()=>getDirectivesInExtensions
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getDirectiveExtensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/getDirectiveExtensions.js [app-route] (ecmascript)");
;
function getDirectivesInExtensions(node, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getDirectiveExtensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDirectiveExtensions"])(node, undefined, pathToDirectivesInExtensions);
    return Object.entries(directiveExtensions).map(([directiveName, directiveArgsArr])=>directiveArgsArr?.map((directiveArgs)=>({
                name: directiveName,
                args: directiveArgs
            }))).flat(Infinity).filter(Boolean);
}
function getDirectiveInExtensions(node, directiveName, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getDirectiveExtensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDirectiveExtensions"])(node, undefined, pathToDirectivesInExtensions);
    return directiveExtensions[directiveName];
}
function getDirectives(schema, node, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getDirectiveExtensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDirectiveExtensions"])(node, schema, pathToDirectivesInExtensions);
    return Object.entries(directiveExtensions).map(([directiveName, directiveArgsArr])=>directiveArgsArr?.map((directiveArgs)=>({
                name: directiveName,
                args: directiveArgs
            }))).flat(Infinity).filter(Boolean);
}
function getDirective(schema, node, directiveName, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getDirectiveExtensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDirectiveExtensions"])(node, schema, pathToDirectivesInExtensions);
    return directiveExtensions[directiveName];
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/rootTypes.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getDefinedRootType",
    ()=>getDefinedRootType,
    "getRootTypeMap",
    ()=>getRootTypeMap,
    "getRootTypeNames",
    ()=>getRootTypeNames,
    "getRootTypes",
    ()=>getRootTypes
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/errors.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$memoize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/memoize.js [app-route] (ecmascript)");
;
;
function getDefinedRootType(schema, operation, nodes) {
    const rootTypeMap = getRootTypeMap(schema);
    const rootType = rootTypeMap.get(operation);
    if (rootType == null) {
        throw (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGraphQLError"])(`Schema is not configured to execute ${operation} operation.`, {
            nodes
        });
    }
    return rootType;
}
const getRootTypeNames = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$memoize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["memoize1"])(function getRootTypeNames(schema) {
    const rootTypes = getRootTypes(schema);
    return new Set([
        ...rootTypes
    ].map((type)=>type.name));
});
const getRootTypes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$memoize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["memoize1"])(function getRootTypes(schema) {
    const rootTypeMap = getRootTypeMap(schema);
    return new Set(rootTypeMap.values());
});
const getRootTypeMap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$memoize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["memoize1"])(function getRootTypeMap(schema) {
    const rootTypeMap = new Map();
    const queryType = schema.getQueryType();
    if (queryType) {
        rootTypeMap.set('query', queryType);
    }
    const mutationType = schema.getMutationType();
    if (mutationType) {
        rootTypeMap.set('mutation', mutationType);
    }
    const subscriptionType = schema.getSubscriptionType();
    if (subscriptionType) {
        rootTypeMap.set('subscription', subscriptionType);
    }
    return rootTypeMap;
});
}),
"[project]/node_modules/@graphql-tools/utils/esm/print-schema-with-directives.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "astFromArg",
    ()=>astFromArg,
    "astFromDirective",
    ()=>astFromDirective,
    "astFromEnumType",
    ()=>astFromEnumType,
    "astFromEnumValue",
    ()=>astFromEnumValue,
    "astFromField",
    ()=>astFromField,
    "astFromInputField",
    ()=>astFromInputField,
    "astFromInputObjectType",
    ()=>astFromInputObjectType,
    "astFromInterfaceType",
    ()=>astFromInterfaceType,
    "astFromObjectType",
    ()=>astFromObjectType,
    "astFromScalarType",
    ()=>astFromScalarType,
    "astFromSchema",
    ()=>astFromSchema,
    "astFromUnionType",
    ()=>astFromUnionType,
    "getDirectiveNodes",
    ()=>getDirectiveNodes,
    "getDocumentNodeFromSchema",
    ()=>getDocumentNodeFromSchema,
    "makeDeprecatedDirective",
    ()=>makeDeprecatedDirective,
    "makeDirectiveNode",
    ()=>makeDirectiveNode,
    "makeDirectiveNodes",
    ()=>makeDirectiveNodes,
    "printSchemaWithDirectives",
    ()=>printSchemaWithDirectives
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/directives.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$introspection$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/introspection.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/scalars.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/printer.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/astFromType.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/astFromValue.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValueUntyped$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/astFromValueUntyped.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/descriptionFromObject.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$get$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/get-directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$rootTypes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/rootTypes.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
function getDocumentNodeFromSchema(schema, options = {}) {
    const pathToDirectivesInExtensions = options.pathToDirectivesInExtensions;
    const typesMap = schema.getTypeMap();
    const schemaNode = astFromSchema(schema, pathToDirectivesInExtensions);
    const definitions = schemaNode != null ? [
        schemaNode
    ] : [];
    const directives = schema.getDirectives();
    for (const directive of directives){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedDirective"])(directive)) {
            continue;
        }
        definitions.push(astFromDirective(directive, schema, pathToDirectivesInExtensions));
    }
    for(const typeName in typesMap){
        const type = typesMap[typeName];
        const isPredefinedScalar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedScalarType"])(type);
        const isIntrospection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$introspection$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isIntrospectionType"])(type);
        if (isPredefinedScalar || isIntrospection) {
            continue;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
            definitions.push(astFromObjectType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            definitions.push(astFromInterfaceType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            definitions.push(astFromUnionType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
            definitions.push(astFromInputObjectType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            definitions.push(astFromEnumType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])(type)) {
            definitions.push(astFromScalarType(type, schema, pathToDirectivesInExtensions));
        } else {
            throw new Error(`Unknown type ${type}.`);
        }
    }
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DOCUMENT,
        definitions
    };
}
function printSchemaWithDirectives(schema, options = {}) {
    const documentNode = getDocumentNodeFromSchema(schema, options);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["print"])(documentNode);
}
function astFromSchema(schema, pathToDirectivesInExtensions) {
    const operationTypeMap = new Map([
        [
            'query',
            undefined
        ],
        [
            'mutation',
            undefined
        ],
        [
            'subscription',
            undefined
        ]
    ]);
    const nodes = [];
    if (schema.astNode != null) {
        nodes.push(schema.astNode);
    }
    if (schema.extensionASTNodes != null) {
        for (const extensionASTNode of schema.extensionASTNodes){
            nodes.push(extensionASTNode);
        }
    }
    for (const node of nodes){
        if (node.operationTypes) {
            for (const operationTypeDefinitionNode of node.operationTypes){
                operationTypeMap.set(operationTypeDefinitionNode.operation, operationTypeDefinitionNode);
            }
        }
    }
    const rootTypeMap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$rootTypes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRootTypeMap"])(schema);
    for (const [operationTypeNode, operationTypeDefinitionNode] of operationTypeMap){
        const rootType = rootTypeMap.get(operationTypeNode);
        if (rootType != null) {
            const rootTypeAST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(rootType);
            if (operationTypeDefinitionNode != null) {
                operationTypeDefinitionNode.type = rootTypeAST;
            } else {
                operationTypeMap.set(operationTypeNode, {
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OPERATION_TYPE_DEFINITION,
                    operation: operationTypeNode,
                    type: rootTypeAST
                });
            }
        }
    }
    const operationTypes = [
        ...operationTypeMap.values()
    ].filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSome"]);
    const directives = getDirectiveNodes(schema, schema, pathToDirectivesInExtensions);
    if (!operationTypes.length && !directives.length) {
        return null;
    }
    const schemaNode = {
        kind: operationTypes != null ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_EXTENSION,
        operationTypes,
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: directives
    };
    const descriptionNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(schema);
    if (descriptionNode) {
        schemaNode.description = descriptionNode;
    }
    return schemaNode;
}
function astFromDirective(directive, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DIRECTIVE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(directive),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: directive.name
        },
        arguments: directive.args?.map((arg)=>astFromArg(arg, schema, pathToDirectivesInExtensions)),
        repeatable: directive.isRepeatable,
        locations: directive.locations?.map((location)=>({
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
                value: location
            })) || []
    };
}
function getDirectiveNodes(entity, schema, pathToDirectivesInExtensions) {
    let directiveNodesBesidesNativeDirectives = [];
    const directivesInExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$get$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDirectivesInExtensions"])(entity, pathToDirectivesInExtensions);
    let directives;
    if (directivesInExtensions != null) {
        directives = makeDirectiveNodes(schema, directivesInExtensions);
    }
    let deprecatedDirectiveNode = null;
    let specifiedByDirectiveNode = null;
    let oneOfDirectiveNode = null;
    if (directives != null) {
        directiveNodesBesidesNativeDirectives = directives.filter((directive)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["specifiedDirectives"].every((specifiedDirective)=>specifiedDirective.name !== directive.name.value));
        deprecatedDirectiveNode = directives.find((directive)=>directive.name.value === 'deprecated');
        specifiedByDirectiveNode = directives.find((directive)=>directive.name.value === 'specifiedBy');
        oneOfDirectiveNode = directives.find((directive)=>directive.name.value === 'oneOf');
    }
    if (entity.deprecationReason != null && deprecatedDirectiveNode == null) {
        deprecatedDirectiveNode = makeDeprecatedDirective(entity.deprecationReason);
    }
    if (entity.specifiedByUrl != null || entity.specifiedByURL != null && specifiedByDirectiveNode == null) {
        const specifiedByValue = entity.specifiedByUrl || entity.specifiedByURL;
        const specifiedByArgs = {
            url: specifiedByValue
        };
        specifiedByDirectiveNode = makeDirectiveNode('specifiedBy', specifiedByArgs);
    }
    if (entity.isOneOf && oneOfDirectiveNode == null) {
        oneOfDirectiveNode = makeDirectiveNode('oneOf');
    }
    if (deprecatedDirectiveNode != null) {
        directiveNodesBesidesNativeDirectives.push(deprecatedDirectiveNode);
    }
    if (specifiedByDirectiveNode != null) {
        directiveNodesBesidesNativeDirectives.push(specifiedByDirectiveNode);
    }
    if (oneOfDirectiveNode != null) {
        directiveNodesBesidesNativeDirectives.push(oneOfDirectiveNode);
    }
    return directiveNodesBesidesNativeDirectives;
}
function astFromArg(arg, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_VALUE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(arg),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: arg.name
        },
        type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(arg.type),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        defaultValue: arg.defaultValue !== undefined ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromValue"])(arg.defaultValue, arg.type) ?? undefined : undefined,
        directives: getDirectiveNodes(arg, schema, pathToDirectivesInExtensions)
    };
}
function astFromObjectType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(type),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        },
        fields: Object.values(type.getFields()).map((field)=>astFromField(field, schema, pathToDirectivesInExtensions)),
        interfaces: Object.values(type.getInterfaces()).map((iFace)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(iFace)),
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
}
function astFromInterfaceType(type, schema, pathToDirectivesInExtensions) {
    const node = {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(type),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        },
        fields: Object.values(type.getFields()).map((field)=>astFromField(field, schema, pathToDirectivesInExtensions)),
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
    if ('getInterfaces' in type) {
        node.interfaces = Object.values(type.getInterfaces()).map((iFace)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(iFace));
    }
    return node;
}
function astFromUnionType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(type),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        },
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),
        types: type.getTypes().map((type)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(type))
    };
}
function astFromInputObjectType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(type),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        },
        fields: Object.values(type.getFields()).map((field)=>astFromInputField(field, schema, pathToDirectivesInExtensions)),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
}
function astFromEnumType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(type),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        },
        values: Object.values(type.getValues()).map((value)=>astFromEnumValue(value, schema, pathToDirectivesInExtensions)),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
}
function astFromScalarType(type, schema, pathToDirectivesInExtensions) {
    const directivesInExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$get$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDirectivesInExtensions"])(type, pathToDirectivesInExtensions);
    const directives = makeDirectiveNodes(schema, directivesInExtensions);
    const specifiedByValue = type['specifiedByUrl'] || type['specifiedByURL'];
    if (specifiedByValue && !directives.some((directiveNode)=>directiveNode.name.value === 'specifiedBy')) {
        const specifiedByArgs = {
            url: specifiedByValue
        };
        directives.push(makeDirectiveNode('specifiedBy', specifiedByArgs));
    }
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(type),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: type.name
        },
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: directives
    };
}
function astFromField(field, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].FIELD_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(field),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: field.name
        },
        arguments: field.args.map((arg)=>astFromArg(arg, schema, pathToDirectivesInExtensions)),
        type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(field.type),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions)
    };
}
function astFromInputField(field, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_VALUE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(field),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: field.name
        },
        type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromType$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromType"])(field.type),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),
        defaultValue: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromValue"])(field.defaultValue, field.type) ?? undefined
    };
}
function astFromEnumValue(value, schema, pathToDirectivesInExtensions) {
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_VALUE_DEFINITION,
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$descriptionFromObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDescriptionNode"])(value),
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: value.name
        },
        directives: getDirectiveNodes(value, schema, pathToDirectivesInExtensions)
    };
}
function makeDeprecatedDirective(deprecationReason) {
    return makeDirectiveNode('deprecated', {
        reason: deprecationReason
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLDeprecatedDirective"]);
}
function makeDirectiveNode(name, args, directive) {
    const directiveArguments = [];
    for(const argName in args){
        const argValue = args[argName];
        let value;
        if (directive != null) {
            const arg = directive.args.find((arg)=>arg.name === argName);
            if (arg) {
                value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromValue"])(argValue, arg.type);
            }
        }
        if (value == null) {
            value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$astFromValueUntyped$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["astFromValueUntyped"])(argValue);
        }
        if (value != null) {
            directiveArguments.push({
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ARGUMENT,
                name: {
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
                    value: argName
                },
                value
            });
        }
    }
    return {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DIRECTIVE,
        name: {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
            value: name
        },
        arguments: directiveArguments
    };
}
function makeDirectiveNodes(schema, directiveValues) {
    const directiveNodes = [];
    for (const { name, args } of directiveValues){
        const directive = schema?.getDirective(name);
        directiveNodes.push(makeDirectiveNode(name, args, directive));
    }
    return directiveNodes;
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/isDocumentNode.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "isDocumentNode",
    ()=>isDocumentNode
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
;
function isDocumentNode(object) {
    return object && typeof object === 'object' && 'kind' in object && object.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DOCUMENT;
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/comments.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "collectComment",
    ()=>collectComment,
    "dedentBlockStringValue",
    ()=>dedentBlockStringValue,
    "getBlockStringIndentation",
    ()=>getBlockStringIndentation,
    "getComment",
    ()=>getComment,
    "getDescription",
    ()=>getDescription,
    "getLeadingCommentBlock",
    ()=>getLeadingCommentBlock,
    "printComment",
    ()=>printComment,
    "printWithComments",
    ()=>printWithComments,
    "pushComment",
    ()=>pushComment,
    "resetComments",
    ()=>resetComments
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$tokenKind$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/tokenKind.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$visitor$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/visitor.mjs [app-route] (ecmascript)");
;
const MAX_LINE_LENGTH = 80;
let commentsRegistry = {};
function resetComments() {
    commentsRegistry = {};
}
function collectComment(node) {
    const entityName = node.name?.value;
    if (entityName == null) {
        return;
    }
    pushComment(node, entityName);
    switch(node.kind){
        case 'EnumTypeDefinition':
            if (node.values) {
                for (const value of node.values){
                    pushComment(value, entityName, value.name.value);
                }
            }
            break;
        case 'ObjectTypeDefinition':
        case 'InputObjectTypeDefinition':
        case 'InterfaceTypeDefinition':
            if (node.fields) {
                for (const field of node.fields){
                    pushComment(field, entityName, field.name.value);
                    if (isFieldDefinitionNode(field) && field.arguments) {
                        for (const arg of field.arguments){
                            pushComment(arg, entityName, field.name.value, arg.name.value);
                        }
                    }
                }
            }
            break;
    }
}
function pushComment(node, entity, field, argument) {
    const comment = getComment(node);
    if (typeof comment !== 'string' || comment.length === 0) {
        return;
    }
    const keys = [
        entity
    ];
    if (field) {
        keys.push(field);
        if (argument) {
            keys.push(argument);
        }
    }
    const path = keys.join('.');
    if (!commentsRegistry[path]) {
        commentsRegistry[path] = [];
    }
    commentsRegistry[path].push(comment);
}
function printComment(comment) {
    return '\n# ' + comment.replace(/\n/g, '\n# ');
}
/**
 * Copyright (c) 2015-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ /**
 * NOTE: ==> This file has been modified just to add comments to the printed AST
 * This is a temp measure, we will move to using the original non modified printer.js ASAP.
 */ /**
 * Given maybeArray, print an empty string if it is null or empty, otherwise
 * print all items together separated by separator if provided
 */ function join(maybeArray, separator) {
    return maybeArray ? maybeArray.filter((x)=>x).join(separator || '') : '';
}
function hasMultilineItems(maybeArray) {
    return maybeArray?.some((str)=>str.includes('\n')) ?? false;
}
function addDescription(cb) {
    return (node, _key, _parent, path, ancestors)=>{
        const keys = [];
        const parent = path.reduce((prev, key)=>{
            if ([
                'fields',
                'arguments',
                'values'
            ].includes(key) && prev.name) {
                keys.push(prev.name.value);
            }
            return prev[key];
        }, ancestors[0]);
        const key = [
            ...keys,
            parent?.name?.value
        ].filter(Boolean).join('.');
        const items = [];
        if (node.kind.includes('Definition') && commentsRegistry[key]) {
            items.push(...commentsRegistry[key]);
        }
        return join([
            ...items.map(printComment),
            node.description,
            cb(node, _key, _parent, path, ancestors)
        ], '\n');
    };
}
function indent(maybeString) {
    return maybeString && `  ${maybeString.replace(/\n/g, '\n  ')}`;
}
/**
 * Given array, print each item on its own line, wrapped in an
 * indented "{ }" block.
 */ function block(array) {
    return array && array.length !== 0 ? `{\n${indent(join(array, '\n'))}\n}` : '';
}
/**
 * If maybeString is not null or empty, then wrap with start and end, otherwise
 * print an empty string.
 */ function wrap(start, maybeString, end) {
    return maybeString ? start + maybeString + (end || '') : '';
}
/**
 * Print a block string in the indented block form by adding a leading and
 * trailing blank line. However, if a block string starts with whitespace and is
 * a single-line, adding a leading blank line would strip that whitespace.
 */ function printBlockString(value, isDescription = false) {
    const escaped = value.replace(/\\/g, '\\\\').replace(/"""/g, '\\"""');
    return (value[0] === ' ' || value[0] === '\t') && value.indexOf('\n') === -1 ? `"""${escaped.replace(/"$/, '"\n')}"""` : `"""\n${isDescription ? escaped : indent(escaped)}\n"""`;
}
const printDocASTReducer = {
    Name: {
        leave: (node)=>node.value
    },
    Variable: {
        leave: (node)=>'$' + node.name
    },
    // Document
    Document: {
        leave: (node)=>join(node.definitions, '\n\n')
    },
    OperationDefinition: {
        leave: (node)=>{
            const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');
            const prefix = join([
                node.operation,
                join([
                    node.name,
                    varDefs
                ]),
                join(node.directives, ' ')
            ], ' ');
            // the query short form.
            return prefix + ' ' + node.selectionSet;
        }
    },
    VariableDefinition: {
        leave: ({ variable, type, defaultValue, directives })=>variable + ': ' + type + wrap(' = ', defaultValue) + wrap(' ', join(directives, ' '))
    },
    SelectionSet: {
        leave: ({ selections })=>block(selections)
    },
    Field: {
        leave ({ alias, name, arguments: args, directives, selectionSet }) {
            const prefix = wrap('', alias, ': ') + name;
            let argsLine = prefix + wrap('(', join(args, ', '), ')');
            if (argsLine.length > MAX_LINE_LENGTH) {
                argsLine = prefix + wrap('(\n', indent(join(args, '\n')), '\n)');
            }
            return join([
                argsLine,
                join(directives, ' '),
                selectionSet
            ], ' ');
        }
    },
    Argument: {
        leave: ({ name, value })=>name + ': ' + value
    },
    // Fragments
    FragmentSpread: {
        leave: ({ name, directives })=>'...' + name + wrap(' ', join(directives, ' '))
    },
    InlineFragment: {
        leave: ({ typeCondition, directives, selectionSet })=>join([
                '...',
                wrap('on ', typeCondition),
                join(directives, ' '),
                selectionSet
            ], ' ')
    },
    FragmentDefinition: {
        leave: ({ name, typeCondition, variableDefinitions, directives, selectionSet })=>// Note: fragment variable definitions are experimental and may be changed
            // or removed in the future.
            `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` + `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` + selectionSet
    },
    // Value
    IntValue: {
        leave: ({ value })=>value
    },
    FloatValue: {
        leave: ({ value })=>value
    },
    StringValue: {
        leave: ({ value, block: isBlockString })=>{
            if (isBlockString) {
                return printBlockString(value);
            }
            return JSON.stringify(value);
        }
    },
    BooleanValue: {
        leave: ({ value })=>value ? 'true' : 'false'
    },
    NullValue: {
        leave: ()=>'null'
    },
    EnumValue: {
        leave: ({ value })=>value
    },
    ListValue: {
        leave: ({ values })=>'[' + join(values, ', ') + ']'
    },
    ObjectValue: {
        leave: ({ fields })=>'{' + join(fields, ', ') + '}'
    },
    ObjectField: {
        leave: ({ name, value })=>name + ': ' + value
    },
    // Directive
    Directive: {
        leave: ({ name, arguments: args })=>'@' + name + wrap('(', join(args, ', '), ')')
    },
    // Type
    NamedType: {
        leave: ({ name })=>name
    },
    ListType: {
        leave: ({ type })=>'[' + type + ']'
    },
    NonNullType: {
        leave: ({ type })=>type + '!'
    },
    // Type System Definitions
    SchemaDefinition: {
        leave: ({ directives, operationTypes })=>join([
                'schema',
                join(directives, ' '),
                block(operationTypes)
            ], ' ')
    },
    OperationTypeDefinition: {
        leave: ({ operation, type })=>operation + ': ' + type
    },
    ScalarTypeDefinition: {
        leave: ({ name, directives })=>join([
                'scalar',
                name,
                join(directives, ' ')
            ], ' ')
    },
    ObjectTypeDefinition: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'type',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    FieldDefinition: {
        leave: ({ name, arguments: args, type, directives })=>name + (hasMultilineItems(args) ? wrap('(\n', indent(join(args, '\n')), '\n)') : wrap('(', join(args, ', '), ')')) + ': ' + type + wrap(' ', join(directives, ' '))
    },
    InputValueDefinition: {
        leave: ({ name, type, defaultValue, directives })=>join([
                name + ': ' + type,
                wrap('= ', defaultValue),
                join(directives, ' ')
            ], ' ')
    },
    InterfaceTypeDefinition: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'interface',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    UnionTypeDefinition: {
        leave: ({ name, directives, types })=>join([
                'union',
                name,
                join(directives, ' '),
                wrap('= ', join(types, ' | '))
            ], ' ')
    },
    EnumTypeDefinition: {
        leave: ({ name, directives, values })=>join([
                'enum',
                name,
                join(directives, ' '),
                block(values)
            ], ' ')
    },
    EnumValueDefinition: {
        leave: ({ name, directives })=>join([
                name,
                join(directives, ' ')
            ], ' ')
    },
    InputObjectTypeDefinition: {
        leave: ({ name, directives, fields })=>join([
                'input',
                name,
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    DirectiveDefinition: {
        leave: ({ name, arguments: args, repeatable, locations })=>'directive @' + name + (hasMultilineItems(args) ? wrap('(\n', indent(join(args, '\n')), '\n)') : wrap('(', join(args, ', '), ')')) + (repeatable ? ' repeatable' : '') + ' on ' + join(locations, ' | ')
    },
    SchemaExtension: {
        leave: ({ directives, operationTypes })=>join([
                'extend schema',
                join(directives, ' '),
                block(operationTypes)
            ], ' ')
    },
    ScalarTypeExtension: {
        leave: ({ name, directives })=>join([
                'extend scalar',
                name,
                join(directives, ' ')
            ], ' ')
    },
    ObjectTypeExtension: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'extend type',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    InterfaceTypeExtension: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'extend interface',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    UnionTypeExtension: {
        leave: ({ name, directives, types })=>join([
                'extend union',
                name,
                join(directives, ' '),
                wrap('= ', join(types, ' | '))
            ], ' ')
    },
    EnumTypeExtension: {
        leave: ({ name, directives, values })=>join([
                'extend enum',
                name,
                join(directives, ' '),
                block(values)
            ], ' ')
    },
    InputObjectTypeExtension: {
        leave: ({ name, directives, fields })=>join([
                'extend input',
                name,
                join(directives, ' '),
                block(fields)
            ], ' ')
    }
};
const printDocASTReducerWithComments = Object.keys(printDocASTReducer).reduce((prev, key)=>({
        ...prev,
        [key]: {
            leave: addDescription(printDocASTReducer[key].leave)
        }
    }), {});
function printWithComments(ast) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$visitor$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["visit"])(ast, printDocASTReducerWithComments);
}
function isFieldDefinitionNode(node) {
    return node.kind === 'FieldDefinition';
}
function getDescription(node, options) {
    if (node.description != null) {
        return node.description.value;
    }
    if (options?.commentDescriptions) {
        return getComment(node);
    }
}
function getComment(node) {
    const rawValue = getLeadingCommentBlock(node);
    if (rawValue !== undefined) {
        return dedentBlockStringValue(`\n${rawValue}`);
    }
}
function getLeadingCommentBlock(node) {
    const loc = node.loc;
    if (!loc) {
        return;
    }
    const comments = [];
    let token = loc.startToken.prev;
    while(token != null && token.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$tokenKind$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TokenKind"].COMMENT && token.next != null && token.prev != null && token.line + 1 === token.next.line && token.line !== token.prev.line){
        const value = String(token.value);
        comments.push(value);
        token = token.prev;
    }
    return comments.length > 0 ? comments.reverse().join('\n') : undefined;
}
function dedentBlockStringValue(rawString) {
    // Expand a block string's raw value into independent lines.
    const lines = rawString.split(/\r\n|[\n\r]/g);
    // Remove common indentation from all lines but first.
    const commonIndent = getBlockStringIndentation(lines);
    if (commonIndent !== 0) {
        for(let i = 1; i < lines.length; i++){
            lines[i] = lines[i].slice(commonIndent);
        }
    }
    // Remove leading and trailing blank lines.
    while(lines.length > 0 && isBlank(lines[0])){
        lines.shift();
    }
    while(lines.length > 0 && isBlank(lines[lines.length - 1])){
        lines.pop();
    }
    // Return a string of the lines joined with U+000A.
    return lines.join('\n');
}
function getBlockStringIndentation(lines) {
    let commonIndent = null;
    for(let i = 1; i < lines.length; i++){
        const line = lines[i];
        const indent = leadingWhitespace(line);
        if (indent === line.length) {
            continue; // skip empty lines
        }
        if (commonIndent === null || indent < commonIndent) {
            commonIndent = indent;
            if (commonIndent === 0) {
                break;
            }
        }
    }
    return commonIndent === null ? 0 : commonIndent;
}
function leadingWhitespace(str) {
    let i = 0;
    while(i < str.length && (str[i] === ' ' || str[i] === '\t')){
        i++;
    }
    return i;
}
function isBlank(str) {
    return leadingWhitespace(str) === str.length;
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/forEachDefaultValue.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "forEachDefaultValue",
    ()=>forEachDefaultValue
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
;
function forEachDefaultValue(schema, fn) {
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        const type = typeMap[typeName];
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNamedType"])(type).name.startsWith('__')) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
                const fields = type.getFields();
                for(const fieldName in fields){
                    const field = fields[fieldName];
                    for (const arg of field.args){
                        arg.defaultValue = fn(arg.type, arg.defaultValue);
                    }
                }
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
                const fields = type.getFields();
                for(const fieldName in fields){
                    const field = fields[fieldName];
                    field.defaultValue = fn(field.type, field.defaultValue);
                }
            }
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/forEachField.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "forEachField",
    ()=>forEachField
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
;
function forEachField(schema, fn) {
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        const type = typeMap[typeName];
        // TODO: maybe have an option to include these?
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNamedType"])(type).name.startsWith('__') && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
            const fields = type.getFields();
            for(const fieldName in fields){
                const field = fields[fieldName];
                fn(field, typeName, fieldName);
            }
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/heal.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "healSchema",
    ()=>healSchema,
    "healTypes",
    ()=>healTypes
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
;
function healSchema(schema) {
    healTypes(schema.getTypeMap(), schema.getDirectives());
    return schema;
}
function healTypes(originalTypeMap, directives) {
    const actualNamedTypeMap = Object.create(null);
    // If any of the .name properties of the GraphQLNamedType objects in
    // schema.getTypeMap() have changed, the keys of the type map need to
    // be updated accordingly.
    for(const typeName in originalTypeMap){
        const namedType = originalTypeMap[typeName];
        if (namedType == null || typeName.startsWith('__')) {
            continue;
        }
        const actualName = namedType.name;
        if (actualName.startsWith('__')) {
            continue;
        }
        if (actualNamedTypeMap[actualName] != null) {
            console.warn(`Duplicate schema type name ${actualName} found; keeping the existing one found in the schema`);
            continue;
        }
        actualNamedTypeMap[actualName] = namedType;
    // Note: we are deliberately leaving namedType in the schema by its
    // original name (which might be different from actualName), so that
    // references by that name can be healed.
    }
    // Now add back every named type by its actual name.
    for(const typeName in actualNamedTypeMap){
        const namedType = actualNamedTypeMap[typeName];
        originalTypeMap[typeName] = namedType;
    }
    // Directive declaration argument types can refer to named types.
    for (const decl of directives){
        decl.args = decl.args.filter((arg)=>{
            arg.type = healType(arg.type);
            return arg.type !== null;
        });
    }
    for(const typeName in originalTypeMap){
        const namedType = originalTypeMap[typeName];
        // Heal all named types, except for dangling references, kept only to redirect.
        if (!typeName.startsWith('__') && typeName in actualNamedTypeMap) {
            if (namedType != null) {
                healNamedType(namedType);
            }
        }
    }
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__') && !(typeName in actualNamedTypeMap)) {
            delete originalTypeMap[typeName];
        }
    }
    function healNamedType(type) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
            healFields(type);
            healInterfaces(type);
            return;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            healFields(type);
            if ('getInterfaces' in type) {
                healInterfaces(type);
            }
            return;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            healUnderlyingTypes(type);
            return;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
            healInputFields(type);
            return;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLeafType"])(type)) {
            return;
        }
        throw new Error(`Unexpected schema type: ${type}`);
    }
    function healFields(type) {
        const fieldMap = type.getFields();
        for (const [key, field] of Object.entries(fieldMap)){
            field.args.map((arg)=>{
                arg.type = healType(arg.type);
                return arg.type === null ? null : arg;
            }).filter(Boolean);
            field.type = healType(field.type);
            if (field.type === null) {
                delete fieldMap[key];
            }
        }
    }
    function healInterfaces(type) {
        if ('getInterfaces' in type) {
            const interfaces = type.getInterfaces();
            interfaces.push(...interfaces.splice(0).map((iface)=>healType(iface)).filter(Boolean));
        }
    }
    function healInputFields(type) {
        const fieldMap = type.getFields();
        for (const [key, field] of Object.entries(fieldMap)){
            field.type = healType(field.type);
            if (field.type === null) {
                delete fieldMap[key];
            }
        }
    }
    function healUnderlyingTypes(type) {
        const types = type.getTypes();
        types.push(...types.splice(0).map((t)=>healType(t)).filter(Boolean));
    }
    function healType(type) {
        // Unwrap the two known wrapper types
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListType"])(type)) {
            const healedType = healType(type.ofType);
            return healedType != null ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLList"](healedType) : null;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(type)) {
            const healedType = healType(type.ofType);
            return healedType != null ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLNonNull"](healedType) : null;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNamedType"])(type)) {
            // If a type annotation on a field or an argument or a union member is
            // any `GraphQLNamedType` with a `name`, then it must end up identical
            // to `schema.getType(name)`, since `schema.getTypeMap()` is the source
            // of truth for all named schema types.
            // Note that new types can still be simply added by adding a field, as
            // the official type will be undefined, not null.
            const officialType = originalTypeMap[type.name];
            if (officialType && type !== officialType) {
                return officialType;
            }
        }
        return type;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/Interfaces.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MapperKind",
    ()=>MapperKind
]);
var MapperKind;
(function(MapperKind) {
    MapperKind["TYPE"] = "MapperKind.TYPE";
    MapperKind["SCALAR_TYPE"] = "MapperKind.SCALAR_TYPE";
    MapperKind["ENUM_TYPE"] = "MapperKind.ENUM_TYPE";
    MapperKind["COMPOSITE_TYPE"] = "MapperKind.COMPOSITE_TYPE";
    MapperKind["OBJECT_TYPE"] = "MapperKind.OBJECT_TYPE";
    MapperKind["INPUT_OBJECT_TYPE"] = "MapperKind.INPUT_OBJECT_TYPE";
    MapperKind["ABSTRACT_TYPE"] = "MapperKind.ABSTRACT_TYPE";
    MapperKind["UNION_TYPE"] = "MapperKind.UNION_TYPE";
    MapperKind["INTERFACE_TYPE"] = "MapperKind.INTERFACE_TYPE";
    MapperKind["ROOT_OBJECT"] = "MapperKind.ROOT_OBJECT";
    MapperKind["QUERY"] = "MapperKind.QUERY";
    MapperKind["MUTATION"] = "MapperKind.MUTATION";
    MapperKind["SUBSCRIPTION"] = "MapperKind.SUBSCRIPTION";
    MapperKind["DIRECTIVE"] = "MapperKind.DIRECTIVE";
    MapperKind["FIELD"] = "MapperKind.FIELD";
    MapperKind["COMPOSITE_FIELD"] = "MapperKind.COMPOSITE_FIELD";
    MapperKind["OBJECT_FIELD"] = "MapperKind.OBJECT_FIELD";
    MapperKind["ROOT_FIELD"] = "MapperKind.ROOT_FIELD";
    MapperKind["QUERY_ROOT_FIELD"] = "MapperKind.QUERY_ROOT_FIELD";
    MapperKind["MUTATION_ROOT_FIELD"] = "MapperKind.MUTATION_ROOT_FIELD";
    MapperKind["SUBSCRIPTION_ROOT_FIELD"] = "MapperKind.SUBSCRIPTION_ROOT_FIELD";
    MapperKind["INTERFACE_FIELD"] = "MapperKind.INTERFACE_FIELD";
    MapperKind["INPUT_OBJECT_FIELD"] = "MapperKind.INPUT_OBJECT_FIELD";
    MapperKind["ARGUMENT"] = "MapperKind.ARGUMENT";
    MapperKind["ENUM_VALUE"] = "MapperKind.ENUM_VALUE";
})(MapperKind || (MapperKind = {}));
}),
"[project]/node_modules/@graphql-tools/utils/esm/getObjectTypeFromTypeMap.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getObjectTypeFromTypeMap",
    ()=>getObjectTypeFromTypeMap
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
;
function getObjectTypeFromTypeMap(typeMap, type) {
    if (type) {
        const maybeObjectType = typeMap[type.name];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(maybeObjectType)) {
            return maybeObjectType;
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/stub.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "createNamedStub",
    ()=>createNamedStub,
    "createStub",
    ()=>createStub,
    "getBuiltInForStub",
    ()=>getBuiltInForStub,
    "isNamedStub",
    ()=>isNamedStub
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/scalars.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
;
function createNamedStub(name, type) {
    let constructor;
    if (type === 'object') {
        constructor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"];
    } else if (type === 'interface') {
        constructor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"];
    } else {
        constructor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInputObjectType"];
    }
    return new constructor({
        name,
        fields: {
            _fake: {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLString"]
            }
        }
    });
}
function createStub(node, type) {
    switch(node.kind){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST_TYPE:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLList"](createStub(node.type, type));
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NON_NULL_TYPE:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLNonNull"](createStub(node.type, type));
        default:
            if (type === 'output') {
                return createNamedStub(node.name.value, 'object');
            }
            return createNamedStub(node.name.value, 'input');
    }
}
function isNamedStub(type) {
    if ('getFields' in type) {
        const fields = type.getFields();
        // eslint-disable-next-line no-unreachable-loop
        for(const fieldName in fields){
            const field = fields[fieldName];
            return field.name === '_fake';
        }
    }
    return false;
}
function getBuiltInForStub(type) {
    switch(type.name){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInt"].name:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInt"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLFloat"].name:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLFloat"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLString"].name:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLString"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLBoolean"].name:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLBoolean"];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLID"].name:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLID"];
        default:
            return type;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/rewire.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "rewireTypes",
    ()=>rewireTypes
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/directives.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/scalars.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$stub$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/stub.js [app-route] (ecmascript)");
;
;
function rewireTypes(originalTypeMap, directives) {
    const referenceTypeMap = Object.create(null);
    for(const typeName in originalTypeMap){
        referenceTypeMap[typeName] = originalTypeMap[typeName];
    }
    const newTypeMap = Object.create(null);
    for(const typeName in referenceTypeMap){
        const namedType = referenceTypeMap[typeName];
        if (namedType == null || typeName.startsWith('__')) {
            continue;
        }
        const newName = namedType.name;
        if (newName.startsWith('__')) {
            continue;
        }
        if (newTypeMap[newName] != null) {
            console.warn(`Duplicate schema type name ${newName} found; keeping the existing one found in the schema`);
            continue;
        }
        newTypeMap[newName] = namedType;
    }
    for(const typeName in newTypeMap){
        newTypeMap[typeName] = rewireNamedType(newTypeMap[typeName]);
    }
    const newDirectives = directives.map((directive)=>rewireDirective(directive));
    return {
        typeMap: newTypeMap,
        directives: newDirectives
    };
    //TURBOPACK unreachable
    ;
    function rewireDirective(directive) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedDirective"])(directive)) {
            return directive;
        }
        const directiveConfig = directive.toConfig();
        directiveConfig.args = rewireArgs(directiveConfig.args);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$directives$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLDirective"](directiveConfig);
    }
    function rewireArgs(args) {
        const rewiredArgs = {};
        for(const argName in args){
            const arg = args[argName];
            const rewiredArgType = rewireType(arg.type);
            if (rewiredArgType != null) {
                arg.type = rewiredArgType;
                rewiredArgs[argName] = arg;
            }
        }
        return rewiredArgs;
    }
    function rewireNamedType(type) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                fields: ()=>rewireFields(config.fields),
                interfaces: ()=>rewireNamedTypes(config.interfaces)
            };
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"](newConfig);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                fields: ()=>rewireFields(config.fields)
            };
            if ('interfaces' in newConfig) {
                newConfig.interfaces = ()=>rewireNamedTypes(config.interfaces);
            }
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"](newConfig);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                types: ()=>rewireNamedTypes(config.types)
            };
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLUnionType"](newConfig);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                fields: ()=>rewireInputFields(config.fields)
            };
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInputObjectType"](newConfig);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            const enumConfig = type.toConfig();
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"](enumConfig);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])(type)) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedScalarType"])(type)) {
                return type;
            }
            const scalarConfig = type.toConfig();
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLScalarType"](scalarConfig);
        }
        throw new Error(`Unexpected schema type: ${type}`);
    }
    function rewireFields(fields) {
        const rewiredFields = {};
        for(const fieldName in fields){
            const field = fields[fieldName];
            const rewiredFieldType = rewireType(field.type);
            if (rewiredFieldType != null && field.args) {
                field.type = rewiredFieldType;
                field.args = rewireArgs(field.args);
                rewiredFields[fieldName] = field;
            }
        }
        return rewiredFields;
    }
    function rewireInputFields(fields) {
        const rewiredFields = {};
        for(const fieldName in fields){
            const field = fields[fieldName];
            const rewiredFieldType = rewireType(field.type);
            if (rewiredFieldType != null) {
                field.type = rewiredFieldType;
                rewiredFields[fieldName] = field;
            }
        }
        return rewiredFields;
    }
    function rewireNamedTypes(namedTypes) {
        const rewiredTypes = [];
        for (const namedType of namedTypes){
            const rewiredType = rewireType(namedType);
            if (rewiredType != null) {
                rewiredTypes.push(rewiredType);
            }
        }
        return rewiredTypes;
    }
    function rewireType(type) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListType"])(type)) {
            const rewiredType = rewireType(type.ofType);
            return rewiredType != null ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLList"](rewiredType) : null;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(type)) {
            const rewiredType = rewireType(type.ofType);
            return rewiredType != null ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLNonNull"](rewiredType) : null;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNamedType"])(type)) {
            let rewiredType = referenceTypeMap[type.name];
            if (rewiredType === undefined) {
                rewiredType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$stub$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNamedStub"])(type) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$stub$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBuiltInForStub"])(type) : rewireNamedType(type);
                newTypeMap[rewiredType.name] = referenceTypeMap[type.name] = rewiredType;
            }
            return rewiredType != null ? newTypeMap[rewiredType.name] : null;
        }
        return null;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/transformInputValue.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "parseInputValue",
    ()=>parseInputValue,
    "parseInputValueLiteral",
    ()=>parseInputValueLiteral,
    "serializeInputValue",
    ()=>serializeInputValue,
    "transformInputValue",
    ()=>transformInputValue
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
;
function transformInputValue(type, value, inputLeafValueTransformer = null, inputObjectValueTransformer = null) {
    if (value == null) {
        return value;
    }
    const nullableType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNullableType"])(type);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLeafType"])(nullableType)) {
        return inputLeafValueTransformer != null ? inputLeafValueTransformer(nullableType, value) : value;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListType"])(nullableType)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["asArray"])(value).map((listMember)=>transformInputValue(nullableType.ofType, listMember, inputLeafValueTransformer, inputObjectValueTransformer));
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(nullableType)) {
        const fields = nullableType.getFields();
        const newValue = {};
        for(const key in value){
            const field = fields[key];
            if (field != null) {
                newValue[key] = transformInputValue(field.type, value[key], inputLeafValueTransformer, inputObjectValueTransformer);
            }
        }
        return inputObjectValueTransformer != null ? inputObjectValueTransformer(nullableType, newValue) : newValue;
    }
// unreachable, no other possible return value
}
function serializeInputValue(type, value) {
    return transformInputValue(type, value, (t, v)=>{
        try {
            return t.serialize(v);
        } catch  {
            return v;
        }
    });
}
function parseInputValue(type, value) {
    return transformInputValue(type, value, (t, v)=>{
        try {
            return t.parseValue(v);
        } catch  {
            return v;
        }
    });
}
function parseInputValueLiteral(type, value) {
    return transformInputValue(type, value, (t, v)=>t.parseLiteral(v, {}));
}
}),
"[project]/node_modules/@graphql-tools/utils/esm/mapSchema.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "correctASTNodes",
    ()=>correctASTNodes,
    "mapSchema",
    ()=>mapSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/schema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/getObjectTypeFromTypeMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/Interfaces.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$rewire$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/rewire.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/transformInputValue.js [app-route] (ecmascript)");
;
;
;
;
;
function mapSchema(schema, schemaMapper = {}) {
    const newTypeMap = mapArguments(mapFields(mapTypes(mapDefaultValues(mapEnumValues(mapTypes(mapDefaultValues(schema.getTypeMap(), schema, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serializeInputValue"]), schema, schemaMapper, (type)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLeafType"])(type)), schema, schemaMapper), schema, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseInputValue"]), schema, schemaMapper, (type)=>!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLeafType"])(type)), schema, schemaMapper), schema, schemaMapper);
    const originalDirectives = schema.getDirectives();
    const newDirectives = mapDirectives(originalDirectives, schema, schemaMapper);
    const { typeMap, directives } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$rewire$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rewireTypes"])(newTypeMap, newDirectives);
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLSchema"]({
        ...schema.toConfig(),
        query: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getObjectTypeFromTypeMap"])(typeMap, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getObjectTypeFromTypeMap"])(newTypeMap, schema.getQueryType())),
        mutation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getObjectTypeFromTypeMap"])(typeMap, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getObjectTypeFromTypeMap"])(newTypeMap, schema.getMutationType())),
        subscription: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getObjectTypeFromTypeMap"])(typeMap, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$getObjectTypeFromTypeMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getObjectTypeFromTypeMap"])(newTypeMap, schema.getSubscriptionType())),
        types: Object.values(typeMap),
        directives
    });
}
function mapTypes(originalTypeMap, schema, schemaMapper, testFn = ()=>true) {
    const newTypeMap = {};
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__')) {
            const originalType = originalTypeMap[typeName];
            if (originalType == null || !testFn(originalType)) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const typeMapper = getTypeMapper(schema, schemaMapper, typeName);
            if (typeMapper == null) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const maybeNewType = typeMapper(originalType, schema);
            if (maybeNewType === undefined) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            newTypeMap[typeName] = maybeNewType;
        }
    }
    return newTypeMap;
}
function mapEnumValues(originalTypeMap, schema, schemaMapper) {
    const enumValueMapper = getEnumValueMapper(schemaMapper);
    if (!enumValueMapper) {
        return originalTypeMap;
    }
    return mapTypes(originalTypeMap, schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ENUM_TYPE]: (type)=>{
            const config = type.toConfig();
            const originalEnumValueConfigMap = config.values;
            const newEnumValueConfigMap = {};
            for(const externalValue in originalEnumValueConfigMap){
                const originalEnumValueConfig = originalEnumValueConfigMap[externalValue];
                const mappedEnumValue = enumValueMapper(originalEnumValueConfig, type.name, schema, externalValue);
                if (mappedEnumValue === undefined) {
                    newEnumValueConfigMap[externalValue] = originalEnumValueConfig;
                } else if (Array.isArray(mappedEnumValue)) {
                    const [newExternalValue, newEnumValueConfig] = mappedEnumValue;
                    newEnumValueConfigMap[newExternalValue] = newEnumValueConfig === undefined ? originalEnumValueConfig : newEnumValueConfig;
                } else if (mappedEnumValue !== null) {
                    newEnumValueConfigMap[externalValue] = mappedEnumValue;
                }
            }
            return correctASTNodes(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"]({
                ...config,
                values: newEnumValueConfigMap
            }));
        }
    }, (type)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type));
}
function mapDefaultValues(originalTypeMap, schema, fn) {
    const newTypeMap = mapArguments(originalTypeMap, schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ARGUMENT]: (argumentConfig)=>{
            if (argumentConfig.defaultValue === undefined) {
                return argumentConfig;
            }
            const maybeNewType = getNewType(originalTypeMap, argumentConfig.type);
            if (maybeNewType != null) {
                return {
                    ...argumentConfig,
                    defaultValue: fn(maybeNewType, argumentConfig.defaultValue)
                };
            }
        }
    });
    return mapFields(newTypeMap, schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INPUT_OBJECT_FIELD]: (inputFieldConfig)=>{
            if (inputFieldConfig.defaultValue === undefined) {
                return inputFieldConfig;
            }
            const maybeNewType = getNewType(newTypeMap, inputFieldConfig.type);
            if (maybeNewType != null) {
                return {
                    ...inputFieldConfig,
                    defaultValue: fn(maybeNewType, inputFieldConfig.defaultValue)
                };
            }
        }
    });
}
function getNewType(newTypeMap, type) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListType"])(type)) {
        const newType = getNewType(newTypeMap, type.ofType);
        return newType != null ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLList"](newType) : null;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullType"])(type)) {
        const newType = getNewType(newTypeMap, type.ofType);
        return newType != null ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLNonNull"](newType) : null;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNamedType"])(type)) {
        const newType = newTypeMap[type.name];
        return newType != null ? newType : null;
    }
    return null;
}
function mapFields(originalTypeMap, schema, schemaMapper) {
    const newTypeMap = {};
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__')) {
            const originalType = originalTypeMap[typeName];
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(originalType) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(originalType) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(originalType)) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const fieldMapper = getFieldMapper(schema, schemaMapper, typeName);
            if (fieldMapper == null) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const config = originalType.toConfig();
            const originalFieldConfigMap = config.fields;
            const newFieldConfigMap = {};
            for(const fieldName in originalFieldConfigMap){
                const originalFieldConfig = originalFieldConfigMap[fieldName];
                const mappedField = fieldMapper(originalFieldConfig, fieldName, typeName, schema);
                if (mappedField === undefined) {
                    newFieldConfigMap[fieldName] = originalFieldConfig;
                } else if (Array.isArray(mappedField)) {
                    const [newFieldName, newFieldConfig] = mappedField;
                    if (newFieldConfig.astNode != null) {
                        newFieldConfig.astNode = {
                            ...newFieldConfig.astNode,
                            name: {
                                ...newFieldConfig.astNode.name,
                                value: newFieldName
                            }
                        };
                    }
                    newFieldConfigMap[newFieldName] = newFieldConfig === undefined ? originalFieldConfig : newFieldConfig;
                } else if (mappedField !== null) {
                    newFieldConfigMap[fieldName] = mappedField;
                }
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(originalType)) {
                newTypeMap[typeName] = correctASTNodes(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"]({
                    ...config,
                    fields: newFieldConfigMap
                }));
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(originalType)) {
                newTypeMap[typeName] = correctASTNodes(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"]({
                    ...config,
                    fields: newFieldConfigMap
                }));
            } else {
                newTypeMap[typeName] = correctASTNodes(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInputObjectType"]({
                    ...config,
                    fields: newFieldConfigMap
                }));
            }
        }
    }
    return newTypeMap;
}
function mapArguments(originalTypeMap, schema, schemaMapper) {
    const newTypeMap = {};
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__')) {
            const originalType = originalTypeMap[typeName];
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(originalType) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(originalType)) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const argumentMapper = getArgumentMapper(schemaMapper);
            if (argumentMapper == null) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const config = originalType.toConfig();
            const originalFieldConfigMap = config.fields;
            const newFieldConfigMap = {};
            for(const fieldName in originalFieldConfigMap){
                const originalFieldConfig = originalFieldConfigMap[fieldName];
                const originalArgumentConfigMap = originalFieldConfig.args;
                if (originalArgumentConfigMap == null) {
                    newFieldConfigMap[fieldName] = originalFieldConfig;
                    continue;
                }
                const argumentNames = Object.keys(originalArgumentConfigMap);
                if (!argumentNames.length) {
                    newFieldConfigMap[fieldName] = originalFieldConfig;
                    continue;
                }
                const newArgumentConfigMap = {};
                for (const argumentName of argumentNames){
                    const originalArgumentConfig = originalArgumentConfigMap[argumentName];
                    const mappedArgument = argumentMapper(originalArgumentConfig, fieldName, typeName, schema);
                    if (mappedArgument === undefined) {
                        newArgumentConfigMap[argumentName] = originalArgumentConfig;
                    } else if (Array.isArray(mappedArgument)) {
                        const [newArgumentName, newArgumentConfig] = mappedArgument;
                        newArgumentConfigMap[newArgumentName] = newArgumentConfig;
                    } else if (mappedArgument !== null) {
                        newArgumentConfigMap[argumentName] = mappedArgument;
                    }
                }
                newFieldConfigMap[fieldName] = {
                    ...originalFieldConfig,
                    args: newArgumentConfigMap
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(originalType)) {
                newTypeMap[typeName] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"]({
                    ...config,
                    fields: newFieldConfigMap
                });
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(originalType)) {
                newTypeMap[typeName] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"]({
                    ...config,
                    fields: newFieldConfigMap
                });
            } else {
                newTypeMap[typeName] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInputObjectType"]({
                    ...config,
                    fields: newFieldConfigMap
                });
            }
        }
    }
    return newTypeMap;
}
function mapDirectives(originalDirectives, schema, schemaMapper) {
    const directiveMapper = getDirectiveMapper(schemaMapper);
    if (directiveMapper == null) {
        return originalDirectives.slice();
    }
    const newDirectives = [];
    for (const directive of originalDirectives){
        const mappedDirective = directiveMapper(directive, schema);
        if (mappedDirective === undefined) {
            newDirectives.push(directive);
        } else if (mappedDirective !== null) {
            newDirectives.push(mappedDirective);
        }
    }
    return newDirectives;
}
function getTypeSpecifiers(schema, typeName) {
    const type = schema.getType(typeName);
    const specifiers = [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].TYPE
    ];
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_TYPE, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].OBJECT_TYPE);
        if (typeName === schema.getQueryType()?.name) {
            specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ROOT_OBJECT, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].QUERY);
        } else if (typeName === schema.getMutationType()?.name) {
            specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ROOT_OBJECT, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].MUTATION);
        } else if (typeName === schema.getSubscriptionType()?.name) {
            specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ROOT_OBJECT, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].SUBSCRIPTION);
        }
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INPUT_OBJECT_TYPE);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_TYPE, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ABSTRACT_TYPE, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INTERFACE_TYPE);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_TYPE, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ABSTRACT_TYPE, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].UNION_TYPE);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ENUM_TYPE);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].SCALAR_TYPE);
    }
    return specifiers;
}
function getTypeMapper(schema, schemaMapper, typeName) {
    const specifiers = getTypeSpecifiers(schema, typeName);
    let typeMapper;
    const stack = [
        ...specifiers
    ];
    while(!typeMapper && stack.length > 0){
        // It is safe to use the ! operator here as we check the length.
        const next = stack.pop();
        typeMapper = schemaMapper[next];
    }
    return typeMapper != null ? typeMapper : null;
}
function getFieldSpecifiers(schema, typeName) {
    const type = schema.getType(typeName);
    const specifiers = [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].FIELD
    ];
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_FIELD, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].OBJECT_FIELD);
        if (typeName === schema.getQueryType()?.name) {
            specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ROOT_FIELD, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].QUERY_ROOT_FIELD);
        } else if (typeName === schema.getMutationType()?.name) {
            specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ROOT_FIELD, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].MUTATION_ROOT_FIELD);
        } else if (typeName === schema.getSubscriptionType()?.name) {
            specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ROOT_FIELD, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].SUBSCRIPTION_ROOT_FIELD);
        }
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_FIELD, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INTERFACE_FIELD);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
        specifiers.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INPUT_OBJECT_FIELD);
    }
    return specifiers;
}
function getFieldMapper(schema, schemaMapper, typeName) {
    const specifiers = getFieldSpecifiers(schema, typeName);
    let fieldMapper;
    const stack = [
        ...specifiers
    ];
    while(!fieldMapper && stack.length > 0){
        // It is safe to use the ! operator here as we check the length.
        const next = stack.pop();
        // TODO: fix this as unknown cast
        fieldMapper = schemaMapper[next];
    }
    return fieldMapper ?? null;
}
function getArgumentMapper(schemaMapper) {
    const argumentMapper = schemaMapper[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ARGUMENT];
    return argumentMapper != null ? argumentMapper : null;
}
function getDirectiveMapper(schemaMapper) {
    const directiveMapper = schemaMapper[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].DIRECTIVE];
    return directiveMapper != null ? directiveMapper : null;
}
function getEnumValueMapper(schemaMapper) {
    const enumValueMapper = schemaMapper[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ENUM_VALUE];
    return enumValueMapper != null ? enumValueMapper : null;
}
function correctASTNodes(type) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const fields = [];
            for(const fieldName in config.fields){
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig.astNode != null) {
                    fields.push(fieldConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_DEFINITION,
                fields
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_EXTENSION,
                    fields: undefined
                }));
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"](config);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const fields = [];
            for(const fieldName in config.fields){
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig.astNode != null) {
                    fields.push(fieldConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_DEFINITION,
                fields
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_EXTENSION,
                    fields: undefined
                }));
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"](config);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInputObjectType"])(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const fields = [];
            for(const fieldName in config.fields){
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig.astNode != null) {
                    fields.push(fieldConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_DEFINITION,
                fields
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_EXTENSION,
                    fields: undefined
                }));
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInputObjectType"](config);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const values = [];
            for(const enumKey in config.values){
                const enumValueConfig = config.values[enumKey];
                if (enumValueConfig.astNode != null) {
                    values.push(enumValueConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                values
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    values: undefined
                }));
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"](config);
    } else {
        return type;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/loaders.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[project]/node_modules/@graphql-tools/utils/cjs/helpers.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.asArray = void 0;
exports.isUrl = isUrl;
exports.isDocumentString = isDocumentString;
exports.isValidPath = isValidPath;
exports.compareStrings = compareStrings;
exports.nodeToString = nodeToString;
exports.compareNodes = compareNodes;
exports.isSome = isSome;
exports.assertSome = assertSome;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const URL_REGEXP = /^(https?|wss?|file):\/\//;
/**
 * Checks if the given string is a valid URL.
 *
 * @param str - The string to validate as a URL
 * @returns A boolean indicating whether the string is a valid URL
 *
 * @remarks
 * This function first attempts to use the `URL.canParse` method if available.
 * If not, it falls back to creating a new `URL` object to validate the string.
 */ function isUrl(str) {
    if (typeof str !== 'string') {
        return false;
    }
    if (!URL_REGEXP.test(str)) {
        return false;
    }
    if (URL.canParse) {
        return URL.canParse(str);
    }
    try {
        const url = new URL(str);
        return !!url;
    } catch (e) {
        return false;
    }
}
const asArray = (fns)=>Array.isArray(fns) ? fns : fns ? [
        fns
    ] : [];
exports.asArray = asArray;
const invalidDocRegex = /\.[a-z0-9]+$/i;
/**
 * Determines if a given input is a valid GraphQL document string.
 *
 * @param str - The input to validate as a GraphQL document
 * @returns A boolean indicating whether the input is a valid GraphQL document string
 *
 * @remarks
 * This function performs several validation checks:
 * - Ensures the input is a string
 * - Filters out strings with invalid document extensions
 * - Excludes URLs
 * - Attempts to parse the string as a GraphQL document
 *
 * @throws {Error} If the document fails to parse and is empty except GraphQL comments
 */ function isDocumentString(str) {
    if (typeof str !== 'string') {
        return false;
    }
    // XXX: is-valid-path or is-glob treat SDL as a valid path
    // (`scalar Date` for example)
    // this why checking the extension is fast enough
    // and prevent from parsing the string in order to find out
    // if the string is a SDL
    if (invalidDocRegex.test(str) || isUrl(str)) {
        return false;
    }
    try {
        (0, graphql_1.parse)(str);
        return true;
    } catch (e) {
        if (!e.message.includes('EOF') && str.replace(/(\#[^*]*)/g, '').trim() !== '' && str.includes(' ')) {
            throw new Error(`Failed to parse the GraphQL document. ${e.message}\n${str}`);
        }
    }
    return false;
}
const invalidPathRegex = /[‘“!%^<>`\n]/;
/**
 * Checkes whether the `str` contains any path illegal characters.
 *
 * A string may sometimes look like a path but is not (like an SDL of a simple
 * GraphQL schema). To make sure we don't yield false-positives in such cases,
 * we disallow new lines in paths (even though most Unix systems support new
 * lines in file names).
 */ function isValidPath(str) {
    return typeof str === 'string' && !invalidPathRegex.test(str);
}
function compareStrings(a, b) {
    if (String(a) < String(b)) {
        return -1;
    }
    if (String(a) > String(b)) {
        return 1;
    }
    return 0;
}
function nodeToString(a) {
    let name;
    if ('alias' in a) {
        name = a.alias?.value;
    }
    if (name == null && 'name' in a) {
        name = a.name?.value;
    }
    if (name == null) {
        name = a.kind;
    }
    return name;
}
function compareNodes(a, b, customFn) {
    const aStr = nodeToString(a);
    const bStr = nodeToString(b);
    if (typeof customFn === 'function') {
        return customFn(aStr, bStr);
    }
    return compareStrings(aStr, bStr);
}
function isSome(input) {
    return input != null;
}
function assertSome(input, message = 'Value should be something') {
    if (input == null) {
        throw new Error(message);
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/errors.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createGraphQLError = createGraphQLError;
exports.relocatedError = relocatedError;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const possibleGraphQLErrorProperties = [
    'message',
    'locations',
    'path',
    'nodes',
    'source',
    'positions',
    'originalError',
    'name',
    'stack',
    'extensions'
];
function isGraphQLErrorLike(error) {
    return error != null && typeof error === 'object' && Object.keys(error).every((key)=>possibleGraphQLErrorProperties.includes(key));
}
function createGraphQLError(message, options) {
    if (options?.originalError && !(options.originalError instanceof Error) && isGraphQLErrorLike(options.originalError)) {
        options.originalError = createGraphQLError(options.originalError.message, options.originalError);
    }
    if (graphql_1.versionInfo.major >= 17) {
        return new graphql_1.GraphQLError(message, options);
    }
    return new graphql_1.GraphQLError(message, options?.nodes, options?.source, options?.positions, options?.path, options?.originalError, options?.extensions);
}
function relocatedError(originalError, path) {
    return createGraphQLError(originalError.message, {
        nodes: originalError.nodes,
        source: originalError.source,
        positions: originalError.positions,
        path: path == null ? originalError.path : path,
        originalError,
        extensions: originalError.extensions
    });
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/jsutils.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.isPromise = void 0;
exports.isIterableObject = isIterableObject;
exports.isObjectLike = isObjectLike;
exports.promiseReduce = promiseReduce;
exports.hasOwnProperty = hasOwnProperty;
const promise_helpers_1 = __turbopack_context__.r("[project]/node_modules/@whatwg-node/promise-helpers/cjs/index.js [app-route] (ecmascript)");
Object.defineProperty(exports, "isPromise", {
    enumerable: true,
    get: function() {
        return promise_helpers_1.isPromise;
    }
});
function isIterableObject(value) {
    return value != null && typeof value === 'object' && Symbol.iterator in value;
}
function isObjectLike(value) {
    return typeof value === 'object' && value !== null;
}
function promiseReduce(values, callbackFn, initialValue) {
    let accumulator = initialValue;
    for (const value of values){
        accumulator = (0, promise_helpers_1.handleMaybePromise)(()=>accumulator, (resolved)=>callbackFn(resolved, value));
    }
    return accumulator;
}
function hasOwnProperty(obj, prop) {
    return Object.prototype.hasOwnProperty.call(obj, prop);
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/getArgumentValues.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getArgumentValues = getArgumentValues;
const cross_inspect_1 = __turbopack_context__.r("[project]/node_modules/cross-inspect/cjs/index.js [app-route] (ecmascript)");
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/errors.js [app-route] (ecmascript)");
const jsutils_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/jsutils.js [app-route] (ecmascript)");
/**
 * Prepares an object map of argument values given a list of argument
 * definitions and list of argument AST nodes.
 *
 * Note: The returned value is a plain Object with a prototype, since it is
 * exposed to user code. Care should be taken to not pull values from the
 * Object prototype.
 */ function getArgumentValues(def, node, variableValues = {}) {
    const coercedValues = {};
    const argumentNodes = node.arguments ?? [];
    const argNodeMap = argumentNodes.reduce((prev, arg)=>({
            ...prev,
            [arg.name.value]: arg
        }), {});
    for (const { name, type: argType, defaultValue } of def.args){
        const argumentNode = argNodeMap[name];
        if (!argumentNode) {
            if (defaultValue !== undefined) {
                coercedValues[name] = defaultValue;
            } else if ((0, graphql_1.isNonNullType)(argType)) {
                throw (0, errors_js_1.createGraphQLError)(`Argument "${name}" of required type "${(0, cross_inspect_1.inspect)(argType)}" ` + 'was not provided.', {
                    nodes: [
                        node
                    ]
                });
            }
            continue;
        }
        const valueNode = argumentNode.value;
        let isNull = valueNode.kind === graphql_1.Kind.NULL;
        if (valueNode.kind === graphql_1.Kind.VARIABLE) {
            const variableName = valueNode.name.value;
            if (variableValues == null || !(0, jsutils_js_1.hasOwnProperty)(variableValues, variableName)) {
                if (defaultValue !== undefined) {
                    coercedValues[name] = defaultValue;
                } else if ((0, graphql_1.isNonNullType)(argType)) {
                    throw (0, errors_js_1.createGraphQLError)(`Argument "${name}" of required type "${(0, cross_inspect_1.inspect)(argType)}" ` + `was provided the variable "$${variableName}" which was not provided a runtime value.`, {
                        nodes: [
                            valueNode
                        ]
                    });
                }
                continue;
            }
            isNull = variableValues[variableName] == null;
        }
        if (isNull && (0, graphql_1.isNonNullType)(argType)) {
            throw (0, errors_js_1.createGraphQLError)(`Argument "${name}" of non-null type "${(0, cross_inspect_1.inspect)(argType)}" ` + 'must not be null.', {
                nodes: [
                    valueNode
                ]
            });
        }
        const coercedValue = (0, graphql_1.valueFromAST)(valueNode, argType, variableValues);
        if (coercedValue === undefined) {
            // Note: ValuesOfCorrectTypeRule validation should catch this before
            // execution. This is a runtime check to ensure execution does not
            // continue with an invalid argument value.
            throw (0, errors_js_1.createGraphQLError)(`Argument "${name}" has invalid value ${(0, graphql_1.print)(valueNode)}.`, {
                nodes: [
                    valueNode
                ]
            });
        }
        coercedValues[name] = coercedValue;
    }
    return coercedValues;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.memoize1 = memoize1;
exports.memoize2 = memoize2;
exports.memoize3 = memoize3;
exports.memoize4 = memoize4;
exports.memoize5 = memoize5;
exports.memoize2of4 = memoize2of4;
exports.memoize2of5 = memoize2of5;
function memoize1(fn) {
    const memoize1cache = new WeakMap();
    return function memoized(a1) {
        const cachedValue = memoize1cache.get(a1);
        if (cachedValue === undefined) {
            const newValue = fn(a1);
            memoize1cache.set(a1, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize2(fn) {
    const memoize2cache = new WeakMap();
    return function memoized(a1, a2) {
        let cache2 = memoize2cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize2cache.set(a1, cache2);
            const newValue = fn(a1, a2);
            cache2.set(a2, newValue);
            return newValue;
        }
        const cachedValue = cache2.get(a2);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2);
            cache2.set(a2, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize3(fn) {
    const memoize3Cache = new WeakMap();
    return function memoized(a1, a2, a3) {
        let cache2 = memoize3Cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize3Cache.set(a1, cache2);
            const cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const newValue = fn(a1, a2, a3);
            cache3.set(a3, newValue);
            return newValue;
        }
        let cache3 = cache2.get(a2);
        if (!cache3) {
            cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const newValue = fn(a1, a2, a3);
            cache3.set(a3, newValue);
            return newValue;
        }
        const cachedValue = cache3.get(a3);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3);
            cache3.set(a3, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize4(fn) {
    const memoize4Cache = new WeakMap();
    return function memoized(a1, a2, a3, a4) {
        let cache2 = memoize4Cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize4Cache.set(a1, cache2);
            const cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        let cache3 = cache2.get(a2);
        if (!cache3) {
            cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        const cache4 = cache3.get(a3);
        if (!cache4) {
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        const cachedValue = cache4.get(a4);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4);
            cache4.set(a4, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize5(fn) {
    const memoize5Cache = new WeakMap();
    return function memoized(a1, a2, a3, a4, a5) {
        let cache2 = memoize5Cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize5Cache.set(a1, cache2);
            const cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        let cache3 = cache2.get(a2);
        if (!cache3) {
            cache3 = new WeakMap();
            cache2.set(a2, cache3);
            const cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        let cache4 = cache3.get(a3);
        if (!cache4) {
            cache4 = new WeakMap();
            cache3.set(a3, cache4);
            const cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        let cache5 = cache4.get(a4);
        if (!cache5) {
            cache5 = new WeakMap();
            cache4.set(a4, cache5);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        const cachedValue = cache5.get(a5);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4, a5);
            cache5.set(a5, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize2of4(fn) {
    const memoize2of4cache = new WeakMap();
    return function memoized(a1, a2, a3, a4) {
        let cache2 = memoize2of4cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize2of4cache.set(a1, cache2);
            const newValue = fn(a1, a2, a3, a4);
            cache2.set(a2, newValue);
            return newValue;
        }
        const cachedValue = cache2.get(a2);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4);
            cache2.set(a2, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
function memoize2of5(fn) {
    const memoize2of4cache = new WeakMap();
    return function memoized(a1, a2, a3, a4, a5) {
        let cache2 = memoize2of4cache.get(a1);
        if (!cache2) {
            cache2 = new WeakMap();
            memoize2of4cache.set(a1, cache2);
            const newValue = fn(a1, a2, a3, a4, a5);
            cache2.set(a2, newValue);
            return newValue;
        }
        const cachedValue = cache2.get(a2);
        if (cachedValue === undefined) {
            const newValue = fn(a1, a2, a3, a4, a5);
            cache2.set(a2, newValue);
            return newValue;
        }
        return cachedValue;
    };
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/getDirectiveExtensions.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getDirectiveExtensions = getDirectiveExtensions;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const getArgumentValues_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getArgumentValues.js [app-route] (ecmascript)");
const memoize_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)");
function getDirectiveExtensions(directableObj, schema, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = {};
    if (directableObj.extensions) {
        let directivesInExtensions = directableObj.extensions;
        for (const pathSegment of pathToDirectivesInExtensions){
            directivesInExtensions = directivesInExtensions?.[pathSegment];
        }
        if (directivesInExtensions != null) {
            for(const directiveNameProp in directivesInExtensions){
                const directiveObjs = directivesInExtensions[directiveNameProp];
                const directiveName = directiveNameProp;
                if (Array.isArray(directiveObjs)) {
                    for (const directiveObj of directiveObjs){
                        let existingDirectiveExtensions = directiveExtensions[directiveName];
                        if (!existingDirectiveExtensions) {
                            existingDirectiveExtensions = [];
                            directiveExtensions[directiveName] = existingDirectiveExtensions;
                        }
                        existingDirectiveExtensions.push(directiveObj);
                    }
                } else {
                    let existingDirectiveExtensions = directiveExtensions[directiveName];
                    if (!existingDirectiveExtensions) {
                        existingDirectiveExtensions = [];
                        directiveExtensions[directiveName] = existingDirectiveExtensions;
                    }
                    existingDirectiveExtensions.push(directiveObjs);
                }
            }
        }
    }
    const memoizedStringify = (0, memoize_js_1.memoize1)((obj)=>JSON.stringify(obj));
    const astNodes = [];
    if (directableObj.astNode) {
        astNodes.push(directableObj.astNode);
    }
    if (directableObj.extensionASTNodes) {
        astNodes.push(...directableObj.extensionASTNodes);
    }
    for (const astNode of astNodes){
        if (astNode.directives?.length) {
            for (const directive of astNode.directives){
                const directiveName = directive.name.value;
                let existingDirectiveExtensions = directiveExtensions[directiveName];
                if (!existingDirectiveExtensions) {
                    existingDirectiveExtensions = [];
                    directiveExtensions[directiveName] = existingDirectiveExtensions;
                }
                const directiveInSchema = schema?.getDirective(directiveName);
                let value = {};
                if (directiveInSchema) {
                    value = (0, getArgumentValues_js_1.getArgumentValues)(directiveInSchema, directive);
                }
                if (directive.arguments) {
                    for (const argNode of directive.arguments){
                        const argName = argNode.name.value;
                        if (value[argName] == null) {
                            const argInDirective = directiveInSchema?.args.find((arg)=>arg.name === argName);
                            if (argInDirective) {
                                value[argName] = (0, graphql_1.valueFromAST)(argNode.value, argInDirective.type);
                            }
                        }
                        if (value[argName] == null) {
                            value[argName] = (0, graphql_1.valueFromASTUntyped)(argNode.value);
                        }
                    }
                }
                if (astNodes.length > 0 && existingDirectiveExtensions.length > 0) {
                    const valStr = memoizedStringify(value);
                    if (existingDirectiveExtensions.some((val)=>memoizedStringify(val) === valStr)) {
                        continue;
                    }
                }
                existingDirectiveExtensions.push(value);
            }
        }
    }
    return directiveExtensions;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/get-directives.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getDirectivesInExtensions = getDirectivesInExtensions;
exports.getDirectiveInExtensions = getDirectiveInExtensions;
exports.getDirectives = getDirectives;
exports.getDirective = getDirective;
const getDirectiveExtensions_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getDirectiveExtensions.js [app-route] (ecmascript)");
function getDirectivesInExtensions(node, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, undefined, pathToDirectivesInExtensions);
    return Object.entries(directiveExtensions).map(([directiveName, directiveArgsArr])=>directiveArgsArr?.map((directiveArgs)=>({
                name: directiveName,
                args: directiveArgs
            }))).flat(Infinity).filter(Boolean);
}
function getDirectiveInExtensions(node, directiveName, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, undefined, pathToDirectivesInExtensions);
    return directiveExtensions[directiveName];
}
function getDirectives(schema, node, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, schema, pathToDirectivesInExtensions);
    return Object.entries(directiveExtensions).map(([directiveName, directiveArgsArr])=>directiveArgsArr?.map((directiveArgs)=>({
                name: directiveName,
                args: directiveArgs
            }))).flat(Infinity).filter(Boolean);
}
function getDirective(schema, node, directiveName, pathToDirectivesInExtensions = [
    'directives'
]) {
    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, schema, pathToDirectivesInExtensions);
    return directiveExtensions[directiveName];
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/get-fields-with-directives.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getFieldsWithDirectives = getFieldsWithDirectives;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function getFieldsWithDirectives(documentNode, options = {}) {
    const result = {};
    let selected = [
        'ObjectTypeDefinition',
        'ObjectTypeExtension'
    ];
    if (options.includeInputTypes) {
        selected = [
            ...selected,
            'InputObjectTypeDefinition',
            'InputObjectTypeExtension'
        ];
    }
    const allTypes = documentNode.definitions.filter((obj)=>selected.includes(obj.kind));
    for (const type of allTypes){
        const typeName = type.name.value;
        if (type.fields == null) {
            continue;
        }
        for (const field of type.fields){
            if (field.directives && field.directives.length > 0) {
                const fieldName = field.name.value;
                const key = `${typeName}.${fieldName}`;
                const directives = field.directives.map((d)=>({
                        name: d.name.value,
                        args: (d.arguments || []).reduce((prev, arg)=>({
                                ...prev,
                                [arg.name.value]: (0, graphql_1.valueFromASTUntyped)(arg.value)
                            }), {})
                    }));
                result[key] = directives;
            }
        }
    }
    return result;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/get-arguments-with-directives.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getArgumentsWithDirectives = getArgumentsWithDirectives;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function isTypeWithFields(t) {
    return t.kind === graphql_1.Kind.OBJECT_TYPE_DEFINITION || t.kind === graphql_1.Kind.OBJECT_TYPE_EXTENSION;
}
function getArgumentsWithDirectives(documentNode) {
    const result = {};
    const allTypes = documentNode.definitions.filter(isTypeWithFields);
    for (const type of allTypes){
        if (type.fields == null) {
            continue;
        }
        for (const field of type.fields){
            const argsWithDirectives = field.arguments?.filter((arg)=>arg.directives?.length);
            if (!argsWithDirectives?.length) {
                continue;
            }
            const typeFieldResult = result[`${type.name.value}.${field.name.value}`] = {};
            for (const arg of argsWithDirectives){
                const directives = arg.directives.map((d)=>({
                        name: d.name.value,
                        args: (d.arguments || []).reduce((prev, dArg)=>({
                                ...prev,
                                [dArg.name.value]: (0, graphql_1.valueFromASTUntyped)(dArg.value)
                            }), {})
                    }));
                typeFieldResult[arg.name.value] = directives;
            }
        }
    }
    return result;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/get-implementing-types.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getImplementingTypes = getImplementingTypes;
function getImplementingTypes(interfaceName, schema) {
    const allTypesMap = schema.getTypeMap();
    const result = [];
    for(const graphqlTypeName in allTypesMap){
        const graphqlType = allTypesMap[graphqlTypeName];
        if ('getInterfaces' in graphqlType) {
            const allInterfaces = graphqlType.getInterfaces();
            if (allInterfaces.find((int)=>int.name === interfaceName)) {
                result.push(graphqlType.name);
            }
        }
    }
    return result;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/astFromType.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.astFromType = astFromType;
const cross_inspect_1 = __turbopack_context__.r("[project]/node_modules/cross-inspect/cjs/index.js [app-route] (ecmascript)");
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function astFromType(type) {
    if ((0, graphql_1.isNonNullType)(type)) {
        const innerType = astFromType(type.ofType);
        if (innerType.kind === graphql_1.Kind.NON_NULL_TYPE) {
            throw new Error(`Invalid type node ${(0, cross_inspect_1.inspect)(type)}. Inner type of non-null type cannot be a non-null type.`);
        }
        return {
            kind: graphql_1.Kind.NON_NULL_TYPE,
            type: innerType
        };
    } else if ((0, graphql_1.isListType)(type)) {
        return {
            kind: graphql_1.Kind.LIST_TYPE,
            type: astFromType(type.ofType)
        };
    }
    return {
        kind: graphql_1.Kind.NAMED_TYPE,
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        }
    };
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/astFromValueUntyped.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.astFromValueUntyped = astFromValueUntyped;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
/**
 * Produces a GraphQL Value AST given a JavaScript object.
 * Function will match JavaScript/JSON values to GraphQL AST schema format
 * by using the following mapping.
 *
 * | JSON Value    | GraphQL Value        |
 * | ------------- | -------------------- |
 * | Object        | Input Object         |
 * | Array         | List                 |
 * | Boolean       | Boolean              |
 * | String        | String               |
 * | Number        | Int / Float          |
 * | BigInt        | Int                  |
 * | null          | NullValue            |
 *
 */ function astFromValueUntyped(value) {
    // only explicit null, not undefined, NaN
    if (value === null) {
        return {
            kind: graphql_1.Kind.NULL
        };
    }
    // undefined
    if (value === undefined) {
        return null;
    }
    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but
    // the value is not an array, convert the value using the list's item type.
    if (Array.isArray(value)) {
        const valuesNodes = [];
        for (const item of value){
            const itemNode = astFromValueUntyped(item);
            if (itemNode != null) {
                valuesNodes.push(itemNode);
            }
        }
        return {
            kind: graphql_1.Kind.LIST,
            values: valuesNodes
        };
    }
    if (typeof value === 'object') {
        if (value?.toJSON) {
            return astFromValueUntyped(value.toJSON());
        }
        const fieldNodes = [];
        for(const fieldName in value){
            const fieldValue = value[fieldName];
            const ast = astFromValueUntyped(fieldValue);
            if (ast) {
                fieldNodes.push({
                    kind: graphql_1.Kind.OBJECT_FIELD,
                    name: {
                        kind: graphql_1.Kind.NAME,
                        value: fieldName
                    },
                    value: ast
                });
            }
        }
        return {
            kind: graphql_1.Kind.OBJECT,
            fields: fieldNodes
        };
    }
    // Others serialize based on their corresponding JavaScript scalar types.
    if (typeof value === 'boolean') {
        return {
            kind: graphql_1.Kind.BOOLEAN,
            value
        };
    }
    if (typeof value === 'bigint') {
        return {
            kind: graphql_1.Kind.INT,
            value: String(value)
        };
    }
    // JavaScript numbers can be Int or Float values.
    if (typeof value === 'number' && isFinite(value)) {
        const stringNum = String(value);
        return integerStringRegExp.test(stringNum) ? {
            kind: graphql_1.Kind.INT,
            value: stringNum
        } : {
            kind: graphql_1.Kind.FLOAT,
            value: stringNum
        };
    }
    if (typeof value === 'string') {
        return {
            kind: graphql_1.Kind.STRING,
            value
        };
    }
    throw new TypeError(`Cannot convert value to AST: ${value}.`);
}
/**
 * IntValue:
 *   - NegativeSign? 0
 *   - NegativeSign? NonZeroDigit ( Digit+ )?
 */ const integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;
}),
"[project]/node_modules/@graphql-tools/utils/cjs/astFromValue.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.astFromValue = astFromValue;
const cross_inspect_1 = __turbopack_context__.r("[project]/node_modules/cross-inspect/cjs/index.js [app-route] (ecmascript)");
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const astFromValueUntyped_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromValueUntyped.js [app-route] (ecmascript)");
const jsutils_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/jsutils.js [app-route] (ecmascript)");
/**
 * Produces a GraphQL Value AST given a JavaScript object.
 * Function will match JavaScript/JSON values to GraphQL AST schema format
 * by using suggested GraphQLInputType. For example:
 *
 *     astFromValue("value", GraphQLString)
 *
 * A GraphQL type must be provided, which will be used to interpret different
 * JavaScript values.
 *
 * | JSON Value    | GraphQL Value        |
 * | ------------- | -------------------- |
 * | Object        | Input Object         |
 * | Array         | List                 |
 * | Boolean       | Boolean              |
 * | String        | String / Enum Value  |
 * | Number        | Int / Float          |
 * | BigInt        | Int                  |
 * | Unknown       | Enum Value           |
 * | null          | NullValue            |
 *
 */ function astFromValue(value, type) {
    if ((0, graphql_1.isNonNullType)(type)) {
        const astValue = astFromValue(value, type.ofType);
        if (astValue?.kind === graphql_1.Kind.NULL) {
            return null;
        }
        return astValue;
    }
    // only explicit null, not undefined, NaN
    if (value === null) {
        return {
            kind: graphql_1.Kind.NULL
        };
    }
    // undefined
    if (value === undefined) {
        return null;
    }
    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but
    // the value is not an array, convert the value using the list's item type.
    if ((0, graphql_1.isListType)(type)) {
        const itemType = type.ofType;
        if ((0, jsutils_js_1.isIterableObject)(value)) {
            const valuesNodes = [];
            for (const item of value){
                const itemNode = astFromValue(item, itemType);
                if (itemNode != null) {
                    valuesNodes.push(itemNode);
                }
            }
            return {
                kind: graphql_1.Kind.LIST,
                values: valuesNodes
            };
        }
        return astFromValue(value, itemType);
    }
    // Populate the fields of the input object by creating ASTs from each value
    // in the JavaScript object according to the fields in the input type.
    if ((0, graphql_1.isInputObjectType)(type)) {
        if (!(0, jsutils_js_1.isObjectLike)(value)) {
            return null;
        }
        const fieldNodes = [];
        for (const field of Object.values(type.getFields())){
            const fieldValue = astFromValue(value[field.name], field.type);
            if (fieldValue) {
                fieldNodes.push({
                    kind: graphql_1.Kind.OBJECT_FIELD,
                    name: {
                        kind: graphql_1.Kind.NAME,
                        value: field.name
                    },
                    value: fieldValue
                });
            }
        }
        return {
            kind: graphql_1.Kind.OBJECT,
            fields: fieldNodes
        };
    }
    if ((0, graphql_1.isLeafType)(type)) {
        // Since value is an internally represented value, it must be serialized
        // to an externally represented value before converting into an AST.
        const serialized = type.serialize(value);
        if (serialized == null) {
            return null;
        }
        if ((0, graphql_1.isEnumType)(type)) {
            return {
                kind: graphql_1.Kind.ENUM,
                value: serialized
            };
        }
        // ID types can use Int literals.
        if (type.name === 'ID' && typeof serialized === 'string' && integerStringRegExp.test(serialized)) {
            return {
                kind: graphql_1.Kind.INT,
                value: serialized
            };
        }
        return (0, astFromValueUntyped_js_1.astFromValueUntyped)(serialized);
    }
    /* c8 ignore next 3 */ // Not reachable, all possible types have been considered.
    console.assert(false, 'Unexpected input type: ' + (0, cross_inspect_1.inspect)(type));
}
/**
 * IntValue:
 *   - NegativeSign? 0
 *   - NegativeSign? NonZeroDigit ( Digit+ )?
 */ const integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;
}),
"[project]/node_modules/@graphql-tools/utils/cjs/descriptionFromObject.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getDescriptionNode = getDescriptionNode;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function getDescriptionNode(obj) {
    if (obj.astNode?.description) {
        return {
            ...obj.astNode.description,
            block: true
        };
    }
    if (obj.description) {
        return {
            kind: graphql_1.Kind.STRING,
            value: obj.description,
            block: true
        };
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/rootTypes.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getRootTypeMap = exports.getRootTypes = exports.getRootTypeNames = void 0;
exports.getDefinedRootType = getDefinedRootType;
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/errors.js [app-route] (ecmascript)");
const memoize_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)");
function getDefinedRootType(schema, operation, nodes) {
    const rootTypeMap = (0, exports.getRootTypeMap)(schema);
    const rootType = rootTypeMap.get(operation);
    if (rootType == null) {
        throw (0, errors_js_1.createGraphQLError)(`Schema is not configured to execute ${operation} operation.`, {
            nodes
        });
    }
    return rootType;
}
exports.getRootTypeNames = (0, memoize_js_1.memoize1)(function getRootTypeNames(schema) {
    const rootTypes = (0, exports.getRootTypes)(schema);
    return new Set([
        ...rootTypes
    ].map((type)=>type.name));
});
exports.getRootTypes = (0, memoize_js_1.memoize1)(function getRootTypes(schema) {
    const rootTypeMap = (0, exports.getRootTypeMap)(schema);
    return new Set(rootTypeMap.values());
});
exports.getRootTypeMap = (0, memoize_js_1.memoize1)(function getRootTypeMap(schema) {
    const rootTypeMap = new Map();
    const queryType = schema.getQueryType();
    if (queryType) {
        rootTypeMap.set('query', queryType);
    }
    const mutationType = schema.getMutationType();
    if (mutationType) {
        rootTypeMap.set('mutation', mutationType);
    }
    const subscriptionType = schema.getSubscriptionType();
    if (subscriptionType) {
        rootTypeMap.set('subscription', subscriptionType);
    }
    return rootTypeMap;
});
}),
"[project]/node_modules/@graphql-tools/utils/cjs/print-schema-with-directives.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getDocumentNodeFromSchema = getDocumentNodeFromSchema;
exports.printSchemaWithDirectives = printSchemaWithDirectives;
exports.astFromSchema = astFromSchema;
exports.astFromDirective = astFromDirective;
exports.getDirectiveNodes = getDirectiveNodes;
exports.astFromArg = astFromArg;
exports.astFromObjectType = astFromObjectType;
exports.astFromInterfaceType = astFromInterfaceType;
exports.astFromUnionType = astFromUnionType;
exports.astFromInputObjectType = astFromInputObjectType;
exports.astFromEnumType = astFromEnumType;
exports.astFromScalarType = astFromScalarType;
exports.astFromField = astFromField;
exports.astFromInputField = astFromInputField;
exports.astFromEnumValue = astFromEnumValue;
exports.makeDeprecatedDirective = makeDeprecatedDirective;
exports.makeDirectiveNode = makeDirectiveNode;
exports.makeDirectiveNodes = makeDirectiveNodes;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const astFromType_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromType.js [app-route] (ecmascript)");
const astFromValue_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromValue.js [app-route] (ecmascript)");
const astFromValueUntyped_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromValueUntyped.js [app-route] (ecmascript)");
const descriptionFromObject_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/descriptionFromObject.js [app-route] (ecmascript)");
const get_directives_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-directives.js [app-route] (ecmascript)");
const helpers_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/helpers.js [app-route] (ecmascript)");
const rootTypes_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rootTypes.js [app-route] (ecmascript)");
function getDocumentNodeFromSchema(schema, options = {}) {
    const pathToDirectivesInExtensions = options.pathToDirectivesInExtensions;
    const typesMap = schema.getTypeMap();
    const schemaNode = astFromSchema(schema, pathToDirectivesInExtensions);
    const definitions = schemaNode != null ? [
        schemaNode
    ] : [];
    const directives = schema.getDirectives();
    for (const directive of directives){
        if ((0, graphql_1.isSpecifiedDirective)(directive)) {
            continue;
        }
        definitions.push(astFromDirective(directive, schema, pathToDirectivesInExtensions));
    }
    for(const typeName in typesMap){
        const type = typesMap[typeName];
        const isPredefinedScalar = (0, graphql_1.isSpecifiedScalarType)(type);
        const isIntrospection = (0, graphql_1.isIntrospectionType)(type);
        if (isPredefinedScalar || isIntrospection) {
            continue;
        }
        if ((0, graphql_1.isObjectType)(type)) {
            definitions.push(astFromObjectType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, graphql_1.isInterfaceType)(type)) {
            definitions.push(astFromInterfaceType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, graphql_1.isUnionType)(type)) {
            definitions.push(astFromUnionType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, graphql_1.isInputObjectType)(type)) {
            definitions.push(astFromInputObjectType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, graphql_1.isEnumType)(type)) {
            definitions.push(astFromEnumType(type, schema, pathToDirectivesInExtensions));
        } else if ((0, graphql_1.isScalarType)(type)) {
            definitions.push(astFromScalarType(type, schema, pathToDirectivesInExtensions));
        } else {
            throw new Error(`Unknown type ${type}.`);
        }
    }
    return {
        kind: graphql_1.Kind.DOCUMENT,
        definitions
    };
}
// this approach uses the default schema printer rather than a custom solution, so may be more backwards compatible
// currently does not allow customization of printSchema options having to do with comments.
function printSchemaWithDirectives(schema, options = {}) {
    const documentNode = getDocumentNodeFromSchema(schema, options);
    return (0, graphql_1.print)(documentNode);
}
function astFromSchema(schema, pathToDirectivesInExtensions) {
    const operationTypeMap = new Map([
        [
            'query',
            undefined
        ],
        [
            'mutation',
            undefined
        ],
        [
            'subscription',
            undefined
        ]
    ]);
    const nodes = [];
    if (schema.astNode != null) {
        nodes.push(schema.astNode);
    }
    if (schema.extensionASTNodes != null) {
        for (const extensionASTNode of schema.extensionASTNodes){
            nodes.push(extensionASTNode);
        }
    }
    for (const node of nodes){
        if (node.operationTypes) {
            for (const operationTypeDefinitionNode of node.operationTypes){
                operationTypeMap.set(operationTypeDefinitionNode.operation, operationTypeDefinitionNode);
            }
        }
    }
    const rootTypeMap = (0, rootTypes_js_1.getRootTypeMap)(schema);
    for (const [operationTypeNode, operationTypeDefinitionNode] of operationTypeMap){
        const rootType = rootTypeMap.get(operationTypeNode);
        if (rootType != null) {
            const rootTypeAST = (0, astFromType_js_1.astFromType)(rootType);
            if (operationTypeDefinitionNode != null) {
                operationTypeDefinitionNode.type = rootTypeAST;
            } else {
                operationTypeMap.set(operationTypeNode, {
                    kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,
                    operation: operationTypeNode,
                    type: rootTypeAST
                });
            }
        }
    }
    const operationTypes = [
        ...operationTypeMap.values()
    ].filter(helpers_js_1.isSome);
    const directives = getDirectiveNodes(schema, schema, pathToDirectivesInExtensions);
    if (!operationTypes.length && !directives.length) {
        return null;
    }
    const schemaNode = {
        kind: operationTypes != null ? graphql_1.Kind.SCHEMA_DEFINITION : graphql_1.Kind.SCHEMA_EXTENSION,
        operationTypes,
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: directives
    };
    const descriptionNode = (0, descriptionFromObject_js_1.getDescriptionNode)(schema);
    if (descriptionNode) {
        schemaNode.description = descriptionNode;
    }
    return schemaNode;
}
function astFromDirective(directive, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.DIRECTIVE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(directive),
        name: {
            kind: graphql_1.Kind.NAME,
            value: directive.name
        },
        arguments: directive.args?.map((arg)=>astFromArg(arg, schema, pathToDirectivesInExtensions)),
        repeatable: directive.isRepeatable,
        locations: directive.locations?.map((location)=>({
                kind: graphql_1.Kind.NAME,
                value: location
            })) || []
    };
}
function getDirectiveNodes(entity, schema, pathToDirectivesInExtensions) {
    let directiveNodesBesidesNativeDirectives = [];
    const directivesInExtensions = (0, get_directives_js_1.getDirectivesInExtensions)(entity, pathToDirectivesInExtensions);
    let directives;
    if (directivesInExtensions != null) {
        directives = makeDirectiveNodes(schema, directivesInExtensions);
    }
    let deprecatedDirectiveNode = null;
    let specifiedByDirectiveNode = null;
    let oneOfDirectiveNode = null;
    if (directives != null) {
        directiveNodesBesidesNativeDirectives = directives.filter((directive)=>graphql_1.specifiedDirectives.every((specifiedDirective)=>specifiedDirective.name !== directive.name.value));
        deprecatedDirectiveNode = directives.find((directive)=>directive.name.value === 'deprecated');
        specifiedByDirectiveNode = directives.find((directive)=>directive.name.value === 'specifiedBy');
        oneOfDirectiveNode = directives.find((directive)=>directive.name.value === 'oneOf');
    }
    if (entity.deprecationReason != null && deprecatedDirectiveNode == null) {
        deprecatedDirectiveNode = makeDeprecatedDirective(entity.deprecationReason);
    }
    if (entity.specifiedByUrl != null || entity.specifiedByURL != null && specifiedByDirectiveNode == null) {
        const specifiedByValue = entity.specifiedByUrl || entity.specifiedByURL;
        const specifiedByArgs = {
            url: specifiedByValue
        };
        specifiedByDirectiveNode = makeDirectiveNode('specifiedBy', specifiedByArgs);
    }
    if (entity.isOneOf && oneOfDirectiveNode == null) {
        oneOfDirectiveNode = makeDirectiveNode('oneOf');
    }
    if (deprecatedDirectiveNode != null) {
        directiveNodesBesidesNativeDirectives.push(deprecatedDirectiveNode);
    }
    if (specifiedByDirectiveNode != null) {
        directiveNodesBesidesNativeDirectives.push(specifiedByDirectiveNode);
    }
    if (oneOfDirectiveNode != null) {
        directiveNodesBesidesNativeDirectives.push(oneOfDirectiveNode);
    }
    return directiveNodesBesidesNativeDirectives;
}
function astFromArg(arg, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.INPUT_VALUE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(arg),
        name: {
            kind: graphql_1.Kind.NAME,
            value: arg.name
        },
        type: (0, astFromType_js_1.astFromType)(arg.type),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        defaultValue: arg.defaultValue !== undefined ? (0, astFromValue_js_1.astFromValue)(arg.defaultValue, arg.type) ?? undefined : undefined,
        directives: getDirectiveNodes(arg, schema, pathToDirectivesInExtensions)
    };
}
function astFromObjectType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.OBJECT_TYPE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        },
        fields: Object.values(type.getFields()).map((field)=>astFromField(field, schema, pathToDirectivesInExtensions)),
        interfaces: Object.values(type.getInterfaces()).map((iFace)=>(0, astFromType_js_1.astFromType)(iFace)),
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
}
function astFromInterfaceType(type, schema, pathToDirectivesInExtensions) {
    const node = {
        kind: graphql_1.Kind.INTERFACE_TYPE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        },
        fields: Object.values(type.getFields()).map((field)=>astFromField(field, schema, pathToDirectivesInExtensions)),
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
    if ('getInterfaces' in type) {
        node.interfaces = Object.values(type.getInterfaces()).map((iFace)=>(0, astFromType_js_1.astFromType)(iFace));
    }
    return node;
}
function astFromUnionType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.UNION_TYPE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        },
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),
        types: type.getTypes().map((type)=>(0, astFromType_js_1.astFromType)(type))
    };
}
function astFromInputObjectType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        },
        fields: Object.values(type.getFields()).map((field)=>astFromInputField(field, schema, pathToDirectivesInExtensions)),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
}
function astFromEnumType(type, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.ENUM_TYPE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        },
        values: Object.values(type.getValues()).map((value)=>astFromEnumValue(value, schema, pathToDirectivesInExtensions)),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions)
    };
}
function astFromScalarType(type, schema, pathToDirectivesInExtensions) {
    const directivesInExtensions = (0, get_directives_js_1.getDirectivesInExtensions)(type, pathToDirectivesInExtensions);
    const directives = makeDirectiveNodes(schema, directivesInExtensions);
    const specifiedByValue = type['specifiedByUrl'] || type['specifiedByURL'];
    if (specifiedByValue && !directives.some((directiveNode)=>directiveNode.name.value === 'specifiedBy')) {
        const specifiedByArgs = {
            url: specifiedByValue
        };
        directives.push(makeDirectiveNode('specifiedBy', specifiedByArgs));
    }
    return {
        kind: graphql_1.Kind.SCALAR_TYPE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name
        },
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: directives
    };
}
function astFromField(field, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.FIELD_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(field),
        name: {
            kind: graphql_1.Kind.NAME,
            value: field.name
        },
        arguments: field.args.map((arg)=>astFromArg(arg, schema, pathToDirectivesInExtensions)),
        type: (0, astFromType_js_1.astFromType)(field.type),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions)
    };
}
function astFromInputField(field, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.INPUT_VALUE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(field),
        name: {
            kind: graphql_1.Kind.NAME,
            value: field.name
        },
        type: (0, astFromType_js_1.astFromType)(field.type),
        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),
        defaultValue: (0, astFromValue_js_1.astFromValue)(field.defaultValue, field.type) ?? undefined
    };
}
function astFromEnumValue(value, schema, pathToDirectivesInExtensions) {
    return {
        kind: graphql_1.Kind.ENUM_VALUE_DEFINITION,
        description: (0, descriptionFromObject_js_1.getDescriptionNode)(value),
        name: {
            kind: graphql_1.Kind.NAME,
            value: value.name
        },
        directives: getDirectiveNodes(value, schema, pathToDirectivesInExtensions)
    };
}
function makeDeprecatedDirective(deprecationReason) {
    return makeDirectiveNode('deprecated', {
        reason: deprecationReason
    }, graphql_1.GraphQLDeprecatedDirective);
}
function makeDirectiveNode(name, args, directive) {
    const directiveArguments = [];
    for(const argName in args){
        const argValue = args[argName];
        let value;
        if (directive != null) {
            const arg = directive.args.find((arg)=>arg.name === argName);
            if (arg) {
                value = (0, astFromValue_js_1.astFromValue)(argValue, arg.type);
            }
        }
        if (value == null) {
            value = (0, astFromValueUntyped_js_1.astFromValueUntyped)(argValue);
        }
        if (value != null) {
            directiveArguments.push({
                kind: graphql_1.Kind.ARGUMENT,
                name: {
                    kind: graphql_1.Kind.NAME,
                    value: argName
                },
                value
            });
        }
    }
    return {
        kind: graphql_1.Kind.DIRECTIVE,
        name: {
            kind: graphql_1.Kind.NAME,
            value: name
        },
        arguments: directiveArguments
    };
}
function makeDirectiveNodes(schema, directiveValues) {
    const directiveNodes = [];
    for (const { name, args } of directiveValues){
        const directive = schema?.getDirective(name);
        directiveNodes.push(makeDirectiveNode(name, args, directive));
    }
    return directiveNodes;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/validate-documents.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.validateGraphQlDocuments = validateGraphQlDocuments;
exports.createDefaultRules = createDefaultRules;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function validateGraphQlDocuments(schema, documents, rules = createDefaultRules()) {
    const definitions = new Set();
    const fragmentsDefinitionsMap = new Map();
    for (const document of documents){
        for (const docDefinition of document.definitions){
            if (docDefinition.kind === graphql_1.Kind.FRAGMENT_DEFINITION) {
                fragmentsDefinitionsMap.set(docDefinition.name.value, docDefinition);
            } else {
                definitions.add(docDefinition);
            }
        }
    }
    const fullAST = {
        kind: graphql_1.Kind.DOCUMENT,
        definitions: Array.from([
            ...definitions,
            ...fragmentsDefinitionsMap.values()
        ])
    };
    const errors = (0, graphql_1.validate)(schema, fullAST, rules);
    for (const error of errors){
        error.stack = error.message;
        if (error.locations) {
            for (const location of error.locations){
                error.stack += `\n    at ${error.source?.name}:${location.line}:${location.column}`;
            }
        }
    }
    return errors;
}
function createDefaultRules() {
    let ignored = [
        'NoUnusedFragmentsRule',
        'NoUnusedVariablesRule',
        'KnownDirectivesRule'
    ];
    if (graphql_1.versionInfo.major < 15) {
        ignored = ignored.map((rule)=>rule.replace(/Rule$/, ''));
    }
    return graphql_1.specifiedRules.filter((f)=>!ignored.includes(f.name));
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/parse-graphql-json.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.parseGraphQLJSON = parseGraphQLJSON;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function stripBOM(content) {
    content = content.toString();
    // Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)
    // because the buffer-to-string conversion in `fs.readFileSync()`
    // translates it to FEFF, the UTF-16 BOM.
    if (content.charCodeAt(0) === 0xfeff) {
        content = content.slice(1);
    }
    return content;
}
function parseBOM(content) {
    return JSON.parse(stripBOM(content));
}
function parseGraphQLJSON(location, jsonContent, options) {
    let parsedJson = parseBOM(jsonContent);
    if (parsedJson.data) {
        parsedJson = parsedJson.data;
    }
    if (parsedJson.kind === 'Document') {
        return {
            location,
            document: parsedJson
        };
    } else if (parsedJson.__schema) {
        const schema = (0, graphql_1.buildClientSchema)(parsedJson, options);
        return {
            location,
            schema
        };
    } else if (typeof parsedJson === 'string') {
        return {
            location,
            rawSDL: parsedJson
        };
    }
    throw new Error(`Not valid JSON content`);
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/comments.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.resetComments = resetComments;
exports.collectComment = collectComment;
exports.pushComment = pushComment;
exports.printComment = printComment;
exports.printWithComments = printWithComments;
exports.getDescription = getDescription;
exports.getComment = getComment;
exports.getLeadingCommentBlock = getLeadingCommentBlock;
exports.dedentBlockStringValue = dedentBlockStringValue;
exports.getBlockStringIndentation = getBlockStringIndentation;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const MAX_LINE_LENGTH = 80;
let commentsRegistry = {};
function resetComments() {
    commentsRegistry = {};
}
function collectComment(node) {
    const entityName = node.name?.value;
    if (entityName == null) {
        return;
    }
    pushComment(node, entityName);
    switch(node.kind){
        case 'EnumTypeDefinition':
            if (node.values) {
                for (const value of node.values){
                    pushComment(value, entityName, value.name.value);
                }
            }
            break;
        case 'ObjectTypeDefinition':
        case 'InputObjectTypeDefinition':
        case 'InterfaceTypeDefinition':
            if (node.fields) {
                for (const field of node.fields){
                    pushComment(field, entityName, field.name.value);
                    if (isFieldDefinitionNode(field) && field.arguments) {
                        for (const arg of field.arguments){
                            pushComment(arg, entityName, field.name.value, arg.name.value);
                        }
                    }
                }
            }
            break;
    }
}
function pushComment(node, entity, field, argument) {
    const comment = getComment(node);
    if (typeof comment !== 'string' || comment.length === 0) {
        return;
    }
    const keys = [
        entity
    ];
    if (field) {
        keys.push(field);
        if (argument) {
            keys.push(argument);
        }
    }
    const path = keys.join('.');
    if (!commentsRegistry[path]) {
        commentsRegistry[path] = [];
    }
    commentsRegistry[path].push(comment);
}
function printComment(comment) {
    return '\n# ' + comment.replace(/\n/g, '\n# ');
}
/**
 * Copyright (c) 2015-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ /**
 * NOTE: ==> This file has been modified just to add comments to the printed AST
 * This is a temp measure, we will move to using the original non modified printer.js ASAP.
 */ /**
 * Given maybeArray, print an empty string if it is null or empty, otherwise
 * print all items together separated by separator if provided
 */ function join(maybeArray, separator) {
    return maybeArray ? maybeArray.filter((x)=>x).join(separator || '') : '';
}
function hasMultilineItems(maybeArray) {
    return maybeArray?.some((str)=>str.includes('\n')) ?? false;
}
function addDescription(cb) {
    return (node, _key, _parent, path, ancestors)=>{
        const keys = [];
        const parent = path.reduce((prev, key)=>{
            if ([
                'fields',
                'arguments',
                'values'
            ].includes(key) && prev.name) {
                keys.push(prev.name.value);
            }
            return prev[key];
        }, ancestors[0]);
        const key = [
            ...keys,
            parent?.name?.value
        ].filter(Boolean).join('.');
        const items = [];
        if (node.kind.includes('Definition') && commentsRegistry[key]) {
            items.push(...commentsRegistry[key]);
        }
        return join([
            ...items.map(printComment),
            node.description,
            cb(node, _key, _parent, path, ancestors)
        ], '\n');
    };
}
function indent(maybeString) {
    return maybeString && `  ${maybeString.replace(/\n/g, '\n  ')}`;
}
/**
 * Given array, print each item on its own line, wrapped in an
 * indented "{ }" block.
 */ function block(array) {
    return array && array.length !== 0 ? `{\n${indent(join(array, '\n'))}\n}` : '';
}
/**
 * If maybeString is not null or empty, then wrap with start and end, otherwise
 * print an empty string.
 */ function wrap(start, maybeString, end) {
    return maybeString ? start + maybeString + (end || '') : '';
}
/**
 * Print a block string in the indented block form by adding a leading and
 * trailing blank line. However, if a block string starts with whitespace and is
 * a single-line, adding a leading blank line would strip that whitespace.
 */ function printBlockString(value, isDescription = false) {
    const escaped = value.replace(/\\/g, '\\\\').replace(/"""/g, '\\"""');
    return (value[0] === ' ' || value[0] === '\t') && value.indexOf('\n') === -1 ? `"""${escaped.replace(/"$/, '"\n')}"""` : `"""\n${isDescription ? escaped : indent(escaped)}\n"""`;
}
const printDocASTReducer = {
    Name: {
        leave: (node)=>node.value
    },
    Variable: {
        leave: (node)=>'$' + node.name
    },
    // Document
    Document: {
        leave: (node)=>join(node.definitions, '\n\n')
    },
    OperationDefinition: {
        leave: (node)=>{
            const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');
            const prefix = join([
                node.operation,
                join([
                    node.name,
                    varDefs
                ]),
                join(node.directives, ' ')
            ], ' ');
            // the query short form.
            return prefix + ' ' + node.selectionSet;
        }
    },
    VariableDefinition: {
        leave: ({ variable, type, defaultValue, directives })=>variable + ': ' + type + wrap(' = ', defaultValue) + wrap(' ', join(directives, ' '))
    },
    SelectionSet: {
        leave: ({ selections })=>block(selections)
    },
    Field: {
        leave ({ alias, name, arguments: args, directives, selectionSet }) {
            const prefix = wrap('', alias, ': ') + name;
            let argsLine = prefix + wrap('(', join(args, ', '), ')');
            if (argsLine.length > MAX_LINE_LENGTH) {
                argsLine = prefix + wrap('(\n', indent(join(args, '\n')), '\n)');
            }
            return join([
                argsLine,
                join(directives, ' '),
                selectionSet
            ], ' ');
        }
    },
    Argument: {
        leave: ({ name, value })=>name + ': ' + value
    },
    // Fragments
    FragmentSpread: {
        leave: ({ name, directives })=>'...' + name + wrap(' ', join(directives, ' '))
    },
    InlineFragment: {
        leave: ({ typeCondition, directives, selectionSet })=>join([
                '...',
                wrap('on ', typeCondition),
                join(directives, ' '),
                selectionSet
            ], ' ')
    },
    FragmentDefinition: {
        leave: ({ name, typeCondition, variableDefinitions, directives, selectionSet })=>// Note: fragment variable definitions are experimental and may be changed
            // or removed in the future.
            `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` + `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` + selectionSet
    },
    // Value
    IntValue: {
        leave: ({ value })=>value
    },
    FloatValue: {
        leave: ({ value })=>value
    },
    StringValue: {
        leave: ({ value, block: isBlockString })=>{
            if (isBlockString) {
                return printBlockString(value);
            }
            return JSON.stringify(value);
        }
    },
    BooleanValue: {
        leave: ({ value })=>value ? 'true' : 'false'
    },
    NullValue: {
        leave: ()=>'null'
    },
    EnumValue: {
        leave: ({ value })=>value
    },
    ListValue: {
        leave: ({ values })=>'[' + join(values, ', ') + ']'
    },
    ObjectValue: {
        leave: ({ fields })=>'{' + join(fields, ', ') + '}'
    },
    ObjectField: {
        leave: ({ name, value })=>name + ': ' + value
    },
    // Directive
    Directive: {
        leave: ({ name, arguments: args })=>'@' + name + wrap('(', join(args, ', '), ')')
    },
    // Type
    NamedType: {
        leave: ({ name })=>name
    },
    ListType: {
        leave: ({ type })=>'[' + type + ']'
    },
    NonNullType: {
        leave: ({ type })=>type + '!'
    },
    // Type System Definitions
    SchemaDefinition: {
        leave: ({ directives, operationTypes })=>join([
                'schema',
                join(directives, ' '),
                block(operationTypes)
            ], ' ')
    },
    OperationTypeDefinition: {
        leave: ({ operation, type })=>operation + ': ' + type
    },
    ScalarTypeDefinition: {
        leave: ({ name, directives })=>join([
                'scalar',
                name,
                join(directives, ' ')
            ], ' ')
    },
    ObjectTypeDefinition: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'type',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    FieldDefinition: {
        leave: ({ name, arguments: args, type, directives })=>name + (hasMultilineItems(args) ? wrap('(\n', indent(join(args, '\n')), '\n)') : wrap('(', join(args, ', '), ')')) + ': ' + type + wrap(' ', join(directives, ' '))
    },
    InputValueDefinition: {
        leave: ({ name, type, defaultValue, directives })=>join([
                name + ': ' + type,
                wrap('= ', defaultValue),
                join(directives, ' ')
            ], ' ')
    },
    InterfaceTypeDefinition: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'interface',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    UnionTypeDefinition: {
        leave: ({ name, directives, types })=>join([
                'union',
                name,
                join(directives, ' '),
                wrap('= ', join(types, ' | '))
            ], ' ')
    },
    EnumTypeDefinition: {
        leave: ({ name, directives, values })=>join([
                'enum',
                name,
                join(directives, ' '),
                block(values)
            ], ' ')
    },
    EnumValueDefinition: {
        leave: ({ name, directives })=>join([
                name,
                join(directives, ' ')
            ], ' ')
    },
    InputObjectTypeDefinition: {
        leave: ({ name, directives, fields })=>join([
                'input',
                name,
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    DirectiveDefinition: {
        leave: ({ name, arguments: args, repeatable, locations })=>'directive @' + name + (hasMultilineItems(args) ? wrap('(\n', indent(join(args, '\n')), '\n)') : wrap('(', join(args, ', '), ')')) + (repeatable ? ' repeatable' : '') + ' on ' + join(locations, ' | ')
    },
    SchemaExtension: {
        leave: ({ directives, operationTypes })=>join([
                'extend schema',
                join(directives, ' '),
                block(operationTypes)
            ], ' ')
    },
    ScalarTypeExtension: {
        leave: ({ name, directives })=>join([
                'extend scalar',
                name,
                join(directives, ' ')
            ], ' ')
    },
    ObjectTypeExtension: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'extend type',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    InterfaceTypeExtension: {
        leave: ({ name, interfaces, directives, fields })=>join([
                'extend interface',
                name,
                wrap('implements ', join(interfaces, ' & ')),
                join(directives, ' '),
                block(fields)
            ], ' ')
    },
    UnionTypeExtension: {
        leave: ({ name, directives, types })=>join([
                'extend union',
                name,
                join(directives, ' '),
                wrap('= ', join(types, ' | '))
            ], ' ')
    },
    EnumTypeExtension: {
        leave: ({ name, directives, values })=>join([
                'extend enum',
                name,
                join(directives, ' '),
                block(values)
            ], ' ')
    },
    InputObjectTypeExtension: {
        leave: ({ name, directives, fields })=>join([
                'extend input',
                name,
                join(directives, ' '),
                block(fields)
            ], ' ')
    }
};
const printDocASTReducerWithComments = Object.keys(printDocASTReducer).reduce((prev, key)=>({
        ...prev,
        [key]: {
            leave: addDescription(printDocASTReducer[key].leave)
        }
    }), {});
/**
 * Converts an AST into a string, using one set of reasonable
 * formatting rules.
 */ function printWithComments(ast) {
    return (0, graphql_1.visit)(ast, printDocASTReducerWithComments);
}
function isFieldDefinitionNode(node) {
    return node.kind === 'FieldDefinition';
}
// graphql < v13 and > v15 does not export getDescription
function getDescription(node, options) {
    if (node.description != null) {
        return node.description.value;
    }
    if (options?.commentDescriptions) {
        return getComment(node);
    }
}
function getComment(node) {
    const rawValue = getLeadingCommentBlock(node);
    if (rawValue !== undefined) {
        return dedentBlockStringValue(`\n${rawValue}`);
    }
}
function getLeadingCommentBlock(node) {
    const loc = node.loc;
    if (!loc) {
        return;
    }
    const comments = [];
    let token = loc.startToken.prev;
    while(token != null && token.kind === graphql_1.TokenKind.COMMENT && token.next != null && token.prev != null && token.line + 1 === token.next.line && token.line !== token.prev.line){
        const value = String(token.value);
        comments.push(value);
        token = token.prev;
    }
    return comments.length > 0 ? comments.reverse().join('\n') : undefined;
}
function dedentBlockStringValue(rawString) {
    // Expand a block string's raw value into independent lines.
    const lines = rawString.split(/\r\n|[\n\r]/g);
    // Remove common indentation from all lines but first.
    const commonIndent = getBlockStringIndentation(lines);
    if (commonIndent !== 0) {
        for(let i = 1; i < lines.length; i++){
            lines[i] = lines[i].slice(commonIndent);
        }
    }
    // Remove leading and trailing blank lines.
    while(lines.length > 0 && isBlank(lines[0])){
        lines.shift();
    }
    while(lines.length > 0 && isBlank(lines[lines.length - 1])){
        lines.pop();
    }
    // Return a string of the lines joined with U+000A.
    return lines.join('\n');
}
/**
 * @internal
 */ function getBlockStringIndentation(lines) {
    let commonIndent = null;
    for(let i = 1; i < lines.length; i++){
        const line = lines[i];
        const indent = leadingWhitespace(line);
        if (indent === line.length) {
            continue; // skip empty lines
        }
        if (commonIndent === null || indent < commonIndent) {
            commonIndent = indent;
            if (commonIndent === 0) {
                break;
            }
        }
    }
    return commonIndent === null ? 0 : commonIndent;
}
function leadingWhitespace(str) {
    let i = 0;
    while(i < str.length && (str[i] === ' ' || str[i] === '\t')){
        i++;
    }
    return i;
}
function isBlank(str) {
    return leadingWhitespace(str) === str.length;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/parse-graphql-sdl.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.parseGraphQLSDL = parseGraphQLSDL;
exports.transformCommentsToDescriptions = transformCommentsToDescriptions;
exports.isDescribable = isDescribable;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const comments_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/comments.js [app-route] (ecmascript)");
function parseGraphQLSDL(location, rawSDL, options = {}) {
    let document;
    try {
        if (options.commentDescriptions && rawSDL.includes('#')) {
            document = transformCommentsToDescriptions(rawSDL, options);
            // If noLocation=true, we need to make sure to print and parse it again, to remove locations,
            // since `transformCommentsToDescriptions` must have locations set in order to transform the comments
            // into descriptions.
            if (options.noLocation) {
                document = (0, graphql_1.parse)((0, graphql_1.print)(document), options);
            }
        } else {
            document = (0, graphql_1.parse)(new graphql_1.Source(rawSDL, location), options);
        }
    } catch (e) {
        if (e.message.includes('EOF') && rawSDL.replace(/(\#[^*]*)/g, '').trim() === '') {
            document = {
                kind: graphql_1.Kind.DOCUMENT,
                definitions: []
            };
        } else {
            throw e;
        }
    }
    return {
        location,
        document
    };
}
function transformCommentsToDescriptions(sourceSdl, options = {}) {
    const parsedDoc = (0, graphql_1.parse)(sourceSdl, {
        ...options,
        noLocation: false
    });
    const modifiedDoc = (0, graphql_1.visit)(parsedDoc, {
        leave: (node)=>{
            if (isDescribable(node)) {
                const rawValue = (0, comments_js_1.getLeadingCommentBlock)(node);
                if (rawValue !== undefined) {
                    const commentsBlock = (0, comments_js_1.dedentBlockStringValue)('\n' + rawValue);
                    const isBlock = commentsBlock.includes('\n');
                    if (!node.description) {
                        return {
                            ...node,
                            description: {
                                kind: graphql_1.Kind.STRING,
                                value: commentsBlock,
                                block: isBlock
                            }
                        };
                    } else {
                        return {
                            ...node,
                            description: {
                                ...node.description,
                                value: node.description.value + '\n' + commentsBlock,
                                block: true
                            }
                        };
                    }
                }
            }
        }
    });
    return modifiedDoc;
}
function isDescribable(node) {
    return (0, graphql_1.isTypeSystemDefinitionNode)(node) || node.kind === graphql_1.Kind.FIELD_DEFINITION || node.kind === graphql_1.Kind.INPUT_VALUE_DEFINITION || node.kind === graphql_1.Kind.ENUM_VALUE_DEFINITION;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/build-operation-for-field.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.buildOperationNodeForField = buildOperationNodeForField;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const rootTypes_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rootTypes.js [app-route] (ecmascript)");
let operationVariables = [];
let fieldTypeMap = new Map();
function addOperationVariable(variable) {
    operationVariables.push(variable);
}
function resetOperationVariables() {
    operationVariables = [];
}
function resetFieldMap() {
    fieldTypeMap = new Map();
}
function buildOperationNodeForField({ schema, kind, field, models, ignore = [], depthLimit, circularReferenceDepth, argNames, selectedFields = true }) {
    resetOperationVariables();
    resetFieldMap();
    const rootTypeNames = (0, rootTypes_js_1.getRootTypeNames)(schema);
    const operationNode = buildOperationAndCollectVariables({
        schema,
        fieldName: field,
        kind,
        models: models || [],
        ignore,
        depthLimit: depthLimit || Infinity,
        circularReferenceDepth: circularReferenceDepth || 1,
        argNames,
        selectedFields,
        rootTypeNames
    });
    // attach variables
    operationNode.variableDefinitions = [
        ...operationVariables
    ];
    resetOperationVariables();
    resetFieldMap();
    return operationNode;
}
function buildOperationAndCollectVariables({ schema, fieldName, kind, models, ignore, depthLimit, circularReferenceDepth, argNames, selectedFields, rootTypeNames }) {
    const type = (0, rootTypes_js_1.getDefinedRootType)(schema, kind);
    const field = type.getFields()[fieldName];
    const operationName = `${fieldName}_${kind}`;
    if (field.args) {
        for (const arg of field.args){
            const argName = arg.name;
            if (!argNames || argNames.includes(argName)) {
                addOperationVariable(resolveVariable(arg, argName));
            }
        }
    }
    return {
        kind: graphql_1.Kind.OPERATION_DEFINITION,
        operation: kind,
        name: {
            kind: graphql_1.Kind.NAME,
            value: operationName
        },
        variableDefinitions: [],
        selectionSet: {
            kind: graphql_1.Kind.SELECTION_SET,
            selections: [
                resolveField({
                    type,
                    field,
                    models,
                    firstCall: true,
                    path: [],
                    ancestors: [],
                    ignore,
                    depthLimit,
                    circularReferenceDepth,
                    schema,
                    depth: 0,
                    argNames,
                    selectedFields,
                    rootTypeNames
                })
            ]
        }
    };
}
function resolveSelectionSet({ parent, type, models, firstCall, path, ancestors, ignore, depthLimit, circularReferenceDepth, schema, depth, argNames, selectedFields, rootTypeNames }) {
    if (typeof selectedFields === 'boolean' && depth > depthLimit) {
        return;
    }
    if ((0, graphql_1.isUnionType)(type)) {
        const types = type.getTypes();
        return {
            kind: graphql_1.Kind.SELECTION_SET,
            selections: types.filter((t)=>!hasCircularRef([
                    ...ancestors,
                    t
                ], {
                    depth: circularReferenceDepth
                })).map((t)=>{
                return {
                    kind: graphql_1.Kind.INLINE_FRAGMENT,
                    typeCondition: {
                        kind: graphql_1.Kind.NAMED_TYPE,
                        name: {
                            kind: graphql_1.Kind.NAME,
                            value: t.name
                        }
                    },
                    selectionSet: resolveSelectionSet({
                        parent: type,
                        type: t,
                        models,
                        path,
                        ancestors,
                        ignore,
                        depthLimit,
                        circularReferenceDepth,
                        schema,
                        depth,
                        argNames,
                        selectedFields,
                        rootTypeNames
                    })
                };
            }).filter((fragmentNode)=>fragmentNode?.selectionSet?.selections?.length > 0)
        };
    }
    if ((0, graphql_1.isInterfaceType)(type)) {
        const types = Object.values(schema.getTypeMap()).filter((t)=>(0, graphql_1.isObjectType)(t) && t.getInterfaces().includes(type));
        return {
            kind: graphql_1.Kind.SELECTION_SET,
            selections: types.filter((t)=>!hasCircularRef([
                    ...ancestors,
                    t
                ], {
                    depth: circularReferenceDepth
                })).map((t)=>{
                return {
                    kind: graphql_1.Kind.INLINE_FRAGMENT,
                    typeCondition: {
                        kind: graphql_1.Kind.NAMED_TYPE,
                        name: {
                            kind: graphql_1.Kind.NAME,
                            value: t.name
                        }
                    },
                    selectionSet: resolveSelectionSet({
                        parent: type,
                        type: t,
                        models,
                        path,
                        ancestors,
                        ignore,
                        depthLimit,
                        circularReferenceDepth,
                        schema,
                        depth,
                        argNames,
                        selectedFields,
                        rootTypeNames
                    })
                };
            }).filter((fragmentNode)=>fragmentNode?.selectionSet?.selections?.length > 0)
        };
    }
    if ((0, graphql_1.isObjectType)(type) && !rootTypeNames.has(type.name)) {
        const isIgnored = ignore.includes(type.name) || ignore.includes(`${parent.name}.${path[path.length - 1]}`);
        const isModel = models.includes(type.name);
        if (!firstCall && isModel && !isIgnored) {
            return {
                kind: graphql_1.Kind.SELECTION_SET,
                selections: [
                    {
                        kind: graphql_1.Kind.FIELD,
                        name: {
                            kind: graphql_1.Kind.NAME,
                            value: 'id'
                        }
                    }
                ]
            };
        }
        const fields = type.getFields();
        return {
            kind: graphql_1.Kind.SELECTION_SET,
            selections: Object.keys(fields).filter((fieldName)=>{
                return !hasCircularRef([
                    ...ancestors,
                    (0, graphql_1.getNamedType)(fields[fieldName].type)
                ], {
                    depth: circularReferenceDepth
                });
            }).map((fieldName)=>{
                const selectedSubFields = typeof selectedFields === 'object' ? selectedFields[fieldName] : true;
                if (selectedSubFields) {
                    return resolveField({
                        type,
                        field: fields[fieldName],
                        models,
                        path: [
                            ...path,
                            fieldName
                        ],
                        ancestors,
                        ignore,
                        depthLimit,
                        circularReferenceDepth,
                        schema,
                        depth,
                        argNames,
                        selectedFields: selectedSubFields,
                        rootTypeNames
                    });
                }
                return null;
            }).filter((f)=>{
                if (f == null) {
                    return false;
                } else if ('selectionSet' in f) {
                    return !!f.selectionSet?.selections?.length;
                }
                return true;
            })
        };
    }
}
function resolveVariable(arg, name) {
    function resolveVariableType(type) {
        if ((0, graphql_1.isListType)(type)) {
            return {
                kind: graphql_1.Kind.LIST_TYPE,
                type: resolveVariableType(type.ofType)
            };
        }
        if ((0, graphql_1.isNonNullType)(type)) {
            return {
                kind: graphql_1.Kind.NON_NULL_TYPE,
                // for v16 compatibility
                type: resolveVariableType(type.ofType)
            };
        }
        return {
            kind: graphql_1.Kind.NAMED_TYPE,
            name: {
                kind: graphql_1.Kind.NAME,
                value: type.name
            }
        };
    }
    return {
        kind: graphql_1.Kind.VARIABLE_DEFINITION,
        variable: {
            kind: graphql_1.Kind.VARIABLE,
            name: {
                kind: graphql_1.Kind.NAME,
                value: name || arg.name
            }
        },
        type: resolveVariableType(arg.type)
    };
}
function getArgumentName(name, path) {
    return [
        ...path,
        name
    ].join('_');
}
function resolveField({ type, field, models, firstCall, path, ancestors, ignore, depthLimit, circularReferenceDepth, schema, depth, argNames, selectedFields, rootTypeNames }) {
    const namedType = (0, graphql_1.getNamedType)(field.type);
    let args = [];
    let removeField = false;
    if (field.args && field.args.length) {
        args = field.args.map((arg)=>{
            const argumentName = getArgumentName(arg.name, path);
            if (argNames && !argNames.includes(argumentName)) {
                if ((0, graphql_1.isNonNullType)(arg.type)) {
                    removeField = true;
                }
                return null;
            }
            if (!firstCall) {
                addOperationVariable(resolveVariable(arg, argumentName));
            }
            return {
                kind: graphql_1.Kind.ARGUMENT,
                name: {
                    kind: graphql_1.Kind.NAME,
                    value: arg.name
                },
                value: {
                    kind: graphql_1.Kind.VARIABLE,
                    name: {
                        kind: graphql_1.Kind.NAME,
                        value: getArgumentName(arg.name, path)
                    }
                }
            };
        }).filter(Boolean);
    }
    if (removeField) {
        return null;
    }
    const fieldPath = [
        ...path,
        field.name
    ];
    const fieldPathStr = fieldPath.join('.');
    let fieldName = field.name;
    if (fieldTypeMap.has(fieldPathStr) && fieldTypeMap.get(fieldPathStr) !== field.type.toString()) {
        fieldName += field.type.toString().replace(/!/g, 'NonNull').replace(/\[/g, 'List').replace(/\]/g, '');
    }
    fieldTypeMap.set(fieldPathStr, field.type.toString());
    if (!(0, graphql_1.isScalarType)(namedType) && !(0, graphql_1.isEnumType)(namedType)) {
        return {
            kind: graphql_1.Kind.FIELD,
            name: {
                kind: graphql_1.Kind.NAME,
                value: field.name
            },
            ...fieldName !== field.name && {
                alias: {
                    kind: graphql_1.Kind.NAME,
                    value: fieldName
                }
            },
            selectionSet: resolveSelectionSet({
                parent: type,
                type: namedType,
                models,
                firstCall,
                path: fieldPath,
                ancestors: [
                    ...ancestors,
                    type
                ],
                ignore,
                depthLimit,
                circularReferenceDepth,
                schema,
                depth: depth + 1,
                argNames,
                selectedFields,
                rootTypeNames
            }) || undefined,
            arguments: args
        };
    }
    return {
        kind: graphql_1.Kind.FIELD,
        name: {
            kind: graphql_1.Kind.NAME,
            value: field.name
        },
        ...fieldName !== field.name && {
            alias: {
                kind: graphql_1.Kind.NAME,
                value: fieldName
            }
        },
        arguments: args
    };
}
function hasCircularRef(types, config = {
    depth: 1
}) {
    const type = types[types.length - 1];
    if ((0, graphql_1.isScalarType)(type)) {
        return false;
    }
    const size = types.filter((t)=>t.name === type.name).length;
    return size > config.depth;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/types.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.DirectiveLocation = void 0;
var DirectiveLocation;
(function(DirectiveLocation) {
    /** Request Definitions */ DirectiveLocation["QUERY"] = "QUERY";
    DirectiveLocation["MUTATION"] = "MUTATION";
    DirectiveLocation["SUBSCRIPTION"] = "SUBSCRIPTION";
    DirectiveLocation["FIELD"] = "FIELD";
    DirectiveLocation["FRAGMENT_DEFINITION"] = "FRAGMENT_DEFINITION";
    DirectiveLocation["FRAGMENT_SPREAD"] = "FRAGMENT_SPREAD";
    DirectiveLocation["INLINE_FRAGMENT"] = "INLINE_FRAGMENT";
    DirectiveLocation["VARIABLE_DEFINITION"] = "VARIABLE_DEFINITION";
    /** Type System Definitions */ DirectiveLocation["SCHEMA"] = "SCHEMA";
    DirectiveLocation["SCALAR"] = "SCALAR";
    DirectiveLocation["OBJECT"] = "OBJECT";
    DirectiveLocation["FIELD_DEFINITION"] = "FIELD_DEFINITION";
    DirectiveLocation["ARGUMENT_DEFINITION"] = "ARGUMENT_DEFINITION";
    DirectiveLocation["INTERFACE"] = "INTERFACE";
    DirectiveLocation["UNION"] = "UNION";
    DirectiveLocation["ENUM"] = "ENUM";
    DirectiveLocation["ENUM_VALUE"] = "ENUM_VALUE";
    DirectiveLocation["INPUT_OBJECT"] = "INPUT_OBJECT";
    DirectiveLocation["INPUT_FIELD_DEFINITION"] = "INPUT_FIELD_DEFINITION";
})(DirectiveLocation || (exports.DirectiveLocation = DirectiveLocation = {}));
}),
"[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.MapperKind = void 0;
var MapperKind;
(function(MapperKind) {
    MapperKind["TYPE"] = "MapperKind.TYPE";
    MapperKind["SCALAR_TYPE"] = "MapperKind.SCALAR_TYPE";
    MapperKind["ENUM_TYPE"] = "MapperKind.ENUM_TYPE";
    MapperKind["COMPOSITE_TYPE"] = "MapperKind.COMPOSITE_TYPE";
    MapperKind["OBJECT_TYPE"] = "MapperKind.OBJECT_TYPE";
    MapperKind["INPUT_OBJECT_TYPE"] = "MapperKind.INPUT_OBJECT_TYPE";
    MapperKind["ABSTRACT_TYPE"] = "MapperKind.ABSTRACT_TYPE";
    MapperKind["UNION_TYPE"] = "MapperKind.UNION_TYPE";
    MapperKind["INTERFACE_TYPE"] = "MapperKind.INTERFACE_TYPE";
    MapperKind["ROOT_OBJECT"] = "MapperKind.ROOT_OBJECT";
    MapperKind["QUERY"] = "MapperKind.QUERY";
    MapperKind["MUTATION"] = "MapperKind.MUTATION";
    MapperKind["SUBSCRIPTION"] = "MapperKind.SUBSCRIPTION";
    MapperKind["DIRECTIVE"] = "MapperKind.DIRECTIVE";
    MapperKind["FIELD"] = "MapperKind.FIELD";
    MapperKind["COMPOSITE_FIELD"] = "MapperKind.COMPOSITE_FIELD";
    MapperKind["OBJECT_FIELD"] = "MapperKind.OBJECT_FIELD";
    MapperKind["ROOT_FIELD"] = "MapperKind.ROOT_FIELD";
    MapperKind["QUERY_ROOT_FIELD"] = "MapperKind.QUERY_ROOT_FIELD";
    MapperKind["MUTATION_ROOT_FIELD"] = "MapperKind.MUTATION_ROOT_FIELD";
    MapperKind["SUBSCRIPTION_ROOT_FIELD"] = "MapperKind.SUBSCRIPTION_ROOT_FIELD";
    MapperKind["INTERFACE_FIELD"] = "MapperKind.INTERFACE_FIELD";
    MapperKind["INPUT_OBJECT_FIELD"] = "MapperKind.INPUT_OBJECT_FIELD";
    MapperKind["ARGUMENT"] = "MapperKind.ARGUMENT";
    MapperKind["ENUM_VALUE"] = "MapperKind.ENUM_VALUE";
})(MapperKind || (exports.MapperKind = MapperKind = {}));
}),
"[project]/node_modules/@graphql-tools/utils/cjs/getObjectTypeFromTypeMap.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getObjectTypeFromTypeMap = getObjectTypeFromTypeMap;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function getObjectTypeFromTypeMap(typeMap, type) {
    if (type) {
        const maybeObjectType = typeMap[type.name];
        if ((0, graphql_1.isObjectType)(maybeObjectType)) {
            return maybeObjectType;
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/stub.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createNamedStub = createNamedStub;
exports.createStub = createStub;
exports.isNamedStub = isNamedStub;
exports.getBuiltInForStub = getBuiltInForStub;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function createNamedStub(name, type) {
    let constructor;
    if (type === 'object') {
        constructor = graphql_1.GraphQLObjectType;
    } else if (type === 'interface') {
        constructor = graphql_1.GraphQLInterfaceType;
    } else {
        constructor = graphql_1.GraphQLInputObjectType;
    }
    return new constructor({
        name,
        fields: {
            _fake: {
                type: graphql_1.GraphQLString
            }
        }
    });
}
function createStub(node, type) {
    switch(node.kind){
        case graphql_1.Kind.LIST_TYPE:
            return new graphql_1.GraphQLList(createStub(node.type, type));
        case graphql_1.Kind.NON_NULL_TYPE:
            return new graphql_1.GraphQLNonNull(createStub(node.type, type));
        default:
            if (type === 'output') {
                return createNamedStub(node.name.value, 'object');
            }
            return createNamedStub(node.name.value, 'input');
    }
}
function isNamedStub(type) {
    if ('getFields' in type) {
        const fields = type.getFields();
        // eslint-disable-next-line no-unreachable-loop
        for(const fieldName in fields){
            const field = fields[fieldName];
            return field.name === '_fake';
        }
    }
    return false;
}
function getBuiltInForStub(type) {
    switch(type.name){
        case graphql_1.GraphQLInt.name:
            return graphql_1.GraphQLInt;
        case graphql_1.GraphQLFloat.name:
            return graphql_1.GraphQLFloat;
        case graphql_1.GraphQLString.name:
            return graphql_1.GraphQLString;
        case graphql_1.GraphQLBoolean.name:
            return graphql_1.GraphQLBoolean;
        case graphql_1.GraphQLID.name:
            return graphql_1.GraphQLID;
        default:
            return type;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/rewire.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.rewireTypes = rewireTypes;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const stub_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/stub.js [app-route] (ecmascript)");
function rewireTypes(originalTypeMap, directives) {
    const referenceTypeMap = Object.create(null);
    for(const typeName in originalTypeMap){
        referenceTypeMap[typeName] = originalTypeMap[typeName];
    }
    const newTypeMap = Object.create(null);
    for(const typeName in referenceTypeMap){
        const namedType = referenceTypeMap[typeName];
        if (namedType == null || typeName.startsWith('__')) {
            continue;
        }
        const newName = namedType.name;
        if (newName.startsWith('__')) {
            continue;
        }
        if (newTypeMap[newName] != null) {
            console.warn(`Duplicate schema type name ${newName} found; keeping the existing one found in the schema`);
            continue;
        }
        newTypeMap[newName] = namedType;
    }
    for(const typeName in newTypeMap){
        newTypeMap[typeName] = rewireNamedType(newTypeMap[typeName]);
    }
    const newDirectives = directives.map((directive)=>rewireDirective(directive));
    return {
        typeMap: newTypeMap,
        directives: newDirectives
    };
    //TURBOPACK unreachable
    ;
    function rewireDirective(directive) {
        if ((0, graphql_1.isSpecifiedDirective)(directive)) {
            return directive;
        }
        const directiveConfig = directive.toConfig();
        directiveConfig.args = rewireArgs(directiveConfig.args);
        return new graphql_1.GraphQLDirective(directiveConfig);
    }
    function rewireArgs(args) {
        const rewiredArgs = {};
        for(const argName in args){
            const arg = args[argName];
            const rewiredArgType = rewireType(arg.type);
            if (rewiredArgType != null) {
                arg.type = rewiredArgType;
                rewiredArgs[argName] = arg;
            }
        }
        return rewiredArgs;
    }
    function rewireNamedType(type) {
        if ((0, graphql_1.isObjectType)(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                fields: ()=>rewireFields(config.fields),
                interfaces: ()=>rewireNamedTypes(config.interfaces)
            };
            return new graphql_1.GraphQLObjectType(newConfig);
        } else if ((0, graphql_1.isInterfaceType)(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                fields: ()=>rewireFields(config.fields)
            };
            if ('interfaces' in newConfig) {
                newConfig.interfaces = ()=>rewireNamedTypes(config.interfaces);
            }
            return new graphql_1.GraphQLInterfaceType(newConfig);
        } else if ((0, graphql_1.isUnionType)(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                types: ()=>rewireNamedTypes(config.types)
            };
            return new graphql_1.GraphQLUnionType(newConfig);
        } else if ((0, graphql_1.isInputObjectType)(type)) {
            const config = type.toConfig();
            const newConfig = {
                ...config,
                fields: ()=>rewireInputFields(config.fields)
            };
            return new graphql_1.GraphQLInputObjectType(newConfig);
        } else if ((0, graphql_1.isEnumType)(type)) {
            const enumConfig = type.toConfig();
            return new graphql_1.GraphQLEnumType(enumConfig);
        } else if ((0, graphql_1.isScalarType)(type)) {
            if ((0, graphql_1.isSpecifiedScalarType)(type)) {
                return type;
            }
            const scalarConfig = type.toConfig();
            return new graphql_1.GraphQLScalarType(scalarConfig);
        }
        throw new Error(`Unexpected schema type: ${type}`);
    }
    function rewireFields(fields) {
        const rewiredFields = {};
        for(const fieldName in fields){
            const field = fields[fieldName];
            const rewiredFieldType = rewireType(field.type);
            if (rewiredFieldType != null && field.args) {
                field.type = rewiredFieldType;
                field.args = rewireArgs(field.args);
                rewiredFields[fieldName] = field;
            }
        }
        return rewiredFields;
    }
    function rewireInputFields(fields) {
        const rewiredFields = {};
        for(const fieldName in fields){
            const field = fields[fieldName];
            const rewiredFieldType = rewireType(field.type);
            if (rewiredFieldType != null) {
                field.type = rewiredFieldType;
                rewiredFields[fieldName] = field;
            }
        }
        return rewiredFields;
    }
    function rewireNamedTypes(namedTypes) {
        const rewiredTypes = [];
        for (const namedType of namedTypes){
            const rewiredType = rewireType(namedType);
            if (rewiredType != null) {
                rewiredTypes.push(rewiredType);
            }
        }
        return rewiredTypes;
    }
    function rewireType(type) {
        if ((0, graphql_1.isListType)(type)) {
            const rewiredType = rewireType(type.ofType);
            return rewiredType != null ? new graphql_1.GraphQLList(rewiredType) : null;
        } else if ((0, graphql_1.isNonNullType)(type)) {
            const rewiredType = rewireType(type.ofType);
            return rewiredType != null ? new graphql_1.GraphQLNonNull(rewiredType) : null;
        } else if ((0, graphql_1.isNamedType)(type)) {
            let rewiredType = referenceTypeMap[type.name];
            if (rewiredType === undefined) {
                rewiredType = (0, stub_js_1.isNamedStub)(type) ? (0, stub_js_1.getBuiltInForStub)(type) : rewireNamedType(type);
                newTypeMap[rewiredType.name] = referenceTypeMap[type.name] = rewiredType;
            }
            return rewiredType != null ? newTypeMap[rewiredType.name] : null;
        }
        return null;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/transformInputValue.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.transformInputValue = transformInputValue;
exports.serializeInputValue = serializeInputValue;
exports.parseInputValue = parseInputValue;
exports.parseInputValueLiteral = parseInputValueLiteral;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const helpers_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/helpers.js [app-route] (ecmascript)");
function transformInputValue(type, value, inputLeafValueTransformer = null, inputObjectValueTransformer = null) {
    if (value == null) {
        return value;
    }
    const nullableType = (0, graphql_1.getNullableType)(type);
    if ((0, graphql_1.isLeafType)(nullableType)) {
        return inputLeafValueTransformer != null ? inputLeafValueTransformer(nullableType, value) : value;
    } else if ((0, graphql_1.isListType)(nullableType)) {
        return (0, helpers_js_1.asArray)(value).map((listMember)=>transformInputValue(nullableType.ofType, listMember, inputLeafValueTransformer, inputObjectValueTransformer));
    } else if ((0, graphql_1.isInputObjectType)(nullableType)) {
        const fields = nullableType.getFields();
        const newValue = {};
        for(const key in value){
            const field = fields[key];
            if (field != null) {
                newValue[key] = transformInputValue(field.type, value[key], inputLeafValueTransformer, inputObjectValueTransformer);
            }
        }
        return inputObjectValueTransformer != null ? inputObjectValueTransformer(nullableType, newValue) : newValue;
    }
// unreachable, no other possible return value
}
function serializeInputValue(type, value) {
    return transformInputValue(type, value, (t, v)=>{
        try {
            return t.serialize(v);
        } catch  {
            return v;
        }
    });
}
function parseInputValue(type, value) {
    return transformInputValue(type, value, (t, v)=>{
        try {
            return t.parseValue(v);
        } catch  {
            return v;
        }
    });
}
function parseInputValueLiteral(type, value) {
    return transformInputValue(type, value, (t, v)=>t.parseLiteral(v, {}));
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/mapSchema.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.mapSchema = mapSchema;
exports.correctASTNodes = correctASTNodes;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const getObjectTypeFromTypeMap_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getObjectTypeFromTypeMap.js [app-route] (ecmascript)");
const Interfaces_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)");
const rewire_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rewire.js [app-route] (ecmascript)");
const transformInputValue_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/transformInputValue.js [app-route] (ecmascript)");
function mapSchema(schema, schemaMapper = {}) {
    const newTypeMap = mapArguments(mapFields(mapTypes(mapDefaultValues(mapEnumValues(mapTypes(mapDefaultValues(schema.getTypeMap(), schema, transformInputValue_js_1.serializeInputValue), schema, schemaMapper, (type)=>(0, graphql_1.isLeafType)(type)), schema, schemaMapper), schema, transformInputValue_js_1.parseInputValue), schema, schemaMapper, (type)=>!(0, graphql_1.isLeafType)(type)), schema, schemaMapper), schema, schemaMapper);
    const originalDirectives = schema.getDirectives();
    const newDirectives = mapDirectives(originalDirectives, schema, schemaMapper);
    const { typeMap, directives } = (0, rewire_js_1.rewireTypes)(newTypeMap, newDirectives);
    return new graphql_1.GraphQLSchema({
        ...schema.toConfig(),
        query: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(newTypeMap, schema.getQueryType())),
        mutation: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(newTypeMap, schema.getMutationType())),
        subscription: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(newTypeMap, schema.getSubscriptionType())),
        types: Object.values(typeMap),
        directives
    });
}
function mapTypes(originalTypeMap, schema, schemaMapper, testFn = ()=>true) {
    const newTypeMap = {};
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__')) {
            const originalType = originalTypeMap[typeName];
            if (originalType == null || !testFn(originalType)) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const typeMapper = getTypeMapper(schema, schemaMapper, typeName);
            if (typeMapper == null) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const maybeNewType = typeMapper(originalType, schema);
            if (maybeNewType === undefined) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            newTypeMap[typeName] = maybeNewType;
        }
    }
    return newTypeMap;
}
function mapEnumValues(originalTypeMap, schema, schemaMapper) {
    const enumValueMapper = getEnumValueMapper(schemaMapper);
    if (!enumValueMapper) {
        return originalTypeMap;
    }
    return mapTypes(originalTypeMap, schema, {
        [Interfaces_js_1.MapperKind.ENUM_TYPE]: (type)=>{
            const config = type.toConfig();
            const originalEnumValueConfigMap = config.values;
            const newEnumValueConfigMap = {};
            for(const externalValue in originalEnumValueConfigMap){
                const originalEnumValueConfig = originalEnumValueConfigMap[externalValue];
                const mappedEnumValue = enumValueMapper(originalEnumValueConfig, type.name, schema, externalValue);
                if (mappedEnumValue === undefined) {
                    newEnumValueConfigMap[externalValue] = originalEnumValueConfig;
                } else if (Array.isArray(mappedEnumValue)) {
                    const [newExternalValue, newEnumValueConfig] = mappedEnumValue;
                    newEnumValueConfigMap[newExternalValue] = newEnumValueConfig === undefined ? originalEnumValueConfig : newEnumValueConfig;
                } else if (mappedEnumValue !== null) {
                    newEnumValueConfigMap[externalValue] = mappedEnumValue;
                }
            }
            return correctASTNodes(new graphql_1.GraphQLEnumType({
                ...config,
                values: newEnumValueConfigMap
            }));
        }
    }, (type)=>(0, graphql_1.isEnumType)(type));
}
function mapDefaultValues(originalTypeMap, schema, fn) {
    const newTypeMap = mapArguments(originalTypeMap, schema, {
        [Interfaces_js_1.MapperKind.ARGUMENT]: (argumentConfig)=>{
            if (argumentConfig.defaultValue === undefined) {
                return argumentConfig;
            }
            const maybeNewType = getNewType(originalTypeMap, argumentConfig.type);
            if (maybeNewType != null) {
                return {
                    ...argumentConfig,
                    defaultValue: fn(maybeNewType, argumentConfig.defaultValue)
                };
            }
        }
    });
    return mapFields(newTypeMap, schema, {
        [Interfaces_js_1.MapperKind.INPUT_OBJECT_FIELD]: (inputFieldConfig)=>{
            if (inputFieldConfig.defaultValue === undefined) {
                return inputFieldConfig;
            }
            const maybeNewType = getNewType(newTypeMap, inputFieldConfig.type);
            if (maybeNewType != null) {
                return {
                    ...inputFieldConfig,
                    defaultValue: fn(maybeNewType, inputFieldConfig.defaultValue)
                };
            }
        }
    });
}
function getNewType(newTypeMap, type) {
    if ((0, graphql_1.isListType)(type)) {
        const newType = getNewType(newTypeMap, type.ofType);
        return newType != null ? new graphql_1.GraphQLList(newType) : null;
    } else if ((0, graphql_1.isNonNullType)(type)) {
        const newType = getNewType(newTypeMap, type.ofType);
        return newType != null ? new graphql_1.GraphQLNonNull(newType) : null;
    } else if ((0, graphql_1.isNamedType)(type)) {
        const newType = newTypeMap[type.name];
        return newType != null ? newType : null;
    }
    return null;
}
function mapFields(originalTypeMap, schema, schemaMapper) {
    const newTypeMap = {};
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__')) {
            const originalType = originalTypeMap[typeName];
            if (!(0, graphql_1.isObjectType)(originalType) && !(0, graphql_1.isInterfaceType)(originalType) && !(0, graphql_1.isInputObjectType)(originalType)) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const fieldMapper = getFieldMapper(schema, schemaMapper, typeName);
            if (fieldMapper == null) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const config = originalType.toConfig();
            const originalFieldConfigMap = config.fields;
            const newFieldConfigMap = {};
            for(const fieldName in originalFieldConfigMap){
                const originalFieldConfig = originalFieldConfigMap[fieldName];
                const mappedField = fieldMapper(originalFieldConfig, fieldName, typeName, schema);
                if (mappedField === undefined) {
                    newFieldConfigMap[fieldName] = originalFieldConfig;
                } else if (Array.isArray(mappedField)) {
                    const [newFieldName, newFieldConfig] = mappedField;
                    if (newFieldConfig.astNode != null) {
                        newFieldConfig.astNode = {
                            ...newFieldConfig.astNode,
                            name: {
                                ...newFieldConfig.astNode.name,
                                value: newFieldName
                            }
                        };
                    }
                    newFieldConfigMap[newFieldName] = newFieldConfig === undefined ? originalFieldConfig : newFieldConfig;
                } else if (mappedField !== null) {
                    newFieldConfigMap[fieldName] = mappedField;
                }
            }
            if ((0, graphql_1.isObjectType)(originalType)) {
                newTypeMap[typeName] = correctASTNodes(new graphql_1.GraphQLObjectType({
                    ...config,
                    fields: newFieldConfigMap
                }));
            } else if ((0, graphql_1.isInterfaceType)(originalType)) {
                newTypeMap[typeName] = correctASTNodes(new graphql_1.GraphQLInterfaceType({
                    ...config,
                    fields: newFieldConfigMap
                }));
            } else {
                newTypeMap[typeName] = correctASTNodes(new graphql_1.GraphQLInputObjectType({
                    ...config,
                    fields: newFieldConfigMap
                }));
            }
        }
    }
    return newTypeMap;
}
function mapArguments(originalTypeMap, schema, schemaMapper) {
    const newTypeMap = {};
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__')) {
            const originalType = originalTypeMap[typeName];
            if (!(0, graphql_1.isObjectType)(originalType) && !(0, graphql_1.isInterfaceType)(originalType)) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const argumentMapper = getArgumentMapper(schemaMapper);
            if (argumentMapper == null) {
                newTypeMap[typeName] = originalType;
                continue;
            }
            const config = originalType.toConfig();
            const originalFieldConfigMap = config.fields;
            const newFieldConfigMap = {};
            for(const fieldName in originalFieldConfigMap){
                const originalFieldConfig = originalFieldConfigMap[fieldName];
                const originalArgumentConfigMap = originalFieldConfig.args;
                if (originalArgumentConfigMap == null) {
                    newFieldConfigMap[fieldName] = originalFieldConfig;
                    continue;
                }
                const argumentNames = Object.keys(originalArgumentConfigMap);
                if (!argumentNames.length) {
                    newFieldConfigMap[fieldName] = originalFieldConfig;
                    continue;
                }
                const newArgumentConfigMap = {};
                for (const argumentName of argumentNames){
                    const originalArgumentConfig = originalArgumentConfigMap[argumentName];
                    const mappedArgument = argumentMapper(originalArgumentConfig, fieldName, typeName, schema);
                    if (mappedArgument === undefined) {
                        newArgumentConfigMap[argumentName] = originalArgumentConfig;
                    } else if (Array.isArray(mappedArgument)) {
                        const [newArgumentName, newArgumentConfig] = mappedArgument;
                        newArgumentConfigMap[newArgumentName] = newArgumentConfig;
                    } else if (mappedArgument !== null) {
                        newArgumentConfigMap[argumentName] = mappedArgument;
                    }
                }
                newFieldConfigMap[fieldName] = {
                    ...originalFieldConfig,
                    args: newArgumentConfigMap
                };
            }
            if ((0, graphql_1.isObjectType)(originalType)) {
                newTypeMap[typeName] = new graphql_1.GraphQLObjectType({
                    ...config,
                    fields: newFieldConfigMap
                });
            } else if ((0, graphql_1.isInterfaceType)(originalType)) {
                newTypeMap[typeName] = new graphql_1.GraphQLInterfaceType({
                    ...config,
                    fields: newFieldConfigMap
                });
            } else {
                newTypeMap[typeName] = new graphql_1.GraphQLInputObjectType({
                    ...config,
                    fields: newFieldConfigMap
                });
            }
        }
    }
    return newTypeMap;
}
function mapDirectives(originalDirectives, schema, schemaMapper) {
    const directiveMapper = getDirectiveMapper(schemaMapper);
    if (directiveMapper == null) {
        return originalDirectives.slice();
    }
    const newDirectives = [];
    for (const directive of originalDirectives){
        const mappedDirective = directiveMapper(directive, schema);
        if (mappedDirective === undefined) {
            newDirectives.push(directive);
        } else if (mappedDirective !== null) {
            newDirectives.push(mappedDirective);
        }
    }
    return newDirectives;
}
function getTypeSpecifiers(schema, typeName) {
    const type = schema.getType(typeName);
    const specifiers = [
        Interfaces_js_1.MapperKind.TYPE
    ];
    if ((0, graphql_1.isObjectType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_TYPE, Interfaces_js_1.MapperKind.OBJECT_TYPE);
        if (typeName === schema.getQueryType()?.name) {
            specifiers.push(Interfaces_js_1.MapperKind.ROOT_OBJECT, Interfaces_js_1.MapperKind.QUERY);
        } else if (typeName === schema.getMutationType()?.name) {
            specifiers.push(Interfaces_js_1.MapperKind.ROOT_OBJECT, Interfaces_js_1.MapperKind.MUTATION);
        } else if (typeName === schema.getSubscriptionType()?.name) {
            specifiers.push(Interfaces_js_1.MapperKind.ROOT_OBJECT, Interfaces_js_1.MapperKind.SUBSCRIPTION);
        }
    } else if ((0, graphql_1.isInputObjectType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.INPUT_OBJECT_TYPE);
    } else if ((0, graphql_1.isInterfaceType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_TYPE, Interfaces_js_1.MapperKind.ABSTRACT_TYPE, Interfaces_js_1.MapperKind.INTERFACE_TYPE);
    } else if ((0, graphql_1.isUnionType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_TYPE, Interfaces_js_1.MapperKind.ABSTRACT_TYPE, Interfaces_js_1.MapperKind.UNION_TYPE);
    } else if ((0, graphql_1.isEnumType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.ENUM_TYPE);
    } else if ((0, graphql_1.isScalarType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.SCALAR_TYPE);
    }
    return specifiers;
}
function getTypeMapper(schema, schemaMapper, typeName) {
    const specifiers = getTypeSpecifiers(schema, typeName);
    let typeMapper;
    const stack = [
        ...specifiers
    ];
    while(!typeMapper && stack.length > 0){
        // It is safe to use the ! operator here as we check the length.
        const next = stack.pop();
        typeMapper = schemaMapper[next];
    }
    return typeMapper != null ? typeMapper : null;
}
function getFieldSpecifiers(schema, typeName) {
    const type = schema.getType(typeName);
    const specifiers = [
        Interfaces_js_1.MapperKind.FIELD
    ];
    if ((0, graphql_1.isObjectType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_FIELD, Interfaces_js_1.MapperKind.OBJECT_FIELD);
        if (typeName === schema.getQueryType()?.name) {
            specifiers.push(Interfaces_js_1.MapperKind.ROOT_FIELD, Interfaces_js_1.MapperKind.QUERY_ROOT_FIELD);
        } else if (typeName === schema.getMutationType()?.name) {
            specifiers.push(Interfaces_js_1.MapperKind.ROOT_FIELD, Interfaces_js_1.MapperKind.MUTATION_ROOT_FIELD);
        } else if (typeName === schema.getSubscriptionType()?.name) {
            specifiers.push(Interfaces_js_1.MapperKind.ROOT_FIELD, Interfaces_js_1.MapperKind.SUBSCRIPTION_ROOT_FIELD);
        }
    } else if ((0, graphql_1.isInterfaceType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_FIELD, Interfaces_js_1.MapperKind.INTERFACE_FIELD);
    } else if ((0, graphql_1.isInputObjectType)(type)) {
        specifiers.push(Interfaces_js_1.MapperKind.INPUT_OBJECT_FIELD);
    }
    return specifiers;
}
function getFieldMapper(schema, schemaMapper, typeName) {
    const specifiers = getFieldSpecifiers(schema, typeName);
    let fieldMapper;
    const stack = [
        ...specifiers
    ];
    while(!fieldMapper && stack.length > 0){
        // It is safe to use the ! operator here as we check the length.
        const next = stack.pop();
        // TODO: fix this as unknown cast
        fieldMapper = schemaMapper[next];
    }
    return fieldMapper ?? null;
}
function getArgumentMapper(schemaMapper) {
    const argumentMapper = schemaMapper[Interfaces_js_1.MapperKind.ARGUMENT];
    return argumentMapper != null ? argumentMapper : null;
}
function getDirectiveMapper(schemaMapper) {
    const directiveMapper = schemaMapper[Interfaces_js_1.MapperKind.DIRECTIVE];
    return directiveMapper != null ? directiveMapper : null;
}
function getEnumValueMapper(schemaMapper) {
    const enumValueMapper = schemaMapper[Interfaces_js_1.MapperKind.ENUM_VALUE];
    return enumValueMapper != null ? enumValueMapper : null;
}
function correctASTNodes(type) {
    if ((0, graphql_1.isObjectType)(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const fields = [];
            for(const fieldName in config.fields){
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig.astNode != null) {
                    fields.push(fieldConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                kind: graphql_1.Kind.OBJECT_TYPE_DEFINITION,
                fields
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    kind: graphql_1.Kind.OBJECT_TYPE_EXTENSION,
                    fields: undefined
                }));
        }
        return new graphql_1.GraphQLObjectType(config);
    } else if ((0, graphql_1.isInterfaceType)(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const fields = [];
            for(const fieldName in config.fields){
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig.astNode != null) {
                    fields.push(fieldConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                kind: graphql_1.Kind.INTERFACE_TYPE_DEFINITION,
                fields
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    kind: graphql_1.Kind.INTERFACE_TYPE_EXTENSION,
                    fields: undefined
                }));
        }
        return new graphql_1.GraphQLInterfaceType(config);
    } else if ((0, graphql_1.isInputObjectType)(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const fields = [];
            for(const fieldName in config.fields){
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig.astNode != null) {
                    fields.push(fieldConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                kind: graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION,
                fields
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    kind: graphql_1.Kind.INPUT_OBJECT_TYPE_EXTENSION,
                    fields: undefined
                }));
        }
        return new graphql_1.GraphQLInputObjectType(config);
    } else if ((0, graphql_1.isEnumType)(type)) {
        const config = type.toConfig();
        if (config.astNode != null) {
            const values = [];
            for(const enumKey in config.values){
                const enumValueConfig = config.values[enumKey];
                if (enumValueConfig.astNode != null) {
                    values.push(enumValueConfig.astNode);
                }
            }
            config.astNode = {
                ...config.astNode,
                values
            };
        }
        if (config.extensionASTNodes != null) {
            config.extensionASTNodes = config.extensionASTNodes.map((node)=>({
                    ...node,
                    values: undefined
                }));
        }
        return new graphql_1.GraphQLEnumType(config);
    } else {
        return type;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/filterSchema.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.filterSchema = filterSchema;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const Interfaces_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)");
const mapSchema_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mapSchema.js [app-route] (ecmascript)");
function filterSchema({ schema, typeFilter = ()=>true, fieldFilter = undefined, rootFieldFilter = undefined, objectFieldFilter = undefined, interfaceFieldFilter = undefined, inputObjectFieldFilter = undefined, argumentFilter = undefined, directiveFilter = undefined, enumValueFilter = undefined }) {
    const filteredSchema = (0, mapSchema_js_1.mapSchema)(schema, {
        [Interfaces_js_1.MapperKind.QUERY]: (type)=>filterRootFields(type, 'Query', rootFieldFilter, argumentFilter),
        [Interfaces_js_1.MapperKind.MUTATION]: (type)=>filterRootFields(type, 'Mutation', rootFieldFilter, argumentFilter),
        [Interfaces_js_1.MapperKind.SUBSCRIPTION]: (type)=>filterRootFields(type, 'Subscription', rootFieldFilter, argumentFilter),
        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type)=>typeFilter(type.name, type) ? filterElementFields(graphql_1.GraphQLObjectType, type, objectFieldFilter || fieldFilter, argumentFilter) : null,
        [Interfaces_js_1.MapperKind.INTERFACE_TYPE]: (type)=>typeFilter(type.name, type) ? filterElementFields(graphql_1.GraphQLInterfaceType, type, interfaceFieldFilter || fieldFilter, argumentFilter) : null,
        [Interfaces_js_1.MapperKind.INPUT_OBJECT_TYPE]: (type)=>typeFilter(type.name, type) ? filterElementFields(graphql_1.GraphQLInputObjectType, type, inputObjectFieldFilter || fieldFilter) : null,
        [Interfaces_js_1.MapperKind.UNION_TYPE]: (type)=>typeFilter(type.name, type) ? undefined : null,
        [Interfaces_js_1.MapperKind.ENUM_TYPE]: (type)=>typeFilter(type.name, type) ? undefined : null,
        [Interfaces_js_1.MapperKind.SCALAR_TYPE]: (type)=>typeFilter(type.name, type) ? undefined : null,
        [Interfaces_js_1.MapperKind.DIRECTIVE]: (directive)=>directiveFilter && !directiveFilter(directive.name, directive) ? null : undefined,
        [Interfaces_js_1.MapperKind.ENUM_VALUE]: (valueConfig, typeName, _schema, externalValue)=>enumValueFilter && !enumValueFilter(typeName, externalValue, valueConfig) ? null : undefined
    });
    return filteredSchema;
}
function filterRootFields(type, operation, rootFieldFilter, argumentFilter) {
    if (rootFieldFilter || argumentFilter) {
        const config = type.toConfig();
        for(const fieldName in config.fields){
            const field = config.fields[fieldName];
            if (rootFieldFilter && !rootFieldFilter(operation, fieldName, config.fields[fieldName])) {
                delete config.fields[fieldName];
            } else if (argumentFilter && field.args) {
                for(const argName in field.args){
                    if (!argumentFilter(type.name, fieldName, argName, field.args[argName])) {
                        delete field.args[argName];
                    }
                }
            }
        }
        return new graphql_1.GraphQLObjectType(config);
    }
    return type;
}
function filterElementFields(ElementConstructor, type, fieldFilter, argumentFilter) {
    if (fieldFilter || argumentFilter) {
        const config = type.toConfig();
        for(const fieldName in config.fields){
            const field = config.fields[fieldName];
            if (fieldFilter && !fieldFilter(type.name, fieldName, config.fields[fieldName])) {
                delete config.fields[fieldName];
            } else if (argumentFilter && 'args' in field) {
                for(const argName in field.args){
                    if (!argumentFilter(type.name, fieldName, argName, field.args[argName])) {
                        delete field.args[argName];
                    }
                }
            }
        }
        return new ElementConstructor(config);
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/heal.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.healSchema = healSchema;
exports.healTypes = healTypes;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
// Update any references to named schema types that disagree with the named
// types found in schema.getTypeMap().
//
// healSchema and its callers (visitSchema/visitSchemaDirectives) all modify the schema in place.
// Therefore, private variables (such as the stored implementation map and the proper root types)
// are not updated.
//
// If this causes issues, the schema could be more aggressively healed as follows:
//
// healSchema(schema);
// const config = schema.toConfig()
// const healedSchema = new GraphQLSchema({
//   ...config,
//   query: schema.getType('<desired new root query type name>'),
//   mutation: schema.getType('<desired new root mutation type name>'),
//   subscription: schema.getType('<desired new root subscription type name>'),
// });
//
// One can then also -- if necessary --  assign the correct private variables to the initial schema
// as follows:
// Object.assign(schema, healedSchema);
//
// These steps are not taken automatically to preserve backwards compatibility with graphql-tools v4.
// See https://github.com/ardatan/graphql-tools/issues/1462
//
// They were briefly taken in v5, but can now be phased out as they were only required when other
// areas of the codebase were using healSchema and visitSchema more extensively.
//
function healSchema(schema) {
    healTypes(schema.getTypeMap(), schema.getDirectives());
    return schema;
}
function healTypes(originalTypeMap, directives) {
    const actualNamedTypeMap = Object.create(null);
    // If any of the .name properties of the GraphQLNamedType objects in
    // schema.getTypeMap() have changed, the keys of the type map need to
    // be updated accordingly.
    for(const typeName in originalTypeMap){
        const namedType = originalTypeMap[typeName];
        if (namedType == null || typeName.startsWith('__')) {
            continue;
        }
        const actualName = namedType.name;
        if (actualName.startsWith('__')) {
            continue;
        }
        if (actualNamedTypeMap[actualName] != null) {
            console.warn(`Duplicate schema type name ${actualName} found; keeping the existing one found in the schema`);
            continue;
        }
        actualNamedTypeMap[actualName] = namedType;
    // Note: we are deliberately leaving namedType in the schema by its
    // original name (which might be different from actualName), so that
    // references by that name can be healed.
    }
    // Now add back every named type by its actual name.
    for(const typeName in actualNamedTypeMap){
        const namedType = actualNamedTypeMap[typeName];
        originalTypeMap[typeName] = namedType;
    }
    // Directive declaration argument types can refer to named types.
    for (const decl of directives){
        decl.args = decl.args.filter((arg)=>{
            arg.type = healType(arg.type);
            return arg.type !== null;
        });
    }
    for(const typeName in originalTypeMap){
        const namedType = originalTypeMap[typeName];
        // Heal all named types, except for dangling references, kept only to redirect.
        if (!typeName.startsWith('__') && typeName in actualNamedTypeMap) {
            if (namedType != null) {
                healNamedType(namedType);
            }
        }
    }
    for(const typeName in originalTypeMap){
        if (!typeName.startsWith('__') && !(typeName in actualNamedTypeMap)) {
            delete originalTypeMap[typeName];
        }
    }
    function healNamedType(type) {
        if ((0, graphql_1.isObjectType)(type)) {
            healFields(type);
            healInterfaces(type);
            return;
        } else if ((0, graphql_1.isInterfaceType)(type)) {
            healFields(type);
            if ('getInterfaces' in type) {
                healInterfaces(type);
            }
            return;
        } else if ((0, graphql_1.isUnionType)(type)) {
            healUnderlyingTypes(type);
            return;
        } else if ((0, graphql_1.isInputObjectType)(type)) {
            healInputFields(type);
            return;
        } else if ((0, graphql_1.isLeafType)(type)) {
            return;
        }
        throw new Error(`Unexpected schema type: ${type}`);
    }
    function healFields(type) {
        const fieldMap = type.getFields();
        for (const [key, field] of Object.entries(fieldMap)){
            field.args.map((arg)=>{
                arg.type = healType(arg.type);
                return arg.type === null ? null : arg;
            }).filter(Boolean);
            field.type = healType(field.type);
            if (field.type === null) {
                delete fieldMap[key];
            }
        }
    }
    function healInterfaces(type) {
        if ('getInterfaces' in type) {
            const interfaces = type.getInterfaces();
            interfaces.push(...interfaces.splice(0).map((iface)=>healType(iface)).filter(Boolean));
        }
    }
    function healInputFields(type) {
        const fieldMap = type.getFields();
        for (const [key, field] of Object.entries(fieldMap)){
            field.type = healType(field.type);
            if (field.type === null) {
                delete fieldMap[key];
            }
        }
    }
    function healUnderlyingTypes(type) {
        const types = type.getTypes();
        types.push(...types.splice(0).map((t)=>healType(t)).filter(Boolean));
    }
    function healType(type) {
        // Unwrap the two known wrapper types
        if ((0, graphql_1.isListType)(type)) {
            const healedType = healType(type.ofType);
            return healedType != null ? new graphql_1.GraphQLList(healedType) : null;
        } else if ((0, graphql_1.isNonNullType)(type)) {
            const healedType = healType(type.ofType);
            return healedType != null ? new graphql_1.GraphQLNonNull(healedType) : null;
        } else if ((0, graphql_1.isNamedType)(type)) {
            // If a type annotation on a field or an argument or a union member is
            // any `GraphQLNamedType` with a `name`, then it must end up identical
            // to `schema.getType(name)`, since `schema.getTypeMap()` is the source
            // of truth for all named schema types.
            // Note that new types can still be simply added by adding a field, as
            // the official type will be undefined, not null.
            const officialType = originalTypeMap[type.name];
            if (officialType && type !== officialType) {
                return officialType;
            }
        }
        return type;
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/getResolversFromSchema.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getResolversFromSchema = getResolversFromSchema;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function getResolversFromSchema(schema, // Include default merged resolvers
includeDefaultMergedResolver) {
    const resolvers = Object.create(null);
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        if (!typeName.startsWith('__')) {
            const type = typeMap[typeName];
            if ((0, graphql_1.isScalarType)(type)) {
                if (!(0, graphql_1.isSpecifiedScalarType)(type)) {
                    const config = type.toConfig();
                    delete config.astNode; // avoid AST duplication elsewhere
                    resolvers[typeName] = new graphql_1.GraphQLScalarType(config);
                }
            } else if ((0, graphql_1.isEnumType)(type)) {
                resolvers[typeName] = {};
                const values = type.getValues();
                for (const value of values){
                    resolvers[typeName][value.name] = value.value;
                }
            } else if ((0, graphql_1.isInterfaceType)(type)) {
                if (type.resolveType != null) {
                    resolvers[typeName] = {
                        __resolveType: type.resolveType
                    };
                }
            } else if ((0, graphql_1.isUnionType)(type)) {
                if (type.resolveType != null) {
                    resolvers[typeName] = {
                        __resolveType: type.resolveType
                    };
                }
            } else if ((0, graphql_1.isObjectType)(type)) {
                resolvers[typeName] = {};
                if (type.isTypeOf != null) {
                    resolvers[typeName].__isTypeOf = type.isTypeOf;
                }
                const fields = type.getFields();
                for(const fieldName in fields){
                    const field = fields[fieldName];
                    if (field.subscribe != null) {
                        resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};
                        resolvers[typeName][fieldName].subscribe = field.subscribe;
                    }
                    if (field.resolve != null && field.resolve?.name !== 'defaultFieldResolver') {
                        switch(field.resolve?.name){
                            case 'defaultMergedResolver':
                                if (!includeDefaultMergedResolver) {
                                    continue;
                                }
                                break;
                            case 'defaultFieldResolver':
                                continue;
                        }
                        resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};
                        resolvers[typeName][fieldName].resolve = field.resolve;
                    }
                }
            }
        }
    }
    return resolvers;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/forEachField.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.forEachField = forEachField;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function forEachField(schema, fn) {
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        const type = typeMap[typeName];
        // TODO: maybe have an option to include these?
        if (!(0, graphql_1.getNamedType)(type).name.startsWith('__') && (0, graphql_1.isObjectType)(type)) {
            const fields = type.getFields();
            for(const fieldName in fields){
                const field = fields[fieldName];
                fn(field, typeName, fieldName);
            }
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/forEachDefaultValue.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.forEachDefaultValue = forEachDefaultValue;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function forEachDefaultValue(schema, fn) {
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        const type = typeMap[typeName];
        if (!(0, graphql_1.getNamedType)(type).name.startsWith('__')) {
            if ((0, graphql_1.isObjectType)(type)) {
                const fields = type.getFields();
                for(const fieldName in fields){
                    const field = fields[fieldName];
                    for (const arg of field.args){
                        arg.defaultValue = fn(arg.type, arg.defaultValue);
                    }
                }
            } else if ((0, graphql_1.isInputObjectType)(type)) {
                const fields = type.getFields();
                for(const fieldName in fields){
                    const field = fields[fieldName];
                    field.defaultValue = fn(field.type, field.defaultValue);
                }
            }
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/addTypes.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

// addTypes uses toConfig to create a new schema with a new or replaced
// type or directive. Rewiring is employed so that the replaced type can be
// reconnected with the existing types.
//
// Rewiring is employed even for new types or directives as a convenience, so
// that type references within the new type or directive do not have to be to
// the identical objects within the original schema.
//
// In fact, the type references could even be stub types with entirely different
// fields, as long as the type references share the same name as the desired
// type within the original schema's type map.
//
// This makes it easy to perform simple schema operations (e.g. adding a new
// type with a fiew fields removed from an existing type) that could normally be
// performed by using toConfig directly, but is blocked if any intervening
// more advanced schema operations have caused the types to be recreated via
// rewiring.
//
// Type recreation happens, for example, with every use of mapSchema, as the
// types are always rewired. If fields are selected and removed using
// mapSchema, adding those fields to a new type can no longer be simply done
// by toConfig, as the types are not the identical JavaScript objects, and
// schema creation will fail with errors referencing multiple types with the
// same names.
//
// enhanceSchema can fill this gap by adding an additional round of rewiring.
//
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.addTypes = addTypes;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const getObjectTypeFromTypeMap_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getObjectTypeFromTypeMap.js [app-route] (ecmascript)");
const rewire_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rewire.js [app-route] (ecmascript)");
function addTypes(schema, newTypesOrDirectives) {
    const config = schema.toConfig();
    const originalTypeMap = {};
    for (const type of config.types){
        originalTypeMap[type.name] = type;
    }
    const originalDirectiveMap = {};
    for (const directive of config.directives){
        originalDirectiveMap[directive.name] = directive;
    }
    for (const newTypeOrDirective of newTypesOrDirectives){
        if ((0, graphql_1.isNamedType)(newTypeOrDirective)) {
            originalTypeMap[newTypeOrDirective.name] = newTypeOrDirective;
        } else if ((0, graphql_1.isDirective)(newTypeOrDirective)) {
            originalDirectiveMap[newTypeOrDirective.name] = newTypeOrDirective;
        }
    }
    const { typeMap, directives } = (0, rewire_js_1.rewireTypes)(originalTypeMap, Object.values(originalDirectiveMap));
    return new graphql_1.GraphQLSchema({
        ...config,
        query: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, schema.getQueryType()),
        mutation: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, schema.getMutationType()),
        subscription: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, schema.getSubscriptionType()),
        types: Object.values(typeMap),
        directives
    });
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/prune.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.pruneSchema = pruneSchema;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const get_implementing_types_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-implementing-types.js [app-route] (ecmascript)");
const Interfaces_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)");
const mapSchema_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mapSchema.js [app-route] (ecmascript)");
const rootTypes_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rootTypes.js [app-route] (ecmascript)");
/**
 * Prunes the provided schema, removing unused and empty types
 * @param schema The schema to prune
 * @param options Additional options for removing unused types from the schema
 */ function pruneSchema(schema, options = {}) {
    const { skipEmptyCompositeTypePruning, skipEmptyUnionPruning, skipPruning, skipUnimplementedInterfacesPruning, skipUnusedTypesPruning } = options;
    let prunedTypes = []; // Pruned types during mapping
    let prunedSchema = schema;
    do {
        let visited = visitSchema(prunedSchema);
        // Custom pruning  was defined, so we need to pre-emptively revisit the schema accounting for this
        if (skipPruning) {
            const revisit = [];
            for(const typeName in prunedSchema.getTypeMap()){
                if (typeName.startsWith('__')) {
                    continue;
                }
                const type = prunedSchema.getType(typeName);
                // if we want to skip pruning for this type, add it to the list of types to revisit
                if (type && skipPruning(type)) {
                    revisit.push(typeName);
                }
            }
            visited = visitQueue(revisit, prunedSchema, visited); // visit again
        }
        prunedTypes = [];
        prunedSchema = (0, mapSchema_js_1.mapSchema)(prunedSchema, {
            [Interfaces_js_1.MapperKind.TYPE]: (type)=>{
                if (!visited.has(type.name) && !(0, graphql_1.isSpecifiedScalarType)(type)) {
                    if ((0, graphql_1.isUnionType)(type) || (0, graphql_1.isInputObjectType)(type) || (0, graphql_1.isInterfaceType)(type) || (0, graphql_1.isObjectType)(type) || (0, graphql_1.isScalarType)(type)) {
                        // skipUnusedTypesPruning: skip pruning unused types
                        if (skipUnusedTypesPruning) {
                            return type;
                        }
                        // skipEmptyUnionPruning: skip pruning empty unions
                        if ((0, graphql_1.isUnionType)(type) && skipEmptyUnionPruning && !Object.keys(type.getTypes()).length) {
                            return type;
                        }
                        if ((0, graphql_1.isInputObjectType)(type) || (0, graphql_1.isInterfaceType)(type) || (0, graphql_1.isObjectType)(type)) {
                            // skipEmptyCompositeTypePruning: skip pruning object types or interfaces with no fields
                            if (skipEmptyCompositeTypePruning && !Object.keys(type.getFields()).length) {
                                return type;
                            }
                        }
                        // skipUnimplementedInterfacesPruning: skip pruning interfaces that are not implemented by any other types
                        if ((0, graphql_1.isInterfaceType)(type) && skipUnimplementedInterfacesPruning) {
                            return type;
                        }
                    }
                    prunedTypes.push(type.name);
                    visited.delete(type.name);
                    return null;
                }
                return type;
            }
        });
    }while (prunedTypes.length) // Might have empty types and need to prune again
    return prunedSchema;
}
function visitSchema(schema) {
    const queue = []; // queue of nodes to visit
    // Grab the root types and start there
    for (const type of (0, rootTypes_js_1.getRootTypes)(schema)){
        queue.push(type.name);
    }
    return visitQueue(queue, schema);
}
function visitQueue(queue, schema, visited = new Set()) {
    // Interfaces encountered that are field return types need to be revisited to add their implementations
    const revisit = new Map();
    // Navigate all types starting with pre-queued types (root types)
    while(queue.length){
        const typeName = queue.pop();
        // Skip types we already visited unless it is an interface type that needs revisiting
        if (visited.has(typeName) && revisit[typeName] !== true) {
            continue;
        }
        const type = schema.getType(typeName);
        if (type) {
            // Get types for union
            if ((0, graphql_1.isUnionType)(type)) {
                queue.push(...type.getTypes().map((type)=>type.name));
            }
            // If it is an interface and it is a returned type, grab all implementations so we can use proper __typename in fragments
            if ((0, graphql_1.isInterfaceType)(type) && revisit[typeName] === true) {
                queue.push(...(0, get_implementing_types_js_1.getImplementingTypes)(type.name, schema));
                // No need to revisit this interface again
                revisit[typeName] = false;
            }
            if ((0, graphql_1.isEnumType)(type)) {
                // Visit enum values directives argument types
                queue.push(...type.getValues().flatMap((value)=>getDirectivesArgumentsTypeNames(schema, value)));
            }
            // Visit interfaces this type is implementing if they haven't been visited yet
            if ('getInterfaces' in type) {
                // Only pushes to queue to visit but not return types
                queue.push(...type.getInterfaces().map((iface)=>iface.name));
            }
            // If the type has fields visit those field types
            if ('getFields' in type) {
                const fields = type.getFields();
                const entries = Object.entries(fields);
                if (!entries.length) {
                    continue;
                }
                for (const [, field] of entries){
                    if ((0, graphql_1.isObjectType)(type)) {
                        // Visit arg types and arg directives arguments types
                        queue.push(...field.args.flatMap((arg)=>{
                            const typeNames = [
                                (0, graphql_1.getNamedType)(arg.type).name
                            ];
                            typeNames.push(...getDirectivesArgumentsTypeNames(schema, arg));
                            return typeNames;
                        }));
                    }
                    const namedType = (0, graphql_1.getNamedType)(field.type);
                    queue.push(namedType.name);
                    queue.push(...getDirectivesArgumentsTypeNames(schema, field));
                    // Interfaces returned on fields need to be revisited to add their implementations
                    if ((0, graphql_1.isInterfaceType)(namedType) && !(namedType.name in revisit)) {
                        revisit[namedType.name] = true;
                    }
                }
            }
            queue.push(...getDirectivesArgumentsTypeNames(schema, type));
            visited.add(typeName); // Mark as visited (and therefore it is used and should be kept)
        }
    }
    return visited;
}
function getDirectivesArgumentsTypeNames(schema, directableObj) {
    const argTypeNames = new Set();
    if (directableObj.astNode?.directives) {
        for (const directiveNode of directableObj.astNode.directives){
            const directive = schema.getDirective(directiveNode.name.value);
            if (directive?.args) {
                for (const arg of directive.args){
                    const argType = (0, graphql_1.getNamedType)(arg.type);
                    argTypeNames.add(argType.name);
                }
            }
        }
    }
    if (directableObj.extensions?.['directives']) {
        for(const directiveName in directableObj.extensions['directives']){
            const directive = schema.getDirective(directiveName);
            if (directive?.args) {
                for (const arg of directive.args){
                    const argType = (0, graphql_1.getNamedType)(arg.type);
                    argTypeNames.add(argType.name);
                }
            }
        }
    }
    return [
        ...argTypeNames
    ];
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/mergeDeep.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.mergeDeep = mergeDeep;
const helpers_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/helpers.js [app-route] (ecmascript)");
function mergeDeep(sources, respectPrototype = false, respectArrays = false, respectArrayLength = false) {
    let expectedLength;
    let allArrays = true;
    const areArraysInTheSameLength = sources.every((source)=>{
        if (Array.isArray(source)) {
            if (expectedLength === undefined) {
                expectedLength = source.length;
                return true;
            } else if (expectedLength === source.length) {
                return true;
            }
        } else {
            allArrays = false;
        }
        return false;
    });
    if (respectArrayLength && areArraysInTheSameLength) {
        return new Array(expectedLength).fill(null).map((_, index)=>mergeDeep(sources.map((source)=>source[index]), respectPrototype, respectArrays, respectArrayLength));
    }
    if (allArrays) {
        return sources.flat(1);
    }
    let output;
    let firstObjectSource;
    if (respectPrototype) {
        firstObjectSource = sources.find((source)=>isObject(source));
        if (output == null) {
            output = {};
        }
        if (firstObjectSource) {
            Object.setPrototypeOf(output, Object.create(Object.getPrototypeOf(firstObjectSource)));
        }
    }
    for (const source of sources){
        if (isObject(source)) {
            if (firstObjectSource) {
                const outputPrototype = Object.getPrototypeOf(output);
                const sourcePrototype = Object.getPrototypeOf(source);
                if (sourcePrototype) {
                    for (const key of Object.getOwnPropertyNames(sourcePrototype)){
                        const descriptor = Object.getOwnPropertyDescriptor(sourcePrototype, key);
                        if ((0, helpers_js_1.isSome)(descriptor)) {
                            Object.defineProperty(outputPrototype, key, descriptor);
                        }
                    }
                }
            }
            for(const key in source){
                if (output == null) {
                    output = {};
                }
                if (key in output) {
                    output[key] = mergeDeep([
                        output[key],
                        source[key]
                    ], respectPrototype, respectArrays, respectArrayLength);
                } else {
                    output[key] = source[key];
                }
            }
        } else if (Array.isArray(source)) {
            if (!Array.isArray(output)) {
                output = source;
            } else {
                output = mergeDeep([
                    output,
                    source
                ], respectPrototype, respectArrays, respectArrayLength);
            }
        } else {
            output = source;
        }
    }
    return output;
}
function isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/selectionSets.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.parseSelectionSet = parseSelectionSet;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function parseSelectionSet(selectionSet, options) {
    const query = (0, graphql_1.parse)(selectionSet, options).definitions[0];
    return query.selectionSet;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/getResponseKeyFromInfo.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getResponseKeyFromInfo = getResponseKeyFromInfo;
/**
 * Get the key under which the result of this resolver will be placed in the response JSON. Basically, just
 * resolves aliases.
 * @param info The info argument to the resolver.
 */ function getResponseKeyFromInfo(info) {
    return info.fieldNodes[0].alias != null ? info.fieldNodes[0].alias.value : info.fieldName;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/fields.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.appendObjectFields = appendObjectFields;
exports.removeObjectFields = removeObjectFields;
exports.selectObjectFields = selectObjectFields;
exports.modifyObjectFields = modifyObjectFields;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const addTypes_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/addTypes.js [app-route] (ecmascript)");
const Interfaces_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)");
const mapSchema_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mapSchema.js [app-route] (ecmascript)");
function appendObjectFields(schema, typeName, additionalFields) {
    if (schema.getType(typeName) == null) {
        return (0, addTypes_js_1.addTypes)(schema, [
            new graphql_1.GraphQLObjectType({
                name: typeName,
                fields: additionalFields
            })
        ]);
    }
    return (0, mapSchema_js_1.mapSchema)(schema, {
        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type)=>{
            if (type.name === typeName) {
                const config = type.toConfig();
                const originalFieldConfigMap = config.fields;
                const newFieldConfigMap = {};
                for(const fieldName in originalFieldConfigMap){
                    newFieldConfigMap[fieldName] = originalFieldConfigMap[fieldName];
                }
                for(const fieldName in additionalFields){
                    newFieldConfigMap[fieldName] = additionalFields[fieldName];
                }
                return (0, mapSchema_js_1.correctASTNodes)(new graphql_1.GraphQLObjectType({
                    ...config,
                    fields: newFieldConfigMap
                }));
            }
        }
    });
}
function removeObjectFields(schema, typeName, testFn) {
    const removedFields = {};
    const newSchema = (0, mapSchema_js_1.mapSchema)(schema, {
        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type)=>{
            if (type.name === typeName) {
                const config = type.toConfig();
                const originalFieldConfigMap = config.fields;
                const newFieldConfigMap = {};
                for(const fieldName in originalFieldConfigMap){
                    const originalFieldConfig = originalFieldConfigMap[fieldName];
                    if (testFn(fieldName, originalFieldConfig)) {
                        removedFields[fieldName] = originalFieldConfig;
                    } else {
                        newFieldConfigMap[fieldName] = originalFieldConfig;
                    }
                }
                return (0, mapSchema_js_1.correctASTNodes)(new graphql_1.GraphQLObjectType({
                    ...config,
                    fields: newFieldConfigMap
                }));
            }
        }
    });
    return [
        newSchema,
        removedFields
    ];
}
function selectObjectFields(schema, typeName, testFn) {
    const selectedFields = {};
    (0, mapSchema_js_1.mapSchema)(schema, {
        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type)=>{
            if (type.name === typeName) {
                const config = type.toConfig();
                const originalFieldConfigMap = config.fields;
                for(const fieldName in originalFieldConfigMap){
                    const originalFieldConfig = originalFieldConfigMap[fieldName];
                    if (testFn(fieldName, originalFieldConfig)) {
                        selectedFields[fieldName] = originalFieldConfig;
                    }
                }
            }
            return undefined;
        }
    });
    return selectedFields;
}
function modifyObjectFields(schema, typeName, testFn, newFields) {
    const removedFields = {};
    const newSchema = (0, mapSchema_js_1.mapSchema)(schema, {
        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type)=>{
            if (type.name === typeName) {
                const config = type.toConfig();
                const originalFieldConfigMap = config.fields;
                const newFieldConfigMap = {};
                for(const fieldName in originalFieldConfigMap){
                    const originalFieldConfig = originalFieldConfigMap[fieldName];
                    if (testFn(fieldName, originalFieldConfig)) {
                        removedFields[fieldName] = originalFieldConfig;
                    } else {
                        newFieldConfigMap[fieldName] = originalFieldConfig;
                    }
                }
                for(const fieldName in newFields){
                    const fieldConfig = newFields[fieldName];
                    newFieldConfigMap[fieldName] = fieldConfig;
                }
                return (0, mapSchema_js_1.correctASTNodes)(new graphql_1.GraphQLObjectType({
                    ...config,
                    fields: newFieldConfigMap
                }));
            }
        }
    });
    return [
        newSchema,
        removedFields
    ];
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/renameType.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.renameType = renameType;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function renameType(type, newTypeName) {
    if ((0, graphql_1.isObjectType)(type)) {
        return new graphql_1.GraphQLObjectType({
            ...type.toConfig(),
            name: newTypeName,
            astNode: type.astNode == null ? type.astNode : {
                ...type.astNode,
                name: {
                    ...type.astNode.name,
                    value: newTypeName
                }
            },
            extensionASTNodes: type.extensionASTNodes == null ? type.extensionASTNodes : type.extensionASTNodes.map((node)=>({
                    ...node,
                    name: {
                        ...node.name,
                        value: newTypeName
                    }
                }))
        });
    } else if ((0, graphql_1.isInterfaceType)(type)) {
        return new graphql_1.GraphQLInterfaceType({
            ...type.toConfig(),
            name: newTypeName,
            astNode: type.astNode == null ? type.astNode : {
                ...type.astNode,
                name: {
                    ...type.astNode.name,
                    value: newTypeName
                }
            },
            extensionASTNodes: type.extensionASTNodes == null ? type.extensionASTNodes : type.extensionASTNodes.map((node)=>({
                    ...node,
                    name: {
                        ...node.name,
                        value: newTypeName
                    }
                }))
        });
    } else if ((0, graphql_1.isUnionType)(type)) {
        return new graphql_1.GraphQLUnionType({
            ...type.toConfig(),
            name: newTypeName,
            astNode: type.astNode == null ? type.astNode : {
                ...type.astNode,
                name: {
                    ...type.astNode.name,
                    value: newTypeName
                }
            },
            extensionASTNodes: type.extensionASTNodes == null ? type.extensionASTNodes : type.extensionASTNodes.map((node)=>({
                    ...node,
                    name: {
                        ...node.name,
                        value: newTypeName
                    }
                }))
        });
    } else if ((0, graphql_1.isInputObjectType)(type)) {
        return new graphql_1.GraphQLInputObjectType({
            ...type.toConfig(),
            name: newTypeName,
            astNode: type.astNode == null ? type.astNode : {
                ...type.astNode,
                name: {
                    ...type.astNode.name,
                    value: newTypeName
                }
            },
            extensionASTNodes: type.extensionASTNodes == null ? type.extensionASTNodes : type.extensionASTNodes.map((node)=>({
                    ...node,
                    name: {
                        ...node.name,
                        value: newTypeName
                    }
                }))
        });
    } else if ((0, graphql_1.isEnumType)(type)) {
        return new graphql_1.GraphQLEnumType({
            ...type.toConfig(),
            name: newTypeName,
            astNode: type.astNode == null ? type.astNode : {
                ...type.astNode,
                name: {
                    ...type.astNode.name,
                    value: newTypeName
                }
            },
            extensionASTNodes: type.extensionASTNodes == null ? type.extensionASTNodes : type.extensionASTNodes.map((node)=>({
                    ...node,
                    name: {
                        ...node.name,
                        value: newTypeName
                    }
                }))
        });
    } else if ((0, graphql_1.isScalarType)(type)) {
        return new graphql_1.GraphQLScalarType({
            ...type.toConfig(),
            name: newTypeName,
            astNode: type.astNode == null ? type.astNode : {
                ...type.astNode,
                name: {
                    ...type.astNode.name,
                    value: newTypeName
                }
            },
            extensionASTNodes: type.extensionASTNodes == null ? type.extensionASTNodes : type.extensionASTNodes.map((node)=>({
                    ...node,
                    name: {
                        ...node.name,
                        value: newTypeName
                    }
                }))
        });
    }
    throw new Error(`Unknown type ${type}.`);
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/updateArgument.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.updateArgument = updateArgument;
exports.createVariableNameGenerator = createVariableNameGenerator;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const astFromType_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromType.js [app-route] (ecmascript)");
function updateArgument(argumentNodes, variableDefinitionsMap, variableValues, argName, varName, type, value) {
    argumentNodes[argName] = {
        kind: graphql_1.Kind.ARGUMENT,
        name: {
            kind: graphql_1.Kind.NAME,
            value: argName
        },
        value: {
            kind: graphql_1.Kind.VARIABLE,
            name: {
                kind: graphql_1.Kind.NAME,
                value: varName
            }
        }
    };
    variableDefinitionsMap[varName] = {
        kind: graphql_1.Kind.VARIABLE_DEFINITION,
        variable: {
            kind: graphql_1.Kind.VARIABLE,
            name: {
                kind: graphql_1.Kind.NAME,
                value: varName
            }
        },
        type: (0, astFromType_js_1.astFromType)(type)
    };
    if (value !== undefined) {
        variableValues[varName] = value;
        return;
    }
    // including the variable in the map with value of `undefined`
    // will actually be translated by graphql-js into `null`
    // see https://github.com/graphql/graphql-js/issues/2533
    if (varName in variableValues) {
        delete variableValues[varName];
    }
}
function createVariableNameGenerator(variableDefinitionMap) {
    let varCounter = 0;
    return (argName)=>{
        let varName;
        do {
            varName = varCounter === 0 ? argName : `_v${varCounter.toString()}_${argName}`;
            varCounter++;
        }while (varName in variableDefinitionMap)
        return varName;
    };
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/implementsAbstractType.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.implementsAbstractType = implementsAbstractType;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function implementsAbstractType(schema, typeA, typeB) {
    if (typeB == null || typeA == null) {
        return false;
    } else if (typeA === typeB) {
        return true;
    } else if ((0, graphql_1.isCompositeType)(typeA) && (0, graphql_1.isCompositeType)(typeB)) {
        return (0, graphql_1.doTypesOverlap)(schema, typeA, typeB);
    }
    return false;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/observableToAsyncIterable.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.observableToAsyncIterable = observableToAsyncIterable;
const promise_helpers_1 = __turbopack_context__.r("[project]/node_modules/@whatwg-node/promise-helpers/cjs/index.js [app-route] (ecmascript)");
function observableToAsyncIterable(observable) {
    const pullQueue = [];
    const pushQueue = [];
    let listening = true;
    const pushValue = (value)=>{
        if (pullQueue.length !== 0) {
            // It is safe to use the ! operator here as we check the length.
            pullQueue.shift()({
                value,
                done: false
            });
        } else {
            pushQueue.push({
                value,
                done: false
            });
        }
    };
    const pushError = (error)=>{
        if (pullQueue.length !== 0) {
            // It is safe to use the ! operator here as we check the length.
            pullQueue.shift()({
                value: {
                    errors: [
                        error
                    ]
                },
                done: false
            });
        } else {
            pushQueue.push({
                value: {
                    errors: [
                        error
                    ]
                },
                done: false
            });
        }
    };
    const pushDone = ()=>{
        if (pullQueue.length !== 0) {
            // It is safe to use the ! operator here as we check the length.
            pullQueue.shift()({
                done: true
            });
        } else {
            pushQueue.push({
                done: true
            });
        }
    };
    const pullValue = ()=>new Promise((resolve)=>{
            if (pushQueue.length !== 0) {
                const element = pushQueue.shift();
                // either {value: {errors: [...]}} or {value: ...}
                resolve(element);
            } else {
                pullQueue.push(resolve);
            }
        });
    const subscription = observable.subscribe({
        next (value) {
            return pushValue(value);
        },
        error (err) {
            return pushError(err);
        },
        complete () {
            return pushDone();
        }
    });
    const emptyQueue = ()=>{
        if (listening) {
            listening = false;
            subscription.unsubscribe();
            for (const resolve of pullQueue){
                resolve({
                    value: undefined,
                    done: true
                });
            }
            pullQueue.length = 0;
            pushQueue.length = 0;
        }
    };
    return {
        next () {
            // return is a defined method, so it is safe to call it.
            return listening ? pullValue() : this.return();
        },
        return () {
            emptyQueue();
            return (0, promise_helpers_1.fakePromise)({
                value: undefined,
                done: true
            });
        },
        throw (error) {
            emptyQueue();
            return (0, promise_helpers_1.fakeRejectPromise)(error);
        },
        [Symbol.asyncIterator] () {
            return this;
        }
    };
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/AccumulatorMap.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AccumulatorMap = void 0;
/**
 * ES6 Map with additional `add` method to accumulate items.
 */ class AccumulatorMap extends Map {
    get [Symbol.toStringTag]() {
        return 'AccumulatorMap';
    }
    add(key, item) {
        const group = this.get(key);
        if (group === undefined) {
            this.set(key, [
                item
            ]);
        } else {
            group.push(item);
        }
    }
}
exports.AccumulatorMap = AccumulatorMap;
}),
"[project]/node_modules/@graphql-tools/utils/cjs/directives.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.GraphQLStreamDirective = exports.GraphQLDeferDirective = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
/**
 * Used to conditionally defer fragments.
 */ exports.GraphQLDeferDirective = new graphql_1.GraphQLDirective({
    name: 'defer',
    description: 'Directs the executor to defer this fragment when the `if` argument is true or undefined.',
    locations: [
        graphql_1.DirectiveLocation.FRAGMENT_SPREAD,
        graphql_1.DirectiveLocation.INLINE_FRAGMENT
    ],
    args: {
        if: {
            type: new graphql_1.GraphQLNonNull(graphql_1.GraphQLBoolean),
            description: 'Deferred when true or undefined.',
            defaultValue: true
        },
        label: {
            type: graphql_1.GraphQLString,
            description: 'Unique name'
        }
    }
});
/**
 * Used to conditionally stream list fields.
 */ exports.GraphQLStreamDirective = new graphql_1.GraphQLDirective({
    name: 'stream',
    description: 'Directs the executor to stream plural fields when the `if` argument is true or undefined.',
    locations: [
        graphql_1.DirectiveLocation.FIELD
    ],
    args: {
        if: {
            type: new graphql_1.GraphQLNonNull(graphql_1.GraphQLBoolean),
            description: 'Stream when true or undefined.',
            defaultValue: true
        },
        label: {
            type: graphql_1.GraphQLString,
            description: 'Unique name'
        },
        initialCount: {
            defaultValue: 0,
            type: graphql_1.GraphQLInt,
            description: 'Number of items to return immediately'
        }
    }
});
}),
"[project]/node_modules/@graphql-tools/utils/cjs/collectFields.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.collectSubFields = void 0;
exports.collectFields = collectFields;
exports.shouldIncludeNode = shouldIncludeNode;
exports.doesFragmentConditionMatch = doesFragmentConditionMatch;
exports.getFieldEntryKey = getFieldEntryKey;
exports.getDeferValues = getDeferValues;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const AccumulatorMap_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/AccumulatorMap.js [app-route] (ecmascript)");
const directives_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/directives.js [app-route] (ecmascript)");
const memoize_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)");
function collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, visitedFragmentNames) {
    for (const selection of selectionSet.selections){
        switch(selection.kind){
            case graphql_1.Kind.FIELD:
                {
                    if (!shouldIncludeNode(variableValues, selection)) {
                        continue;
                    }
                    fields.add(getFieldEntryKey(selection), selection);
                    break;
                }
            case graphql_1.Kind.INLINE_FRAGMENT:
                {
                    if (!shouldIncludeNode(variableValues, selection) || !doesFragmentConditionMatch(schema, selection, runtimeType)) {
                        continue;
                    }
                    const defer = getDeferValues(variableValues, selection);
                    if (defer) {
                        const patchFields = new AccumulatorMap_js_1.AccumulatorMap();
                        collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, patchFields, patches, visitedFragmentNames);
                        patches.push({
                            label: defer.label,
                            fields: patchFields
                        });
                    } else {
                        collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, fields, patches, visitedFragmentNames);
                    }
                    break;
                }
            case graphql_1.Kind.FRAGMENT_SPREAD:
                {
                    const fragName = selection.name.value;
                    if (!shouldIncludeNode(variableValues, selection)) {
                        continue;
                    }
                    const defer = getDeferValues(variableValues, selection);
                    if (visitedFragmentNames.has(fragName) && !defer) {
                        continue;
                    }
                    const fragment = fragments[fragName];
                    if (!fragment || !doesFragmentConditionMatch(schema, fragment, runtimeType)) {
                        continue;
                    }
                    if (!defer) {
                        visitedFragmentNames.add(fragName);
                    }
                    if (defer) {
                        const patchFields = new AccumulatorMap_js_1.AccumulatorMap();
                        collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, patchFields, patches, visitedFragmentNames);
                        patches.push({
                            label: defer.label,
                            fields: patchFields
                        });
                    } else {
                        collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, fields, patches, visitedFragmentNames);
                    }
                    break;
                }
        }
    }
}
/**
 * Given a selectionSet, collects all of the fields and returns them.
 *
 * CollectFields requires the "runtime type" of an object. For a field that
 * returns an Interface or Union type, the "runtime type" will be the actual
 * object type returned by that field.
 *
 */ function collectFields(schema, fragments, variableValues, runtimeType, selectionSet) {
    const fields = new AccumulatorMap_js_1.AccumulatorMap();
    const patches = [];
    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, new Set());
    return {
        fields,
        patches
    };
}
/**
 * Determines if a field should be included based on the `@include` and `@skip`
 * directives, where `@skip` has higher precedence than `@include`.
 */ function shouldIncludeNode(variableValues, node) {
    const skip = (0, graphql_1.getDirectiveValues)(graphql_1.GraphQLSkipDirective, node, variableValues);
    if (skip?.['if'] === true) {
        return false;
    }
    const include = (0, graphql_1.getDirectiveValues)(graphql_1.GraphQLIncludeDirective, node, variableValues);
    if (include?.['if'] === false) {
        return false;
    }
    return true;
}
/**
 * Determines if a fragment is applicable to the given type.
 */ function doesFragmentConditionMatch(schema, fragment, type) {
    const typeConditionNode = fragment.typeCondition;
    if (!typeConditionNode) {
        return true;
    }
    const conditionalType = (0, graphql_1.typeFromAST)(schema, typeConditionNode);
    if (conditionalType === type) {
        return true;
    }
    if ((0, graphql_1.isAbstractType)(conditionalType)) {
        const possibleTypes = schema.getPossibleTypes(conditionalType);
        return possibleTypes.includes(type);
    }
    return false;
}
/**
 * Implements the logic to compute the key of a given field's entry
 */ function getFieldEntryKey(node) {
    return node.alias ? node.alias.value : node.name.value;
}
/**
 * Returns an object containing the `@defer` arguments if a field should be
 * deferred based on the experimental flag, defer directive present and
 * not disabled by the "if" argument.
 */ function getDeferValues(variableValues, node) {
    const defer = (0, graphql_1.getDirectiveValues)(directives_js_1.GraphQLDeferDirective, node, variableValues);
    if (!defer) {
        return;
    }
    if (defer['if'] === false) {
        return;
    }
    return {
        label: typeof defer['label'] === 'string' ? defer['label'] : undefined
    };
}
/**
 * Given an array of field nodes, collects all of the subfields of the passed
 * in fields, and returns them at the end.
 *
 * CollectSubFields requires the "return type" of an object. For a field that
 * returns an Interface or Union type, the "return type" will be the actual
 * object type returned by that field.
 *
 */ exports.collectSubFields = (0, memoize_js_1.memoize5)(function collectSubfields(schema, fragments, variableValues, returnType, fieldNodes) {
    const subFieldNodes = new AccumulatorMap_js_1.AccumulatorMap();
    const visitedFragmentNames = new Set();
    const subPatches = [];
    const subFieldsAndPatches = {
        fields: subFieldNodes,
        patches: subPatches
    };
    for (const node of fieldNodes){
        if (node.selectionSet) {
            collectFieldsImpl(schema, fragments, variableValues, returnType, node.selectionSet, subFieldNodes, subPatches, visitedFragmentNames);
        }
    }
    return subFieldsAndPatches;
});
}),
"[project]/node_modules/@graphql-tools/utils/cjs/getOperationASTFromRequest.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getOperationASTFromRequest = void 0;
exports.getOperationASTFromDocument = getOperationASTFromDocument;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const memoize_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)");
function getOperationASTFromDocument(documentNode, operationName) {
    const doc = (0, graphql_1.getOperationAST)(documentNode, operationName);
    if (!doc) {
        throw new Error(`Cannot infer operation ${operationName || ''}`);
    }
    return doc;
}
exports.getOperationASTFromRequest = (0, memoize_js_1.memoize1)(function getOperationASTFromRequest(request) {
    return getOperationASTFromDocument(request.document, request.operationName);
});
}),
"[project]/node_modules/@graphql-tools/utils/cjs/visitResult.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.visitData = visitData;
exports.visitErrors = visitErrors;
exports.visitResult = visitResult;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const collectFields_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/collectFields.js [app-route] (ecmascript)");
const getOperationASTFromRequest_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getOperationASTFromRequest.js [app-route] (ecmascript)");
function visitData(data, enter, leave) {
    if (Array.isArray(data)) {
        return data.map((value)=>visitData(value, enter, leave));
    } else if (typeof data === 'object') {
        const newData = enter != null ? enter(data) : data;
        if (newData != null) {
            for(const key in newData){
                const value = newData[key];
                Object.defineProperty(newData, key, {
                    value: visitData(value, enter, leave)
                });
            }
        }
        return leave != null ? leave(newData) : newData;
    }
    return data;
}
function visitErrors(errors, visitor) {
    return errors.map((error)=>visitor(error));
}
function visitResult(result, request, schema, resultVisitorMap, errorVisitorMap) {
    const fragments = request.document.definitions.reduce((acc, def)=>{
        if (def.kind === graphql_1.Kind.FRAGMENT_DEFINITION) {
            acc[def.name.value] = def;
        }
        return acc;
    }, {});
    const variableValues = request.variables || {};
    const errorInfo = {
        segmentInfoMap: new Map(),
        unpathedErrors: new Set()
    };
    const data = result.data;
    const errors = result.errors;
    const visitingErrors = errors != null && errorVisitorMap != null;
    const operationDocumentNode = (0, getOperationASTFromRequest_js_1.getOperationASTFromRequest)(request);
    if (data != null && operationDocumentNode != null) {
        result.data = visitRoot(data, operationDocumentNode, schema, fragments, variableValues, resultVisitorMap, visitingErrors ? errors : undefined, errorInfo);
    }
    if (errors != null && errorVisitorMap) {
        result.errors = visitErrorsByType(errors, errorVisitorMap, errorInfo);
    }
    return result;
}
function visitErrorsByType(errors, errorVisitorMap, errorInfo) {
    const segmentInfoMap = errorInfo.segmentInfoMap;
    const unpathedErrors = errorInfo.unpathedErrors;
    const unpathedErrorVisitor = errorVisitorMap['__unpathed'];
    return errors.map((originalError)=>{
        const pathSegmentsInfo = segmentInfoMap.get(originalError);
        const newError = pathSegmentsInfo == null ? originalError : pathSegmentsInfo.reduceRight((acc, segmentInfo)=>{
            const typeName = segmentInfo.type.name;
            const typeVisitorMap = errorVisitorMap[typeName];
            if (typeVisitorMap == null) {
                return acc;
            }
            const errorVisitor = typeVisitorMap[segmentInfo.fieldName];
            return errorVisitor == null ? acc : errorVisitor(acc, segmentInfo.pathIndex);
        }, originalError);
        if (unpathedErrorVisitor && unpathedErrors.has(originalError)) {
            return unpathedErrorVisitor(newError);
        }
        return newError;
    });
}
function getOperationRootType(schema, operationDef) {
    switch(operationDef.operation){
        case 'query':
            return schema.getQueryType();
        case 'mutation':
            return schema.getMutationType();
        case 'subscription':
            return schema.getSubscriptionType();
    }
}
function visitRoot(root, operation, schema, fragments, variableValues, resultVisitorMap, errors, errorInfo) {
    const operationRootType = getOperationRootType(schema, operation);
    const { fields: collectedFields } = (0, collectFields_js_1.collectFields)(schema, fragments, variableValues, operationRootType, operation.selectionSet);
    return visitObjectValue(root, operationRootType, collectedFields, schema, fragments, variableValues, resultVisitorMap, 0, errors, errorInfo);
}
function visitObjectValue(object, type, fieldNodeMap, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo) {
    const fieldMap = type.getFields();
    const typeVisitorMap = resultVisitorMap?.[type.name];
    const enterObject = typeVisitorMap?.__enter;
    const newObject = enterObject != null ? enterObject(object) : object;
    let sortedErrors;
    let errorMap = null;
    if (errors != null) {
        sortedErrors = sortErrorsByPathSegment(errors, pathIndex);
        errorMap = sortedErrors.errorMap;
        for (const error of sortedErrors.unpathedErrors){
            errorInfo.unpathedErrors.add(error);
        }
    }
    for (const [responseKey, subFieldNodes] of fieldNodeMap){
        const fieldName = subFieldNodes[0].name.value;
        let fieldType = fieldMap[fieldName]?.type;
        if (fieldType == null) {
            switch(fieldName){
                case '__typename':
                    fieldType = graphql_1.TypeNameMetaFieldDef.type;
                    break;
                case '__schema':
                    fieldType = graphql_1.SchemaMetaFieldDef.type;
                    break;
                case '__type':
                    fieldType = graphql_1.TypeMetaFieldDef.type;
                    break;
            }
        }
        const newPathIndex = pathIndex + 1;
        let fieldErrors;
        if (errorMap) {
            fieldErrors = errorMap[responseKey];
            if (fieldErrors != null) {
                delete errorMap[responseKey];
            }
            addPathSegmentInfo(type, fieldName, newPathIndex, fieldErrors, errorInfo);
        }
        const newValue = visitFieldValue(object[responseKey], fieldType, subFieldNodes, schema, fragments, variableValues, resultVisitorMap, newPathIndex, fieldErrors, errorInfo);
        updateObject(newObject, responseKey, newValue, typeVisitorMap, fieldName);
    }
    const oldTypename = newObject.__typename;
    if (oldTypename != null) {
        updateObject(newObject, '__typename', oldTypename, typeVisitorMap, '__typename');
    }
    if (errorMap) {
        for(const errorsKey in errorMap){
            const errors = errorMap[errorsKey];
            for (const error of errors){
                errorInfo.unpathedErrors.add(error);
            }
        }
    }
    const leaveObject = typeVisitorMap?.__leave;
    return leaveObject != null ? leaveObject(newObject) : newObject;
}
function updateObject(object, responseKey, newValue, typeVisitorMap, fieldName) {
    if (typeVisitorMap == null) {
        object[responseKey] = newValue;
        return;
    }
    const fieldVisitor = typeVisitorMap[fieldName];
    if (fieldVisitor == null) {
        object[responseKey] = newValue;
        return;
    }
    const visitedValue = fieldVisitor(newValue);
    if (visitedValue === undefined) {
        delete object[responseKey];
        return;
    }
    object[responseKey] = visitedValue;
}
function visitListValue(list, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo) {
    return list.map((listMember)=>visitFieldValue(listMember, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex + 1, errors, errorInfo));
}
function visitFieldValue(value, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors = [], errorInfo) {
    if (value == null) {
        return value;
    }
    const nullableType = (0, graphql_1.getNullableType)(returnType);
    if ((0, graphql_1.isListType)(nullableType)) {
        return visitListValue(value, nullableType.ofType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);
    } else if ((0, graphql_1.isAbstractType)(nullableType)) {
        const finalType = schema.getType(value.__typename);
        let { fields: collectedFields, patches } = (0, collectFields_js_1.collectSubFields)(schema, fragments, variableValues, finalType, fieldNodes);
        if (patches.length) {
            collectedFields = new Map(collectedFields);
            for (const patch of patches){
                for (const [responseKey, fields] of patch.fields){
                    const existingFields = collectedFields.get(responseKey);
                    if (existingFields) {
                        existingFields.push(...fields);
                    } else {
                        collectedFields.set(responseKey, fields);
                    }
                }
            }
        }
        return visitObjectValue(value, finalType, collectedFields, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);
    } else if ((0, graphql_1.isObjectType)(nullableType)) {
        let { fields: collectedFields, patches } = (0, collectFields_js_1.collectSubFields)(schema, fragments, variableValues, nullableType, fieldNodes);
        if (patches.length) {
            collectedFields = new Map(collectedFields);
            for (const patch of patches){
                for (const [responseKey, fields] of patch.fields){
                    const existingFields = collectedFields.get(responseKey);
                    if (existingFields) {
                        existingFields.push(...fields);
                    } else {
                        collectedFields.set(responseKey, fields);
                    }
                }
            }
        }
        return visitObjectValue(value, nullableType, collectedFields, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);
    }
    const typeVisitorMap = resultVisitorMap?.[nullableType.name];
    if (typeVisitorMap == null) {
        return value;
    }
    const visitedValue = typeVisitorMap(value);
    return visitedValue === undefined ? value : visitedValue;
}
function sortErrorsByPathSegment(errors, pathIndex) {
    const errorMap = Object.create(null);
    const unpathedErrors = new Set();
    for (const error of errors){
        const pathSegment = error.path?.[pathIndex];
        if (pathSegment == null) {
            unpathedErrors.add(error);
            continue;
        }
        if (pathSegment in errorMap) {
            errorMap[pathSegment].push(error);
        } else {
            errorMap[pathSegment] = [
                error
            ];
        }
    }
    return {
        errorMap,
        unpathedErrors
    };
}
function addPathSegmentInfo(type, fieldName, pathIndex, errors = [], errorInfo) {
    for (const error of errors){
        const segmentInfo = {
            type,
            fieldName,
            pathIndex
        };
        const pathSegmentsInfo = errorInfo.segmentInfoMap.get(error);
        if (pathSegmentsInfo == null) {
            errorInfo.segmentInfoMap.set(error, [
                segmentInfo
            ]);
        } else {
            pathSegmentsInfo.push(segmentInfo);
        }
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/valueMatchesCriteria.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.valueMatchesCriteria = valueMatchesCriteria;
function valueMatchesCriteria(value, criteria) {
    if (value == null) {
        return value === criteria;
    } else if (Array.isArray(value)) {
        return Array.isArray(criteria) && value.every((val, index)=>valueMatchesCriteria(val, criteria[index]));
    } else if (typeof value === 'object') {
        return typeof criteria === 'object' && criteria && Object.keys(criteria).every((propertyName)=>valueMatchesCriteria(value[propertyName], criteria[propertyName]));
    } else if (criteria instanceof RegExp) {
        return criteria.test(value);
    }
    return value === criteria;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/isAsyncIterable.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.isAsyncIterable = isAsyncIterable;
function isAsyncIterable(value) {
    return value?.[Symbol.asyncIterator] != null;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/isDocumentNode.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.isDocumentNode = isDocumentNode;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function isDocumentNode(object) {
    return object && typeof object === 'object' && 'kind' in object && object.kind === graphql_1.Kind.DOCUMENT;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/executor.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[project]/node_modules/@graphql-tools/utils/cjs/withCancel.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getAsyncIteratorWithCancel = getAsyncIteratorWithCancel;
exports.getAsyncIterableWithCancel = getAsyncIterableWithCancel;
exports.withCancel = getAsyncIterableWithCancel;
const memoize_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)");
async function defaultAsyncIteratorReturn(value) {
    return {
        value,
        done: true
    };
}
const proxyMethodFactory = (0, memoize_js_1.memoize2)(function proxyMethodFactory(target, targetMethod) {
    return function proxyMethod(...args) {
        return Reflect.apply(targetMethod, target, args);
    };
});
function getAsyncIteratorWithCancel(asyncIterator, onCancel) {
    return new Proxy(asyncIterator, {
        has (asyncIterator, prop) {
            if (prop === 'return') {
                return true;
            }
            return Reflect.has(asyncIterator, prop);
        },
        get (asyncIterator, prop, receiver) {
            const existingPropValue = Reflect.get(asyncIterator, prop, receiver);
            if (prop === 'return') {
                const existingReturn = existingPropValue || defaultAsyncIteratorReturn;
                return async function returnWithCancel(value) {
                    const returnValue = await onCancel(value);
                    return Reflect.apply(existingReturn, asyncIterator, [
                        returnValue
                    ]);
                };
            } else if (typeof existingPropValue === 'function') {
                return proxyMethodFactory(asyncIterator, existingPropValue);
            }
            return existingPropValue;
        }
    });
}
function getAsyncIterableWithCancel(asyncIterable, onCancel) {
    return new Proxy(asyncIterable, {
        get (asyncIterable, prop, receiver) {
            const existingPropValue = Reflect.get(asyncIterable, prop, receiver);
            if (Symbol.asyncIterator === prop) {
                return function asyncIteratorFactory() {
                    const asyncIterator = Reflect.apply(existingPropValue, asyncIterable, []);
                    return getAsyncIteratorWithCancel(asyncIterator, onCancel);
                };
            } else if (typeof existingPropValue === 'function') {
                return proxyMethodFactory(asyncIterable, existingPropValue);
            }
            return existingPropValue;
        }
    });
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/fixSchemaAst.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.fixSchemaAst = fixSchemaAst;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const print_schema_with_directives_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/print-schema-with-directives.js [app-route] (ecmascript)");
function buildFixedSchema(schema, options) {
    const document = (0, print_schema_with_directives_js_1.getDocumentNodeFromSchema)(schema);
    return (0, graphql_1.buildASTSchema)(document, {
        ...options || {}
    });
}
function fixSchemaAst(schema, options) {
    // eslint-disable-next-line no-undef-init
    let schemaWithValidAst = undefined;
    if (!schema.astNode || !schema.extensionASTNodes) {
        schemaWithValidAst = buildFixedSchema(schema, options);
    }
    if (!schema.astNode && schemaWithValidAst?.astNode) {
        schema.astNode = schemaWithValidAst.astNode;
    }
    if (!schema.extensionASTNodes && schemaWithValidAst?.astNode) {
        schema.extensionASTNodes = schemaWithValidAst.extensionASTNodes;
    }
    return schema;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/extractExtensionsFromSchema.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.extractExtensionsFromSchema = extractExtensionsFromSchema;
const helpers_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/helpers.js [app-route] (ecmascript)");
const Interfaces_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)");
const mapSchema_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mapSchema.js [app-route] (ecmascript)");
function handleDirectiveExtensions(extensions, removeDirectives) {
    extensions = extensions || {};
    const { directives: existingDirectives, ...rest } = extensions;
    const finalExtensions = {
        ...rest
    };
    if (!removeDirectives) {
        if (existingDirectives != null) {
            const directives = {};
            for(const directiveName in existingDirectives){
                directives[directiveName] = [
                    ...(0, helpers_js_1.asArray)(existingDirectives[directiveName])
                ];
            }
            finalExtensions.directives = directives;
        }
    }
    return finalExtensions;
}
function extractExtensionsFromSchema(schema, removeDirectives = false) {
    const result = {
        schemaExtensions: handleDirectiveExtensions(schema.extensions, removeDirectives),
        types: {}
    };
    (0, mapSchema_js_1.mapSchema)(schema, {
        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type)=>{
            result.types[type.name] = {
                fields: {},
                type: 'object',
                extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
            };
            return type;
        },
        [Interfaces_js_1.MapperKind.INTERFACE_TYPE]: (type)=>{
            result.types[type.name] = {
                fields: {},
                type: 'interface',
                extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
            };
            return type;
        },
        [Interfaces_js_1.MapperKind.FIELD]: (field, fieldName, typeName)=>{
            result.types[typeName].fields[fieldName] = {
                arguments: {},
                extensions: handleDirectiveExtensions(field.extensions, removeDirectives)
            };
            const args = field.args;
            if (args != null) {
                for(const argName in args){
                    result.types[typeName].fields[fieldName].arguments[argName] = handleDirectiveExtensions(args[argName].extensions, removeDirectives);
                }
            }
            return field;
        },
        [Interfaces_js_1.MapperKind.ENUM_TYPE]: (type)=>{
            result.types[type.name] = {
                values: {},
                type: 'enum',
                extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
            };
            return type;
        },
        [Interfaces_js_1.MapperKind.ENUM_VALUE]: (value, typeName, _schema, valueName)=>{
            result.types[typeName].values[valueName] = handleDirectiveExtensions(value.extensions, removeDirectives);
            return value;
        },
        [Interfaces_js_1.MapperKind.SCALAR_TYPE]: (type)=>{
            result.types[type.name] = {
                type: 'scalar',
                extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
            };
            return type;
        },
        [Interfaces_js_1.MapperKind.UNION_TYPE]: (type)=>{
            result.types[type.name] = {
                type: 'union',
                extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
            };
            return type;
        },
        [Interfaces_js_1.MapperKind.INPUT_OBJECT_TYPE]: (type)=>{
            result.types[type.name] = {
                fields: {},
                type: 'input',
                extensions: handleDirectiveExtensions(type.extensions, removeDirectives)
            };
            return type;
        },
        [Interfaces_js_1.MapperKind.INPUT_OBJECT_FIELD]: (field, fieldName, typeName)=>{
            result.types[typeName].fields[fieldName] = {
                extensions: handleDirectiveExtensions(field.extensions, removeDirectives)
            };
            return field;
        }
    });
    return result;
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/Path.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.addPath = addPath;
exports.pathToArray = pathToArray;
exports.printPathArray = printPathArray;
/**
 * Given a Path and a key, return a new Path containing the new key.
 */ function addPath(prev, key, typename) {
    return {
        prev,
        key,
        typename
    };
}
/**
 * Given a Path, return an Array of the path keys.
 */ function pathToArray(path) {
    const flattened = [];
    let curr = path;
    while(curr){
        flattened.push(curr.key);
        curr = curr.prev;
    }
    return flattened.reverse();
}
/**
 * Build a string describing the path.
 */ function printPathArray(path) {
    return path.map((key)=>typeof key === 'number' ? '[' + key.toString() + ']' : '.' + key).join('');
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/mergeIncrementalResult.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.mergeIncrementalResult = mergeIncrementalResult;
const merge_1 = __turbopack_context__.r("[project]/node_modules/dset/merge/index.js [app-route] (ecmascript)");
function mergeIncrementalResult({ incrementalResult, executionResult }) {
    const path = [
        'data',
        ...incrementalResult.path ?? []
    ];
    if (incrementalResult.items) {
        for (const item of incrementalResult.items){
            (0, merge_1.dset)(executionResult, path, item);
            // Increment the last path segment (the array index) to merge the next item at the next index
            path[path.length - 1]++;
        }
    }
    if (incrementalResult.data) {
        (0, merge_1.dset)(executionResult, path, incrementalResult.data);
    }
    if (incrementalResult.errors) {
        executionResult.errors = executionResult.errors || [];
        executionResult.errors.push(...incrementalResult.errors);
    }
    if (incrementalResult.extensions) {
        (0, merge_1.dset)(executionResult, 'extensions', incrementalResult.extensions);
    }
    if (incrementalResult.incremental) {
        incrementalResult.incremental.forEach((incrementalSubResult)=>{
            mergeIncrementalResult({
                incrementalResult: incrementalSubResult,
                executionResult
            });
        });
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/debugTimer.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.debugTimerStart = debugTimerStart;
exports.debugTimerEnd = debugTimerEnd;
const debugNamesOngoing = new Set();
function debugTimerStart(name) {
    const debugEnvVar = globalThis.process?.env?.['DEBUG'] || globalThis.DEBUG;
    if (debugEnvVar === '1' || debugEnvVar?.includes(name)) {
        debugNamesOngoing.add(name);
        console.time(name);
    }
}
function debugTimerEnd(name) {
    if (debugNamesOngoing.has(name)) {
        console.timeEnd(name);
    }
}
}),
"[project]/node_modules/@graphql-tools/utils/cjs/registerAbortSignalListener.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getAbortPromise = void 0;
exports.registerAbortSignalListener = registerAbortSignalListener;
const promise_helpers_1 = __turbopack_context__.r("[project]/node_modules/@whatwg-node/promise-helpers/cjs/index.js [app-route] (ecmascript)");
const memoize_js_1 = __turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)");
// AbortSignal handler cache to avoid the "possible EventEmitter memory leak detected"
// on Node.js
const getListenersOfAbortSignal = (0, memoize_js_1.memoize1)(function getListenersOfAbortSignal(signal) {
    const listeners = new Set();
    signal.addEventListener('abort', (e)=>{
        for (const listener of listeners){
            listener(e);
        }
    }, {
        once: true
    });
    return listeners;
});
/**
 * Register an AbortSignal handler for a signal.
 * This helper function mainly exists to work around the
 * "possible EventEmitter memory leak detected. 11 listeners added. Use emitter.setMaxListeners() to increase limit."
 * warning occuring on Node.js
 */ function registerAbortSignalListener(signal, listener) {
    // If the signal is already aborted, call the listener immediately
    if (signal.aborted) {
        listener();
        return;
    }
    getListenersOfAbortSignal(signal).add(listener);
}
exports.getAbortPromise = (0, memoize_js_1.memoize1)(function getAbortPromise(signal) {
    // If the signal is already aborted, return a rejected promise
    if (signal.aborted) {
        return (0, promise_helpers_1.fakeRejectPromise)(signal.reason);
    }
    return new Promise((_resolve, reject)=>{
        if (signal.aborted) {
            reject(signal.reason);
            return;
        }
        registerAbortSignalListener(signal, ()=>{
            reject(signal.reason);
        });
    });
});
}),
"[project]/node_modules/@graphql-tools/utils/cjs/index.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createDeferred = exports.fakePromise = exports.mapMaybePromise = exports.mapAsyncIterator = exports.inspect = void 0;
const tslib_1 = __turbopack_context__.r("[project]/node_modules/tslib/tslib.es6.mjs [app-route] (ecmascript)");
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/loaders.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/helpers.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-directives.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-fields-with-directives.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-arguments-with-directives.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-implementing-types.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/print-schema-with-directives.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/get-fields-with-directives.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/validate-documents.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/parse-graphql-json.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/parse-graphql-sdl.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/build-operation-for-field.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/types.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/filterSchema.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/heal.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getResolversFromSchema.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/forEachField.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/forEachDefaultValue.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mapSchema.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/addTypes.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rewire.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/prune.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mergeDeep.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Interfaces.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/stub.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/selectionSets.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getResponseKeyFromInfo.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/fields.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/renameType.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/transformInputValue.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/updateArgument.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromType.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/implementsAbstractType.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/errors.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/observableToAsyncIterable.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/visitResult.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getArgumentValues.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/valueMatchesCriteria.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/isAsyncIterable.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/isDocumentNode.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/astFromValueUntyped.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/executor.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/withCancel.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/rootTypes.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/comments.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/collectFields.js [app-route] (ecmascript)"), exports);
var cross_inspect_1 = __turbopack_context__.r("[project]/node_modules/cross-inspect/cjs/index.js [app-route] (ecmascript)");
Object.defineProperty(exports, "inspect", {
    enumerable: true,
    get: function() {
        return cross_inspect_1.inspect;
    }
});
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/memoize.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/fixSchemaAst.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getOperationASTFromRequest.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/extractExtensionsFromSchema.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/Path.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/jsutils.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/directives.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/mergeIncrementalResult.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/debugTimer.js [app-route] (ecmascript)"), exports);
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/getDirectiveExtensions.js [app-route] (ecmascript)"), exports);
var promise_helpers_1 = __turbopack_context__.r("[project]/node_modules/@whatwg-node/promise-helpers/cjs/index.js [app-route] (ecmascript)");
Object.defineProperty(exports, "mapAsyncIterator", {
    enumerable: true,
    get: function() {
        return promise_helpers_1.mapAsyncIterator;
    }
});
Object.defineProperty(exports, "mapMaybePromise", {
    enumerable: true,
    get: function() {
        return promise_helpers_1.mapMaybePromise;
    }
});
Object.defineProperty(exports, "fakePromise", {
    enumerable: true,
    get: function() {
        return promise_helpers_1.fakePromise;
    }
});
Object.defineProperty(exports, "createDeferred", {
    enumerable: true,
    get: function() {
        return promise_helpers_1.createDeferredPromise;
    }
});
tslib_1.__exportStar(__turbopack_context__.r("[project]/node_modules/@graphql-tools/utils/cjs/registerAbortSignalListener.js [app-route] (ecmascript)"), exports);
}),
];

//# sourceMappingURL=node_modules_%40graphql-tools_utils_c749fea5._.js.map