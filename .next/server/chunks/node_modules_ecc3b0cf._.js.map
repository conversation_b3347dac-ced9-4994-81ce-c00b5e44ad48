{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/getEmbeddedHTML.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/getEmbeddedHTML.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;AAcA,SAAS,sBAAsB,CAAC,MAAc;IAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAC1B,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAC7B,CAAC;AAEM,MAAM,uBAAuB,GAAG,CACrC,kBAA0B,EAC1B,MAAqE,EACrE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IA2BF,MAAM,yCAAyC,GAAG;QAChD,cAAc,EAAE,CAAA,CAAE;QAClB,oBAAoB,EAAE,KAAK;QAC3B,YAAY,EAAE,IAAI;QAClB,GAAG,AAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;KAC3D,CAAC;IACF,MAAM,sBAAsB,GAGF;QACxB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,MAAM,EAAE,qBAAqB;QAC7B,YAAY,EAAE;YACZ,GAAG,AAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,GACpE;gBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,GACD,CAAA,CAAE,CAAC;YACP,GAAG,AAAC,cAAc,IAAI,MAAM,GACxB;gBACE,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,GACD,CAAA,CAAE,CAAC;YACP,cAAc,EAAE;gBACd,GAAG,yCAAyC,CAAC,cAAc;aAC5D;SACF;QACD,oBAAoB,EAClB,yCAAyC,CAAC,oBAAoB;QAChE,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,yCAAyC,CAAC,YAAY;QACpE,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IAEF,OAAO,CAAA;;;;;eAKM,KAAK,CAAA;;;;;;;;;;;;;;;iBAeH,KAAK,CAAA,yDAAA,EAA4D,kBAAkB,CAChG,kBAAkB,CACnB,CAAA,mDAAA,EAAsD,kBAAkB,CACvE,mBAAmB,CACpB,CAAA;iBACc,KAAK,CAAA;;iCAEW,sBAAsB,CACnD,sBAAsB,CACvB,CAAA;;;;;;CAMF,CAAC;AACF,CAAC,CAAC;AAEK,MAAM,sBAAsB,GAAG,CACpC,iBAAyB,EACzB,MAAgE,EAChE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,oCAAoC,GAAG;QAC3C,YAAY,EAAE,IAAI;QAClB,kBAAkB,EAAE,KAAK;QACzB,YAAY,EAAE,CAAA,CAAE;QAChB,GAAG,AAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,AAAC,MAAM,CAAC,KAAK,IAAI,CAAA,CAAE,AAAC,CAAC;KACnE,CAAC;IACF,MAAM,qBAAqB,GAAG;QAC5B,MAAM,EAAE,oBAAoB;QAC5B,YAAY,EAAE;YACZ,GAAG,AAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,GACpE;gBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,GACD,CAAA,CAAE,CAAC;YACP,GAAG,AAAC,cAAc,IAAI,MAAM,GACxB;gBACE,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,GACD,CAAA,CAAE,CAAC;YACP,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,GAAG,oCAAoC,CAAC,YAAY;SACrD;QACD,gBAAgB,EAAE,KAAK;QACvB,kBAAkB,EAAE,oCAAoC,CAAC,kBAAkB;QAC3E,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,oCAAoC,CAAC,YAAY;QAC/D,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IACF,OAAO,CAAA;;;;;eAKM,KAAK,CAAA;;;;;;;;;;;;;;;iBAeH,KAAK,CAAA,wDAAA,EAA2D,kBAAkB,CAC/F,iBAAiB,CAClB,CAAA,kDAAA,EAAqD,kBAAkB,CACtE,mBAAmB,CACpB,CAAA;iBACc,KAAK,CAAA;;gCAEU,sBAAsB,CAAC,qBAAqB,CAAC,CAAA;;;;;;;;CAQ5E,CAAC;AACF,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@apollo/server/dist/esm/generated/packageVersion.js", "sourceRoot": "", "sources": ["../../../src/generated/packageVersion.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAO,MAAM,cAAc,GAAG,OAAO,CAAC", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/uuid/dist/esm/native.js"], "sourcesContent": ["import { randomUUID } from 'crypto';\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,YAAA,mHAAU;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/uuid/dist/esm/rng.js"], "sourcesContent": ["import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW;AACjC,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACpB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACjC,IAAA,uHAAc,EAAC;QACf,UAAU;IACd;IACA,OAAO,UAAU,KAAK,CAAC,SAAU,WAAW;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/uuid/dist/esm/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/uuid/dist/esm/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,yJAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/uuid/dist/esm/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAC9B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,IAAA,4JAAQ,EAAC,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/uuid/dist/esm/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,0JAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,0JAAM,CAAC,UAAU;IAC5B;IACA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,GAAG,QAAQ,IAAA,uJAAG;IACrD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,GAAG,wBAAwB,CAAC;QAC3F;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,IAAA,qKAAe,EAAC;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/index.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;AAUA,OAAO,EACL,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AACtE,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;;;;;AAO9B,SAAU,yCAAyC,CACvD,UAA4D,CAAA,CAAE;IAE9D,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE,GAAG,IAAI,EAAE,GAAG;QAEzD,KAAK,EAAE,IAAa;QACpB,GAAG,OAAO;KACX,CAAC;IACF,OAAO,oCAAoC,CAAC,OAAO,EAAE;QACnD,MAAM,EAAE,KAAK;QACb,eAAe,EAAE,4BAA4B;QAC7C,GAAG,IAAI;KACR,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,8CAA8C,CAC5D,UAAiE,CAAA,CAAE;IAEnE,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IACnE,OAAO,oCAAoC,CAAC,OAAO,EAAE;QACnD,MAAM,EAAE,IAAI;QACZ,eAAe,EAAE,4BAA4B;QAC7C,GAAG,IAAI;KACR,CAAC,CAAC;AACL,CAAC;AAUD,SAAS,YAAY,CAAC,MAAyB;IAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,MAAM,6BAA6B,GAAG,CACpC,UAAkB,EAClB,MAAyB,EACzB,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAE3C,OAAO,CAAA;;;;;iBAKQ,KAAK,CAAA,uBAAA,EAA0B,aAAa,CAAA;iBAC5C,KAAK,CAAA,gEAAA,EAAmE,kBAAkB,CACvG,UAAU,CACX,CAAA,2BAAA,EAA8B,mBAAmB,CAAA,WAAA,CAAa,CAAC;AAClE,CAAC,CAAC;AAEK,MAAM,iCAAiC,GAAG,IAAI,CAAC;AAC/C,MAAM,gCAAgC,GAAG,IAAI,CAAC;AAC9C,MAAM,0CAA0C,GAAG,SAAS,CAAC;AAGpE,SAAS,oCAAoC,CAC3C,YAAgC,EAChC,MAGC;IAED,MAAM,eAAe,GAAG,YAAY,IAAI,iCAAiC,CAAC;IAC1E,MAAM,cAAc,GAAG,YAAY,IAAI,gCAAgC,CAAC;IACxE,MAAM,8BAA8B,GAClC,YAAY,IAAI,0CAA0C,CAAC;IAC7D,MAAM,mBAAmB,GAAG,CAAA,eAAA,EAAkB,oMAAc,EAAE,CAAC;IAE/D,MAAM,cAAc,GAAG;QACrB,0DAA0D;QAC1D,kDAAkD;QAClD,mDAAmD;KACpD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,MAAM,aAAa,GAAG;QACpB,0DAA0D;QAC1D,kDAAkD;QAClD,mDAAmD;QACnD,8BAA8B;KAC/B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,MAAM,cAAc,GAAG;QACrB,0CAA0C;QAC1C,yCAAyC;QACzC,iCAAiC;KAClC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,OAAO;QACL,iCAAiC,EAAE,KAAK;QACxC,KAAK,CAAC,eAAe;YACnB,OAAO;gBACL,KAAK,CAAC,iBAAiB;oBACrB,MAAM,2BAA2B,GAAG,kBAAkB,CACpD,8BAA8B,CAC/B,CAAC;oBACF,KAAK,UAAU,IAAI;wBACjB,MAAM,KAAK,OAAG,gLAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,KAAC,kLAAM,EAAE,CAAC,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAClE,MAAM,SAAS,GAAG,CAAA,yBAAA,EAA4B,KAAK,CAAA,EAAA,EAAK,cAAc,EAAE,CAAC;wBACzE,MAAM,QAAQ,GAAG,CAAA,iBAAA,EAAoB,KAAK,CAAA,EAAA,EAAK,aAAa,EAAE,CAAC;wBAC/D,MAAM,QAAQ,GAAG,CAAA,gEAAA,CAAkE,CAAC;wBACpF,MAAM,WAAW,GAAG,CAAA,qEAAA,CAAuE,CAAC;wBAC5F,MAAM,QAAQ,GAAG,CAAA,UAAA,EAAa,cAAc,EAAE,CAAC;wBAC/C,OAAO,CAAA;;;;;0DAKuC,SAAS,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,EAAK,WAAW,CAAA,EAAA,EAAK,QAAQ,CAAA;;;uEAGnD,2BAA2B,CAAA;;;;;;;;;;;;uEAY3B,2BAA2B,CAAA;;;;uEAI3B,2BAA2B,CAAA;;;;;;;qBAO7E,KAAK,CAAA;;;;;;;;;;;;;;;;;;MAmBpB,MAAM,CAAC,KAAK,GACR,UAAU,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,OACrC,qOAAuB,EACrB,eAAe,EACf,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACD,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC,OACrB,oOAAsB,EACpB,cAAc,EACd,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACD,6BAA6B,CAC3B,8BAA8B,EAC9B,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACL,6BAA6B,CAC3B,8BAA8B,EAC9B,MAAM,EACN,mBAAmB,EACnB,KAAK,CAEb,CAAA;;;;WAIO,CAAC;oBACF,CAAC;oBACD,OAAO;wBAAE,IAAI;oBAAA,CAAE,CAAC;gBAClB,CAAC;aACF,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}]}