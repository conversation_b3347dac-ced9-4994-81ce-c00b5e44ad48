{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/lib/graphql/schema.ts"], "sourcesContent": ["import { gql } from \"@apollo/client/core\";\n\nexport const typeDefs = gql`\n  type Warehouse {\n    code: ID!\n    name: String!\n    city: String!\n    country: String!\n  }\n\n  type Product {\n    id: ID!\n    name: String!\n    sku: String!\n    warehouse: String!\n    stock: Int!\n    demand: Int!\n  }\n\n  type KPI {\n    date: String!\n    stock: Int!\n    demand: Int!\n  }\n\n  type Query {\n    products(search: String, status: String, warehouse: String): [Product!]!\n    warehouses: [Warehouse!]!\n    kpis(range: String!): [KPI!]!\n  }\n\n  type Mutation {\n    updateDemand(id: ID!, demand: Int!): Product!\n    transferStock(id: ID!, from: String!, to: String!, qty: Int!): Product!\n  }\n`;\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,WAAW,uJAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiC5B,CAAC", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/lib/graphql/mockData.ts"], "sourcesContent": ["export interface Product {\n  id: string;\n  name: string;\n  sku: string;\n  warehouse: string;\n  stock: number;\n  demand: number;\n}\n\nexport interface Warehouse {\n  code: string;\n  name: string;\n  city: string;\n  country: string;\n}\n\nexport interface KPI {\n  date: string;\n  stock: number;\n  demand: number;\n}\n\n// Mock data for the list of warehouses\nexport const warehouses: Warehouse[] = [\n  {\n    code: \"BLR-A\",\n    name: \"Bangalore Alpha\",\n    city: \"Bangalore\",\n    country: \"India\",\n  },\n  { code: \"PNQ-C\", name: \"Pune Central\", city: \"Pune\", country: \"India\" },\n  { code: \"DEL-B\", name: \"Delhi Beta\", city: \"Delhi\", country: \"India\" },\n  { code: \"MUM-D\", name: \"Mumbai Delta\", city: \"Mumbai\", country: \"India\" },\n];\n\n// Mock data for the list of prodcuts with relationship to warehouses\nexport let products: Product[] = [\n  {\n    id: \"P-1001\",\n    name: \"12mm Hex Bolt\",\n    sku: \"HEX-12-100\",\n    warehouse: \"BLR-A\",\n    stock: 180,\n    demand: 120,\n  },\n  {\n    id: \"P-1002\",\n    name: \"Steel Washer\",\n    sku: \"WSR-08-500\",\n    warehouse: \"BLR-A\",\n    stock: 50,\n    demand: 80,\n  },\n  {\n    id: \"P-1003\",\n    name: \"M8 Nut\",\n    sku: \"NUT-08-200\",\n    warehouse: \"PNQ-C\",\n    stock: 80,\n    demand: 80,\n  },\n  {\n    id: \"P-1004\",\n    name: \"Bearing 608ZZ\",\n    sku: \"BRG-608-50\",\n    warehouse: \"DEL-B\",\n    stock: 24,\n    demand: 120,\n  },\n  {\n    id: \"P-1005\",\n    name: \"Steel Rod 10mm\",\n    sku: \"ROD-10-300\",\n    warehouse: \"BLR-A\",\n    stock: 200,\n    demand: 150,\n  },\n  {\n    id: \"P-1006\",\n    name: \"Aluminum Sheet\",\n    sku: \"ALU-SHT-001\",\n    warehouse: \"MUM-D\",\n    stock: 45,\n    demand: 60,\n  },\n  {\n    id: \"P-1007\",\n    name: \"Copper Wire 2mm\",\n    sku: \"COP-WIR-002\",\n    warehouse: \"PNQ-C\",\n    stock: 300,\n    demand: 250,\n  },\n  {\n    id: \"P-1008\",\n    name: \"Plastic Tube\",\n    sku: \"PLA-TUB-005\",\n    warehouse: \"DEL-B\",\n    stock: 15,\n    demand: 90,\n  },\n  {\n    id: \"P-1009\",\n    name: \"Rubber Gasket\",\n    sku: \"RUB-GAS-008\",\n    warehouse: \"BLR-A\",\n    stock: 120,\n    demand: 100,\n  },\n  {\n    id: \"P-1010\",\n    name: \"Stainless Screw\",\n    sku: \"STA-SCR-012\",\n    warehouse: \"MUM-D\",\n    stock: 75,\n    demand: 75,\n  },\n];\n\n// Generate mock KPI data for different date ranges\nexport const generateKPIData = (range: string): KPI[] => {\n  const days = range === \"7d\" ? 7 : range === \"14d\" ? 14 : 30;\n  const kpis: KPI[] = [];\n\n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date();\n    date.setDate(date.getDate() - i);\n\n    // Calculate totals with some variation\n    const baseStock = products.reduce((sum, p) => sum + p.stock, 0);\n    const baseDemand = products.reduce((sum, p) => sum + p.demand, 0);\n\n    // Here I am adding some random variation to make the chart interesting\n    // so we don't have constant KPIs for all dates\n    const stockVariation = Math.floor(Math.random() * 200) - 100;\n    const demandVariation = Math.floor(Math.random() * 150) - 75;\n\n    kpis.push({\n      date: date.toISOString().split(\"T\")[0],\n      stock: Math.max(0, baseStock + stockVariation),\n      demand: Math.max(0, baseDemand + demandVariation),\n    });\n  }\n\n  return kpis;\n};\n"], "names": [], "mappings": ";;;;;;;;AAuBO,MAAM,aAA0B;IACrC;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,SAAS;IACX;IACA;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;QAAQ,SAAS;IAAQ;IACtE;QAAE,MAAM;QAAS,MAAM;QAAc,MAAM;QAAS,SAAS;IAAQ;IACrE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;QAAU,SAAS;IAAQ;CACzE;AAGM,IAAI,WAAsB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,kBAAkB,CAAC;IAC9B,MAAM,OAAO,UAAU,OAAO,IAAI,UAAU,QAAQ,KAAK;IACzD,MAAM,OAAc,EAAE;IAEtB,IAAK,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAK;QAClC,MAAM,OAAO,IAAI;QACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAE9B,uCAAuC;QACvC,MAAM,YAAY,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE;QAC7D,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAE/D,uEAAuE;QACvE,+CAA+C;QAC/C,MAAM,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACzD,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QAE1D,KAAK,IAAI,CAAC;YACR,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,OAAO,KAAK,GAAG,CAAC,GAAG,YAAY;YAC/B,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa;QACnC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/lib/graphql/resolvers.ts"], "sourcesContent": ["import { products, warehouses, generateKPIData, Product } from \"./mockData\";\n\nexport const resolvers = {\n  Query: {\n    products: (\n      _: any,\n      args: { search?: string; status?: string; warehouse?: string }\n    ) => {\n      let filteredProducts = [...products];\n\n      // Filter by search term (name, sku, or id)\n      if (args.search) {\n        const searchTerm = args.search.toLowerCase();\n        filteredProducts = filteredProducts.filter(\n          (product) =>\n            product.name.toLowerCase().includes(searchTerm) ||\n            product.sku.toLowerCase().includes(searchTerm) ||\n            product.id.toLowerCase().includes(searchTerm)\n        );\n      }\n\n      // Filter by warehouse\n      if (args.warehouse && args.warehouse !== \"all\") {\n        filteredProducts = filteredProducts.filter(\n          (product) => product.warehouse === args.warehouse\n        );\n      }\n\n      // Filter by status\n      if (args.status && args.status !== \"all\") {\n        filteredProducts = filteredProducts.filter((product) => {\n          const status = getProductStatus(product);\n          return status.toLowerCase() === args.status!.toLowerCase();\n        });\n      }\n\n      return filteredProducts;\n    },\n\n    warehouses: () => warehouses,\n\n    kpis: (_: any, args: { range: string }) => {\n      return generateKPIData(args.range);\n    },\n  },\n\n  Mutation: {\n    updateDemand: (_: any, args: { id: string; demand: number }) => {\n      const productIndex = products.findIndex((p) => p.id === args.id);\n      if (productIndex === -1) {\n        throw new Error(`Product with id ${args.id} not found`);\n      }\n\n      products[productIndex] = {\n        ...products[productIndex],\n        demand: args.demand,\n      };\n\n      return products[productIndex];\n    },\n\n    transferStock: (\n      _: any,\n      args: { id: string; from: string; to: string; qty: number }\n    ) => {\n      const productIndex = products.findIndex((p) => p.id === args.id);\n      if (productIndex === -1) {\n        throw new Error(`Product with id ${args.id} not found`);\n      }\n\n      const product = products[productIndex];\n\n      // Validate that the product is in the 'from' warehouse\n      if (product.warehouse !== args.from) {\n        throw new Error(`Product is not in warehouse ${args.from}`);\n      }\n\n      // Validate that we have enough stock\n      if (product.stock < args.qty) {\n        throw new Error(\n          `Insufficient stock. Available: ${product.stock}, Requested: ${args.qty}`\n        );\n      }\n\n      // Update the product\n      products[productIndex] = {\n        ...product,\n        warehouse: args.to,\n        stock: product.stock - args.qty,\n      };\n\n      // In a real system, you might also create a new product entry in the destination warehouse\n      // For simplicity, we're just moving the entire product\n\n      return products[productIndex];\n    },\n  },\n};\n\n// Helper function to determine product status\nfunction getProductStatus(product: Product): string {\n  if (product.stock > product.demand) return \"healthy\";\n  if (product.stock === product.demand) return \"low\";\n  return \"critical\";\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,YAAY;IACvB,OAAO;QACL,UAAU,CACR,GACA;YAEA,IAAI,mBAAmB;mBAAI,+IAAQ;aAAC;YAEpC,2CAA2C;YAC3C,IAAI,KAAK,MAAM,EAAE;gBACf,MAAM,aAAa,KAAK,MAAM,CAAC,WAAW;gBAC1C,mBAAmB,iBAAiB,MAAM,CACxC,CAAC,UACC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,QAAQ,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,eACnC,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC;YAExC;YAEA,sBAAsB;YACtB,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,KAAK,OAAO;gBAC9C,mBAAmB,iBAAiB,MAAM,CACxC,CAAC,UAAY,QAAQ,SAAS,KAAK,KAAK,SAAS;YAErD;YAEA,mBAAmB;YACnB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO;gBACxC,mBAAmB,iBAAiB,MAAM,CAAC,CAAC;oBAC1C,MAAM,SAAS,iBAAiB;oBAChC,OAAO,OAAO,WAAW,OAAO,KAAK,MAAM,CAAE,WAAW;gBAC1D;YACF;YAEA,OAAO;QACT;QAEA,YAAY,IAAM,iJAAU;QAE5B,MAAM,CAAC,GAAQ;YACb,OAAO,IAAA,sJAAe,EAAC,KAAK,KAAK;QACnC;IACF;IAEA,UAAU;QACR,cAAc,CAAC,GAAQ;YACrB,MAAM,eAAe,+IAAQ,CAAC,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;YAC/D,IAAI,iBAAiB,CAAC,GAAG;gBACvB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC;YACxD;YAEA,+IAAQ,CAAC,aAAa,GAAG;gBACvB,GAAG,+IAAQ,CAAC,aAAa;gBACzB,QAAQ,KAAK,MAAM;YACrB;YAEA,OAAO,+IAAQ,CAAC,aAAa;QAC/B;QAEA,eAAe,CACb,GACA;YAEA,MAAM,eAAe,+IAAQ,CAAC,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;YAC/D,IAAI,iBAAiB,CAAC,GAAG;gBACvB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC;YACxD;YAEA,MAAM,UAAU,+IAAQ,CAAC,aAAa;YAEtC,uDAAuD;YACvD,IAAI,QAAQ,SAAS,KAAK,KAAK,IAAI,EAAE;gBACnC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC5D;YAEA,qCAAqC;YACrC,IAAI,QAAQ,KAAK,GAAG,KAAK,GAAG,EAAE;gBAC5B,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,QAAQ,KAAK,CAAC,aAAa,EAAE,KAAK,GAAG,EAAE;YAE7E;YAEA,qBAAqB;YACrB,+IAAQ,CAAC,aAAa,GAAG;gBACvB,GAAG,OAAO;gBACV,WAAW,KAAK,EAAE;gBAClB,OAAO,QAAQ,KAAK,GAAG,KAAK,GAAG;YACjC;YAEA,2FAA2F;YAC3F,uDAAuD;YAEvD,OAAO,+IAAQ,CAAC,aAAa;QAC/B;IACF;AACF;AAEA,8CAA8C;AAC9C,SAAS,iBAAiB,OAAgB;IACxC,IAAI,QAAQ,KAAK,GAAG,QAAQ,MAAM,EAAE,OAAO;IAC3C,IAAI,QAAQ,KAAK,KAAK,QAAQ,MAAM,EAAE,OAAO;IAC7C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/app/api/graphql/route.ts"], "sourcesContent": ["import { ApolloServer } from '@apollo/server';\nimport { startServerAndCreateNextHandler } from '@as-integrations/next';\nimport { typeDefs } from '@/lib/graphql/schema';\nimport { resolvers } from '@/lib/graphql/resolvers';\n\nconst server = new ApolloServer({\n  typeDefs,\n  resolvers,\n  introspection: true, // Enable GraphQL Playground in development\n});\n\nconst handler = startServerAndCreateNextHandler(server, {\n  context: async (req, res) => ({ req, res }),\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,SAAS,IAAI,mLAAY,CAAC;IAC9B,UAAA,6IAAQ;IACR,WAAA,iJAAS;IACT,eAAe;AACjB;AAEA,MAAM,UAAU,IAAA,kMAA+B,EAAC,QAAQ;IACtD,SAAS,OAAO,KAAK,MAAQ,CAAC;YAAE;YAAK;QAAI,CAAC;AAC5C", "debugId": null}}]}