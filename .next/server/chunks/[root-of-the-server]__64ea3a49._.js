module.exports = [
"[project]/.next-internal/server/app/api/graphql/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/buffer [external] (buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[project]/src/lib/graphql/schema.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "typeDefs",
    ()=>typeDefs
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2d$tag$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql-tag/lib/index.js [app-route] (ecmascript)");
;
const typeDefs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2d$tag$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["gql"]`
  type Warehouse {
    code: ID!
    name: String!
    city: String!
    country: String!
  }

  type Product {
    id: ID!
    name: String!
    sku: String!
    warehouse: String!
    stock: Int!
    demand: Int!
  }

  type KPI {
    date: String!
    stock: Int!
    demand: Int!
  }

  type Query {
    products(search: String, status: String, warehouse: String): [Product!]!
    warehouses: [Warehouse!]!
    kpis(range: String!): [KPI!]!
  }

  type Mutation {
    updateDemand(id: ID!, demand: Int!): Product!
    transferStock(id: ID!, from: String!, to: String!, qty: Int!): Product!
  }
`;
}),
"[project]/src/lib/graphql/mockData.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "generateKPIData",
    ()=>generateKPIData,
    "products",
    ()=>products,
    "warehouses",
    ()=>warehouses
]);
const warehouses = [
    {
        code: "BLR-A",
        name: "Bangalore Alpha",
        city: "Bangalore",
        country: "India"
    },
    {
        code: "PNQ-C",
        name: "Pune Central",
        city: "Pune",
        country: "India"
    },
    {
        code: "DEL-B",
        name: "Delhi Beta",
        city: "Delhi",
        country: "India"
    },
    {
        code: "MUM-D",
        name: "Mumbai Delta",
        city: "Mumbai",
        country: "India"
    }
];
let products = [
    {
        id: "P-1001",
        name: "12mm Hex Bolt",
        sku: "HEX-12-100",
        warehouse: "BLR-A",
        stock: 180,
        demand: 120
    },
    {
        id: "P-1002",
        name: "Steel Washer",
        sku: "WSR-08-500",
        warehouse: "BLR-A",
        stock: 50,
        demand: 80
    },
    {
        id: "P-1003",
        name: "M8 Nut",
        sku: "NUT-08-200",
        warehouse: "PNQ-C",
        stock: 80,
        demand: 80
    },
    {
        id: "P-1004",
        name: "Bearing 608ZZ",
        sku: "BRG-608-50",
        warehouse: "DEL-B",
        stock: 24,
        demand: 120
    },
    {
        id: "P-1005",
        name: "Steel Rod 10mm",
        sku: "ROD-10-300",
        warehouse: "BLR-A",
        stock: 200,
        demand: 150
    },
    {
        id: "P-1006",
        name: "Aluminum Sheet",
        sku: "ALU-SHT-001",
        warehouse: "MUM-D",
        stock: 45,
        demand: 60
    },
    {
        id: "P-1007",
        name: "Copper Wire 2mm",
        sku: "COP-WIR-002",
        warehouse: "PNQ-C",
        stock: 300,
        demand: 250
    },
    {
        id: "P-1008",
        name: "Plastic Tube",
        sku: "PLA-TUB-005",
        warehouse: "DEL-B",
        stock: 15,
        demand: 90
    },
    {
        id: "P-1009",
        name: "Rubber Gasket",
        sku: "RUB-GAS-008",
        warehouse: "BLR-A",
        stock: 120,
        demand: 100
    },
    {
        id: "P-1010",
        name: "Stainless Screw",
        sku: "STA-SCR-012",
        warehouse: "MUM-D",
        stock: 75,
        demand: 75
    }
];
const generateKPIData = (range)=>{
    const days = range === "7d" ? 7 : range === "14d" ? 14 : 30;
    const kpis = [];
    for(let i = days - 1; i >= 0; i--){
        const date = new Date();
        date.setDate(date.getDate() - i);
        // Calculate totals with some variation
        const baseStock = products.reduce((sum, p)=>sum + p.stock, 0);
        const baseDemand = products.reduce((sum, p)=>sum + p.demand, 0);
        // Here I am adding some random variation to make the chart interesting
        // so we don't have constant KPIs for all dates
        const stockVariation = Math.floor(Math.random() * 200) - 100;
        const demandVariation = Math.floor(Math.random() * 150) - 75;
        kpis.push({
            date: date.toISOString().split("T")[0],
            stock: Math.max(0, baseStock + stockVariation),
            demand: Math.max(0, baseDemand + demandVariation)
        });
    }
    return kpis;
};
}),
"[project]/src/lib/graphql/resolvers.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "resolvers",
    ()=>resolvers
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/graphql/mockData.ts [app-route] (ecmascript)");
;
const resolvers = {
    Query: {
        products: (_, args)=>{
            let filteredProducts = [
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"]
            ];
            // Filter by search term (name, sku, or id)
            if (args.search) {
                const searchTerm = args.search.toLowerCase();
                filteredProducts = filteredProducts.filter((product)=>product.name.toLowerCase().includes(searchTerm) || product.sku.toLowerCase().includes(searchTerm) || product.id.toLowerCase().includes(searchTerm));
            }
            // Filter by warehouse
            if (args.warehouse && args.warehouse !== "all") {
                filteredProducts = filteredProducts.filter((product)=>product.warehouse === args.warehouse);
            }
            // Filter by status
            if (args.status && args.status !== "all") {
                filteredProducts = filteredProducts.filter((product)=>{
                    const status = getProductStatus(product);
                    return status.toLowerCase() === args.status.toLowerCase();
                });
            }
            return filteredProducts;
        },
        warehouses: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["warehouses"],
        kpis: (_, args)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateKPIData"])(args.range);
        }
    },
    Mutation: {
        updateDemand: (_, args)=>{
            const productIndex = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"].findIndex((p)=>p.id === args.id);
            if (productIndex === -1) {
                throw new Error(`Product with id ${args.id} not found`);
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"][productIndex] = {
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"][productIndex],
                demand: args.demand
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"][productIndex];
        },
        transferStock: (_, args)=>{
            const productIndex = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"].findIndex((p)=>p.id === args.id);
            if (productIndex === -1) {
                throw new Error(`Product with id ${args.id} not found`);
            }
            const product = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"][productIndex];
            // Validate that the product is in the 'from' warehouse
            if (product.warehouse !== args.from) {
                throw new Error(`Product is not in warehouse ${args.from}`);
            }
            // Validate that we have enough stock
            if (product.stock < args.qty) {
                throw new Error(`Insufficient stock. Available: ${product.stock}, Requested: ${args.qty}`);
            }
            // Update the product
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"][productIndex] = {
                ...product,
                warehouse: args.to,
                stock: product.stock - args.qty
            };
            // In a real system, you might also create a new product entry in the destination warehouse
            // For simplicity, we're just moving the entire product
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["products"][productIndex];
        }
    }
};
// Helper function to determine product status
function getProductStatus(product) {
    if (product.stock > product.demand) return "healthy";
    if (product.stock === product.demand) return "low";
    return "critical";
}
}),
"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>handler,
    "POST",
    ()=>handler
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$as$2d$integrations$2f$next$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@as-integrations/next/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/graphql/schema.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$resolvers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/graphql/resolvers.ts [app-route] (ecmascript)");
;
;
;
;
const server = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServer"]({
    typeDefs: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["typeDefs"],
    resolvers: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$resolvers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolvers"],
    introspection: true
});
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$as$2d$integrations$2f$next$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["startServerAndCreateNextHandler"])(server, {
    context: async (req, res)=>({
            req,
            res
        })
});
;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__64ea3a49._.js.map