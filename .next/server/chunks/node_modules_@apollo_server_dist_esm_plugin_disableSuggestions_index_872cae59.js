module.exports = [
"[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ApolloServerPluginDisableSuggestions",
    ()=>ApolloServerPluginDisableSuggestions
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalPlugin.js [app-route] (ecmascript)");
;
function ApolloServerPluginDisableSuggestions() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["internalPlugin"])({
        __internal_plugin_id__: 'DisableSuggestions',
        __is_disabled_plugin__: false,
        async requestDidStart () {
            return {
                async validationDidStart () {
                    return async (validationErrors)=>{
                        validationErrors?.forEach((error)=>{
                            error.message = error.message.replace(/ ?Did you mean(.+?)\?$/, '');
                        });
                    };
                }
            };
        }
    });
} //# sourceMappingURL=index.js.map
}),
];

//# sourceMappingURL=node_modules_%40apollo_server_dist_esm_plugin_disableSuggestions_index_872cae59.js.map