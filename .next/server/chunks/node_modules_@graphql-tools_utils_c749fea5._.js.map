{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/helpers.js"], "sourcesContent": ["import { parse } from 'graphql';\nconst URL_REGEXP = /^(https?|wss?|file):\\/\\//;\n/**\n * Checks if the given string is a valid URL.\n *\n * @param str - The string to validate as a URL\n * @returns A boolean indicating whether the string is a valid URL\n *\n * @remarks\n * This function first attempts to use the `URL.canParse` method if available.\n * If not, it falls back to creating a new `URL` object to validate the string.\n */\nexport function isUrl(str) {\n    if (typeof str !== 'string') {\n        return false;\n    }\n    if (!URL_REGEXP.test(str)) {\n        return false;\n    }\n    if (URL.canParse) {\n        return URL.canParse(str);\n    }\n    try {\n        const url = new URL(str);\n        return !!url;\n    }\n    catch (e) {\n        return false;\n    }\n}\nexport const asArray = (fns) => (Array.isArray(fns) ? fns : fns ? [fns] : []);\nconst invalidDocRegex = /\\.[a-z0-9]+$/i;\n/**\n * Determines if a given input is a valid GraphQL document string.\n *\n * @param str - The input to validate as a GraphQL document\n * @returns A boolean indicating whether the input is a valid GraphQL document string\n *\n * @remarks\n * This function performs several validation checks:\n * - Ensures the input is a string\n * - Filters out strings with invalid document extensions\n * - Excludes URLs\n * - Attempts to parse the string as a GraphQL document\n *\n * @throws {Error} If the document fails to parse and is empty except GraphQL comments\n */\nexport function isDocumentString(str) {\n    if (typeof str !== 'string') {\n        return false;\n    }\n    // XXX: is-valid-path or is-glob treat SDL as a valid path\n    // (`scalar Date` for example)\n    // this why checking the extension is fast enough\n    // and prevent from parsing the string in order to find out\n    // if the string is a SDL\n    if (invalidDocRegex.test(str) || isUrl(str)) {\n        return false;\n    }\n    try {\n        parse(str);\n        return true;\n    }\n    catch (e) {\n        if (!e.message.includes('EOF') &&\n            str.replace(/(\\#[^*]*)/g, '').trim() !== '' &&\n            str.includes(' ')) {\n            throw new Error(`Failed to parse the GraphQL document. ${e.message}\\n${str}`);\n        }\n    }\n    return false;\n}\nconst invalidPathRegex = /[‘“!%^<>`\\n]/;\n/**\n * Checkes whether the `str` contains any path illegal characters.\n *\n * A string may sometimes look like a path but is not (like an SDL of a simple\n * GraphQL schema). To make sure we don't yield false-positives in such cases,\n * we disallow new lines in paths (even though most Unix systems support new\n * lines in file names).\n */\nexport function isValidPath(str) {\n    return typeof str === 'string' && !invalidPathRegex.test(str);\n}\nexport function compareStrings(a, b) {\n    if (String(a) < String(b)) {\n        return -1;\n    }\n    if (String(a) > String(b)) {\n        return 1;\n    }\n    return 0;\n}\nexport function nodeToString(a) {\n    let name;\n    if ('alias' in a) {\n        name = a.alias?.value;\n    }\n    if (name == null && 'name' in a) {\n        name = a.name?.value;\n    }\n    if (name == null) {\n        name = a.kind;\n    }\n    return name;\n}\nexport function compareNodes(a, b, customFn) {\n    const aStr = nodeToString(a);\n    const bStr = nodeToString(b);\n    if (typeof customFn === 'function') {\n        return customFn(aStr, bStr);\n    }\n    return compareStrings(aStr, bStr);\n}\nexport function isSome(input) {\n    return input != null;\n}\nexport function assertSome(input, message = 'Value should be something') {\n    if (input == null) {\n        throw new Error(message);\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AACA,MAAM,aAAa;AAWZ,SAAS,MAAM,GAAG;IACrB,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;IACX;IACA,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM;QACvB,OAAO;IACX;IACA,IAAI,IAAI,QAAQ,EAAE;QACd,OAAO,IAAI,QAAQ,CAAC;IACxB;IACA,IAAI;QACA,MAAM,MAAM,IAAI,IAAI;QACpB,OAAO,CAAC,CAAC;IACb,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACO,MAAM,UAAU,CAAC,MAAS,MAAM,OAAO,CAAC,OAAO,MAAM,MAAM;QAAC;KAAI,GAAG,EAAE;AAC5E,MAAM,kBAAkB;AAgBjB,SAAS,iBAAiB,GAAG;IAChC,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;IACX;IACA,0DAA0D;IAC1D,8BAA8B;IAC9B,iDAAiD;IACjD,2DAA2D;IAC3D,yBAAyB;IACzB,IAAI,gBAAgB,IAAI,CAAC,QAAQ,MAAM,MAAM;QACzC,OAAO;IACX;IACA,IAAI;QACA,IAAA,yJAAK,EAAC;QACN,OAAO;IACX,EACA,OAAO,GAAG;QACN,IAAI,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,UACpB,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,OAAO,MACzC,IAAI,QAAQ,CAAC,MAAM;YACnB,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK;QAChF;IACJ;IACA,OAAO;AACX;AACA,MAAM,mBAAmB;AASlB,SAAS,YAAY,GAAG;IAC3B,OAAO,OAAO,QAAQ,YAAY,CAAC,iBAAiB,IAAI,CAAC;AAC7D;AACO,SAAS,eAAe,CAAC,EAAE,CAAC;IAC/B,IAAI,OAAO,KAAK,OAAO,IAAI;QACvB,OAAO,CAAC;IACZ;IACA,IAAI,OAAO,KAAK,OAAO,IAAI;QACvB,OAAO;IACX;IACA,OAAO;AACX;AACO,SAAS,aAAa,CAAC;IAC1B,IAAI;IACJ,IAAI,WAAW,GAAG;QACd,OAAO,EAAE,KAAK,EAAE;IACpB;IACA,IAAI,QAAQ,QAAQ,UAAU,GAAG;QAC7B,OAAO,EAAE,IAAI,EAAE;IACnB;IACA,IAAI,QAAQ,MAAM;QACd,OAAO,EAAE,IAAI;IACjB;IACA,OAAO;AACX;AACO,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,QAAQ;IACvC,MAAM,OAAO,aAAa;IAC1B,MAAM,OAAO,aAAa;IAC1B,IAAI,OAAO,aAAa,YAAY;QAChC,OAAO,SAAS,MAAM;IAC1B;IACA,OAAO,eAAe,MAAM;AAChC;AACO,SAAS,OAAO,KAAK;IACxB,OAAO,SAAS;AACpB;AACO,SAAS,WAAW,KAAK,EAAE,UAAU,2BAA2B;IACnE,IAAI,SAAS,MAAM;QACf,MAAM,IAAI,MAAM;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/mergeDeep.js"], "sourcesContent": ["import { isSome } from './helpers.js';\nexport function mergeDeep(sources, respectPrototype = false, respectArrays = false, respectArrayLength = false) {\n    let expectedLength;\n    let allArrays = true;\n    const areArraysInTheSameLength = sources.every(source => {\n        if (Array.isArray(source)) {\n            if (expectedLength === undefined) {\n                expectedLength = source.length;\n                return true;\n            }\n            else if (expectedLength === source.length) {\n                return true;\n            }\n        }\n        else {\n            allArrays = false;\n        }\n        return false;\n    });\n    if (respectArrayLength && areArraysInTheSameLength) {\n        return new Array(expectedLength).fill(null).map((_, index) => mergeDeep(sources.map(source => source[index]), respectPrototype, respectArrays, respectArrayLength));\n    }\n    if (allArrays) {\n        return sources.flat(1);\n    }\n    let output;\n    let firstObjectSource;\n    if (respectPrototype) {\n        firstObjectSource = sources.find(source => isObject(source));\n        if (output == null) {\n            output = {};\n        }\n        if (firstObjectSource) {\n            Object.setPrototypeOf(output, Object.create(Object.getPrototypeOf(firstObjectSource)));\n        }\n    }\n    for (const source of sources) {\n        if (isObject(source)) {\n            if (firstObjectSource) {\n                const outputPrototype = Object.getPrototypeOf(output);\n                const sourcePrototype = Object.getPrototypeOf(source);\n                if (sourcePrototype) {\n                    for (const key of Object.getOwnPropertyNames(sourcePrototype)) {\n                        const descriptor = Object.getOwnPropertyDescriptor(sourcePrototype, key);\n                        if (isSome(descriptor)) {\n                            Object.defineProperty(outputPrototype, key, descriptor);\n                        }\n                    }\n                }\n            }\n            for (const key in source) {\n                if (output == null) {\n                    output = {};\n                }\n                if (key in output) {\n                    output[key] = mergeDeep([output[key], source[key]], respectPrototype, respectArrays, respectArrayLength);\n                }\n                else {\n                    output[key] = source[key];\n                }\n            }\n        }\n        else if (Array.isArray(source)) {\n            if (!Array.isArray(output)) {\n                output = source;\n            }\n            else {\n                output = mergeDeep([output, source], respectPrototype, respectArrays, respectArrayLength);\n            }\n        }\n        else {\n            output = source;\n        }\n    }\n    return output;\n}\nfunction isObject(item) {\n    return item && typeof item === 'object' && !Array.isArray(item);\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,UAAU,OAAO,EAAE,mBAAmB,KAAK,EAAE,gBAAgB,KAAK,EAAE,qBAAqB,KAAK;IAC1G,IAAI;IACJ,IAAI,YAAY;IAChB,MAAM,2BAA2B,QAAQ,KAAK,CAAC,CAAA;QAC3C,IAAI,MAAM,OAAO,CAAC,SAAS;YACvB,IAAI,mBAAmB,WAAW;gBAC9B,iBAAiB,OAAO,MAAM;gBAC9B,OAAO;YACX,OACK,IAAI,mBAAmB,OAAO,MAAM,EAAE;gBACvC,OAAO;YACX;QACJ,OACK;YACD,YAAY;QAChB;QACA,OAAO;IACX;IACA,IAAI,sBAAsB,0BAA0B;QAChD,OAAO,IAAI,MAAM,gBAAgB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAU,UAAU,QAAQ,GAAG,CAAC,CAAA,SAAU,MAAM,CAAC,MAAM,GAAG,kBAAkB,eAAe;IACnJ;IACA,IAAI,WAAW;QACX,OAAO,QAAQ,IAAI,CAAC;IACxB;IACA,IAAI;IACJ,IAAI;IACJ,IAAI,kBAAkB;QAClB,oBAAoB,QAAQ,IAAI,CAAC,CAAA,SAAU,SAAS;QACpD,IAAI,UAAU,MAAM;YAChB,SAAS,CAAC;QACd;QACA,IAAI,mBAAmB;YACnB,OAAO,cAAc,CAAC,QAAQ,OAAO,MAAM,CAAC,OAAO,cAAc,CAAC;QACtE;IACJ;IACA,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,SAAS,SAAS;YAClB,IAAI,mBAAmB;gBACnB,MAAM,kBAAkB,OAAO,cAAc,CAAC;gBAC9C,MAAM,kBAAkB,OAAO,cAAc,CAAC;gBAC9C,IAAI,iBAAiB;oBACjB,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,iBAAkB;wBAC3D,MAAM,aAAa,OAAO,wBAAwB,CAAC,iBAAiB;wBACpE,IAAI,IAAA,yKAAM,EAAC,aAAa;4BACpB,OAAO,cAAc,CAAC,iBAAiB,KAAK;wBAChD;oBACJ;gBACJ;YACJ;YACA,IAAK,MAAM,OAAO,OAAQ;gBACtB,IAAI,UAAU,MAAM;oBAChB,SAAS,CAAC;gBACd;gBACA,IAAI,OAAO,QAAQ;oBACf,MAAM,CAAC,IAAI,GAAG,UAAU;wBAAC,MAAM,CAAC,IAAI;wBAAE,MAAM,CAAC,IAAI;qBAAC,EAAE,kBAAkB,eAAe;gBACzF,OACK;oBACD,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;gBAC7B;YACJ;QACJ,OACK,IAAI,MAAM,OAAO,CAAC,SAAS;YAC5B,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;gBACxB,SAAS;YACb,OACK;gBACD,SAAS,UAAU;oBAAC;oBAAQ;iBAAO,EAAE,kBAAkB,eAAe;YAC1E;QACJ,OACK;YACD,SAAS;QACb;IACJ;IACA,OAAO;AACX;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,OAAO,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/astFromType.js"], "sourcesContent": ["import { inspect } from 'cross-inspect';\nimport { isListType, isNonNullType, Kind } from 'graphql';\nexport function astFromType(type) {\n    if (isNonNullType(type)) {\n        const innerType = astFromType(type.ofType);\n        if (innerType.kind === Kind.NON_NULL_TYPE) {\n            throw new Error(`Invalid type node ${inspect(type)}. Inner type of non-null type cannot be a non-null type.`);\n        }\n        return {\n            kind: Kind.NON_NULL_TYPE,\n            type: innerType,\n        };\n    }\n    else if (isListType(type)) {\n        return {\n            kind: Kind.LIST_TYPE,\n            type: astFromType(type.ofType),\n        };\n    }\n    return {\n        kind: Kind.NAMED_TYPE,\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n    };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AACO,SAAS,YAAY,IAAI;IAC5B,IAAI,IAAA,iKAAa,EAAC,OAAO;QACrB,MAAM,YAAY,YAAY,KAAK,MAAM;QACzC,IAAI,UAAU,IAAI,KAAK,uJAAI,CAAC,aAAa,EAAE;YACvC,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,IAAA,6JAAO,EAAC,MAAM,wDAAwD,CAAC;QAChH;QACA,OAAO;YACH,MAAM,uJAAI,CAAC,aAAa;YACxB,MAAM;QACV;IACJ,OACK,IAAI,IAAA,8JAAU,EAAC,OAAO;QACvB,OAAO;YACH,MAAM,uJAAI,CAAC,SAAS;YACpB,MAAM,YAAY,KAAK,MAAM;QACjC;IACJ;IACA,OAAO;QACH,MAAM,uJAAI,CAAC,UAAU;QACrB,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/astFromValueUntyped.js"], "sourcesContent": ["import { Kind } from 'graphql';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using the following mapping.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String               |\n * | Number        | Int / Float          |\n * | BigInt        | Int                  |\n * | null          | NullValue            |\n *\n */\nexport function astFromValueUntyped(value) {\n    // only explicit null, not undefined, NaN\n    if (value === null) {\n        return { kind: Kind.NULL };\n    }\n    // undefined\n    if (value === undefined) {\n        return null;\n    }\n    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n    // the value is not an array, convert the value using the list's item type.\n    if (Array.isArray(value)) {\n        const valuesNodes = [];\n        for (const item of value) {\n            const itemNode = astFromValueUntyped(item);\n            if (itemNode != null) {\n                valuesNodes.push(itemNode);\n            }\n        }\n        return { kind: Kind.LIST, values: valuesNodes };\n    }\n    if (typeof value === 'object') {\n        if (value?.toJSON) {\n            return astFromValueUntyped(value.toJSON());\n        }\n        const fieldNodes = [];\n        for (const fieldName in value) {\n            const fieldValue = value[fieldName];\n            const ast = astFromValueUntyped(fieldValue);\n            if (ast) {\n                fieldNodes.push({\n                    kind: Kind.OBJECT_FIELD,\n                    name: { kind: Kind.NAME, value: fieldName },\n                    value: ast,\n                });\n            }\n        }\n        return { kind: Kind.OBJECT, fields: fieldNodes };\n    }\n    // Others serialize based on their corresponding JavaScript scalar types.\n    if (typeof value === 'boolean') {\n        return { kind: Kind.BOOLEAN, value };\n    }\n    if (typeof value === 'bigint') {\n        return { kind: Kind.INT, value: String(value) };\n    }\n    // JavaScript numbers can be Int or Float values.\n    if (typeof value === 'number' && isFinite(value)) {\n        const stringNum = String(value);\n        return integerStringRegExp.test(stringNum)\n            ? { kind: Kind.INT, value: stringNum }\n            : { kind: Kind.FLOAT, value: stringNum };\n    }\n    if (typeof value === 'string') {\n        return { kind: Kind.STRING, value };\n    }\n    throw new TypeError(`Cannot convert value to AST: ${value}.`);\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n"], "names": [], "mappings": ";;;;AAAA;;AAiBO,SAAS,oBAAoB,KAAK;IACrC,yCAAyC;IACzC,IAAI,UAAU,MAAM;QAChB,OAAO;YAAE,MAAM,uJAAI,CAAC,IAAI;QAAC;IAC7B;IACA,YAAY;IACZ,IAAI,UAAU,WAAW;QACrB,OAAO;IACX;IACA,8EAA8E;IAC9E,2EAA2E;IAC3E,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,MAAM,cAAc,EAAE;QACtB,KAAK,MAAM,QAAQ,MAAO;YACtB,MAAM,WAAW,oBAAoB;YACrC,IAAI,YAAY,MAAM;gBAClB,YAAY,IAAI,CAAC;YACrB;QACJ;QACA,OAAO;YAAE,MAAM,uJAAI,CAAC,IAAI;YAAE,QAAQ;QAAY;IAClD;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,IAAI,OAAO,QAAQ;YACf,OAAO,oBAAoB,MAAM,MAAM;QAC3C;QACA,MAAM,aAAa,EAAE;QACrB,IAAK,MAAM,aAAa,MAAO;YAC3B,MAAM,aAAa,KAAK,CAAC,UAAU;YACnC,MAAM,MAAM,oBAAoB;YAChC,IAAI,KAAK;gBACL,WAAW,IAAI,CAAC;oBACZ,MAAM,uJAAI,CAAC,YAAY;oBACvB,MAAM;wBAAE,MAAM,uJAAI,CAAC,IAAI;wBAAE,OAAO;oBAAU;oBAC1C,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;YAAE,MAAM,uJAAI,CAAC,MAAM;YAAE,QAAQ;QAAW;IACnD;IACA,yEAAyE;IACzE,IAAI,OAAO,UAAU,WAAW;QAC5B,OAAO;YAAE,MAAM,uJAAI,CAAC,OAAO;YAAE;QAAM;IACvC;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;YAAE,MAAM,uJAAI,CAAC,GAAG;YAAE,OAAO,OAAO;QAAO;IAClD;IACA,iDAAiD;IACjD,IAAI,OAAO,UAAU,YAAY,SAAS,QAAQ;QAC9C,MAAM,YAAY,OAAO;QACzB,OAAO,oBAAoB,IAAI,CAAC,aAC1B;YAAE,MAAM,uJAAI,CAAC,GAAG;YAAE,OAAO;QAAU,IACnC;YAAE,MAAM,uJAAI,CAAC,KAAK;YAAE,OAAO;QAAU;IAC/C;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;YAAE,MAAM,uJAAI,CAAC,MAAM;YAAE;QAAM;IACtC;IACA,MAAM,IAAI,UAAU,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;AAChE;AACA;;;;CAIC,GACD,MAAM,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/jsutils.js"], "sourcesContent": ["import { handleMaybePromise, isPromise } from '@whatwg-node/promise-helpers';\nexport function isIterableObject(value) {\n    return value != null && typeof value === 'object' && Symbol.iterator in value;\n}\nexport function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport { isPromise };\nexport function promiseReduce(values, callbackFn, initialValue) {\n    let accumulator = initialValue;\n    for (const value of values) {\n        accumulator = handleMaybePromise(() => accumulator, resolved => callbackFn(resolved, value));\n    }\n    return accumulator;\n}\nexport function hasOwnProperty(obj, prop) {\n    return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,SAAS,iBAAiB,KAAK;IAClC,OAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,QAAQ,IAAI;AAC5E;AACO,SAAS,aAAa,KAAK;IAC9B,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;;AAEO,SAAS,cAAc,MAAM,EAAE,UAAU,EAAE,YAAY;IAC1D,IAAI,cAAc;IAClB,KAAK,MAAM,SAAS,OAAQ;QACxB,cAAc,IAAA,8LAAkB,EAAC,IAAM,aAAa,CAAA,WAAY,WAAW,UAAU;IACzF;IACA,OAAO;AACX;AACO,SAAS,eAAe,GAAG,EAAE,IAAI;IACpC,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/astFromValue.js"], "sourcesContent": ["import { inspect } from 'cross-inspect';\nimport { isEnumType, isInputObjectType, isLeafType, isListType, isNonNullType, Kind, } from 'graphql';\nimport { astFromValueUntyped } from './astFromValueUntyped.js';\nimport { isIterableObject, isObjectLike } from './jsutils.js';\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using suggested GraphQLInputType. For example:\n *\n *     astFromValue(\"value\", GraphQLString)\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * JavaScript values.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String / Enum Value  |\n * | Number        | Int / Float          |\n * | BigInt        | Int                  |\n * | Unknown       | Enum Value           |\n * | null          | NullValue            |\n *\n */\nexport function astFromValue(value, type) {\n    if (isNonNullType(type)) {\n        const astValue = astFromValue(value, type.ofType);\n        if (astValue?.kind === Kind.NULL) {\n            return null;\n        }\n        return astValue;\n    }\n    // only explicit null, not undefined, NaN\n    if (value === null) {\n        return { kind: Kind.NULL };\n    }\n    // undefined\n    if (value === undefined) {\n        return null;\n    }\n    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n    // the value is not an array, convert the value using the list's item type.\n    if (isListType(type)) {\n        const itemType = type.ofType;\n        if (isIterableObject(value)) {\n            const valuesNodes = [];\n            for (const item of value) {\n                const itemNode = astFromValue(item, itemType);\n                if (itemNode != null) {\n                    valuesNodes.push(itemNode);\n                }\n            }\n            return { kind: Kind.LIST, values: valuesNodes };\n        }\n        return astFromValue(value, itemType);\n    }\n    // Populate the fields of the input object by creating ASTs from each value\n    // in the JavaScript object according to the fields in the input type.\n    if (isInputObjectType(type)) {\n        if (!isObjectLike(value)) {\n            return null;\n        }\n        const fieldNodes = [];\n        for (const field of Object.values(type.getFields())) {\n            const fieldValue = astFromValue(value[field.name], field.type);\n            if (fieldValue) {\n                fieldNodes.push({\n                    kind: Kind.OBJECT_FIELD,\n                    name: { kind: Kind.NAME, value: field.name },\n                    value: fieldValue,\n                });\n            }\n        }\n        return { kind: Kind.OBJECT, fields: fieldNodes };\n    }\n    if (isLeafType(type)) {\n        // Since value is an internally represented value, it must be serialized\n        // to an externally represented value before converting into an AST.\n        const serialized = type.serialize(value);\n        if (serialized == null) {\n            return null;\n        }\n        if (isEnumType(type)) {\n            return { kind: Kind.ENUM, value: serialized };\n        }\n        // ID types can use Int literals.\n        if (type.name === 'ID' &&\n            typeof serialized === 'string' &&\n            integerStringRegExp.test(serialized)) {\n            return { kind: Kind.INT, value: serialized };\n        }\n        return astFromValueUntyped(serialized);\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible types have been considered.\n    console.assert(false, 'Unexpected input type: ' + inspect(type));\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAuBO,SAAS,aAAa,KAAK,EAAE,IAAI;IACpC,IAAI,IAAA,iKAAa,EAAC,OAAO;QACrB,MAAM,WAAW,aAAa,OAAO,KAAK,MAAM;QAChD,IAAI,UAAU,SAAS,uJAAI,CAAC,IAAI,EAAE;YAC9B,OAAO;QACX;QACA,OAAO;IACX;IACA,yCAAyC;IACzC,IAAI,UAAU,MAAM;QAChB,OAAO;YAAE,MAAM,uJAAI,CAAC,IAAI;QAAC;IAC7B;IACA,YAAY;IACZ,IAAI,UAAU,WAAW;QACrB,OAAO;IACX;IACA,8EAA8E;IAC9E,2EAA2E;IAC3E,IAAI,IAAA,8JAAU,EAAC,OAAO;QAClB,MAAM,WAAW,KAAK,MAAM;QAC5B,IAAI,IAAA,mMAAgB,EAAC,QAAQ;YACzB,MAAM,cAAc,EAAE;YACtB,KAAK,MAAM,QAAQ,MAAO;gBACtB,MAAM,WAAW,aAAa,MAAM;gBACpC,IAAI,YAAY,MAAM;oBAClB,YAAY,IAAI,CAAC;gBACrB;YACJ;YACA,OAAO;gBAAE,MAAM,uJAAI,CAAC,IAAI;gBAAE,QAAQ;YAAY;QAClD;QACA,OAAO,aAAa,OAAO;IAC/B;IACA,2EAA2E;IAC3E,sEAAsE;IACtE,IAAI,IAAA,qKAAiB,EAAC,OAAO;QACzB,IAAI,CAAC,IAAA,+LAAY,EAAC,QAAQ;YACtB,OAAO;QACX;QACA,MAAM,aAAa,EAAE;QACrB,KAAK,MAAM,SAAS,OAAO,MAAM,CAAC,KAAK,SAAS,IAAK;YACjD,MAAM,aAAa,aAAa,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,MAAM,IAAI;YAC7D,IAAI,YAAY;gBACZ,WAAW,IAAI,CAAC;oBACZ,MAAM,uJAAI,CAAC,YAAY;oBACvB,MAAM;wBAAE,MAAM,uJAAI,CAAC,IAAI;wBAAE,OAAO,MAAM,IAAI;oBAAC;oBAC3C,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;YAAE,MAAM,uJAAI,CAAC,MAAM;YAAE,QAAQ;QAAW;IACnD;IACA,IAAI,IAAA,8JAAU,EAAC,OAAO;QAClB,wEAAwE;QACxE,oEAAoE;QACpE,MAAM,aAAa,KAAK,SAAS,CAAC;QAClC,IAAI,cAAc,MAAM;YACpB,OAAO;QACX;QACA,IAAI,IAAA,8JAAU,EAAC,OAAO;YAClB,OAAO;gBAAE,MAAM,uJAAI,CAAC,IAAI;gBAAE,OAAO;YAAW;QAChD;QACA,iCAAiC;QACjC,IAAI,KAAK,IAAI,KAAK,QACd,OAAO,eAAe,YACtB,oBAAoB,IAAI,CAAC,aAAa;YACtC,OAAO;gBAAE,MAAM,uJAAI,CAAC,GAAG;gBAAE,OAAO;YAAW;QAC/C;QACA,OAAO,IAAA,kMAAmB,EAAC;IAC/B;IACA,oBAAoB,GACpB,0DAA0D;IAC1D,QAAQ,MAAM,CAAC,OAAO,4BAA4B,IAAA,6JAAO,EAAC;AAC9D;AACA;;;;CAIC,GACD,MAAM,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/descriptionFromObject.js"], "sourcesContent": ["import { Kind } from 'graphql';\nexport function getDescriptionNode(obj) {\n    if (obj.astNode?.description) {\n        return {\n            ...obj.astNode.description,\n            block: true,\n        };\n    }\n    if (obj.description) {\n        return {\n            kind: Kind.STRING,\n            value: obj.description,\n            block: true,\n        };\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,mBAAmB,GAAG;IAClC,IAAI,IAAI,OAAO,EAAE,aAAa;QAC1B,OAAO;YACH,GAAG,IAAI,OAAO,CAAC,WAAW;YAC1B,OAAO;QACX;IACJ;IACA,IAAI,IAAI,WAAW,EAAE;QACjB,OAAO;YACH,MAAM,uJAAI,CAAC,MAAM;YACjB,OAAO,IAAI,WAAW;YACtB,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/errors.js"], "sourcesContent": ["import { GraphQLError, versionInfo } from 'graphql';\nconst possibleGraphQLErrorProperties = [\n    'message',\n    'locations',\n    'path',\n    'nodes',\n    'source',\n    'positions',\n    'originalError',\n    'name',\n    'stack',\n    'extensions',\n];\nfunction isGraphQLErrorLike(error) {\n    return (error != null &&\n        typeof error === 'object' &&\n        Object.keys(error).every(key => possibleGraphQLErrorProperties.includes(key)));\n}\nexport function createGraphQLError(message, options) {\n    if (options?.originalError &&\n        !(options.originalError instanceof Error) &&\n        isGraphQLErrorLike(options.originalError)) {\n        options.originalError = createGraphQLError(options.originalError.message, options.originalError);\n    }\n    if (versionInfo.major >= 17) {\n        return new GraphQLError(message, options);\n    }\n    return new GraphQLError(message, options?.nodes, options?.source, options?.positions, options?.path, options?.originalError, options?.extensions);\n}\nexport function relocatedError(originalError, path) {\n    return createGraphQLError(originalError.message, {\n        nodes: originalError.nodes,\n        source: originalError.source,\n        positions: originalError.positions,\n        path: path == null ? originalError.path : path,\n        originalError,\n        extensions: originalError.extensions,\n    });\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AACA,MAAM,iCAAiC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,mBAAmB,KAAK;IAC7B,OAAQ,SAAS,QACb,OAAO,UAAU,YACjB,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAA,MAAO,+BAA+B,QAAQ,CAAC;AAChF;AACO,SAAS,mBAAmB,OAAO,EAAE,OAAO;IAC/C,IAAI,SAAS,iBACT,CAAC,CAAC,QAAQ,aAAa,YAAY,KAAK,KACxC,mBAAmB,QAAQ,aAAa,GAAG;QAC3C,QAAQ,aAAa,GAAG,mBAAmB,QAAQ,aAAa,CAAC,OAAO,EAAE,QAAQ,aAAa;IACnG;IACA,IAAI,oJAAW,CAAC,KAAK,IAAI,IAAI;QACzB,OAAO,IAAI,mKAAY,CAAC,SAAS;IACrC;IACA,OAAO,IAAI,mKAAY,CAAC,SAAS,SAAS,OAAO,SAAS,QAAQ,SAAS,WAAW,SAAS,MAAM,SAAS,eAAe,SAAS;AAC1I;AACO,SAAS,eAAe,aAAa,EAAE,IAAI;IAC9C,OAAO,mBAAmB,cAAc,OAAO,EAAE;QAC7C,OAAO,cAAc,KAAK;QAC1B,QAAQ,cAAc,MAAM;QAC5B,WAAW,cAAc,SAAS;QAClC,MAAM,QAAQ,OAAO,cAAc,IAAI,GAAG;QAC1C;QACA,YAAY,cAAc,UAAU;IACxC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/getArgumentValues.js"], "sourcesContent": ["import { inspect } from 'cross-inspect';\nimport { isNonNullType, Kind, print, valueFromAST, } from 'graphql';\nimport { createGraphQLError } from './errors.js';\nimport { hasOwnProperty } from './jsutils.js';\n/**\n * Prepares an object map of argument values given a list of argument\n * definitions and list of argument AST nodes.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\nexport function getArgumentValues(def, node, variableValues = {}) {\n    const coercedValues = {};\n    const argumentNodes = node.arguments ?? [];\n    const argNodeMap = argumentNodes.reduce((prev, arg) => ({\n        ...prev,\n        [arg.name.value]: arg,\n    }), {});\n    for (const { name, type: argType, defaultValue } of def.args) {\n        const argumentNode = argNodeMap[name];\n        if (!argumentNode) {\n            if (defaultValue !== undefined) {\n                coercedValues[name] = defaultValue;\n            }\n            else if (isNonNullType(argType)) {\n                throw createGraphQLError(`Argument \"${name}\" of required type \"${inspect(argType)}\" ` + 'was not provided.', {\n                    nodes: [node],\n                });\n            }\n            continue;\n        }\n        const valueNode = argumentNode.value;\n        let isNull = valueNode.kind === Kind.NULL;\n        if (valueNode.kind === Kind.VARIABLE) {\n            const variableName = valueNode.name.value;\n            if (variableValues == null || !hasOwnProperty(variableValues, variableName)) {\n                if (defaultValue !== undefined) {\n                    coercedValues[name] = defaultValue;\n                }\n                else if (isNonNullType(argType)) {\n                    throw createGraphQLError(`Argument \"${name}\" of required type \"${inspect(argType)}\" ` +\n                        `was provided the variable \"$${variableName}\" which was not provided a runtime value.`, {\n                        nodes: [valueNode],\n                    });\n                }\n                continue;\n            }\n            isNull = variableValues[variableName] == null;\n        }\n        if (isNull && isNonNullType(argType)) {\n            throw createGraphQLError(`Argument \"${name}\" of non-null type \"${inspect(argType)}\" ` + 'must not be null.', {\n                nodes: [valueNode],\n            });\n        }\n        const coercedValue = valueFromAST(valueNode, argType, variableValues);\n        if (coercedValue === undefined) {\n            // Note: ValuesOfCorrectTypeRule validation should catch this before\n            // execution. This is a runtime check to ensure execution does not\n            // continue with an invalid argument value.\n            throw createGraphQLError(`Argument \"${name}\" has invalid value ${print(valueNode)}.`, {\n                nodes: [valueNode],\n            });\n        }\n        coercedValues[name] = coercedValue;\n    }\n    return coercedValues;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AASO,SAAS,kBAAkB,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;IAC5D,MAAM,gBAAgB,CAAC;IACvB,MAAM,gBAAgB,KAAK,SAAS,IAAI,EAAE;IAC1C,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,MAAM,MAAQ,CAAC;YACpD,GAAG,IAAI;YACP,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;QACtB,CAAC,GAAG,CAAC;IACL,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,OAAO,EAAE,YAAY,EAAE,IAAI,IAAI,IAAI,CAAE;QAC1D,MAAM,eAAe,UAAU,CAAC,KAAK;QACrC,IAAI,CAAC,cAAc;YACf,IAAI,iBAAiB,WAAW;gBAC5B,aAAa,CAAC,KAAK,GAAG;YAC1B,OACK,IAAI,IAAA,iKAAa,EAAC,UAAU;gBAC7B,MAAM,IAAA,oLAAkB,EAAC,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,IAAA,6JAAO,EAAC,SAAS,EAAE,CAAC,GAAG,qBAAqB;oBACzG,OAAO;wBAAC;qBAAK;gBACjB;YACJ;YACA;QACJ;QACA,MAAM,YAAY,aAAa,KAAK;QACpC,IAAI,SAAS,UAAU,IAAI,KAAK,uJAAI,CAAC,IAAI;QACzC,IAAI,UAAU,IAAI,KAAK,uJAAI,CAAC,QAAQ,EAAE;YAClC,MAAM,eAAe,UAAU,IAAI,CAAC,KAAK;YACzC,IAAI,kBAAkB,QAAQ,CAAC,IAAA,iMAAc,EAAC,gBAAgB,eAAe;gBACzE,IAAI,iBAAiB,WAAW;oBAC5B,aAAa,CAAC,KAAK,GAAG;gBAC1B,OACK,IAAI,IAAA,iKAAa,EAAC,UAAU;oBAC7B,MAAM,IAAA,oLAAkB,EAAC,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,IAAA,6JAAO,EAAC,SAAS,EAAE,CAAC,GACjF,CAAC,4BAA4B,EAAE,aAAa,yCAAyC,CAAC,EAAE;wBACxF,OAAO;4BAAC;yBAAU;oBACtB;gBACJ;gBACA;YACJ;YACA,SAAS,cAAc,CAAC,aAAa,IAAI;QAC7C;QACA,IAAI,UAAU,IAAA,iKAAa,EAAC,UAAU;YAClC,MAAM,IAAA,oLAAkB,EAAC,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,IAAA,6JAAO,EAAC,SAAS,EAAE,CAAC,GAAG,qBAAqB;gBACzG,OAAO;oBAAC;iBAAU;YACtB;QACJ;QACA,MAAM,eAAe,IAAA,uKAAY,EAAC,WAAW,SAAS;QACtD,IAAI,iBAAiB,WAAW;YAC5B,oEAAoE;YACpE,kEAAkE;YAClE,2CAA2C;YAC3C,MAAM,IAAA,oLAAkB,EAAC,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,IAAA,0JAAK,EAAC,WAAW,CAAC,CAAC,EAAE;gBAClF,OAAO;oBAAC;iBAAU;YACtB;QACJ;QACA,aAAa,CAAC,KAAK,GAAG;IAC1B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/memoize.js"], "sourcesContent": ["export function memoize1(fn) {\n    const memoize1cache = new WeakMap();\n    return function memoized(a1) {\n        const cachedValue = memoize1cache.get(a1);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1);\n            memoize1cache.set(a1, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize2(fn) {\n    const memoize2cache = new WeakMap();\n    return function memoized(a1, a2) {\n        let cache2 = memoize2cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2cache.set(a1, cache2);\n            const newValue = fn(a1, a2);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize3(fn) {\n    const memoize3Cache = new WeakMap();\n    return function memoized(a1, a2, a3) {\n        let cache2 = memoize3Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize3Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        const cachedValue = cache3.get(a3);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize4(fn) {\n    const memoize4Cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4) {\n        let cache2 = memoize4Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize4Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        const cache4 = cache3.get(a3);\n        if (!cache4) {\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        const cachedValue = cache4.get(a4);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize5(fn) {\n    const memoize5Cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4, a5) {\n        let cache2 = memoize5Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize5Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache4 = cache3.get(a3);\n        if (!cache4) {\n            cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache5 = cache4.get(a4);\n        if (!cache5) {\n            cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        const cachedValue = cache5.get(a5);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize2of4(fn) {\n    const memoize2of4cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4) {\n        let cache2 = memoize2of4cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2of4cache.set(a1, cache2);\n            const newValue = fn(a1, a2, a3, a4);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nexport function memoize2of5(fn) {\n    const memoize2of4cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4, a5) {\n        let cache2 = memoize2of4cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2of4cache.set(a1, cache2);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAO,SAAS,SAAS,EAAE;IACvB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE;QACvB,MAAM,cAAc,cAAc,GAAG,CAAC;QACtC,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG;YACpB,cAAc,GAAG,CAAC,IAAI;YACtB,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACO,SAAS,SAAS,EAAE;IACvB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE;QAC3B,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,WAAW,GAAG,IAAI;YACxB,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI;YACxB,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACO,SAAS,SAAS,EAAE;IACvB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE;QAC/B,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI;YAC5B,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI;YAC5B,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI;YAC5B,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACO,SAAS,SAAS,EAAE;IACvB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACnC,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,SAAS,OAAO,GAAG,CAAC;QAC1B,IAAI,CAAC,QAAQ;YACT,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACO,SAAS,SAAS,EAAE;IACvB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvC,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACO,SAAS,YAAY,EAAE;IAC1B,MAAM,mBAAmB,IAAI;IAC7B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACnC,IAAI,SAAS,iBAAiB,GAAG,CAAC;QAClC,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,iBAAiB,GAAG,CAAC,IAAI;YACzB,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACO,SAAS,YAAY,EAAE;IAC1B,MAAM,mBAAmB,IAAI;IAC7B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvC,IAAI,SAAS,iBAAiB,GAAG,CAAC;QAClC,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,iBAAiB,GAAG,CAAC,IAAI;YACzB,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/getDirectiveExtensions.js"], "sourcesContent": ["import { valueFromAST, valueFromASTUntyped } from 'graphql';\nimport { getArgumentValues } from './getArgumentValues.js';\nimport { memoize1 } from './memoize.js';\nexport function getDirectiveExtensions(directableObj, schema, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = {};\n    if (directableObj.extensions) {\n        let directivesInExtensions = directableObj.extensions;\n        for (const pathSegment of pathToDirectivesInExtensions) {\n            directivesInExtensions = directivesInExtensions?.[pathSegment];\n        }\n        if (directivesInExtensions != null) {\n            for (const directiveNameProp in directivesInExtensions) {\n                const directiveObjs = directivesInExtensions[directiveNameProp];\n                const directiveName = directiveNameProp;\n                if (Array.isArray(directiveObjs)) {\n                    for (const directiveObj of directiveObjs) {\n                        let existingDirectiveExtensions = directiveExtensions[directiveName];\n                        if (!existingDirectiveExtensions) {\n                            existingDirectiveExtensions = [];\n                            directiveExtensions[directiveName] = existingDirectiveExtensions;\n                        }\n                        existingDirectiveExtensions.push(directiveObj);\n                    }\n                }\n                else {\n                    let existingDirectiveExtensions = directiveExtensions[directiveName];\n                    if (!existingDirectiveExtensions) {\n                        existingDirectiveExtensions = [];\n                        directiveExtensions[directiveName] = existingDirectiveExtensions;\n                    }\n                    existingDirectiveExtensions.push(directiveObjs);\n                }\n            }\n        }\n    }\n    const memoizedStringify = memoize1(obj => JSON.stringify(obj));\n    const astNodes = [];\n    if (directableObj.astNode) {\n        astNodes.push(directableObj.astNode);\n    }\n    if (directableObj.extensionASTNodes) {\n        astNodes.push(...directableObj.extensionASTNodes);\n    }\n    for (const astNode of astNodes) {\n        if (astNode.directives?.length) {\n            for (const directive of astNode.directives) {\n                const directiveName = directive.name.value;\n                let existingDirectiveExtensions = directiveExtensions[directiveName];\n                if (!existingDirectiveExtensions) {\n                    existingDirectiveExtensions = [];\n                    directiveExtensions[directiveName] = existingDirectiveExtensions;\n                }\n                const directiveInSchema = schema?.getDirective(directiveName);\n                let value = {};\n                if (directiveInSchema) {\n                    value = getArgumentValues(directiveInSchema, directive);\n                }\n                if (directive.arguments) {\n                    for (const argNode of directive.arguments) {\n                        const argName = argNode.name.value;\n                        if (value[argName] == null) {\n                            const argInDirective = directiveInSchema?.args.find(arg => arg.name === argName);\n                            if (argInDirective) {\n                                value[argName] = valueFromAST(argNode.value, argInDirective.type);\n                            }\n                        }\n                        if (value[argName] == null) {\n                            value[argName] = valueFromASTUntyped(argNode.value);\n                        }\n                    }\n                }\n                if (astNodes.length > 0 && existingDirectiveExtensions.length > 0) {\n                    const valStr = memoizedStringify(value);\n                    if (existingDirectiveExtensions.some(val => memoizedStringify(val) === valStr)) {\n                        continue;\n                    }\n                }\n                existingDirectiveExtensions.push(value);\n            }\n        }\n    }\n    return directiveExtensions;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;AACO,SAAS,uBAAuB,aAAa,EAAE,MAAM,EAAE,+BAA+B;IAAC;CAAa;IACvG,MAAM,sBAAsB,CAAC;IAC7B,IAAI,cAAc,UAAU,EAAE;QAC1B,IAAI,yBAAyB,cAAc,UAAU;QACrD,KAAK,MAAM,eAAe,6BAA8B;YACpD,yBAAyB,wBAAwB,CAAC,YAAY;QAClE;QACA,IAAI,0BAA0B,MAAM;YAChC,IAAK,MAAM,qBAAqB,uBAAwB;gBACpD,MAAM,gBAAgB,sBAAsB,CAAC,kBAAkB;gBAC/D,MAAM,gBAAgB;gBACtB,IAAI,MAAM,OAAO,CAAC,gBAAgB;oBAC9B,KAAK,MAAM,gBAAgB,cAAe;wBACtC,IAAI,8BAA8B,mBAAmB,CAAC,cAAc;wBACpE,IAAI,CAAC,6BAA6B;4BAC9B,8BAA8B,EAAE;4BAChC,mBAAmB,CAAC,cAAc,GAAG;wBACzC;wBACA,4BAA4B,IAAI,CAAC;oBACrC;gBACJ,OACK;oBACD,IAAI,8BAA8B,mBAAmB,CAAC,cAAc;oBACpE,IAAI,CAAC,6BAA6B;wBAC9B,8BAA8B,EAAE;wBAChC,mBAAmB,CAAC,cAAc,GAAG;oBACzC;oBACA,4BAA4B,IAAI,CAAC;gBACrC;YACJ;QACJ;IACJ;IACA,MAAM,oBAAoB,IAAA,2KAAQ,EAAC,CAAA,MAAO,KAAK,SAAS,CAAC;IACzD,MAAM,WAAW,EAAE;IACnB,IAAI,cAAc,OAAO,EAAE;QACvB,SAAS,IAAI,CAAC,cAAc,OAAO;IACvC;IACA,IAAI,cAAc,iBAAiB,EAAE;QACjC,SAAS,IAAI,IAAI,cAAc,iBAAiB;IACpD;IACA,KAAK,MAAM,WAAW,SAAU;QAC5B,IAAI,QAAQ,UAAU,EAAE,QAAQ;YAC5B,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;gBACxC,MAAM,gBAAgB,UAAU,IAAI,CAAC,KAAK;gBAC1C,IAAI,8BAA8B,mBAAmB,CAAC,cAAc;gBACpE,IAAI,CAAC,6BAA6B;oBAC9B,8BAA8B,EAAE;oBAChC,mBAAmB,CAAC,cAAc,GAAG;gBACzC;gBACA,MAAM,oBAAoB,QAAQ,aAAa;gBAC/C,IAAI,QAAQ,CAAC;gBACb,IAAI,mBAAmB;oBACnB,QAAQ,IAAA,8LAAiB,EAAC,mBAAmB;gBACjD;gBACA,IAAI,UAAU,SAAS,EAAE;oBACrB,KAAK,MAAM,WAAW,UAAU,SAAS,CAAE;wBACvC,MAAM,UAAU,QAAQ,IAAI,CAAC,KAAK;wBAClC,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM;4BACxB,MAAM,iBAAiB,mBAAmB,KAAK,KAAK,CAAA,MAAO,IAAI,IAAI,KAAK;4BACxE,IAAI,gBAAgB;gCAChB,KAAK,CAAC,QAAQ,GAAG,IAAA,uKAAY,EAAC,QAAQ,KAAK,EAAE,eAAe,IAAI;4BACpE;wBACJ;wBACA,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM;4BACxB,KAAK,CAAC,QAAQ,GAAG,IAAA,qLAAmB,EAAC,QAAQ,KAAK;wBACtD;oBACJ;gBACJ;gBACA,IAAI,SAAS,MAAM,GAAG,KAAK,4BAA4B,MAAM,GAAG,GAAG;oBAC/D,MAAM,SAAS,kBAAkB;oBACjC,IAAI,4BAA4B,IAAI,CAAC,CAAA,MAAO,kBAAkB,SAAS,SAAS;wBAC5E;oBACJ;gBACJ;gBACA,4BAA4B,IAAI,CAAC;YACrC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/get-directives.js"], "sourcesContent": ["import { getDirectiveExtensions } from './getDirectiveExtensions.js';\nexport function getDirectivesInExtensions(node, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, undefined, pathToDirectivesInExtensions);\n    return Object.entries(directiveExtensions)\n        .map(([directiveName, directiveArgsArr]) => directiveArgsArr?.map(directiveArgs => ({\n        name: directiveName,\n        args: directiveArgs,\n    })))\n        .flat(Infinity)\n        .filter(Boolean);\n}\nexport function getDirectiveInExtensions(node, directiveName, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, undefined, pathToDirectivesInExtensions);\n    return directiveExtensions[directiveName];\n}\nexport function getDirectives(schema, node, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, schema, pathToDirectivesInExtensions);\n    return Object.entries(directiveExtensions)\n        .map(([directiveName, directiveArgsArr]) => directiveArgsArr?.map(directiveArgs => ({\n        name: directiveName,\n        args: directiveArgs,\n    })))\n        .flat(Infinity)\n        .filter(Boolean);\n}\nexport function getDirective(schema, node, directiveName, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = getDirectiveExtensions(node, schema, pathToDirectivesInExtensions);\n    return directiveExtensions[directiveName];\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,SAAS,0BAA0B,IAAI,EAAE,+BAA+B;IAAC;CAAa;IACzF,MAAM,sBAAsB,IAAA,wMAAsB,EAAC,MAAM,WAAW;IACpE,OAAO,OAAO,OAAO,CAAC,qBACjB,GAAG,CAAC,CAAC,CAAC,eAAe,iBAAiB,GAAK,kBAAkB,IAAI,CAAA,gBAAiB,CAAC;gBACpF,MAAM;gBACN,MAAM;YACV,CAAC,IACI,IAAI,CAAC,UACL,MAAM,CAAC;AAChB;AACO,SAAS,yBAAyB,IAAI,EAAE,aAAa,EAAE,+BAA+B;IAAC;CAAa;IACvG,MAAM,sBAAsB,IAAA,wMAAsB,EAAC,MAAM,WAAW;IACpE,OAAO,mBAAmB,CAAC,cAAc;AAC7C;AACO,SAAS,cAAc,MAAM,EAAE,IAAI,EAAE,+BAA+B;IAAC;CAAa;IACrF,MAAM,sBAAsB,IAAA,wMAAsB,EAAC,MAAM,QAAQ;IACjE,OAAO,OAAO,OAAO,CAAC,qBACjB,GAAG,CAAC,CAAC,CAAC,eAAe,iBAAiB,GAAK,kBAAkB,IAAI,CAAA,gBAAiB,CAAC;gBACpF,MAAM;gBACN,MAAM;YACV,CAAC,IACI,IAAI,CAAC,UACL,MAAM,CAAC;AAChB;AACO,SAAS,aAAa,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,+BAA+B;IAAC;CAAa;IACnG,MAAM,sBAAsB,IAAA,wMAAsB,EAAC,MAAM,QAAQ;IACjE,OAAO,mBAAmB,CAAC,cAAc;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/rootTypes.js"], "sourcesContent": ["import { createGraphQLError } from './errors.js';\nimport { memoize1 } from './memoize.js';\nexport function getDefinedRootType(schema, operation, nodes) {\n    const rootTypeMap = getRootTypeMap(schema);\n    const rootType = rootTypeMap.get(operation);\n    if (rootType == null) {\n        throw createGraphQLError(`<PERSON>hem<PERSON> is not configured to execute ${operation} operation.`, {\n            nodes,\n        });\n    }\n    return rootType;\n}\nexport const getRootTypeNames = memoize1(function getRootTypeNames(schema) {\n    const rootTypes = getRootTypes(schema);\n    return new Set([...rootTypes].map(type => type.name));\n});\nexport const getRootTypes = memoize1(function getRootTypes(schema) {\n    const rootTypeMap = getRootTypeMap(schema);\n    return new Set(rootTypeMap.values());\n});\nexport const getRootTypeMap = memoize1(function getRootTypeMap(schema) {\n    const rootTypeMap = new Map();\n    const queryType = schema.getQueryType();\n    if (queryType) {\n        rootTypeMap.set('query', queryType);\n    }\n    const mutationType = schema.getMutationType();\n    if (mutationType) {\n        rootTypeMap.set('mutation', mutationType);\n    }\n    const subscriptionType = schema.getSubscriptionType();\n    if (subscriptionType) {\n        rootTypeMap.set('subscription', subscriptionType);\n    }\n    return rootTypeMap;\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AACO,SAAS,mBAAmB,MAAM,EAAE,SAAS,EAAE,KAAK;IACvD,MAAM,cAAc,eAAe;IACnC,MAAM,WAAW,YAAY,GAAG,CAAC;IACjC,IAAI,YAAY,MAAM;QAClB,MAAM,IAAA,oLAAkB,EAAC,CAAC,oCAAoC,EAAE,UAAU,WAAW,CAAC,EAAE;YACpF;QACJ;IACJ;IACA,OAAO;AACX;AACO,MAAM,mBAAmB,IAAA,2KAAQ,EAAC,SAAS,iBAAiB,MAAM;IACrE,MAAM,YAAY,aAAa;IAC/B,OAAO,IAAI,IAAI;WAAI;KAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;AACvD;AACO,MAAM,eAAe,IAAA,2KAAQ,EAAC,SAAS,aAAa,MAAM;IAC7D,MAAM,cAAc,eAAe;IACnC,OAAO,IAAI,IAAI,YAAY,MAAM;AACrC;AACO,MAAM,iBAAiB,IAAA,2KAAQ,EAAC,SAAS,eAAe,MAAM;IACjE,MAAM,cAAc,IAAI;IACxB,MAAM,YAAY,OAAO,YAAY;IACrC,IAAI,WAAW;QACX,YAAY,GAAG,CAAC,SAAS;IAC7B;IACA,MAAM,eAAe,OAAO,eAAe;IAC3C,IAAI,cAAc;QACd,YAAY,GAAG,CAAC,YAAY;IAChC;IACA,MAAM,mBAAmB,OAAO,mBAAmB;IACnD,IAAI,kBAAkB;QAClB,YAAY,GAAG,CAAC,gBAAgB;IACpC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/print-schema-with-directives.js"], "sourcesContent": ["import { GraphQLDeprecatedDirective, isEnumType, isInputObjectType, isInterfaceType, isIntrospectionType, isObjectType, isScalarType, isSpecifiedDirective, isSpecifiedScalarType, isUnionType, Kind, print, specifiedDirectives, } from 'graphql';\nimport { astFromType } from './astFromType.js';\nimport { astFromValue } from './astFromValue.js';\nimport { astFromValueUntyped } from './astFromValueUntyped.js';\nimport { getDescriptionNode } from './descriptionFromObject.js';\nimport { getDirectivesInExtensions, } from './get-directives.js';\nimport { isSome } from './helpers.js';\nimport { getRootTypeMap } from './rootTypes.js';\nexport function getDocumentNodeFromSchema(schema, options = {}) {\n    const pathToDirectivesInExtensions = options.pathToDirectivesInExtensions;\n    const typesMap = schema.getTypeMap();\n    const schemaNode = astFromSchema(schema, pathToDirectivesInExtensions);\n    const definitions = schemaNode != null ? [schemaNode] : [];\n    const directives = schema.getDirectives();\n    for (const directive of directives) {\n        if (isSpecifiedDirective(directive)) {\n            continue;\n        }\n        definitions.push(astFromDirective(directive, schema, pathToDirectivesInExtensions));\n    }\n    for (const typeName in typesMap) {\n        const type = typesMap[typeName];\n        const isPredefinedScalar = isSpecifiedScalarType(type);\n        const isIntrospection = isIntrospectionType(type);\n        if (isPredefinedScalar || isIntrospection) {\n            continue;\n        }\n        if (isObjectType(type)) {\n            definitions.push(astFromObjectType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isInterfaceType(type)) {\n            definitions.push(astFromInterfaceType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isUnionType(type)) {\n            definitions.push(astFromUnionType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isInputObjectType(type)) {\n            definitions.push(astFromInputObjectType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isEnumType(type)) {\n            definitions.push(astFromEnumType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if (isScalarType(type)) {\n            definitions.push(astFromScalarType(type, schema, pathToDirectivesInExtensions));\n        }\n        else {\n            throw new Error(`Unknown type ${type}.`);\n        }\n    }\n    return {\n        kind: Kind.DOCUMENT,\n        definitions,\n    };\n}\n// this approach uses the default schema printer rather than a custom solution, so may be more backwards compatible\n// currently does not allow customization of printSchema options having to do with comments.\nexport function printSchemaWithDirectives(schema, options = {}) {\n    const documentNode = getDocumentNodeFromSchema(schema, options);\n    return print(documentNode);\n}\nexport function astFromSchema(schema, pathToDirectivesInExtensions) {\n    const operationTypeMap = new Map([\n        ['query', undefined],\n        ['mutation', undefined],\n        ['subscription', undefined],\n    ]);\n    const nodes = [];\n    if (schema.astNode != null) {\n        nodes.push(schema.astNode);\n    }\n    if (schema.extensionASTNodes != null) {\n        for (const extensionASTNode of schema.extensionASTNodes) {\n            nodes.push(extensionASTNode);\n        }\n    }\n    for (const node of nodes) {\n        if (node.operationTypes) {\n            for (const operationTypeDefinitionNode of node.operationTypes) {\n                operationTypeMap.set(operationTypeDefinitionNode.operation, operationTypeDefinitionNode);\n            }\n        }\n    }\n    const rootTypeMap = getRootTypeMap(schema);\n    for (const [operationTypeNode, operationTypeDefinitionNode] of operationTypeMap) {\n        const rootType = rootTypeMap.get(operationTypeNode);\n        if (rootType != null) {\n            const rootTypeAST = astFromType(rootType);\n            if (operationTypeDefinitionNode != null) {\n                operationTypeDefinitionNode.type = rootTypeAST;\n            }\n            else {\n                operationTypeMap.set(operationTypeNode, {\n                    kind: Kind.OPERATION_TYPE_DEFINITION,\n                    operation: operationTypeNode,\n                    type: rootTypeAST,\n                });\n            }\n        }\n    }\n    const operationTypes = [...operationTypeMap.values()].filter(isSome);\n    const directives = getDirectiveNodes(schema, schema, pathToDirectivesInExtensions);\n    if (!operationTypes.length && !directives.length) {\n        return null;\n    }\n    const schemaNode = {\n        kind: operationTypes != null ? Kind.SCHEMA_DEFINITION : Kind.SCHEMA_EXTENSION,\n        operationTypes,\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: directives,\n    };\n    const descriptionNode = getDescriptionNode(schema);\n    if (descriptionNode) {\n        schemaNode.description = descriptionNode;\n    }\n    return schemaNode;\n}\nexport function astFromDirective(directive, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.DIRECTIVE_DEFINITION,\n        description: getDescriptionNode(directive),\n        name: {\n            kind: Kind.NAME,\n            value: directive.name,\n        },\n        arguments: directive.args?.map(arg => astFromArg(arg, schema, pathToDirectivesInExtensions)),\n        repeatable: directive.isRepeatable,\n        locations: directive.locations?.map(location => ({\n            kind: Kind.NAME,\n            value: location,\n        })) || [],\n    };\n}\nexport function getDirectiveNodes(entity, schema, pathToDirectivesInExtensions) {\n    let directiveNodesBesidesNativeDirectives = [];\n    const directivesInExtensions = getDirectivesInExtensions(entity, pathToDirectivesInExtensions);\n    let directives;\n    if (directivesInExtensions != null) {\n        directives = makeDirectiveNodes(schema, directivesInExtensions);\n    }\n    let deprecatedDirectiveNode = null;\n    let specifiedByDirectiveNode = null;\n    let oneOfDirectiveNode = null;\n    if (directives != null) {\n        directiveNodesBesidesNativeDirectives = directives.filter(directive => specifiedDirectives.every(specifiedDirective => specifiedDirective.name !== directive.name.value));\n        deprecatedDirectiveNode = directives.find(directive => directive.name.value === 'deprecated');\n        specifiedByDirectiveNode = directives.find(directive => directive.name.value === 'specifiedBy');\n        oneOfDirectiveNode = directives.find(directive => directive.name.value === 'oneOf');\n    }\n    if (entity.deprecationReason != null && deprecatedDirectiveNode == null) {\n        deprecatedDirectiveNode = makeDeprecatedDirective(entity.deprecationReason);\n    }\n    if (entity.specifiedByUrl != null ||\n        (entity.specifiedByURL != null && specifiedByDirectiveNode == null)) {\n        const specifiedByValue = entity.specifiedByUrl || entity.specifiedByURL;\n        const specifiedByArgs = {\n            url: specifiedByValue,\n        };\n        specifiedByDirectiveNode = makeDirectiveNode('specifiedBy', specifiedByArgs);\n    }\n    if (entity.isOneOf && oneOfDirectiveNode == null) {\n        oneOfDirectiveNode = makeDirectiveNode('oneOf');\n    }\n    if (deprecatedDirectiveNode != null) {\n        directiveNodesBesidesNativeDirectives.push(deprecatedDirectiveNode);\n    }\n    if (specifiedByDirectiveNode != null) {\n        directiveNodesBesidesNativeDirectives.push(specifiedByDirectiveNode);\n    }\n    if (oneOfDirectiveNode != null) {\n        directiveNodesBesidesNativeDirectives.push(oneOfDirectiveNode);\n    }\n    return directiveNodesBesidesNativeDirectives;\n}\nexport function astFromArg(arg, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.INPUT_VALUE_DEFINITION,\n        description: getDescriptionNode(arg),\n        name: {\n            kind: Kind.NAME,\n            value: arg.name,\n        },\n        type: astFromType(arg.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        defaultValue: arg.defaultValue !== undefined\n            ? (astFromValue(arg.defaultValue, arg.type) ?? undefined)\n            : undefined,\n        directives: getDirectiveNodes(arg, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromObjectType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.OBJECT_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromField(field, schema, pathToDirectivesInExtensions)),\n        interfaces: Object.values(type.getInterfaces()).map(iFace => astFromType(iFace)),\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromInterfaceType(type, schema, pathToDirectivesInExtensions) {\n    const node = {\n        kind: Kind.INTERFACE_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromField(field, schema, pathToDirectivesInExtensions)),\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n    if ('getInterfaces' in type) {\n        node.interfaces = Object.values(type.getInterfaces()).map(iFace => astFromType(iFace));\n    }\n    return node;\n}\nexport function astFromUnionType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.UNION_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n        types: type.getTypes().map(type => astFromType(type)),\n    };\n}\nexport function astFromInputObjectType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromInputField(field, schema, pathToDirectivesInExtensions)),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromEnumType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.ENUM_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        values: Object.values(type.getValues()).map(value => astFromEnumValue(value, schema, pathToDirectivesInExtensions)),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromScalarType(type, schema, pathToDirectivesInExtensions) {\n    const directivesInExtensions = getDirectivesInExtensions(type, pathToDirectivesInExtensions);\n    const directives = makeDirectiveNodes(schema, directivesInExtensions);\n    const specifiedByValue = (type['specifiedByUrl'] ||\n        type['specifiedByURL']);\n    if (specifiedByValue &&\n        !directives.some(directiveNode => directiveNode.name.value === 'specifiedBy')) {\n        const specifiedByArgs = {\n            url: specifiedByValue,\n        };\n        directives.push(makeDirectiveNode('specifiedBy', specifiedByArgs));\n    }\n    return {\n        kind: Kind.SCALAR_TYPE_DEFINITION,\n        description: getDescriptionNode(type),\n        name: {\n            kind: Kind.NAME,\n            value: type.name,\n        },\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: directives,\n    };\n}\nexport function astFromField(field, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.FIELD_DEFINITION,\n        description: getDescriptionNode(field),\n        name: {\n            kind: Kind.NAME,\n            value: field.name,\n        },\n        arguments: field.args.map(arg => astFromArg(arg, schema, pathToDirectivesInExtensions)),\n        type: astFromType(field.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function astFromInputField(field, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.INPUT_VALUE_DEFINITION,\n        description: getDescriptionNode(field),\n        name: {\n            kind: Kind.NAME,\n            value: field.name,\n        },\n        type: astFromType(field.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),\n        defaultValue: astFromValue(field.defaultValue, field.type) ?? undefined,\n    };\n}\nexport function astFromEnumValue(value, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: Kind.ENUM_VALUE_DEFINITION,\n        description: getDescriptionNode(value),\n        name: {\n            kind: Kind.NAME,\n            value: value.name,\n        },\n        directives: getDirectiveNodes(value, schema, pathToDirectivesInExtensions),\n    };\n}\nexport function makeDeprecatedDirective(deprecationReason) {\n    return makeDirectiveNode('deprecated', { reason: deprecationReason }, GraphQLDeprecatedDirective);\n}\nexport function makeDirectiveNode(name, args, directive) {\n    const directiveArguments = [];\n    for (const argName in args) {\n        const argValue = args[argName];\n        let value;\n        if (directive != null) {\n            const arg = directive.args.find(arg => arg.name === argName);\n            if (arg) {\n                value = astFromValue(argValue, arg.type);\n            }\n        }\n        if (value == null) {\n            value = astFromValueUntyped(argValue);\n        }\n        if (value != null) {\n            directiveArguments.push({\n                kind: Kind.ARGUMENT,\n                name: {\n                    kind: Kind.NAME,\n                    value: argName,\n                },\n                value,\n            });\n        }\n    }\n    return {\n        kind: Kind.DIRECTIVE,\n        name: {\n            kind: Kind.NAME,\n            value: name,\n        },\n        arguments: directiveArguments,\n    };\n}\nexport function makeDirectiveNodes(schema, directiveValues) {\n    const directiveNodes = [];\n    for (const { name, args } of directiveValues) {\n        const directive = schema?.getDirective(name);\n        directiveNodes.push(makeDirectiveNode(name, args, directive));\n    }\n    return directiveNodes;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,SAAS,0BAA0B,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1D,MAAM,+BAA+B,QAAQ,4BAA4B;IACzE,MAAM,WAAW,OAAO,UAAU;IAClC,MAAM,aAAa,cAAc,QAAQ;IACzC,MAAM,cAAc,cAAc,OAAO;QAAC;KAAW,GAAG,EAAE;IAC1D,MAAM,aAAa,OAAO,aAAa;IACvC,KAAK,MAAM,aAAa,WAAY;QAChC,IAAI,IAAA,wKAAoB,EAAC,YAAY;YACjC;QACJ;QACA,YAAY,IAAI,CAAC,iBAAiB,WAAW,QAAQ;IACzD;IACA,IAAK,MAAM,YAAY,SAAU;QAC7B,MAAM,OAAO,QAAQ,CAAC,SAAS;QAC/B,MAAM,qBAAqB,IAAA,sKAAqB,EAAC;QACjD,MAAM,kBAAkB,IAAA,0KAAmB,EAAC;QAC5C,IAAI,sBAAsB,iBAAiB;YACvC;QACJ;QACA,IAAI,IAAA,gKAAY,EAAC,OAAO;YACpB,YAAY,IAAI,CAAC,kBAAkB,MAAM,QAAQ;QACrD,OACK,IAAI,IAAA,mKAAe,EAAC,OAAO;YAC5B,YAAY,IAAI,CAAC,qBAAqB,MAAM,QAAQ;QACxD,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;YACxB,YAAY,IAAI,CAAC,iBAAiB,MAAM,QAAQ;QACpD,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;YAC9B,YAAY,IAAI,CAAC,uBAAuB,MAAM,QAAQ;QAC1D,OACK,IAAI,IAAA,8JAAU,EAAC,OAAO;YACvB,YAAY,IAAI,CAAC,gBAAgB,MAAM,QAAQ;QACnD,OACK,IAAI,IAAA,gKAAY,EAAC,OAAO;YACzB,YAAY,IAAI,CAAC,kBAAkB,MAAM,QAAQ;QACrD,OACK;YACD,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAC3C;IACJ;IACA,OAAO;QACH,MAAM,uJAAI,CAAC,QAAQ;QACnB;IACJ;AACJ;AAGO,SAAS,0BAA0B,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1D,MAAM,eAAe,0BAA0B,QAAQ;IACvD,OAAO,IAAA,0JAAK,EAAC;AACjB;AACO,SAAS,cAAc,MAAM,EAAE,4BAA4B;IAC9D,MAAM,mBAAmB,IAAI,IAAI;QAC7B;YAAC;YAAS;SAAU;QACpB;YAAC;YAAY;SAAU;QACvB;YAAC;YAAgB;SAAU;KAC9B;IACD,MAAM,QAAQ,EAAE;IAChB,IAAI,OAAO,OAAO,IAAI,MAAM;QACxB,MAAM,IAAI,CAAC,OAAO,OAAO;IAC7B;IACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;QAClC,KAAK,MAAM,oBAAoB,OAAO,iBAAiB,CAAE;YACrD,MAAM,IAAI,CAAC;QACf;IACJ;IACA,KAAK,MAAM,QAAQ,MAAO;QACtB,IAAI,KAAK,cAAc,EAAE;YACrB,KAAK,MAAM,+BAA+B,KAAK,cAAc,CAAE;gBAC3D,iBAAiB,GAAG,CAAC,4BAA4B,SAAS,EAAE;YAChE;QACJ;IACJ;IACA,MAAM,cAAc,IAAA,mLAAc,EAAC;IACnC,KAAK,MAAM,CAAC,mBAAmB,4BAA4B,IAAI,iBAAkB;QAC7E,MAAM,WAAW,YAAY,GAAG,CAAC;QACjC,IAAI,YAAY,MAAM;YAClB,MAAM,cAAc,IAAA,kLAAW,EAAC;YAChC,IAAI,+BAA+B,MAAM;gBACrC,4BAA4B,IAAI,GAAG;YACvC,OACK;gBACD,iBAAiB,GAAG,CAAC,mBAAmB;oBACpC,MAAM,uJAAI,CAAC,yBAAyB;oBACpC,WAAW;oBACX,MAAM;gBACV;YACJ;QACJ;IACJ;IACA,MAAM,iBAAiB;WAAI,iBAAiB,MAAM;KAAG,CAAC,MAAM,CAAC,yKAAM;IACnE,MAAM,aAAa,kBAAkB,QAAQ,QAAQ;IACrD,IAAI,CAAC,eAAe,MAAM,IAAI,CAAC,WAAW,MAAM,EAAE;QAC9C,OAAO;IACX;IACA,MAAM,aAAa;QACf,MAAM,kBAAkB,OAAO,uJAAI,CAAC,iBAAiB,GAAG,uJAAI,CAAC,gBAAgB;QAC7E;QACA,0HAA0H;QAC1H,YAAY;IAChB;IACA,MAAM,kBAAkB,IAAA,mMAAkB,EAAC;IAC3C,IAAI,iBAAiB;QACjB,WAAW,WAAW,GAAG;IAC7B;IACA,OAAO;AACX;AACO,SAAS,iBAAiB,SAAS,EAAE,MAAM,EAAE,4BAA4B;IAC5E,OAAO;QACH,MAAM,uJAAI,CAAC,oBAAoB;QAC/B,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,UAAU,IAAI;QACzB;QACA,WAAW,UAAU,IAAI,EAAE,IAAI,CAAA,MAAO,WAAW,KAAK,QAAQ;QAC9D,YAAY,UAAU,YAAY;QAClC,WAAW,UAAU,SAAS,EAAE,IAAI,CAAA,WAAY,CAAC;gBAC7C,MAAM,uJAAI,CAAC,IAAI;gBACf,OAAO;YACX,CAAC,MAAM,EAAE;IACb;AACJ;AACO,SAAS,kBAAkB,MAAM,EAAE,MAAM,EAAE,4BAA4B;IAC1E,IAAI,wCAAwC,EAAE;IAC9C,MAAM,yBAAyB,IAAA,sMAAyB,EAAC,QAAQ;IACjE,IAAI;IACJ,IAAI,0BAA0B,MAAM;QAChC,aAAa,mBAAmB,QAAQ;IAC5C;IACA,IAAI,0BAA0B;IAC9B,IAAI,2BAA2B;IAC/B,IAAI,qBAAqB;IACzB,IAAI,cAAc,MAAM;QACpB,wCAAwC,WAAW,MAAM,CAAC,CAAA,YAAa,uKAAmB,CAAC,KAAK,CAAC,CAAA,qBAAsB,mBAAmB,IAAI,KAAK,UAAU,IAAI,CAAC,KAAK;QACvK,0BAA0B,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;QAChF,2BAA2B,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;QACjF,qBAAqB,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;IAC/E;IACA,IAAI,OAAO,iBAAiB,IAAI,QAAQ,2BAA2B,MAAM;QACrE,0BAA0B,wBAAwB,OAAO,iBAAiB;IAC9E;IACA,IAAI,OAAO,cAAc,IAAI,QACxB,OAAO,cAAc,IAAI,QAAQ,4BAA4B,MAAO;QACrE,MAAM,mBAAmB,OAAO,cAAc,IAAI,OAAO,cAAc;QACvE,MAAM,kBAAkB;YACpB,KAAK;QACT;QACA,2BAA2B,kBAAkB,eAAe;IAChE;IACA,IAAI,OAAO,OAAO,IAAI,sBAAsB,MAAM;QAC9C,qBAAqB,kBAAkB;IAC3C;IACA,IAAI,2BAA2B,MAAM;QACjC,sCAAsC,IAAI,CAAC;IAC/C;IACA,IAAI,4BAA4B,MAAM;QAClC,sCAAsC,IAAI,CAAC;IAC/C;IACA,IAAI,sBAAsB,MAAM;QAC5B,sCAAsC,IAAI,CAAC;IAC/C;IACA,OAAO;AACX;AACO,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,4BAA4B;IAChE,OAAO;QACH,MAAM,uJAAI,CAAC,sBAAsB;QACjC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,IAAI,IAAI;QACnB;QACA,MAAM,IAAA,kLAAW,EAAC,IAAI,IAAI;QAC1B,0HAA0H;QAC1H,cAAc,IAAI,YAAY,KAAK,YAC5B,IAAA,oLAAY,EAAC,IAAI,YAAY,EAAE,IAAI,IAAI,KAAK,YAC7C;QACN,YAAY,kBAAkB,KAAK,QAAQ;IAC/C;AACJ;AACO,SAAS,kBAAkB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACxE,OAAO;QACH,MAAM,uJAAI,CAAC,sBAAsB;QACjC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,aAAa,OAAO,QAAQ;QACjF,YAAY,OAAO,MAAM,CAAC,KAAK,aAAa,IAAI,GAAG,CAAC,CAAA,QAAS,IAAA,kLAAW,EAAC;QACzE,YAAY,kBAAkB,MAAM,QAAQ;IAChD;AACJ;AACO,SAAS,qBAAqB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IAC3E,MAAM,OAAO;QACT,MAAM,uJAAI,CAAC,yBAAyB;QACpC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,aAAa,OAAO,QAAQ;QACjF,YAAY,kBAAkB,MAAM,QAAQ;IAChD;IACA,IAAI,mBAAmB,MAAM;QACzB,KAAK,UAAU,GAAG,OAAO,MAAM,CAAC,KAAK,aAAa,IAAI,GAAG,CAAC,CAAA,QAAS,IAAA,kLAAW,EAAC;IACnF;IACA,OAAO;AACX;AACO,SAAS,iBAAiB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACvE,OAAO;QACH,MAAM,uJAAI,CAAC,qBAAqB;QAChC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;QACA,0HAA0H;QAC1H,YAAY,kBAAkB,MAAM,QAAQ;QAC5C,OAAO,KAAK,QAAQ,GAAG,GAAG,CAAC,CAAA,OAAQ,IAAA,kLAAW,EAAC;IACnD;AACJ;AACO,SAAS,uBAAuB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IAC7E,OAAO;QACH,MAAM,uJAAI,CAAC,4BAA4B;QACvC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,kBAAkB,OAAO,QAAQ;QACtF,0HAA0H;QAC1H,YAAY,kBAAkB,MAAM,QAAQ;IAChD;AACJ;AACO,SAAS,gBAAgB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACtE,OAAO;QACH,MAAM,uJAAI,CAAC,oBAAoB;QAC/B,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,iBAAiB,OAAO,QAAQ;QACrF,0HAA0H;QAC1H,YAAY,kBAAkB,MAAM,QAAQ;IAChD;AACJ;AACO,SAAS,kBAAkB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACxE,MAAM,yBAAyB,IAAA,sMAAyB,EAAC,MAAM;IAC/D,MAAM,aAAa,mBAAmB,QAAQ;IAC9C,MAAM,mBAAoB,IAAI,CAAC,iBAAiB,IAC5C,IAAI,CAAC,iBAAiB;IAC1B,IAAI,oBACA,CAAC,WAAW,IAAI,CAAC,CAAA,gBAAiB,cAAc,IAAI,CAAC,KAAK,KAAK,gBAAgB;QAC/E,MAAM,kBAAkB;YACpB,KAAK;QACT;QACA,WAAW,IAAI,CAAC,kBAAkB,eAAe;IACrD;IACA,OAAO;QACH,MAAM,uJAAI,CAAC,sBAAsB;QACjC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,KAAK,IAAI;QACpB;QACA,0HAA0H;QAC1H,YAAY;IAChB;AACJ;AACO,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,4BAA4B;IACpE,OAAO;QACH,MAAM,uJAAI,CAAC,gBAAgB;QAC3B,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,MAAM,IAAI;QACrB;QACA,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,WAAW,KAAK,QAAQ;QACzD,MAAM,IAAA,kLAAW,EAAC,MAAM,IAAI;QAC5B,0HAA0H;QAC1H,YAAY,kBAAkB,OAAO,QAAQ;IACjD;AACJ;AACO,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,4BAA4B;IACzE,OAAO;QACH,MAAM,uJAAI,CAAC,sBAAsB;QACjC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,MAAM,IAAI;QACrB;QACA,MAAM,IAAA,kLAAW,EAAC,MAAM,IAAI;QAC5B,0HAA0H;QAC1H,YAAY,kBAAkB,OAAO,QAAQ;QAC7C,cAAc,IAAA,oLAAY,EAAC,MAAM,YAAY,EAAE,MAAM,IAAI,KAAK;IAClE;AACJ;AACO,SAAS,iBAAiB,KAAK,EAAE,MAAM,EAAE,4BAA4B;IACxE,OAAO;QACH,MAAM,uJAAI,CAAC,qBAAqB;QAChC,aAAa,IAAA,mMAAkB,EAAC;QAChC,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO,MAAM,IAAI;QACrB;QACA,YAAY,kBAAkB,OAAO,QAAQ;IACjD;AACJ;AACO,SAAS,wBAAwB,iBAAiB;IACrD,OAAO,kBAAkB,cAAc;QAAE,QAAQ;IAAkB,GAAG,8KAA0B;AACpG;AACO,SAAS,kBAAkB,IAAI,EAAE,IAAI,EAAE,SAAS;IACnD,MAAM,qBAAqB,EAAE;IAC7B,IAAK,MAAM,WAAW,KAAM;QACxB,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,IAAI;QACJ,IAAI,aAAa,MAAM;YACnB,MAAM,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YACpD,IAAI,KAAK;gBACL,QAAQ,IAAA,oLAAY,EAAC,UAAU,IAAI,IAAI;YAC3C;QACJ;QACA,IAAI,SAAS,MAAM;YACf,QAAQ,IAAA,kMAAmB,EAAC;QAChC;QACA,IAAI,SAAS,MAAM;YACf,mBAAmB,IAAI,CAAC;gBACpB,MAAM,uJAAI,CAAC,QAAQ;gBACnB,MAAM;oBACF,MAAM,uJAAI,CAAC,IAAI;oBACf,OAAO;gBACX;gBACA;YACJ;QACJ;IACJ;IACA,OAAO;QACH,MAAM,uJAAI,CAAC,SAAS;QACpB,MAAM;YACF,MAAM,uJAAI,CAAC,IAAI;YACf,OAAO;QACX;QACA,WAAW;IACf;AACJ;AACO,SAAS,mBAAmB,MAAM,EAAE,eAAe;IACtD,MAAM,iBAAiB,EAAE;IACzB,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,gBAAiB;QAC1C,MAAM,YAAY,QAAQ,aAAa;QACvC,eAAe,IAAI,CAAC,kBAAkB,MAAM,MAAM;IACtD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/isDocumentNode.js"], "sourcesContent": ["import { Kind } from 'graphql';\nexport function isDocumentNode(object) {\n    return object && typeof object === 'object' && 'kind' in object && object.kind === Kind.DOCUMENT;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,eAAe,MAAM;IACjC,OAAO,UAAU,OAAO,WAAW,YAAY,UAAU,UAAU,OAAO,IAAI,KAAK,uJAAI,CAAC,QAAQ;AACpG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/comments.js"], "sourcesContent": ["import { TokenKind, visit, } from 'graphql';\nconst MAX_LINE_LENGTH = 80;\nlet commentsRegistry = {};\nexport function resetComments() {\n    commentsRegistry = {};\n}\nexport function collectComment(node) {\n    const entityName = node.name?.value;\n    if (entityName == null) {\n        return;\n    }\n    pushComment(node, entityName);\n    switch (node.kind) {\n        case 'EnumTypeDefinition':\n            if (node.values) {\n                for (const value of node.values) {\n                    pushComment(value, entityName, value.name.value);\n                }\n            }\n            break;\n        case 'ObjectTypeDefinition':\n        case 'InputObjectTypeDefinition':\n        case 'InterfaceTypeDefinition':\n            if (node.fields) {\n                for (const field of node.fields) {\n                    pushComment(field, entityName, field.name.value);\n                    if (isFieldDefinitionNode(field) && field.arguments) {\n                        for (const arg of field.arguments) {\n                            pushComment(arg, entityName, field.name.value, arg.name.value);\n                        }\n                    }\n                }\n            }\n            break;\n    }\n}\nexport function pushComment(node, entity, field, argument) {\n    const comment = getComment(node);\n    if (typeof comment !== 'string' || comment.length === 0) {\n        return;\n    }\n    const keys = [entity];\n    if (field) {\n        keys.push(field);\n        if (argument) {\n            keys.push(argument);\n        }\n    }\n    const path = keys.join('.');\n    if (!commentsRegistry[path]) {\n        commentsRegistry[path] = [];\n    }\n    commentsRegistry[path].push(comment);\n}\nexport function printComment(comment) {\n    return '\\n# ' + comment.replace(/\\n/g, '\\n# ');\n}\n/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/**\n * NOTE: ==> This file has been modified just to add comments to the printed AST\n * This is a temp measure, we will move to using the original non modified printer.js ASAP.\n */\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\nfunction join(maybeArray, separator) {\n    return maybeArray ? maybeArray.filter(x => x).join(separator || '') : '';\n}\nfunction hasMultilineItems(maybeArray) {\n    return maybeArray?.some(str => str.includes('\\n')) ?? false;\n}\nfunction addDescription(cb) {\n    return (node, _key, _parent, path, ancestors) => {\n        const keys = [];\n        const parent = path.reduce((prev, key) => {\n            if (['fields', 'arguments', 'values'].includes(key) && prev.name) {\n                keys.push(prev.name.value);\n            }\n            return prev[key];\n        }, ancestors[0]);\n        const key = [...keys, parent?.name?.value].filter(Boolean).join('.');\n        const items = [];\n        if (node.kind.includes('Definition') && commentsRegistry[key]) {\n            items.push(...commentsRegistry[key]);\n        }\n        return join([...items.map(printComment), node.description, cb(node, _key, _parent, path, ancestors)], '\\n');\n    };\n}\nfunction indent(maybeString) {\n    return maybeString && `  ${maybeString.replace(/\\n/g, '\\n  ')}`;\n}\n/**\n * Given array, print each item on its own line, wrapped in an\n * indented \"{ }\" block.\n */\nfunction block(array) {\n    return array && array.length !== 0 ? `{\\n${indent(join(array, '\\n'))}\\n}` : '';\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise\n * print an empty string.\n */\nfunction wrap(start, maybeString, end) {\n    return maybeString ? start + maybeString + (end || '') : '';\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n */\nfunction printBlockString(value, isDescription = false) {\n    const escaped = value.replace(/\\\\/g, '\\\\\\\\').replace(/\"\"\"/g, '\\\\\"\"\"');\n    return (value[0] === ' ' || value[0] === '\\t') && value.indexOf('\\n') === -1\n        ? `\"\"\"${escaped.replace(/\"$/, '\"\\n')}\"\"\"`\n        : `\"\"\"\\n${isDescription ? escaped : indent(escaped)}\\n\"\"\"`;\n}\nconst printDocASTReducer = {\n    Name: { leave: node => node.value },\n    Variable: { leave: node => '$' + node.name },\n    // Document\n    Document: {\n        leave: node => join(node.definitions, '\\n\\n'),\n    },\n    OperationDefinition: {\n        leave: node => {\n            const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n            const prefix = join([node.operation, join([node.name, varDefs]), join(node.directives, ' ')], ' ');\n            // the query short form.\n            return prefix + ' ' + node.selectionSet;\n        },\n    },\n    VariableDefinition: {\n        leave: ({ variable, type, defaultValue, directives }) => variable + ': ' + type + wrap(' = ', defaultValue) + wrap(' ', join(directives, ' ')),\n    },\n    SelectionSet: { leave: ({ selections }) => block(selections) },\n    Field: {\n        leave({ alias, name, arguments: args, directives, selectionSet }) {\n            const prefix = wrap('', alias, ': ') + name;\n            let argsLine = prefix + wrap('(', join(args, ', '), ')');\n            if (argsLine.length > MAX_LINE_LENGTH) {\n                argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n            }\n            return join([argsLine, join(directives, ' '), selectionSet], ' ');\n        },\n    },\n    Argument: { leave: ({ name, value }) => name + ': ' + value },\n    // Fragments\n    FragmentSpread: {\n        leave: ({ name, directives }) => '...' + name + wrap(' ', join(directives, ' ')),\n    },\n    InlineFragment: {\n        leave: ({ typeCondition, directives, selectionSet }) => join(['...', wrap('on ', typeCondition), join(directives, ' '), selectionSet], ' '),\n    },\n    FragmentDefinition: {\n        leave: ({ name, typeCondition, variableDefinitions, directives, selectionSet }) => \n        // Note: fragment variable definitions are experimental and may be changed\n        // or removed in the future.\n        `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` +\n            `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` +\n            selectionSet,\n    },\n    // Value\n    IntValue: { leave: ({ value }) => value },\n    FloatValue: { leave: ({ value }) => value },\n    StringValue: {\n        leave: ({ value, block: isBlockString }) => {\n            if (isBlockString) {\n                return printBlockString(value);\n            }\n            return JSON.stringify(value);\n        },\n    },\n    BooleanValue: { leave: ({ value }) => (value ? 'true' : 'false') },\n    NullValue: { leave: () => 'null' },\n    EnumValue: { leave: ({ value }) => value },\n    ListValue: { leave: ({ values }) => '[' + join(values, ', ') + ']' },\n    ObjectValue: { leave: ({ fields }) => '{' + join(fields, ', ') + '}' },\n    ObjectField: { leave: ({ name, value }) => name + ': ' + value },\n    // Directive\n    Directive: {\n        leave: ({ name, arguments: args }) => '@' + name + wrap('(', join(args, ', '), ')'),\n    },\n    // Type\n    NamedType: { leave: ({ name }) => name },\n    ListType: { leave: ({ type }) => '[' + type + ']' },\n    NonNullType: { leave: ({ type }) => type + '!' },\n    // Type System Definitions\n    SchemaDefinition: {\n        leave: ({ directives, operationTypes }) => join(['schema', join(directives, ' '), block(operationTypes)], ' '),\n    },\n    OperationTypeDefinition: {\n        leave: ({ operation, type }) => operation + ': ' + type,\n    },\n    ScalarTypeDefinition: {\n        leave: ({ name, directives }) => join(['scalar', name, join(directives, ' ')], ' '),\n    },\n    ObjectTypeDefinition: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'type',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    FieldDefinition: {\n        leave: ({ name, arguments: args, type, directives }) => name +\n            (hasMultilineItems(args)\n                ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n                : wrap('(', join(args, ', '), ')')) +\n            ': ' +\n            type +\n            wrap(' ', join(directives, ' ')),\n    },\n    InputValueDefinition: {\n        leave: ({ name, type, defaultValue, directives }) => join([name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')], ' '),\n    },\n    InterfaceTypeDefinition: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'interface',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    UnionTypeDefinition: {\n        leave: ({ name, directives, types }) => join(['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' '),\n    },\n    EnumTypeDefinition: {\n        leave: ({ name, directives, values }) => join(['enum', name, join(directives, ' '), block(values)], ' '),\n    },\n    EnumValueDefinition: {\n        leave: ({ name, directives }) => join([name, join(directives, ' ')], ' '),\n    },\n    InputObjectTypeDefinition: {\n        leave: ({ name, directives, fields }) => join(['input', name, join(directives, ' '), block(fields)], ' '),\n    },\n    DirectiveDefinition: {\n        leave: ({ name, arguments: args, repeatable, locations }) => 'directive @' +\n            name +\n            (hasMultilineItems(args)\n                ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n                : wrap('(', join(args, ', '), ')')) +\n            (repeatable ? ' repeatable' : '') +\n            ' on ' +\n            join(locations, ' | '),\n    },\n    SchemaExtension: {\n        leave: ({ directives, operationTypes }) => join(['extend schema', join(directives, ' '), block(operationTypes)], ' '),\n    },\n    ScalarTypeExtension: {\n        leave: ({ name, directives }) => join(['extend scalar', name, join(directives, ' ')], ' '),\n    },\n    ObjectTypeExtension: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'extend type',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    InterfaceTypeExtension: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'extend interface',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    UnionTypeExtension: {\n        leave: ({ name, directives, types }) => join(['extend union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' '),\n    },\n    EnumTypeExtension: {\n        leave: ({ name, directives, values }) => join(['extend enum', name, join(directives, ' '), block(values)], ' '),\n    },\n    InputObjectTypeExtension: {\n        leave: ({ name, directives, fields }) => join(['extend input', name, join(directives, ' '), block(fields)], ' '),\n    },\n};\nconst printDocASTReducerWithComments = Object.keys(printDocASTReducer).reduce((prev, key) => ({\n    ...prev,\n    [key]: {\n        leave: addDescription(printDocASTReducer[key].leave),\n    },\n}), {});\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\nexport function printWithComments(ast) {\n    return visit(ast, printDocASTReducerWithComments);\n}\nfunction isFieldDefinitionNode(node) {\n    return node.kind === 'FieldDefinition';\n}\n// graphql < v13 and > v15 does not export getDescription\nexport function getDescription(node, options) {\n    if (node.description != null) {\n        return node.description.value;\n    }\n    if (options?.commentDescriptions) {\n        return getComment(node);\n    }\n}\nexport function getComment(node) {\n    const rawValue = getLeadingCommentBlock(node);\n    if (rawValue !== undefined) {\n        return dedentBlockStringValue(`\\n${rawValue}`);\n    }\n}\nexport function getLeadingCommentBlock(node) {\n    const loc = node.loc;\n    if (!loc) {\n        return;\n    }\n    const comments = [];\n    let token = loc.startToken.prev;\n    while (token != null &&\n        token.kind === TokenKind.COMMENT &&\n        token.next != null &&\n        token.prev != null &&\n        token.line + 1 === token.next.line &&\n        token.line !== token.prev.line) {\n        const value = String(token.value);\n        comments.push(value);\n        token = token.prev;\n    }\n    return comments.length > 0 ? comments.reverse().join('\\n') : undefined;\n}\nexport function dedentBlockStringValue(rawString) {\n    // Expand a block string's raw value into independent lines.\n    const lines = rawString.split(/\\r\\n|[\\n\\r]/g);\n    // Remove common indentation from all lines but first.\n    const commonIndent = getBlockStringIndentation(lines);\n    if (commonIndent !== 0) {\n        for (let i = 1; i < lines.length; i++) {\n            lines[i] = lines[i].slice(commonIndent);\n        }\n    }\n    // Remove leading and trailing blank lines.\n    while (lines.length > 0 && isBlank(lines[0])) {\n        lines.shift();\n    }\n    while (lines.length > 0 && isBlank(lines[lines.length - 1])) {\n        lines.pop();\n    }\n    // Return a string of the lines joined with U+000A.\n    return lines.join('\\n');\n}\n/**\n * @internal\n */\nexport function getBlockStringIndentation(lines) {\n    let commonIndent = null;\n    for (let i = 1; i < lines.length; i++) {\n        const line = lines[i];\n        const indent = leadingWhitespace(line);\n        if (indent === line.length) {\n            continue; // skip empty lines\n        }\n        if (commonIndent === null || indent < commonIndent) {\n            commonIndent = indent;\n            if (commonIndent === 0) {\n                break;\n            }\n        }\n    }\n    return commonIndent === null ? 0 : commonIndent;\n}\nfunction leadingWhitespace(str) {\n    let i = 0;\n    while (i < str.length && (str[i] === ' ' || str[i] === '\\t')) {\n        i++;\n    }\n    return i;\n}\nfunction isBlank(str) {\n    return leadingWhitespace(str) === str.length;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AACA,MAAM,kBAAkB;AACxB,IAAI,mBAAmB,CAAC;AACjB,SAAS;IACZ,mBAAmB,CAAC;AACxB;AACO,SAAS,eAAe,IAAI;IAC/B,MAAM,aAAa,KAAK,IAAI,EAAE;IAC9B,IAAI,cAAc,MAAM;QACpB;IACJ;IACA,YAAY,MAAM;IAClB,OAAQ,KAAK,IAAI;QACb,KAAK;YACD,IAAI,KAAK,MAAM,EAAE;gBACb,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;oBAC7B,YAAY,OAAO,YAAY,MAAM,IAAI,CAAC,KAAK;gBACnD;YACJ;YACA;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,KAAK,MAAM,EAAE;gBACb,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;oBAC7B,YAAY,OAAO,YAAY,MAAM,IAAI,CAAC,KAAK;oBAC/C,IAAI,sBAAsB,UAAU,MAAM,SAAS,EAAE;wBACjD,KAAK,MAAM,OAAO,MAAM,SAAS,CAAE;4BAC/B,YAAY,KAAK,YAAY,MAAM,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK;wBACjE;oBACJ;gBACJ;YACJ;YACA;IACR;AACJ;AACO,SAAS,YAAY,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ;IACrD,MAAM,UAAU,WAAW;IAC3B,IAAI,OAAO,YAAY,YAAY,QAAQ,MAAM,KAAK,GAAG;QACrD;IACJ;IACA,MAAM,OAAO;QAAC;KAAO;IACrB,IAAI,OAAO;QACP,KAAK,IAAI,CAAC;QACV,IAAI,UAAU;YACV,KAAK,IAAI,CAAC;QACd;IACJ;IACA,MAAM,OAAO,KAAK,IAAI,CAAC;IACvB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;QACzB,gBAAgB,CAAC,KAAK,GAAG,EAAE;IAC/B;IACA,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;AAChC;AACO,SAAS,aAAa,OAAO;IAChC,OAAO,SAAS,QAAQ,OAAO,CAAC,OAAO;AAC3C;AACA;;;;;CAKC,GACD;;;CAGC,GACD;;;CAGC,GACD,SAAS,KAAK,UAAU,EAAE,SAAS;IAC/B,OAAO,aAAa,WAAW,MAAM,CAAC,CAAA,IAAK,GAAG,IAAI,CAAC,aAAa,MAAM;AAC1E;AACA,SAAS,kBAAkB,UAAU;IACjC,OAAO,YAAY,KAAK,CAAA,MAAO,IAAI,QAAQ,CAAC,UAAU;AAC1D;AACA,SAAS,eAAe,EAAE;IACtB,OAAO,CAAC,MAAM,MAAM,SAAS,MAAM;QAC/B,MAAM,OAAO,EAAE;QACf,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC,MAAM;YAC9B,IAAI;gBAAC;gBAAU;gBAAa;aAAS,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE;gBAC9D,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK;YAC7B;YACA,OAAO,IAAI,CAAC,IAAI;QACpB,GAAG,SAAS,CAAC,EAAE;QACf,MAAM,MAAM;eAAI;YAAM,QAAQ,MAAM;SAAM,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QAChE,MAAM,QAAQ,EAAE;QAChB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,iBAAiB,gBAAgB,CAAC,IAAI,EAAE;YAC3D,MAAM,IAAI,IAAI,gBAAgB,CAAC,IAAI;QACvC;QACA,OAAO,KAAK;eAAI,MAAM,GAAG,CAAC;YAAe,KAAK,WAAW;YAAE,GAAG,MAAM,MAAM,SAAS,MAAM;SAAW,EAAE;IAC1G;AACJ;AACA,SAAS,OAAO,WAAW;IACvB,OAAO,eAAe,CAAC,EAAE,EAAE,YAAY,OAAO,CAAC,OAAO,SAAS;AACnE;AACA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAChB,OAAO,SAAS,MAAM,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,OAAO,KAAK,OAAO,OAAO,GAAG,CAAC,GAAG;AAChF;AACA;;;CAGC,GACD,SAAS,KAAK,KAAK,EAAE,WAAW,EAAE,GAAG;IACjC,OAAO,cAAc,QAAQ,cAAc,CAAC,OAAO,EAAE,IAAI;AAC7D;AACA;;;;CAIC,GACD,SAAS,iBAAiB,KAAK,EAAE,gBAAgB,KAAK;IAClD,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO,QAAQ,OAAO,CAAC,QAAQ;IAC7D,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,IAAI,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,IACrE,CAAC,GAAG,EAAE,QAAQ,OAAO,CAAC,MAAM,OAAO,GAAG,CAAC,GACvC,CAAC,KAAK,EAAE,gBAAgB,UAAU,OAAO,SAAS,KAAK,CAAC;AAClE;AACA,MAAM,qBAAqB;IACvB,MAAM;QAAE,OAAO,CAAA,OAAQ,KAAK,KAAK;IAAC;IAClC,UAAU;QAAE,OAAO,CAAA,OAAQ,MAAM,KAAK,IAAI;IAAC;IAC3C,WAAW;IACX,UAAU;QACN,OAAO,CAAA,OAAQ,KAAK,KAAK,WAAW,EAAE;IAC1C;IACA,qBAAqB;QACjB,OAAO,CAAA;YACH,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,mBAAmB,EAAE,OAAO;YAChE,MAAM,SAAS,KAAK;gBAAC,KAAK,SAAS;gBAAE,KAAK;oBAAC,KAAK,IAAI;oBAAE;iBAAQ;gBAAG,KAAK,KAAK,UAAU,EAAE;aAAK,EAAE;YAC9F,wBAAwB;YACxB,OAAO,SAAS,MAAM,KAAK,YAAY;QAC3C;IACJ;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,GAAK,WAAW,OAAO,OAAO,KAAK,OAAO,gBAAgB,KAAK,KAAK,KAAK,YAAY;IAC7I;IACA,cAAc;QAAE,OAAO,CAAC,EAAE,UAAU,EAAE,GAAK,MAAM;IAAY;IAC7D,OAAO;QACH,OAAM,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE;YAC5D,MAAM,SAAS,KAAK,IAAI,OAAO,QAAQ;YACvC,IAAI,WAAW,SAAS,KAAK,KAAK,KAAK,MAAM,OAAO;YACpD,IAAI,SAAS,MAAM,GAAG,iBAAiB;gBACnC,WAAW,SAAS,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ;YAC9D;YACA,OAAO,KAAK;gBAAC;gBAAU,KAAK,YAAY;gBAAM;aAAa,EAAE;QACjE;IACJ;IACA,UAAU;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,OAAO,OAAO;IAAM;IAC5D,YAAY;IACZ,gBAAgB;QACZ,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,QAAQ,OAAO,KAAK,KAAK,KAAK,YAAY;IAC/E;IACA,gBAAgB;QACZ,OAAO,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,GAAK,KAAK;gBAAC;gBAAO,KAAK,OAAO;gBAAgB,KAAK,YAAY;gBAAM;aAAa,EAAE;IAC3I;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,mBAAmB,EAAE,UAAU,EAAE,YAAY,EAAE,GAC9E,0EAA0E;YAC1E,4BAA4B;YAC5B,CAAC,SAAS,EAAE,OAAO,KAAK,KAAK,KAAK,qBAAqB,OAAO,KAAK,CAAC,CAAC,GACjE,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,KAAK,IAAI,KAAK,YAAY,MAAM,MAAM,GAC7D;IACR;IACA,QAAQ;IACR,UAAU;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK;IAAM;IACxC,YAAY;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK;IAAM;IAC1C,aAAa;QACT,OAAO,CAAC,EAAE,KAAK,EAAE,OAAO,aAAa,EAAE;YACnC,IAAI,eAAe;gBACf,OAAO,iBAAiB;YAC5B;YACA,OAAO,KAAK,SAAS,CAAC;QAC1B;IACJ;IACA,cAAc;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAM,QAAQ,SAAS;IAAS;IACjE,WAAW;QAAE,OAAO,IAAM;IAAO;IACjC,WAAW;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK;IAAM;IACzC,WAAW;QAAE,OAAO,CAAC,EAAE,MAAM,EAAE,GAAK,MAAM,KAAK,QAAQ,QAAQ;IAAI;IACnE,aAAa;QAAE,OAAO,CAAC,EAAE,MAAM,EAAE,GAAK,MAAM,KAAK,QAAQ,QAAQ;IAAI;IACrE,aAAa;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,OAAO,OAAO;IAAM;IAC/D,YAAY;IACZ,WAAW;QACP,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,GAAK,MAAM,OAAO,KAAK,KAAK,KAAK,MAAM,OAAO;IACnF;IACA,OAAO;IACP,WAAW;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK;IAAK;IACvC,UAAU;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK,MAAM,OAAO;IAAI;IAClD,aAAa;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK,OAAO;IAAI;IAC/C,0BAA0B;IAC1B,kBAAkB;QACd,OAAO,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,GAAK,KAAK;gBAAC;gBAAU,KAAK,YAAY;gBAAM,MAAM;aAAgB,EAAE;IAC9G;IACA,yBAAyB;QACrB,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,YAAY,OAAO;IACvD;IACA,sBAAsB;QAClB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC;gBAAU;gBAAM,KAAK,YAAY;aAAK,EAAE;IACnF;IACA,sBAAsB;QAClB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,iBAAiB;QACb,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,OACpD,CAAC,kBAAkB,QACb,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ,SACtC,KAAK,KAAK,KAAK,MAAM,OAAO,IAAI,IACtC,OACA,OACA,KAAK,KAAK,KAAK,YAAY;IACnC;IACA,sBAAsB;QAClB,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC,OAAO,OAAO;gBAAM,KAAK,MAAM;gBAAe,KAAK,YAAY;aAAK,EAAE;IACrI;IACA,yBAAyB;QACrB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAK,KAAK;gBAAC;gBAAS;gBAAM,KAAK,YAAY;gBAAM,KAAK,MAAM,KAAK,OAAO;aAAQ,EAAE;IACzH;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAQ;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IACxG;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC;gBAAM,KAAK,YAAY;aAAK,EAAE;IACzE;IACA,2BAA2B;QACvB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAS;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IACzG;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAK,gBACzD,OACA,CAAC,kBAAkB,QACb,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ,SACtC,KAAK,KAAK,KAAK,MAAM,OAAO,IAAI,IACtC,CAAC,aAAa,gBAAgB,EAAE,IAChC,SACA,KAAK,WAAW;IACxB;IACA,iBAAiB;QACb,OAAO,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,GAAK,KAAK;gBAAC;gBAAiB,KAAK,YAAY;gBAAM,MAAM;aAAgB,EAAE;IACrH;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC;gBAAiB;gBAAM,KAAK,YAAY;aAAK,EAAE;IAC1F;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,wBAAwB;QACpB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAK,KAAK;gBAAC;gBAAgB;gBAAM,KAAK,YAAY;gBAAM,KAAK,MAAM,KAAK,OAAO;aAAQ,EAAE;IAChI;IACA,mBAAmB;QACf,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAe;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IAC/G;IACA,0BAA0B;QACtB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAgB;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IAChH;AACJ;AACA,MAAM,iCAAiC,OAAO,IAAI,CAAC,oBAAoB,MAAM,CAAC,CAAC,MAAM,MAAQ,CAAC;QAC1F,GAAG,IAAI;QACP,CAAC,IAAI,EAAE;YACH,OAAO,eAAe,kBAAkB,CAAC,IAAI,CAAC,KAAK;QACvD;IACJ,CAAC,GAAG,CAAC;AAKE,SAAS,kBAAkB,GAAG;IACjC,OAAO,IAAA,0JAAK,EAAC,KAAK;AACtB;AACA,SAAS,sBAAsB,IAAI;IAC/B,OAAO,KAAK,IAAI,KAAK;AACzB;AAEO,SAAS,eAAe,IAAI,EAAE,OAAO;IACxC,IAAI,KAAK,WAAW,IAAI,MAAM;QAC1B,OAAO,KAAK,WAAW,CAAC,KAAK;IACjC;IACA,IAAI,SAAS,qBAAqB;QAC9B,OAAO,WAAW;IACtB;AACJ;AACO,SAAS,WAAW,IAAI;IAC3B,MAAM,WAAW,uBAAuB;IACxC,IAAI,aAAa,WAAW;QACxB,OAAO,uBAAuB,CAAC,EAAE,EAAE,UAAU;IACjD;AACJ;AACO,SAAS,uBAAuB,IAAI;IACvC,MAAM,MAAM,KAAK,GAAG;IACpB,IAAI,CAAC,KAAK;QACN;IACJ;IACA,MAAM,WAAW,EAAE;IACnB,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI;IAC/B,MAAO,SAAS,QACZ,MAAM,IAAI,KAAK,gKAAS,CAAC,OAAO,IAChC,MAAM,IAAI,IAAI,QACd,MAAM,IAAI,IAAI,QACd,MAAM,IAAI,GAAG,MAAM,MAAM,IAAI,CAAC,IAAI,IAClC,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,CAAE;QAChC,MAAM,QAAQ,OAAO,MAAM,KAAK;QAChC,SAAS,IAAI,CAAC;QACd,QAAQ,MAAM,IAAI;IACtB;IACA,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,OAAO,GAAG,IAAI,CAAC,QAAQ;AACjE;AACO,SAAS,uBAAuB,SAAS;IAC5C,4DAA4D;IAC5D,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,sDAAsD;IACtD,MAAM,eAAe,0BAA0B;IAC/C,IAAI,iBAAiB,GAAG;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B;IACJ;IACA,2CAA2C;IAC3C,MAAO,MAAM,MAAM,GAAG,KAAK,QAAQ,KAAK,CAAC,EAAE,EAAG;QAC1C,MAAM,KAAK;IACf;IACA,MAAO,MAAM,MAAM,GAAG,KAAK,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAG;QACzD,MAAM,GAAG;IACb;IACA,mDAAmD;IACnD,OAAO,MAAM,IAAI,CAAC;AACtB;AAIO,SAAS,0BAA0B,KAAK;IAC3C,IAAI,eAAe;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,SAAS,kBAAkB;QACjC,IAAI,WAAW,KAAK,MAAM,EAAE;YACxB,UAAU,mBAAmB;QACjC;QACA,IAAI,iBAAiB,QAAQ,SAAS,cAAc;YAChD,eAAe;YACf,IAAI,iBAAiB,GAAG;gBACpB;YACJ;QACJ;IACJ;IACA,OAAO,iBAAiB,OAAO,IAAI;AACvC;AACA,SAAS,kBAAkB,GAAG;IAC1B,IAAI,IAAI;IACR,MAAO,IAAI,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,EAAG;QAC1D;IACJ;IACA,OAAO;AACX;AACA,SAAS,QAAQ,GAAG;IAChB,OAAO,kBAAkB,SAAS,IAAI,MAAM;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/forEachDefaultValue.js"], "sourcesContent": ["import { getNamedType, isInputObjectType, isObjectType } from 'graphql';\nexport function forEachDefaultValue(schema, fn) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        if (!getNamedType(type).name.startsWith('__')) {\n            if (isObjectType(type)) {\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    for (const arg of field.args) {\n                        arg.defaultValue = fn(arg.type, arg.defaultValue);\n                    }\n                }\n            }\n            else if (isInputObjectType(type)) {\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    field.defaultValue = fn(field.type, field.defaultValue);\n                }\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,oBAAoB,MAAM,EAAE,EAAE;IAC1C,MAAM,UAAU,OAAO,UAAU;IACjC,IAAK,MAAM,YAAY,QAAS;QAC5B,MAAM,OAAO,OAAO,CAAC,SAAS;QAC9B,IAAI,CAAC,IAAA,gKAAY,EAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO;YAC3C,IAAI,IAAA,gKAAY,EAAC,OAAO;gBACpB,MAAM,SAAS,KAAK,SAAS;gBAC7B,IAAK,MAAM,aAAa,OAAQ;oBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;oBAC/B,KAAK,MAAM,OAAO,MAAM,IAAI,CAAE;wBAC1B,IAAI,YAAY,GAAG,GAAG,IAAI,IAAI,EAAE,IAAI,YAAY;oBACpD;gBACJ;YACJ,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;gBAC9B,MAAM,SAAS,KAAK,SAAS;gBAC7B,IAAK,MAAM,aAAa,OAAQ;oBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;oBAC/B,MAAM,YAAY,GAAG,GAAG,MAAM,IAAI,EAAE,MAAM,YAAY;gBAC1D;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/forEachField.js"], "sourcesContent": ["import { getNamedType, isObjectType } from 'graphql';\nexport function forEachField(schema, fn) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        // TODO: maybe have an option to include these?\n        if (!getNamedType(type).name.startsWith('__') && isObjectType(type)) {\n            const fields = type.getFields();\n            for (const fieldName in fields) {\n                const field = fields[fieldName];\n                fn(field, typeName, fieldName);\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,aAAa,MAAM,EAAE,EAAE;IACnC,MAAM,UAAU,OAAO,UAAU;IACjC,IAAK,MAAM,YAAY,QAAS;QAC5B,MAAM,OAAO,OAAO,CAAC,SAAS;QAC9B,+CAA+C;QAC/C,IAAI,CAAC,IAAA,gKAAY,EAAC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,IAAA,gKAAY,EAAC,OAAO;YACjE,MAAM,SAAS,KAAK,SAAS;YAC7B,IAAK,MAAM,aAAa,OAAQ;gBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;gBAC/B,GAAG,OAAO,UAAU;YACxB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2029, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/heal.js"], "sourcesContent": ["import { Graph<PERSON><PERSON><PERSON>, GraphQL<PERSON>on<PERSON>ull, isInputObjectType, isInterfaceType, isLeafType, isListType, isNamedType, isNonNullType, isObjectType, isUnionType, } from 'graphql';\n// Update any references to named schema types that disagree with the named\n// types found in schema.getTypeMap().\n//\n// healSchema and its callers (visitSchema/visitSchemaDirectives) all modify the schema in place.\n// Therefore, private variables (such as the stored implementation map and the proper root types)\n// are not updated.\n//\n// If this causes issues, the schema could be more aggressively healed as follows:\n//\n// healSchema(schema);\n// const config = schema.toConfig()\n// const healedSchema = new GraphQLSchema({\n//   ...config,\n//   query: schema.getType('<desired new root query type name>'),\n//   mutation: schema.getType('<desired new root mutation type name>'),\n//   subscription: schema.getType('<desired new root subscription type name>'),\n// });\n//\n// One can then also -- if necessary --  assign the correct private variables to the initial schema\n// as follows:\n// Object.assign(schema, healedSchema);\n//\n// These steps are not taken automatically to preserve backwards compatibility with graphql-tools v4.\n// See https://github.com/ardatan/graphql-tools/issues/1462\n//\n// They were briefly taken in v5, but can now be phased out as they were only required when other\n// areas of the codebase were using healSchema and visitSchema more extensively.\n//\nexport function healSchema(schema) {\n    healTypes(schema.getTypeMap(), schema.getDirectives());\n    return schema;\n}\nexport function healTypes(originalTypeMap, directives) {\n    const actualNamedTypeMap = Object.create(null);\n    // If any of the .name properties of the GraphQLNamedType objects in\n    // schema.getTypeMap() have changed, the keys of the type map need to\n    // be updated accordingly.\n    for (const typeName in originalTypeMap) {\n        const namedType = originalTypeMap[typeName];\n        if (namedType == null || typeName.startsWith('__')) {\n            continue;\n        }\n        const actualName = namedType.name;\n        if (actualName.startsWith('__')) {\n            continue;\n        }\n        if (actualNamedTypeMap[actualName] != null) {\n            console.warn(`Duplicate schema type name ${actualName} found; keeping the existing one found in the schema`);\n            continue;\n        }\n        actualNamedTypeMap[actualName] = namedType;\n        // Note: we are deliberately leaving namedType in the schema by its\n        // original name (which might be different from actualName), so that\n        // references by that name can be healed.\n    }\n    // Now add back every named type by its actual name.\n    for (const typeName in actualNamedTypeMap) {\n        const namedType = actualNamedTypeMap[typeName];\n        originalTypeMap[typeName] = namedType;\n    }\n    // Directive declaration argument types can refer to named types.\n    for (const decl of directives) {\n        decl.args = decl.args.filter(arg => {\n            arg.type = healType(arg.type);\n            return arg.type !== null;\n        });\n    }\n    for (const typeName in originalTypeMap) {\n        const namedType = originalTypeMap[typeName];\n        // Heal all named types, except for dangling references, kept only to redirect.\n        if (!typeName.startsWith('__') && typeName in actualNamedTypeMap) {\n            if (namedType != null) {\n                healNamedType(namedType);\n            }\n        }\n    }\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__') && !(typeName in actualNamedTypeMap)) {\n            delete originalTypeMap[typeName];\n        }\n    }\n    function healNamedType(type) {\n        if (isObjectType(type)) {\n            healFields(type);\n            healInterfaces(type);\n            return;\n        }\n        else if (isInterfaceType(type)) {\n            healFields(type);\n            if ('getInterfaces' in type) {\n                healInterfaces(type);\n            }\n            return;\n        }\n        else if (isUnionType(type)) {\n            healUnderlyingTypes(type);\n            return;\n        }\n        else if (isInputObjectType(type)) {\n            healInputFields(type);\n            return;\n        }\n        else if (isLeafType(type)) {\n            return;\n        }\n        throw new Error(`Unexpected schema type: ${type}`);\n    }\n    function healFields(type) {\n        const fieldMap = type.getFields();\n        for (const [key, field] of Object.entries(fieldMap)) {\n            field.args\n                .map(arg => {\n                arg.type = healType(arg.type);\n                return arg.type === null ? null : arg;\n            })\n                .filter(Boolean);\n            field.type = healType(field.type);\n            if (field.type === null) {\n                delete fieldMap[key];\n            }\n        }\n    }\n    function healInterfaces(type) {\n        if ('getInterfaces' in type) {\n            const interfaces = type.getInterfaces();\n            interfaces.push(...interfaces\n                .splice(0)\n                .map(iface => healType(iface))\n                .filter(Boolean));\n        }\n    }\n    function healInputFields(type) {\n        const fieldMap = type.getFields();\n        for (const [key, field] of Object.entries(fieldMap)) {\n            field.type = healType(field.type);\n            if (field.type === null) {\n                delete fieldMap[key];\n            }\n        }\n    }\n    function healUnderlyingTypes(type) {\n        const types = type.getTypes();\n        types.push(...types\n            .splice(0)\n            .map(t => healType(t))\n            .filter(Boolean));\n    }\n    function healType(type) {\n        // Unwrap the two known wrapper types\n        if (isListType(type)) {\n            const healedType = healType(type.ofType);\n            return healedType != null ? new GraphQLList(healedType) : null;\n        }\n        else if (isNonNullType(type)) {\n            const healedType = healType(type.ofType);\n            return healedType != null ? new GraphQLNonNull(healedType) : null;\n        }\n        else if (isNamedType(type)) {\n            // If a type annotation on a field or an argument or a union member is\n            // any `GraphQLNamedType` with a `name`, then it must end up identical\n            // to `schema.getType(name)`, since `schema.getTypeMap()` is the source\n            // of truth for all named schema types.\n            // Note that new types can still be simply added by adding a field, as\n            // the official type will be undefined, not null.\n            const officialType = originalTypeMap[type.name];\n            if (officialType && type !== officialType) {\n                return officialType;\n            }\n        }\n        return type;\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AA6BO,SAAS,WAAW,MAAM;IAC7B,UAAU,OAAO,UAAU,IAAI,OAAO,aAAa;IACnD,OAAO;AACX;AACO,SAAS,UAAU,eAAe,EAAE,UAAU;IACjD,MAAM,qBAAqB,OAAO,MAAM,CAAC;IACzC,oEAAoE;IACpE,qEAAqE;IACrE,0BAA0B;IAC1B,IAAK,MAAM,YAAY,gBAAiB;QACpC,MAAM,YAAY,eAAe,CAAC,SAAS;QAC3C,IAAI,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;YAChD;QACJ;QACA,MAAM,aAAa,UAAU,IAAI;QACjC,IAAI,WAAW,UAAU,CAAC,OAAO;YAC7B;QACJ;QACA,IAAI,kBAAkB,CAAC,WAAW,IAAI,MAAM;YACxC,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,oDAAoD,CAAC;YAC3G;QACJ;QACA,kBAAkB,CAAC,WAAW,GAAG;IACjC,mEAAmE;IACnE,oEAAoE;IACpE,yCAAyC;IAC7C;IACA,oDAAoD;IACpD,IAAK,MAAM,YAAY,mBAAoB;QACvC,MAAM,YAAY,kBAAkB,CAAC,SAAS;QAC9C,eAAe,CAAC,SAAS,GAAG;IAChC;IACA,iEAAiE;IACjE,KAAK,MAAM,QAAQ,WAAY;QAC3B,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA;YACzB,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI;YAC5B,OAAO,IAAI,IAAI,KAAK;QACxB;IACJ;IACA,IAAK,MAAM,YAAY,gBAAiB;QACpC,MAAM,YAAY,eAAe,CAAC,SAAS;QAC3C,+EAA+E;QAC/E,IAAI,CAAC,SAAS,UAAU,CAAC,SAAS,YAAY,oBAAoB;YAC9D,IAAI,aAAa,MAAM;gBACnB,cAAc;YAClB;QACJ;IACJ;IACA,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,SAAS,CAAC,CAAC,YAAY,kBAAkB,GAAG;YACjE,OAAO,eAAe,CAAC,SAAS;QACpC;IACJ;IACA,SAAS,cAAc,IAAI;QACvB,IAAI,IAAA,gKAAY,EAAC,OAAO;YACpB,WAAW;YACX,eAAe;YACf;QACJ,OACK,IAAI,IAAA,mKAAe,EAAC,OAAO;YAC5B,WAAW;YACX,IAAI,mBAAmB,MAAM;gBACzB,eAAe;YACnB;YACA;QACJ,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;YACxB,oBAAoB;YACpB;QACJ,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;YAC9B,gBAAgB;YAChB;QACJ,OACK,IAAI,IAAA,8JAAU,EAAC,OAAO;YACvB;QACJ;QACA,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM;IACrD;IACA,SAAS,WAAW,IAAI;QACpB,MAAM,WAAW,KAAK,SAAS;QAC/B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;YACjD,MAAM,IAAI,CACL,GAAG,CAAC,CAAA;gBACL,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI;gBAC5B,OAAO,IAAI,IAAI,KAAK,OAAO,OAAO;YACtC,GACK,MAAM,CAAC;YACZ,MAAM,IAAI,GAAG,SAAS,MAAM,IAAI;YAChC,IAAI,MAAM,IAAI,KAAK,MAAM;gBACrB,OAAO,QAAQ,CAAC,IAAI;YACxB;QACJ;IACJ;IACA,SAAS,eAAe,IAAI;QACxB,IAAI,mBAAmB,MAAM;YACzB,MAAM,aAAa,KAAK,aAAa;YACrC,WAAW,IAAI,IAAI,WACd,MAAM,CAAC,GACP,GAAG,CAAC,CAAA,QAAS,SAAS,QACtB,MAAM,CAAC;QAChB;IACJ;IACA,SAAS,gBAAgB,IAAI;QACzB,MAAM,WAAW,KAAK,SAAS;QAC/B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;YACjD,MAAM,IAAI,GAAG,SAAS,MAAM,IAAI;YAChC,IAAI,MAAM,IAAI,KAAK,MAAM;gBACrB,OAAO,QAAQ,CAAC,IAAI;YACxB;QACJ;IACJ;IACA,SAAS,oBAAoB,IAAI;QAC7B,MAAM,QAAQ,KAAK,QAAQ;QAC3B,MAAM,IAAI,IAAI,MACT,MAAM,CAAC,GACP,GAAG,CAAC,CAAA,IAAK,SAAS,IAClB,MAAM,CAAC;IAChB;IACA,SAAS,SAAS,IAAI;QAClB,qCAAqC;QACrC,IAAI,IAAA,8JAAU,EAAC,OAAO;YAClB,MAAM,aAAa,SAAS,KAAK,MAAM;YACvC,OAAO,cAAc,OAAO,IAAI,+JAAW,CAAC,cAAc;QAC9D,OACK,IAAI,IAAA,iKAAa,EAAC,OAAO;YAC1B,MAAM,aAAa,SAAS,KAAK,MAAM;YACvC,OAAO,cAAc,OAAO,IAAI,kKAAc,CAAC,cAAc;QACjE,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;YACxB,sEAAsE;YACtE,sEAAsE;YACtE,uEAAuE;YACvE,uCAAuC;YACvC,sEAAsE;YACtE,iDAAiD;YACjD,MAAM,eAAe,eAAe,CAAC,KAAK,IAAI,CAAC;YAC/C,IAAI,gBAAgB,SAAS,cAAc;gBACvC,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/Interfaces.js"], "sourcesContent": ["export var MapperKind;\n(function (MapperKind) {\n    MapperKind[\"TYPE\"] = \"MapperKind.TYPE\";\n    MapperKind[\"SCALAR_TYPE\"] = \"MapperKind.SCALAR_TYPE\";\n    MapperKind[\"ENUM_TYPE\"] = \"MapperKind.ENUM_TYPE\";\n    MapperKind[\"COMPOSITE_TYPE\"] = \"MapperKind.COMPOSITE_TYPE\";\n    MapperKind[\"OBJECT_TYPE\"] = \"MapperKind.OBJECT_TYPE\";\n    MapperKind[\"INPUT_OBJECT_TYPE\"] = \"MapperKind.INPUT_OBJECT_TYPE\";\n    MapperKind[\"ABSTRACT_TYPE\"] = \"MapperKind.ABSTRACT_TYPE\";\n    MapperKind[\"UNION_TYPE\"] = \"MapperKind.UNION_TYPE\";\n    MapperKind[\"INTERFACE_TYPE\"] = \"MapperKind.INTERFACE_TYPE\";\n    MapperKind[\"ROOT_OBJECT\"] = \"MapperKind.ROOT_OBJECT\";\n    MapperKind[\"QUERY\"] = \"MapperKind.QUERY\";\n    MapperKind[\"MUTATION\"] = \"MapperKind.MUTATION\";\n    MapperKind[\"SUBSCRIPTION\"] = \"MapperKind.SUBSCRIPTION\";\n    MapperKind[\"DIRECTIVE\"] = \"MapperKind.DIRECTIVE\";\n    MapperKind[\"FIELD\"] = \"MapperKind.FIELD\";\n    MapperKind[\"COMPOSITE_FIELD\"] = \"MapperKind.COMPOSITE_FIELD\";\n    MapperKind[\"OBJECT_FIELD\"] = \"MapperKind.OBJECT_FIELD\";\n    MapperKind[\"ROOT_FIELD\"] = \"MapperKind.ROOT_FIELD\";\n    MapperKind[\"QUERY_ROOT_FIELD\"] = \"MapperKind.QUERY_ROOT_FIELD\";\n    MapperKind[\"MUTATION_ROOT_FIELD\"] = \"MapperKind.MUTATION_ROOT_FIELD\";\n    MapperKind[\"SUBSCRIPTION_ROOT_FIELD\"] = \"MapperKind.SUBSCRIPTION_ROOT_FIELD\";\n    MapperKind[\"INTERFACE_FIELD\"] = \"MapperKind.INTERFACE_FIELD\";\n    MapperKind[\"INPUT_OBJECT_FIELD\"] = \"MapperKind.INPUT_OBJECT_FIELD\";\n    MapperKind[\"ARGUMENT\"] = \"MapperKind.ARGUMENT\";\n    MapperKind[\"ENUM_VALUE\"] = \"MapperKind.ENUM_VALUE\";\n})(MapperKind || (MapperKind = {}));\n"], "names": [], "mappings": ";;;;AAAO,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,OAAO,GAAG;IACrB,UAAU,CAAC,cAAc,GAAG;IAC5B,UAAU,CAAC,YAAY,GAAG;IAC1B,UAAU,CAAC,iBAAiB,GAAG;IAC/B,UAAU,CAAC,cAAc,GAAG;IAC5B,UAAU,CAAC,oBAAoB,GAAG;IAClC,UAAU,CAAC,gBAAgB,GAAG;IAC9B,UAAU,CAAC,aAAa,GAAG;IAC3B,UAAU,CAAC,iBAAiB,GAAG;IAC/B,UAAU,CAAC,cAAc,GAAG;IAC5B,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,WAAW,GAAG;IACzB,UAAU,CAAC,eAAe,GAAG;IAC7B,UAAU,CAAC,YAAY,GAAG;IAC1B,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,kBAAkB,GAAG;IAChC,UAAU,CAAC,eAAe,GAAG;IAC7B,UAAU,CAAC,aAAa,GAAG;IAC3B,UAAU,CAAC,mBAAmB,GAAG;IACjC,UAAU,CAAC,sBAAsB,GAAG;IACpC,UAAU,CAAC,0BAA0B,GAAG;IACxC,UAAU,CAAC,kBAAkB,GAAG;IAChC,UAAU,CAAC,qBAAqB,GAAG;IACnC,UAAU,CAAC,WAAW,GAAG;IACzB,UAAU,CAAC,aAAa,GAAG;AAC/B,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/getObjectTypeFromTypeMap.js"], "sourcesContent": ["import { isObjectType } from 'graphql';\nexport function getObjectTypeFromTypeMap(typeMap, type) {\n    if (type) {\n        const maybeObjectType = typeMap[type.name];\n        if (isObjectType(maybeObjectType)) {\n            return maybeObjectType;\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,yBAAyB,OAAO,EAAE,IAAI;IAClD,IAAI,MAAM;QACN,MAAM,kBAAkB,OAAO,CAAC,KAAK,IAAI,CAAC;QAC1C,IAAI,IAAA,gKAAY,EAAC,kBAAkB;YAC/B,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/stub.js"], "sourcesContent": ["import { GraphQLBoolean, GraphQLFloat, GraphQLID, GraphQLInputObjectType, GraphQLInt, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLString, Kind, } from 'graphql';\nexport function createNamedStub(name, type) {\n    let constructor;\n    if (type === 'object') {\n        constructor = GraphQLObjectType;\n    }\n    else if (type === 'interface') {\n        constructor = GraphQLInterfaceType;\n    }\n    else {\n        constructor = GraphQLInputObjectType;\n    }\n    return new constructor({\n        name,\n        fields: {\n            _fake: {\n                type: GraphQLString,\n            },\n        },\n    });\n}\nexport function createStub(node, type) {\n    switch (node.kind) {\n        case Kind.LIST_TYPE:\n            return new GraphQLList(createStub(node.type, type));\n        case Kind.NON_NULL_TYPE:\n            return new GraphQLNonNull(createStub(node.type, type));\n        default:\n            if (type === 'output') {\n                return createNamedStub(node.name.value, 'object');\n            }\n            return createNamedStub(node.name.value, 'input');\n    }\n}\nexport function isNamedStub(type) {\n    if ('getFields' in type) {\n        const fields = type.getFields();\n        // eslint-disable-next-line no-unreachable-loop\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            return field.name === '_fake';\n        }\n    }\n    return false;\n}\nexport function getBuiltInForStub(type) {\n    switch (type.name) {\n        case GraphQLInt.name:\n            return GraphQLInt;\n        case GraphQLFloat.name:\n            return GraphQLFloat;\n        case GraphQLString.name:\n            return GraphQLString;\n        case GraphQLBoolean.name:\n            return GraphQLBoolean;\n        case GraphQLID.name:\n            return GraphQLID;\n        default:\n            return type;\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;;AACO,SAAS,gBAAgB,IAAI,EAAE,IAAI;IACtC,IAAI;IACJ,IAAI,SAAS,UAAU;QACnB,cAAc,qKAAiB;IACnC,OACK,IAAI,SAAS,aAAa;QAC3B,cAAc,wKAAoB;IACtC,OACK;QACD,cAAc,0KAAsB;IACxC;IACA,OAAO,IAAI,YAAY;QACnB;QACA,QAAQ;YACJ,OAAO;gBACH,MAAM,8JAAa;YACvB;QACJ;IACJ;AACJ;AACO,SAAS,WAAW,IAAI,EAAE,IAAI;IACjC,OAAQ,KAAK,IAAI;QACb,KAAK,uJAAI,CAAC,SAAS;YACf,OAAO,IAAI,+JAAW,CAAC,WAAW,KAAK,IAAI,EAAE;QACjD,KAAK,uJAAI,CAAC,aAAa;YACnB,OAAO,IAAI,kKAAc,CAAC,WAAW,KAAK,IAAI,EAAE;QACpD;YACI,IAAI,SAAS,UAAU;gBACnB,OAAO,gBAAgB,KAAK,IAAI,CAAC,KAAK,EAAE;YAC5C;YACA,OAAO,gBAAgB,KAAK,IAAI,CAAC,KAAK,EAAE;IAChD;AACJ;AACO,SAAS,YAAY,IAAI;IAC5B,IAAI,eAAe,MAAM;QACrB,MAAM,SAAS,KAAK,SAAS;QAC7B,+CAA+C;QAC/C,IAAK,MAAM,aAAa,OAAQ;YAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;YAC/B,OAAO,MAAM,IAAI,KAAK;QAC1B;IACJ;IACA,OAAO;AACX;AACO,SAAS,kBAAkB,IAAI;IAClC,OAAQ,KAAK,IAAI;QACb,KAAK,2JAAU,CAAC,IAAI;YAChB,OAAO,2JAAU;QACrB,KAAK,6JAAY,CAAC,IAAI;YAClB,OAAO,6JAAY;QACvB,KAAK,8JAAa,CAAC,IAAI;YACnB,OAAO,8JAAa;QACxB,KAAK,+JAAc,CAAC,IAAI;YACpB,OAAO,+JAAc;QACzB,KAAK,0JAAS,CAAC,IAAI;YACf,OAAO,0JAAS;QACpB;YACI,OAAO;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/rewire.js"], "sourcesContent": ["import { GraphQLDirective, GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNamedType, isNonNullType, isObjectType, isScalarType, isSpecifiedDirective, isSpecifiedScalarType, isUnionType, } from 'graphql';\nimport { getBuiltInForStub, isNamedStub } from './stub.js';\nexport function rewireTypes(originalTypeMap, directives) {\n    const referenceTypeMap = Object.create(null);\n    for (const typeName in originalTypeMap) {\n        referenceTypeMap[typeName] = originalTypeMap[typeName];\n    }\n    const newTypeMap = Object.create(null);\n    for (const typeName in referenceTypeMap) {\n        const namedType = referenceTypeMap[typeName];\n        if (namedType == null || typeName.startsWith('__')) {\n            continue;\n        }\n        const newName = namedType.name;\n        if (newName.startsWith('__')) {\n            continue;\n        }\n        if (newTypeMap[newName] != null) {\n            console.warn(`Duplicate schema type name ${newName} found; keeping the existing one found in the schema`);\n            continue;\n        }\n        newTypeMap[newName] = namedType;\n    }\n    for (const typeName in newTypeMap) {\n        newTypeMap[typeName] = rewireNamedType(newTypeMap[typeName]);\n    }\n    const newDirectives = directives.map(directive => rewireDirective(directive));\n    return {\n        typeMap: newTypeMap,\n        directives: newDirectives,\n    };\n    function rewireDirective(directive) {\n        if (isSpecifiedDirective(directive)) {\n            return directive;\n        }\n        const directiveConfig = directive.toConfig();\n        directiveConfig.args = rewireArgs(directiveConfig.args);\n        return new GraphQLDirective(directiveConfig);\n    }\n    function rewireArgs(args) {\n        const rewiredArgs = {};\n        for (const argName in args) {\n            const arg = args[argName];\n            const rewiredArgType = rewireType(arg.type);\n            if (rewiredArgType != null) {\n                arg.type = rewiredArgType;\n                rewiredArgs[argName] = arg;\n            }\n        }\n        return rewiredArgs;\n    }\n    function rewireNamedType(type) {\n        if (isObjectType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireFields(config.fields),\n                interfaces: () => rewireNamedTypes(config.interfaces),\n            };\n            return new GraphQLObjectType(newConfig);\n        }\n        else if (isInterfaceType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireFields(config.fields),\n            };\n            if ('interfaces' in newConfig) {\n                newConfig.interfaces = () => rewireNamedTypes(config.interfaces);\n            }\n            return new GraphQLInterfaceType(newConfig);\n        }\n        else if (isUnionType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                types: () => rewireNamedTypes(config.types),\n            };\n            return new GraphQLUnionType(newConfig);\n        }\n        else if (isInputObjectType(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireInputFields(config.fields),\n            };\n            return new GraphQLInputObjectType(newConfig);\n        }\n        else if (isEnumType(type)) {\n            const enumConfig = type.toConfig();\n            return new GraphQLEnumType(enumConfig);\n        }\n        else if (isScalarType(type)) {\n            if (isSpecifiedScalarType(type)) {\n                return type;\n            }\n            const scalarConfig = type.toConfig();\n            return new GraphQLScalarType(scalarConfig);\n        }\n        throw new Error(`Unexpected schema type: ${type}`);\n    }\n    function rewireFields(fields) {\n        const rewiredFields = {};\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            const rewiredFieldType = rewireType(field.type);\n            if (rewiredFieldType != null && field.args) {\n                field.type = rewiredFieldType;\n                field.args = rewireArgs(field.args);\n                rewiredFields[fieldName] = field;\n            }\n        }\n        return rewiredFields;\n    }\n    function rewireInputFields(fields) {\n        const rewiredFields = {};\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            const rewiredFieldType = rewireType(field.type);\n            if (rewiredFieldType != null) {\n                field.type = rewiredFieldType;\n                rewiredFields[fieldName] = field;\n            }\n        }\n        return rewiredFields;\n    }\n    function rewireNamedTypes(namedTypes) {\n        const rewiredTypes = [];\n        for (const namedType of namedTypes) {\n            const rewiredType = rewireType(namedType);\n            if (rewiredType != null) {\n                rewiredTypes.push(rewiredType);\n            }\n        }\n        return rewiredTypes;\n    }\n    function rewireType(type) {\n        if (isListType(type)) {\n            const rewiredType = rewireType(type.ofType);\n            return rewiredType != null ? new GraphQLList(rewiredType) : null;\n        }\n        else if (isNonNullType(type)) {\n            const rewiredType = rewireType(type.ofType);\n            return rewiredType != null ? new GraphQLNonNull(rewiredType) : null;\n        }\n        else if (isNamedType(type)) {\n            let rewiredType = referenceTypeMap[type.name];\n            if (rewiredType === undefined) {\n                rewiredType = isNamedStub(type) ? getBuiltInForStub(type) : rewireNamedType(type);\n                newTypeMap[rewiredType.name] = referenceTypeMap[type.name] = rewiredType;\n            }\n            return rewiredType != null ? newTypeMap[rewiredType.name] : null;\n        }\n        return null;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;;;AACO,SAAS,YAAY,eAAe,EAAE,UAAU;IACnD,MAAM,mBAAmB,OAAO,MAAM,CAAC;IACvC,IAAK,MAAM,YAAY,gBAAiB;QACpC,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS;IAC1D;IACA,MAAM,aAAa,OAAO,MAAM,CAAC;IACjC,IAAK,MAAM,YAAY,iBAAkB;QACrC,MAAM,YAAY,gBAAgB,CAAC,SAAS;QAC5C,IAAI,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;YAChD;QACJ;QACA,MAAM,UAAU,UAAU,IAAI;QAC9B,IAAI,QAAQ,UAAU,CAAC,OAAO;YAC1B;QACJ;QACA,IAAI,UAAU,CAAC,QAAQ,IAAI,MAAM;YAC7B,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,QAAQ,oDAAoD,CAAC;YACxG;QACJ;QACA,UAAU,CAAC,QAAQ,GAAG;IAC1B;IACA,IAAK,MAAM,YAAY,WAAY;QAC/B,UAAU,CAAC,SAAS,GAAG,gBAAgB,UAAU,CAAC,SAAS;IAC/D;IACA,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,YAAa,gBAAgB;IAClE,OAAO;QACH,SAAS;QACT,YAAY;IAChB;;;IACA,SAAS,gBAAgB,SAAS;QAC9B,IAAI,IAAA,wKAAoB,EAAC,YAAY;YACjC,OAAO;QACX;QACA,MAAM,kBAAkB,UAAU,QAAQ;QAC1C,gBAAgB,IAAI,GAAG,WAAW,gBAAgB,IAAI;QACtD,OAAO,IAAI,oKAAgB,CAAC;IAChC;IACA,SAAS,WAAW,IAAI;QACpB,MAAM,cAAc,CAAC;QACrB,IAAK,MAAM,WAAW,KAAM;YACxB,MAAM,MAAM,IAAI,CAAC,QAAQ;YACzB,MAAM,iBAAiB,WAAW,IAAI,IAAI;YAC1C,IAAI,kBAAkB,MAAM;gBACxB,IAAI,IAAI,GAAG;gBACX,WAAW,CAAC,QAAQ,GAAG;YAC3B;QACJ;QACA,OAAO;IACX;IACA,SAAS,gBAAgB,IAAI;QACzB,IAAI,IAAA,gKAAY,EAAC,OAAO;YACpB,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,QAAQ,IAAM,aAAa,OAAO,MAAM;gBACxC,YAAY,IAAM,iBAAiB,OAAO,UAAU;YACxD;YACA,OAAO,IAAI,qKAAiB,CAAC;QACjC,OACK,IAAI,IAAA,mKAAe,EAAC,OAAO;YAC5B,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,QAAQ,IAAM,aAAa,OAAO,MAAM;YAC5C;YACA,IAAI,gBAAgB,WAAW;gBAC3B,UAAU,UAAU,GAAG,IAAM,iBAAiB,OAAO,UAAU;YACnE;YACA,OAAO,IAAI,wKAAoB,CAAC;QACpC,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;YACxB,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,OAAO,IAAM,iBAAiB,OAAO,KAAK;YAC9C;YACA,OAAO,IAAI,oKAAgB,CAAC;QAChC,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;YAC9B,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,QAAQ,IAAM,kBAAkB,OAAO,MAAM;YACjD;YACA,OAAO,IAAI,0KAAsB,CAAC;QACtC,OACK,IAAI,IAAA,8JAAU,EAAC,OAAO;YACvB,MAAM,aAAa,KAAK,QAAQ;YAChC,OAAO,IAAI,mKAAe,CAAC;QAC/B,OACK,IAAI,IAAA,gKAAY,EAAC,OAAO;YACzB,IAAI,IAAA,sKAAqB,EAAC,OAAO;gBAC7B,OAAO;YACX;YACA,MAAM,eAAe,KAAK,QAAQ;YAClC,OAAO,IAAI,qKAAiB,CAAC;QACjC;QACA,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM;IACrD;IACA,SAAS,aAAa,MAAM;QACxB,MAAM,gBAAgB,CAAC;QACvB,IAAK,MAAM,aAAa,OAAQ;YAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;YAC/B,MAAM,mBAAmB,WAAW,MAAM,IAAI;YAC9C,IAAI,oBAAoB,QAAQ,MAAM,IAAI,EAAE;gBACxC,MAAM,IAAI,GAAG;gBACb,MAAM,IAAI,GAAG,WAAW,MAAM,IAAI;gBAClC,aAAa,CAAC,UAAU,GAAG;YAC/B;QACJ;QACA,OAAO;IACX;IACA,SAAS,kBAAkB,MAAM;QAC7B,MAAM,gBAAgB,CAAC;QACvB,IAAK,MAAM,aAAa,OAAQ;YAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;YAC/B,MAAM,mBAAmB,WAAW,MAAM,IAAI;YAC9C,IAAI,oBAAoB,MAAM;gBAC1B,MAAM,IAAI,GAAG;gBACb,aAAa,CAAC,UAAU,GAAG;YAC/B;QACJ;QACA,OAAO;IACX;IACA,SAAS,iBAAiB,UAAU;QAChC,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,aAAa,WAAY;YAChC,MAAM,cAAc,WAAW;YAC/B,IAAI,eAAe,MAAM;gBACrB,aAAa,IAAI,CAAC;YACtB;QACJ;QACA,OAAO;IACX;IACA,SAAS,WAAW,IAAI;QACpB,IAAI,IAAA,8JAAU,EAAC,OAAO;YAClB,MAAM,cAAc,WAAW,KAAK,MAAM;YAC1C,OAAO,eAAe,OAAO,IAAI,+JAAW,CAAC,eAAe;QAChE,OACK,IAAI,IAAA,iKAAa,EAAC,OAAO;YAC1B,MAAM,cAAc,WAAW,KAAK,MAAM;YAC1C,OAAO,eAAe,OAAO,IAAI,kKAAc,CAAC,eAAe;QACnE,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;YACxB,IAAI,cAAc,gBAAgB,CAAC,KAAK,IAAI,CAAC;YAC7C,IAAI,gBAAgB,WAAW;gBAC3B,cAAc,IAAA,2KAAW,EAAC,QAAQ,IAAA,iLAAiB,EAAC,QAAQ,gBAAgB;gBAC5E,UAAU,CAAC,YAAY,IAAI,CAAC,GAAG,gBAAgB,CAAC,KAAK,IAAI,CAAC,GAAG;YACjE;YACA,OAAO,eAAe,OAAO,UAAU,CAAC,YAAY,IAAI,CAAC,GAAG;QAChE;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/transformInputValue.js"], "sourcesContent": ["import { getNullableType, isInputObjectType, isLeafType, isListType, } from 'graphql';\nimport { asArray } from './helpers.js';\nexport function transformInputValue(type, value, inputLeafValueTransformer = null, inputObjectValueTransformer = null) {\n    if (value == null) {\n        return value;\n    }\n    const nullableType = getNullableType(type);\n    if (isLeafType(nullableType)) {\n        return inputLeafValueTransformer != null\n            ? inputLeafValueTransformer(nullableType, value)\n            : value;\n    }\n    else if (isListType(nullableType)) {\n        return asArray(value).map((listMember) => transformInputValue(nullableType.ofType, listMember, inputLeafValueTransformer, inputObjectValueTransformer));\n    }\n    else if (isInputObjectType(nullableType)) {\n        const fields = nullableType.getFields();\n        const newValue = {};\n        for (const key in value) {\n            const field = fields[key];\n            if (field != null) {\n                newValue[key] = transformInputValue(field.type, value[key], inputLeafValueTransformer, inputObjectValueTransformer);\n            }\n        }\n        return inputObjectValueTransformer != null\n            ? inputObjectValueTransformer(nullableType, newValue)\n            : newValue;\n    }\n    // unreachable, no other possible return value\n}\nexport function serializeInputValue(type, value) {\n    return transformInputValue(type, value, (t, v) => {\n        try {\n            return t.serialize(v);\n        }\n        catch {\n            return v;\n        }\n    });\n}\nexport function parseInputValue(type, value) {\n    return transformInputValue(type, value, (t, v) => {\n        try {\n            return t.parseValue(v);\n        }\n        catch {\n            return v;\n        }\n    });\n}\nexport function parseInputValueLiteral(type, value) {\n    return transformInputValue(type, value, (t, v) => t.parseLiteral(v, {}));\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AACO,SAAS,oBAAoB,IAAI,EAAE,KAAK,EAAE,4BAA4B,IAAI,EAAE,8BAA8B,IAAI;IACjH,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,MAAM,eAAe,IAAA,mKAAe,EAAC;IACrC,IAAI,IAAA,8JAAU,EAAC,eAAe;QAC1B,OAAO,6BAA6B,OAC9B,0BAA0B,cAAc,SACxC;IACV,OACK,IAAI,IAAA,8JAAU,EAAC,eAAe;QAC/B,OAAO,IAAA,0KAAO,EAAC,OAAO,GAAG,CAAC,CAAC,aAAe,oBAAoB,aAAa,MAAM,EAAE,YAAY,2BAA2B;IAC9H,OACK,IAAI,IAAA,qKAAiB,EAAC,eAAe;QACtC,MAAM,SAAS,aAAa,SAAS;QACrC,MAAM,WAAW,CAAC;QAClB,IAAK,MAAM,OAAO,MAAO;YACrB,MAAM,QAAQ,MAAM,CAAC,IAAI;YACzB,IAAI,SAAS,MAAM;gBACf,QAAQ,CAAC,IAAI,GAAG,oBAAoB,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,2BAA2B;YAC3F;QACJ;QACA,OAAO,+BAA+B,OAChC,4BAA4B,cAAc,YAC1C;IACV;AACA,8CAA8C;AAClD;AACO,SAAS,oBAAoB,IAAI,EAAE,KAAK;IAC3C,OAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG;QACxC,IAAI;YACA,OAAO,EAAE,SAAS,CAAC;QACvB,EACA,OAAM;YACF,OAAO;QACX;IACJ;AACJ;AACO,SAAS,gBAAgB,IAAI,EAAE,KAAK;IACvC,OAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG;QACxC,IAAI;YACA,OAAO,EAAE,UAAU,CAAC;QACxB,EACA,OAAM;YACF,OAAO;QACX;IACJ;AACJ;AACO,SAAS,uBAAuB,IAAI,EAAE,KAAK;IAC9C,OAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG,IAAM,EAAE,YAAY,CAAC,GAAG,CAAC;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/esm/mapSchema.js"], "sourcesContent": ["import { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLSchema, isEnumType, isInputObjectType, isInterfaceType, isLeafType, isListType, isNamedType, isNonNullType, isObjectType, isScalarType, isUnionType, Kind, } from 'graphql';\nimport { getObjectTypeFromTypeMap } from './getObjectTypeFromTypeMap.js';\nimport { MapperKind, } from './Interfaces.js';\nimport { rewireTypes } from './rewire.js';\nimport { parseInputValue, serializeInputValue } from './transformInputValue.js';\nexport function mapSchema(schema, schemaMapper = {}) {\n    const newTypeMap = mapArguments(mapFields(mapTypes(mapDefaultValues(mapEnumValues(mapTypes(mapDefaultValues(schema.getTypeMap(), schema, serializeInputValue), schema, schemaMapper, type => isLeafType(type)), schema, schemaMapper), schema, parseInputValue), schema, schemaMapper, type => !isLeafType(type)), schema, schemaMapper), schema, schemaMapper);\n    const originalDirectives = schema.getDirectives();\n    const newDirectives = mapDirectives(originalDirectives, schema, schemaMapper);\n    const { typeMap, directives } = rewireTypes(newTypeMap, newDirectives);\n    return new GraphQLSchema({\n        ...schema.toConfig(),\n        query: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getQueryType())),\n        mutation: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getMutationType())),\n        subscription: getObjectTypeFromTypeMap(typeMap, getObjectTypeFromTypeMap(newTypeMap, schema.getSubscriptionType())),\n        types: Object.values(typeMap),\n        directives,\n    });\n}\nfunction mapTypes(originalTypeMap, schema, schemaMapper, testFn = () => true) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (originalType == null || !testFn(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const typeMapper = getTypeMapper(schema, schemaMapper, typeName);\n            if (typeMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const maybeNewType = typeMapper(originalType, schema);\n            if (maybeNewType === undefined) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            newTypeMap[typeName] = maybeNewType;\n        }\n    }\n    return newTypeMap;\n}\nfunction mapEnumValues(originalTypeMap, schema, schemaMapper) {\n    const enumValueMapper = getEnumValueMapper(schemaMapper);\n    if (!enumValueMapper) {\n        return originalTypeMap;\n    }\n    return mapTypes(originalTypeMap, schema, {\n        [MapperKind.ENUM_TYPE]: type => {\n            const config = type.toConfig();\n            const originalEnumValueConfigMap = config.values;\n            const newEnumValueConfigMap = {};\n            for (const externalValue in originalEnumValueConfigMap) {\n                const originalEnumValueConfig = originalEnumValueConfigMap[externalValue];\n                const mappedEnumValue = enumValueMapper(originalEnumValueConfig, type.name, schema, externalValue);\n                if (mappedEnumValue === undefined) {\n                    newEnumValueConfigMap[externalValue] = originalEnumValueConfig;\n                }\n                else if (Array.isArray(mappedEnumValue)) {\n                    const [newExternalValue, newEnumValueConfig] = mappedEnumValue;\n                    newEnumValueConfigMap[newExternalValue] =\n                        newEnumValueConfig === undefined ? originalEnumValueConfig : newEnumValueConfig;\n                }\n                else if (mappedEnumValue !== null) {\n                    newEnumValueConfigMap[externalValue] = mappedEnumValue;\n                }\n            }\n            return correctASTNodes(new GraphQLEnumType({\n                ...config,\n                values: newEnumValueConfigMap,\n            }));\n        },\n    }, type => isEnumType(type));\n}\nfunction mapDefaultValues(originalTypeMap, schema, fn) {\n    const newTypeMap = mapArguments(originalTypeMap, schema, {\n        [MapperKind.ARGUMENT]: argumentConfig => {\n            if (argumentConfig.defaultValue === undefined) {\n                return argumentConfig;\n            }\n            const maybeNewType = getNewType(originalTypeMap, argumentConfig.type);\n            if (maybeNewType != null) {\n                return {\n                    ...argumentConfig,\n                    defaultValue: fn(maybeNewType, argumentConfig.defaultValue),\n                };\n            }\n        },\n    });\n    return mapFields(newTypeMap, schema, {\n        [MapperKind.INPUT_OBJECT_FIELD]: inputFieldConfig => {\n            if (inputFieldConfig.defaultValue === undefined) {\n                return inputFieldConfig;\n            }\n            const maybeNewType = getNewType(newTypeMap, inputFieldConfig.type);\n            if (maybeNewType != null) {\n                return {\n                    ...inputFieldConfig,\n                    defaultValue: fn(maybeNewType, inputFieldConfig.defaultValue),\n                };\n            }\n        },\n    });\n}\nfunction getNewType(newTypeMap, type) {\n    if (isListType(type)) {\n        const newType = getNewType(newTypeMap, type.ofType);\n        return newType != null ? new GraphQLList(newType) : null;\n    }\n    else if (isNonNullType(type)) {\n        const newType = getNewType(newTypeMap, type.ofType);\n        return newType != null ? new GraphQLNonNull(newType) : null;\n    }\n    else if (isNamedType(type)) {\n        const newType = newTypeMap[type.name];\n        return newType != null ? newType : null;\n    }\n    return null;\n}\nfunction mapFields(originalTypeMap, schema, schemaMapper) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (!isObjectType(originalType) &&\n                !isInterfaceType(originalType) &&\n                !isInputObjectType(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const fieldMapper = getFieldMapper(schema, schemaMapper, typeName);\n            if (fieldMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const config = originalType.toConfig();\n            const originalFieldConfigMap = config.fields;\n            const newFieldConfigMap = {};\n            for (const fieldName in originalFieldConfigMap) {\n                const originalFieldConfig = originalFieldConfigMap[fieldName];\n                const mappedField = fieldMapper(originalFieldConfig, fieldName, typeName, schema);\n                if (mappedField === undefined) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                }\n                else if (Array.isArray(mappedField)) {\n                    const [newFieldName, newFieldConfig] = mappedField;\n                    if (newFieldConfig.astNode != null) {\n                        newFieldConfig.astNode = {\n                            ...newFieldConfig.astNode,\n                            name: {\n                                ...newFieldConfig.astNode.name,\n                                value: newFieldName,\n                            },\n                        };\n                    }\n                    newFieldConfigMap[newFieldName] =\n                        newFieldConfig === undefined ? originalFieldConfig : newFieldConfig;\n                }\n                else if (mappedField !== null) {\n                    newFieldConfigMap[fieldName] = mappedField;\n                }\n            }\n            if (isObjectType(originalType)) {\n                newTypeMap[typeName] = correctASTNodes(new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n            else if (isInterfaceType(originalType)) {\n                newTypeMap[typeName] = correctASTNodes(new GraphQLInterfaceType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n            else {\n                newTypeMap[typeName] = correctASTNodes(new GraphQLInputObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        }\n    }\n    return newTypeMap;\n}\nfunction mapArguments(originalTypeMap, schema, schemaMapper) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (!isObjectType(originalType) && !isInterfaceType(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const argumentMapper = getArgumentMapper(schemaMapper);\n            if (argumentMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const config = originalType.toConfig();\n            const originalFieldConfigMap = config.fields;\n            const newFieldConfigMap = {};\n            for (const fieldName in originalFieldConfigMap) {\n                const originalFieldConfig = originalFieldConfigMap[fieldName];\n                const originalArgumentConfigMap = originalFieldConfig.args;\n                if (originalArgumentConfigMap == null) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                    continue;\n                }\n                const argumentNames = Object.keys(originalArgumentConfigMap);\n                if (!argumentNames.length) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                    continue;\n                }\n                const newArgumentConfigMap = {};\n                for (const argumentName of argumentNames) {\n                    const originalArgumentConfig = originalArgumentConfigMap[argumentName];\n                    const mappedArgument = argumentMapper(originalArgumentConfig, fieldName, typeName, schema);\n                    if (mappedArgument === undefined) {\n                        newArgumentConfigMap[argumentName] = originalArgumentConfig;\n                    }\n                    else if (Array.isArray(mappedArgument)) {\n                        const [newArgumentName, newArgumentConfig] = mappedArgument;\n                        newArgumentConfigMap[newArgumentName] = newArgumentConfig;\n                    }\n                    else if (mappedArgument !== null) {\n                        newArgumentConfigMap[argumentName] = mappedArgument;\n                    }\n                }\n                newFieldConfigMap[fieldName] = {\n                    ...originalFieldConfig,\n                    args: newArgumentConfigMap,\n                };\n            }\n            if (isObjectType(originalType)) {\n                newTypeMap[typeName] = new GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n            else if (isInterfaceType(originalType)) {\n                newTypeMap[typeName] = new GraphQLInterfaceType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n            else {\n                newTypeMap[typeName] = new GraphQLInputObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n        }\n    }\n    return newTypeMap;\n}\nfunction mapDirectives(originalDirectives, schema, schemaMapper) {\n    const directiveMapper = getDirectiveMapper(schemaMapper);\n    if (directiveMapper == null) {\n        return originalDirectives.slice();\n    }\n    const newDirectives = [];\n    for (const directive of originalDirectives) {\n        const mappedDirective = directiveMapper(directive, schema);\n        if (mappedDirective === undefined) {\n            newDirectives.push(directive);\n        }\n        else if (mappedDirective !== null) {\n            newDirectives.push(mappedDirective);\n        }\n    }\n    return newDirectives;\n}\nfunction getTypeSpecifiers(schema, typeName) {\n    const type = schema.getType(typeName);\n    const specifiers = [MapperKind.TYPE];\n    if (isObjectType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.OBJECT_TYPE);\n        if (typeName === schema.getQueryType()?.name) {\n            specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.QUERY);\n        }\n        else if (typeName === schema.getMutationType()?.name) {\n            specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.MUTATION);\n        }\n        else if (typeName === schema.getSubscriptionType()?.name) {\n            specifiers.push(MapperKind.ROOT_OBJECT, MapperKind.SUBSCRIPTION);\n        }\n    }\n    else if (isInputObjectType(type)) {\n        specifiers.push(MapperKind.INPUT_OBJECT_TYPE);\n    }\n    else if (isInterfaceType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.ABSTRACT_TYPE, MapperKind.INTERFACE_TYPE);\n    }\n    else if (isUnionType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_TYPE, MapperKind.ABSTRACT_TYPE, MapperKind.UNION_TYPE);\n    }\n    else if (isEnumType(type)) {\n        specifiers.push(MapperKind.ENUM_TYPE);\n    }\n    else if (isScalarType(type)) {\n        specifiers.push(MapperKind.SCALAR_TYPE);\n    }\n    return specifiers;\n}\nfunction getTypeMapper(schema, schemaMapper, typeName) {\n    const specifiers = getTypeSpecifiers(schema, typeName);\n    let typeMapper;\n    const stack = [...specifiers];\n    while (!typeMapper && stack.length > 0) {\n        // It is safe to use the ! operator here as we check the length.\n        const next = stack.pop();\n        typeMapper = schemaMapper[next];\n    }\n    return typeMapper != null ? typeMapper : null;\n}\nfunction getFieldSpecifiers(schema, typeName) {\n    const type = schema.getType(typeName);\n    const specifiers = [MapperKind.FIELD];\n    if (isObjectType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_FIELD, MapperKind.OBJECT_FIELD);\n        if (typeName === schema.getQueryType()?.name) {\n            specifiers.push(MapperKind.ROOT_FIELD, MapperKind.QUERY_ROOT_FIELD);\n        }\n        else if (typeName === schema.getMutationType()?.name) {\n            specifiers.push(MapperKind.ROOT_FIELD, MapperKind.MUTATION_ROOT_FIELD);\n        }\n        else if (typeName === schema.getSubscriptionType()?.name) {\n            specifiers.push(MapperKind.ROOT_FIELD, MapperKind.SUBSCRIPTION_ROOT_FIELD);\n        }\n    }\n    else if (isInterfaceType(type)) {\n        specifiers.push(MapperKind.COMPOSITE_FIELD, MapperKind.INTERFACE_FIELD);\n    }\n    else if (isInputObjectType(type)) {\n        specifiers.push(MapperKind.INPUT_OBJECT_FIELD);\n    }\n    return specifiers;\n}\nfunction getFieldMapper(schema, schemaMapper, typeName) {\n    const specifiers = getFieldSpecifiers(schema, typeName);\n    let fieldMapper;\n    const stack = [...specifiers];\n    while (!fieldMapper && stack.length > 0) {\n        // It is safe to use the ! operator here as we check the length.\n        const next = stack.pop();\n        // TODO: fix this as unknown cast\n        fieldMapper = schemaMapper[next];\n    }\n    return fieldMapper ?? null;\n}\nfunction getArgumentMapper(schemaMapper) {\n    const argumentMapper = schemaMapper[MapperKind.ARGUMENT];\n    return argumentMapper != null ? argumentMapper : null;\n}\nfunction getDirectiveMapper(schemaMapper) {\n    const directiveMapper = schemaMapper[MapperKind.DIRECTIVE];\n    return directiveMapper != null ? directiveMapper : null;\n}\nfunction getEnumValueMapper(schemaMapper) {\n    const enumValueMapper = schemaMapper[MapperKind.ENUM_VALUE];\n    return enumValueMapper != null ? enumValueMapper : null;\n}\nexport function correctASTNodes(type) {\n    if (isObjectType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: Kind.OBJECT_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: Kind.OBJECT_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new GraphQLObjectType(config);\n    }\n    else if (isInterfaceType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: Kind.INTERFACE_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: Kind.INTERFACE_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new GraphQLInterfaceType(config);\n    }\n    else if (isInputObjectType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new GraphQLInputObjectType(config);\n    }\n    else if (isEnumType(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const values = [];\n            for (const enumKey in config.values) {\n                const enumValueConfig = config.values[enumKey];\n                if (enumValueConfig.astNode != null) {\n                    values.push(enumValueConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                values,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                values: undefined,\n            }));\n        }\n        return new GraphQLEnumType(config);\n    }\n    else {\n        return type;\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,UAAU,MAAM,EAAE,eAAe,CAAC,CAAC;IAC/C,MAAM,aAAa,aAAa,UAAU,SAAS,iBAAiB,cAAc,SAAS,iBAAiB,OAAO,UAAU,IAAI,QAAQ,kMAAmB,GAAG,QAAQ,cAAc,CAAA,OAAQ,IAAA,8JAAU,EAAC,QAAQ,QAAQ,eAAe,QAAQ,8LAAe,GAAG,QAAQ,cAAc,CAAA,OAAQ,CAAC,IAAA,8JAAU,EAAC,QAAQ,QAAQ,eAAe,QAAQ;IAClV,MAAM,qBAAqB,OAAO,aAAa;IAC/C,MAAM,gBAAgB,cAAc,oBAAoB,QAAQ;IAChE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAA,6KAAW,EAAC,YAAY;IACxD,OAAO,IAAI,6JAAa,CAAC;QACrB,GAAG,OAAO,QAAQ,EAAE;QACpB,OAAO,IAAA,4MAAwB,EAAC,SAAS,IAAA,4MAAwB,EAAC,YAAY,OAAO,YAAY;QACjG,UAAU,IAAA,4MAAwB,EAAC,SAAS,IAAA,4MAAwB,EAAC,YAAY,OAAO,eAAe;QACvG,cAAc,IAAA,4MAAwB,EAAC,SAAS,IAAA,4MAAwB,EAAC,YAAY,OAAO,mBAAmB;QAC/G,OAAO,OAAO,MAAM,CAAC;QACrB;IACJ;AACJ;AACA,SAAS,SAAS,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,IAAM,IAAI;IACxE,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,eAAe,eAAe,CAAC,SAAS;YAC9C,IAAI,gBAAgB,QAAQ,CAAC,OAAO,eAAe;gBAC/C,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,aAAa,cAAc,QAAQ,cAAc;YACvD,IAAI,cAAc,MAAM;gBACpB,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,eAAe,WAAW,cAAc;YAC9C,IAAI,iBAAiB,WAAW;gBAC5B,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,UAAU,CAAC,SAAS,GAAG;QAC3B;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,eAAe,EAAE,MAAM,EAAE,YAAY;IACxD,MAAM,kBAAkB,mBAAmB;IAC3C,IAAI,CAAC,iBAAiB;QAClB,OAAO;IACX;IACA,OAAO,SAAS,iBAAiB,QAAQ;QACrC,CAAC,gLAAU,CAAC,SAAS,CAAC,EAAE,CAAA;YACpB,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,6BAA6B,OAAO,MAAM;YAChD,MAAM,wBAAwB,CAAC;YAC/B,IAAK,MAAM,iBAAiB,2BAA4B;gBACpD,MAAM,0BAA0B,0BAA0B,CAAC,cAAc;gBACzE,MAAM,kBAAkB,gBAAgB,yBAAyB,KAAK,IAAI,EAAE,QAAQ;gBACpF,IAAI,oBAAoB,WAAW;oBAC/B,qBAAqB,CAAC,cAAc,GAAG;gBAC3C,OACK,IAAI,MAAM,OAAO,CAAC,kBAAkB;oBACrC,MAAM,CAAC,kBAAkB,mBAAmB,GAAG;oBAC/C,qBAAqB,CAAC,iBAAiB,GACnC,uBAAuB,YAAY,0BAA0B;gBACrE,OACK,IAAI,oBAAoB,MAAM;oBAC/B,qBAAqB,CAAC,cAAc,GAAG;gBAC3C;YACJ;YACA,OAAO,gBAAgB,IAAI,mKAAe,CAAC;gBACvC,GAAG,MAAM;gBACT,QAAQ;YACZ;QACJ;IACJ,GAAG,CAAA,OAAQ,IAAA,8JAAU,EAAC;AAC1B;AACA,SAAS,iBAAiB,eAAe,EAAE,MAAM,EAAE,EAAE;IACjD,MAAM,aAAa,aAAa,iBAAiB,QAAQ;QACrD,CAAC,gLAAU,CAAC,QAAQ,CAAC,EAAE,CAAA;YACnB,IAAI,eAAe,YAAY,KAAK,WAAW;gBAC3C,OAAO;YACX;YACA,MAAM,eAAe,WAAW,iBAAiB,eAAe,IAAI;YACpE,IAAI,gBAAgB,MAAM;gBACtB,OAAO;oBACH,GAAG,cAAc;oBACjB,cAAc,GAAG,cAAc,eAAe,YAAY;gBAC9D;YACJ;QACJ;IACJ;IACA,OAAO,UAAU,YAAY,QAAQ;QACjC,CAAC,gLAAU,CAAC,kBAAkB,CAAC,EAAE,CAAA;YAC7B,IAAI,iBAAiB,YAAY,KAAK,WAAW;gBAC7C,OAAO;YACX;YACA,MAAM,eAAe,WAAW,YAAY,iBAAiB,IAAI;YACjE,IAAI,gBAAgB,MAAM;gBACtB,OAAO;oBACH,GAAG,gBAAgB;oBACnB,cAAc,GAAG,cAAc,iBAAiB,YAAY;gBAChE;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,UAAU,EAAE,IAAI;IAChC,IAAI,IAAA,8JAAU,EAAC,OAAO;QAClB,MAAM,UAAU,WAAW,YAAY,KAAK,MAAM;QAClD,OAAO,WAAW,OAAO,IAAI,+JAAW,CAAC,WAAW;IACxD,OACK,IAAI,IAAA,iKAAa,EAAC,OAAO;QAC1B,MAAM,UAAU,WAAW,YAAY,KAAK,MAAM;QAClD,OAAO,WAAW,OAAO,IAAI,kKAAc,CAAC,WAAW;IAC3D,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;QACxB,MAAM,UAAU,UAAU,CAAC,KAAK,IAAI,CAAC;QACrC,OAAO,WAAW,OAAO,UAAU;IACvC;IACA,OAAO;AACX;AACA,SAAS,UAAU,eAAe,EAAE,MAAM,EAAE,YAAY;IACpD,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,eAAe,eAAe,CAAC,SAAS;YAC9C,IAAI,CAAC,IAAA,gKAAY,EAAC,iBACd,CAAC,IAAA,mKAAe,EAAC,iBACjB,CAAC,IAAA,qKAAiB,EAAC,eAAe;gBAClC,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,cAAc,eAAe,QAAQ,cAAc;YACzD,IAAI,eAAe,MAAM;gBACrB,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,SAAS,aAAa,QAAQ;YACpC,MAAM,yBAAyB,OAAO,MAAM;YAC5C,MAAM,oBAAoB,CAAC;YAC3B,IAAK,MAAM,aAAa,uBAAwB;gBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;gBAC7D,MAAM,cAAc,YAAY,qBAAqB,WAAW,UAAU;gBAC1E,IAAI,gBAAgB,WAAW;oBAC3B,iBAAiB,CAAC,UAAU,GAAG;gBACnC,OACK,IAAI,MAAM,OAAO,CAAC,cAAc;oBACjC,MAAM,CAAC,cAAc,eAAe,GAAG;oBACvC,IAAI,eAAe,OAAO,IAAI,MAAM;wBAChC,eAAe,OAAO,GAAG;4BACrB,GAAG,eAAe,OAAO;4BACzB,MAAM;gCACF,GAAG,eAAe,OAAO,CAAC,IAAI;gCAC9B,OAAO;4BACX;wBACJ;oBACJ;oBACA,iBAAiB,CAAC,aAAa,GAC3B,mBAAmB,YAAY,sBAAsB;gBAC7D,OACK,IAAI,gBAAgB,MAAM;oBAC3B,iBAAiB,CAAC,UAAU,GAAG;gBACnC;YACJ;YACA,IAAI,IAAA,gKAAY,EAAC,eAAe;gBAC5B,UAAU,CAAC,SAAS,GAAG,gBAAgB,IAAI,qKAAiB,CAAC;oBACzD,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK,IAAI,IAAA,mKAAe,EAAC,eAAe;gBACpC,UAAU,CAAC,SAAS,GAAG,gBAAgB,IAAI,wKAAoB,CAAC;oBAC5D,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK;gBACD,UAAU,CAAC,SAAS,GAAG,gBAAgB,IAAI,0KAAsB,CAAC;oBAC9D,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,eAAe,EAAE,MAAM,EAAE,YAAY;IACvD,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,eAAe,eAAe,CAAC,SAAS;YAC9C,IAAI,CAAC,IAAA,gKAAY,EAAC,iBAAiB,CAAC,IAAA,mKAAe,EAAC,eAAe;gBAC/D,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,iBAAiB,kBAAkB;YACzC,IAAI,kBAAkB,MAAM;gBACxB,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,SAAS,aAAa,QAAQ;YACpC,MAAM,yBAAyB,OAAO,MAAM;YAC5C,MAAM,oBAAoB,CAAC;YAC3B,IAAK,MAAM,aAAa,uBAAwB;gBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;gBAC7D,MAAM,4BAA4B,oBAAoB,IAAI;gBAC1D,IAAI,6BAA6B,MAAM;oBACnC,iBAAiB,CAAC,UAAU,GAAG;oBAC/B;gBACJ;gBACA,MAAM,gBAAgB,OAAO,IAAI,CAAC;gBAClC,IAAI,CAAC,cAAc,MAAM,EAAE;oBACvB,iBAAiB,CAAC,UAAU,GAAG;oBAC/B;gBACJ;gBACA,MAAM,uBAAuB,CAAC;gBAC9B,KAAK,MAAM,gBAAgB,cAAe;oBACtC,MAAM,yBAAyB,yBAAyB,CAAC,aAAa;oBACtE,MAAM,iBAAiB,eAAe,wBAAwB,WAAW,UAAU;oBACnF,IAAI,mBAAmB,WAAW;wBAC9B,oBAAoB,CAAC,aAAa,GAAG;oBACzC,OACK,IAAI,MAAM,OAAO,CAAC,iBAAiB;wBACpC,MAAM,CAAC,iBAAiB,kBAAkB,GAAG;wBAC7C,oBAAoB,CAAC,gBAAgB,GAAG;oBAC5C,OACK,IAAI,mBAAmB,MAAM;wBAC9B,oBAAoB,CAAC,aAAa,GAAG;oBACzC;gBACJ;gBACA,iBAAiB,CAAC,UAAU,GAAG;oBAC3B,GAAG,mBAAmB;oBACtB,MAAM;gBACV;YACJ;YACA,IAAI,IAAA,gKAAY,EAAC,eAAe;gBAC5B,UAAU,CAAC,SAAS,GAAG,IAAI,qKAAiB,CAAC;oBACzC,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK,IAAI,IAAA,mKAAe,EAAC,eAAe;gBACpC,UAAU,CAAC,SAAS,GAAG,IAAI,wKAAoB,CAAC;oBAC5C,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK;gBACD,UAAU,CAAC,SAAS,GAAG,IAAI,0KAAsB,CAAC;oBAC9C,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,kBAAkB,EAAE,MAAM,EAAE,YAAY;IAC3D,MAAM,kBAAkB,mBAAmB;IAC3C,IAAI,mBAAmB,MAAM;QACzB,OAAO,mBAAmB,KAAK;IACnC;IACA,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,aAAa,mBAAoB;QACxC,MAAM,kBAAkB,gBAAgB,WAAW;QACnD,IAAI,oBAAoB,WAAW;YAC/B,cAAc,IAAI,CAAC;QACvB,OACK,IAAI,oBAAoB,MAAM;YAC/B,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,MAAM,EAAE,QAAQ;IACvC,MAAM,OAAO,OAAO,OAAO,CAAC;IAC5B,MAAM,aAAa;QAAC,gLAAU,CAAC,IAAI;KAAC;IACpC,IAAI,IAAA,gKAAY,EAAC,OAAO;QACpB,WAAW,IAAI,CAAC,gLAAU,CAAC,cAAc,EAAE,gLAAU,CAAC,WAAW;QACjE,IAAI,aAAa,OAAO,YAAY,IAAI,MAAM;YAC1C,WAAW,IAAI,CAAC,gLAAU,CAAC,WAAW,EAAE,gLAAU,CAAC,KAAK;QAC5D,OACK,IAAI,aAAa,OAAO,eAAe,IAAI,MAAM;YAClD,WAAW,IAAI,CAAC,gLAAU,CAAC,WAAW,EAAE,gLAAU,CAAC,QAAQ;QAC/D,OACK,IAAI,aAAa,OAAO,mBAAmB,IAAI,MAAM;YACtD,WAAW,IAAI,CAAC,gLAAU,CAAC,WAAW,EAAE,gLAAU,CAAC,YAAY;QACnE;IACJ,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;QAC9B,WAAW,IAAI,CAAC,gLAAU,CAAC,iBAAiB;IAChD,OACK,IAAI,IAAA,mKAAe,EAAC,OAAO;QAC5B,WAAW,IAAI,CAAC,gLAAU,CAAC,cAAc,EAAE,gLAAU,CAAC,aAAa,EAAE,gLAAU,CAAC,cAAc;IAClG,OACK,IAAI,IAAA,+JAAW,EAAC,OAAO;QACxB,WAAW,IAAI,CAAC,gLAAU,CAAC,cAAc,EAAE,gLAAU,CAAC,aAAa,EAAE,gLAAU,CAAC,UAAU;IAC9F,OACK,IAAI,IAAA,8JAAU,EAAC,OAAO;QACvB,WAAW,IAAI,CAAC,gLAAU,CAAC,SAAS;IACxC,OACK,IAAI,IAAA,gKAAY,EAAC,OAAO;QACzB,WAAW,IAAI,CAAC,gLAAU,CAAC,WAAW;IAC1C;IACA,OAAO;AACX;AACA,SAAS,cAAc,MAAM,EAAE,YAAY,EAAE,QAAQ;IACjD,MAAM,aAAa,kBAAkB,QAAQ;IAC7C,IAAI;IACJ,MAAM,QAAQ;WAAI;KAAW;IAC7B,MAAO,CAAC,cAAc,MAAM,MAAM,GAAG,EAAG;QACpC,gEAAgE;QAChE,MAAM,OAAO,MAAM,GAAG;QACtB,aAAa,YAAY,CAAC,KAAK;IACnC;IACA,OAAO,cAAc,OAAO,aAAa;AAC7C;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ;IACxC,MAAM,OAAO,OAAO,OAAO,CAAC;IAC5B,MAAM,aAAa;QAAC,gLAAU,CAAC,KAAK;KAAC;IACrC,IAAI,IAAA,gKAAY,EAAC,OAAO;QACpB,WAAW,IAAI,CAAC,gLAAU,CAAC,eAAe,EAAE,gLAAU,CAAC,YAAY;QACnE,IAAI,aAAa,OAAO,YAAY,IAAI,MAAM;YAC1C,WAAW,IAAI,CAAC,gLAAU,CAAC,UAAU,EAAE,gLAAU,CAAC,gBAAgB;QACtE,OACK,IAAI,aAAa,OAAO,eAAe,IAAI,MAAM;YAClD,WAAW,IAAI,CAAC,gLAAU,CAAC,UAAU,EAAE,gLAAU,CAAC,mBAAmB;QACzE,OACK,IAAI,aAAa,OAAO,mBAAmB,IAAI,MAAM;YACtD,WAAW,IAAI,CAAC,gLAAU,CAAC,UAAU,EAAE,gLAAU,CAAC,uBAAuB;QAC7E;IACJ,OACK,IAAI,IAAA,mKAAe,EAAC,OAAO;QAC5B,WAAW,IAAI,CAAC,gLAAU,CAAC,eAAe,EAAE,gLAAU,CAAC,eAAe;IAC1E,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;QAC9B,WAAW,IAAI,CAAC,gLAAU,CAAC,kBAAkB;IACjD;IACA,OAAO;AACX;AACA,SAAS,eAAe,MAAM,EAAE,YAAY,EAAE,QAAQ;IAClD,MAAM,aAAa,mBAAmB,QAAQ;IAC9C,IAAI;IACJ,MAAM,QAAQ;WAAI;KAAW;IAC7B,MAAO,CAAC,eAAe,MAAM,MAAM,GAAG,EAAG;QACrC,gEAAgE;QAChE,MAAM,OAAO,MAAM,GAAG;QACtB,iCAAiC;QACjC,cAAc,YAAY,CAAC,KAAK;IACpC;IACA,OAAO,eAAe;AAC1B;AACA,SAAS,kBAAkB,YAAY;IACnC,MAAM,iBAAiB,YAAY,CAAC,gLAAU,CAAC,QAAQ,CAAC;IACxD,OAAO,kBAAkB,OAAO,iBAAiB;AACrD;AACA,SAAS,mBAAmB,YAAY;IACpC,MAAM,kBAAkB,YAAY,CAAC,gLAAU,CAAC,SAAS,CAAC;IAC1D,OAAO,mBAAmB,OAAO,kBAAkB;AACvD;AACA,SAAS,mBAAmB,YAAY;IACpC,MAAM,kBAAkB,YAAY,CAAC,gLAAU,CAAC,UAAU,CAAC;IAC3D,OAAO,mBAAmB,OAAO,kBAAkB;AACvD;AACO,SAAS,gBAAgB,IAAI;IAChC,IAAI,IAAA,gKAAY,EAAC,OAAO;QACpB,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;gBACnC,MAAM,cAAc,OAAO,MAAM,CAAC,UAAU;gBAC5C,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC7B,OAAO,IAAI,CAAC,YAAY,OAAO;gBACnC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB,MAAM,uJAAI,CAAC,sBAAsB;gBACjC;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,MAAM,uJAAI,CAAC,qBAAqB;oBAChC,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,qKAAiB,CAAC;IACjC,OACK,IAAI,IAAA,mKAAe,EAAC,OAAO;QAC5B,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;gBACnC,MAAM,cAAc,OAAO,MAAM,CAAC,UAAU;gBAC5C,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC7B,OAAO,IAAI,CAAC,YAAY,OAAO;gBACnC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB,MAAM,uJAAI,CAAC,yBAAyB;gBACpC;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,MAAM,uJAAI,CAAC,wBAAwB;oBACnC,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,wKAAoB,CAAC;IACpC,OACK,IAAI,IAAA,qKAAiB,EAAC,OAAO;QAC9B,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;gBACnC,MAAM,cAAc,OAAO,MAAM,CAAC,UAAU;gBAC5C,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC7B,OAAO,IAAI,CAAC,YAAY,OAAO;gBACnC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB,MAAM,uJAAI,CAAC,4BAA4B;gBACvC;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,MAAM,uJAAI,CAAC,2BAA2B;oBACtC,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,0KAAsB,CAAC;IACtC,OACK,IAAI,IAAA,8JAAU,EAAC,OAAO;QACvB,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,WAAW,OAAO,MAAM,CAAE;gBACjC,MAAM,kBAAkB,OAAO,MAAM,CAAC,QAAQ;gBAC9C,IAAI,gBAAgB,OAAO,IAAI,MAAM;oBACjC,OAAO,IAAI,CAAC,gBAAgB,OAAO;gBACvC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,mKAAe,CAAC;IAC/B,OACK;QACD,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/helpers.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.asArray = void 0;\nexports.isUrl = isUrl;\nexports.isDocumentString = isDocumentString;\nexports.isValidPath = isValidPath;\nexports.compareStrings = compareStrings;\nexports.nodeToString = nodeToString;\nexports.compareNodes = compareNodes;\nexports.isSome = isSome;\nexports.assertSome = assertSome;\nconst graphql_1 = require(\"graphql\");\nconst URL_REGEXP = /^(https?|wss?|file):\\/\\//;\n/**\n * Checks if the given string is a valid URL.\n *\n * @param str - The string to validate as a URL\n * @returns A boolean indicating whether the string is a valid URL\n *\n * @remarks\n * This function first attempts to use the `URL.canParse` method if available.\n * If not, it falls back to creating a new `URL` object to validate the string.\n */\nfunction isUrl(str) {\n    if (typeof str !== 'string') {\n        return false;\n    }\n    if (!URL_REGEXP.test(str)) {\n        return false;\n    }\n    if (URL.canParse) {\n        return URL.canParse(str);\n    }\n    try {\n        const url = new URL(str);\n        return !!url;\n    }\n    catch (e) {\n        return false;\n    }\n}\nconst asArray = (fns) => (Array.isArray(fns) ? fns : fns ? [fns] : []);\nexports.asArray = asArray;\nconst invalidDocRegex = /\\.[a-z0-9]+$/i;\n/**\n * Determines if a given input is a valid GraphQL document string.\n *\n * @param str - The input to validate as a GraphQL document\n * @returns A boolean indicating whether the input is a valid GraphQL document string\n *\n * @remarks\n * This function performs several validation checks:\n * - Ensures the input is a string\n * - Filters out strings with invalid document extensions\n * - Excludes URLs\n * - Attempts to parse the string as a GraphQL document\n *\n * @throws {Error} If the document fails to parse and is empty except GraphQL comments\n */\nfunction isDocumentString(str) {\n    if (typeof str !== 'string') {\n        return false;\n    }\n    // XXX: is-valid-path or is-glob treat SDL as a valid path\n    // (`scalar Date` for example)\n    // this why checking the extension is fast enough\n    // and prevent from parsing the string in order to find out\n    // if the string is a SDL\n    if (invalidDocRegex.test(str) || isUrl(str)) {\n        return false;\n    }\n    try {\n        (0, graphql_1.parse)(str);\n        return true;\n    }\n    catch (e) {\n        if (!e.message.includes('EOF') &&\n            str.replace(/(\\#[^*]*)/g, '').trim() !== '' &&\n            str.includes(' ')) {\n            throw new Error(`Failed to parse the GraphQL document. ${e.message}\\n${str}`);\n        }\n    }\n    return false;\n}\nconst invalidPathRegex = /[‘“!%^<>`\\n]/;\n/**\n * Checkes whether the `str` contains any path illegal characters.\n *\n * A string may sometimes look like a path but is not (like an SDL of a simple\n * GraphQL schema). To make sure we don't yield false-positives in such cases,\n * we disallow new lines in paths (even though most Unix systems support new\n * lines in file names).\n */\nfunction isValidPath(str) {\n    return typeof str === 'string' && !invalidPathRegex.test(str);\n}\nfunction compareStrings(a, b) {\n    if (String(a) < String(b)) {\n        return -1;\n    }\n    if (String(a) > String(b)) {\n        return 1;\n    }\n    return 0;\n}\nfunction nodeToString(a) {\n    let name;\n    if ('alias' in a) {\n        name = a.alias?.value;\n    }\n    if (name == null && 'name' in a) {\n        name = a.name?.value;\n    }\n    if (name == null) {\n        name = a.kind;\n    }\n    return name;\n}\nfunction compareNodes(a, b, customFn) {\n    const aStr = nodeToString(a);\n    const bStr = nodeToString(b);\n    if (typeof customFn === 'function') {\n        return customFn(aStr, bStr);\n    }\n    return compareStrings(aStr, bStr);\n}\nfunction isSome(input) {\n    return input != null;\n}\nfunction assertSome(input, message = 'Value should be something') {\n    if (input == null) {\n        throw new Error(message);\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,WAAW,GAAG;AACtB,QAAQ,cAAc,GAAG;AACzB,QAAQ,YAAY,GAAG;AACvB,QAAQ,YAAY,GAAG;AACvB,QAAQ,MAAM,GAAG;AACjB,QAAQ,UAAU,GAAG;AACrB,MAAM;AACN,MAAM,aAAa;AACnB;;;;;;;;;CASC,GACD,SAAS,MAAM,GAAG;IACd,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;IACX;IACA,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM;QACvB,OAAO;IACX;IACA,IAAI,IAAI,QAAQ,EAAE;QACd,OAAO,IAAI,QAAQ,CAAC;IACxB;IACA,IAAI;QACA,MAAM,MAAM,IAAI,IAAI;QACpB,OAAO,CAAC,CAAC;IACb,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACA,MAAM,UAAU,CAAC,MAAS,MAAM,OAAO,CAAC,OAAO,MAAM,MAAM;QAAC;KAAI,GAAG,EAAE;AACrE,QAAQ,OAAO,GAAG;AAClB,MAAM,kBAAkB;AACxB;;;;;;;;;;;;;;CAcC,GACD,SAAS,iBAAiB,GAAG;IACzB,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;IACX;IACA,0DAA0D;IAC1D,8BAA8B;IAC9B,iDAAiD;IACjD,2DAA2D;IAC3D,yBAAyB;IACzB,IAAI,gBAAgB,IAAI,CAAC,QAAQ,MAAM,MAAM;QACzC,OAAO;IACX;IACA,IAAI;QACA,CAAC,GAAG,UAAU,KAAK,EAAE;QACrB,OAAO;IACX,EACA,OAAO,GAAG;QACN,IAAI,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,UACpB,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,OAAO,MACzC,IAAI,QAAQ,CAAC,MAAM;YACnB,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK;QAChF;IACJ;IACA,OAAO;AACX;AACA,MAAM,mBAAmB;AACzB;;;;;;;CAOC,GACD,SAAS,YAAY,GAAG;IACpB,OAAO,OAAO,QAAQ,YAAY,CAAC,iBAAiB,IAAI,CAAC;AAC7D;AACA,SAAS,eAAe,CAAC,EAAE,CAAC;IACxB,IAAI,OAAO,KAAK,OAAO,IAAI;QACvB,OAAO,CAAC;IACZ;IACA,IAAI,OAAO,KAAK,OAAO,IAAI;QACvB,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,aAAa,CAAC;IACnB,IAAI;IACJ,IAAI,WAAW,GAAG;QACd,OAAO,EAAE,KAAK,EAAE;IACpB;IACA,IAAI,QAAQ,QAAQ,UAAU,GAAG;QAC7B,OAAO,EAAE,IAAI,EAAE;IACnB;IACA,IAAI,QAAQ,MAAM;QACd,OAAO,EAAE,IAAI;IACjB;IACA,OAAO;AACX;AACA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,QAAQ;IAChC,MAAM,OAAO,aAAa;IAC1B,MAAM,OAAO,aAAa;IAC1B,IAAI,OAAO,aAAa,YAAY;QAChC,OAAO,SAAS,MAAM;IAC1B;IACA,OAAO,eAAe,MAAM;AAChC;AACA,SAAS,OAAO,KAAK;IACjB,OAAO,SAAS;AACpB;AACA,SAAS,WAAW,KAAK,EAAE,UAAU,2BAA2B;IAC5D,IAAI,SAAS,MAAM;QACf,MAAM,IAAI,MAAM;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/errors.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createGraphQLError = createGraphQLError;\nexports.relocatedError = relocatedError;\nconst graphql_1 = require(\"graphql\");\nconst possibleGraphQLErrorProperties = [\n    'message',\n    'locations',\n    'path',\n    'nodes',\n    'source',\n    'positions',\n    'originalError',\n    'name',\n    'stack',\n    'extensions',\n];\nfunction isGraphQLErrorLike(error) {\n    return (error != null &&\n        typeof error === 'object' &&\n        Object.keys(error).every(key => possibleGraphQLErrorProperties.includes(key)));\n}\nfunction createGraphQLError(message, options) {\n    if (options?.originalError &&\n        !(options.originalError instanceof Error) &&\n        isGraphQLErrorLike(options.originalError)) {\n        options.originalError = createGraphQLError(options.originalError.message, options.originalError);\n    }\n    if (graphql_1.versionInfo.major >= 17) {\n        return new graphql_1.GraphQLError(message, options);\n    }\n    return new graphql_1.GraphQLError(message, options?.nodes, options?.source, options?.positions, options?.path, options?.originalError, options?.extensions);\n}\nfunction relocatedError(originalError, path) {\n    return createGraphQLError(originalError.message, {\n        nodes: originalError.nodes,\n        source: originalError.source,\n        positions: originalError.positions,\n        path: path == null ? originalError.path : path,\n        originalError,\n        extensions: originalError.extensions,\n    });\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,MAAM,iCAAiC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,mBAAmB,KAAK;IAC7B,OAAQ,SAAS,QACb,OAAO,UAAU,YACjB,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAA,MAAO,+BAA+B,QAAQ,CAAC;AAChF;AACA,SAAS,mBAAmB,OAAO,EAAE,OAAO;IACxC,IAAI,SAAS,iBACT,CAAC,CAAC,QAAQ,aAAa,YAAY,KAAK,KACxC,mBAAmB,QAAQ,aAAa,GAAG;QAC3C,QAAQ,aAAa,GAAG,mBAAmB,QAAQ,aAAa,CAAC,OAAO,EAAE,QAAQ,aAAa;IACnG;IACA,IAAI,UAAU,WAAW,CAAC,KAAK,IAAI,IAAI;QACnC,OAAO,IAAI,UAAU,YAAY,CAAC,SAAS;IAC/C;IACA,OAAO,IAAI,UAAU,YAAY,CAAC,SAAS,SAAS,OAAO,SAAS,QAAQ,SAAS,WAAW,SAAS,MAAM,SAAS,eAAe,SAAS;AACpJ;AACA,SAAS,eAAe,aAAa,EAAE,IAAI;IACvC,OAAO,mBAAmB,cAAc,OAAO,EAAE;QAC7C,OAAO,cAAc,KAAK;QAC1B,QAAQ,cAAc,MAAM;QAC5B,WAAW,cAAc,SAAS;QAClC,MAAM,QAAQ,OAAO,cAAc,IAAI,GAAG;QAC1C;QACA,YAAY,cAAc,UAAU;IACxC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/jsutils.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isPromise = void 0;\nexports.isIterableObject = isIterableObject;\nexports.isObjectLike = isObjectLike;\nexports.promiseReduce = promiseReduce;\nexports.hasOwnProperty = hasOwnProperty;\nconst promise_helpers_1 = require(\"@whatwg-node/promise-helpers\");\nObject.defineProperty(exports, \"isPromise\", { enumerable: true, get: function () { return promise_helpers_1.isPromise; } });\nfunction isIterableObject(value) {\n    return value != null && typeof value === 'object' && Symbol.iterator in value;\n}\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction promiseReduce(values, callbackFn, initialValue) {\n    let accumulator = initialValue;\n    for (const value of values) {\n        accumulator = (0, promise_helpers_1.handleMaybePromise)(() => accumulator, resolved => callbackFn(resolved, value));\n    }\n    return accumulator;\n}\nfunction hasOwnProperty(obj, prop) {\n    return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,YAAY,GAAG;AACvB,QAAQ,aAAa,GAAG;AACxB,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,kBAAkB,SAAS;IAAE;AAAE;AACzH,SAAS,iBAAiB,KAAK;IAC3B,OAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,QAAQ,IAAI;AAC5E;AACA,SAAS,aAAa,KAAK;IACvB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,SAAS,cAAc,MAAM,EAAE,UAAU,EAAE,YAAY;IACnD,IAAI,cAAc;IAClB,KAAK,MAAM,SAAS,OAAQ;QACxB,cAAc,CAAC,GAAG,kBAAkB,kBAAkB,EAAE,IAAM,aAAa,CAAA,WAAY,WAAW,UAAU;IAChH;IACA,OAAO;AACX;AACA,SAAS,eAAe,GAAG,EAAE,IAAI;IAC7B,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/getArgumentValues.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getArgumentValues = getArgumentValues;\nconst cross_inspect_1 = require(\"cross-inspect\");\nconst graphql_1 = require(\"graphql\");\nconst errors_js_1 = require(\"./errors.js\");\nconst jsutils_js_1 = require(\"./jsutils.js\");\n/**\n * Prepares an object map of argument values given a list of argument\n * definitions and list of argument AST nodes.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\nfunction getArgumentValues(def, node, variableValues = {}) {\n    const coercedValues = {};\n    const argumentNodes = node.arguments ?? [];\n    const argNodeMap = argumentNodes.reduce((prev, arg) => ({\n        ...prev,\n        [arg.name.value]: arg,\n    }), {});\n    for (const { name, type: argType, defaultValue } of def.args) {\n        const argumentNode = argNodeMap[name];\n        if (!argumentNode) {\n            if (defaultValue !== undefined) {\n                coercedValues[name] = defaultValue;\n            }\n            else if ((0, graphql_1.isNonNullType)(argType)) {\n                throw (0, errors_js_1.createGraphQLError)(`Argument \"${name}\" of required type \"${(0, cross_inspect_1.inspect)(argType)}\" ` + 'was not provided.', {\n                    nodes: [node],\n                });\n            }\n            continue;\n        }\n        const valueNode = argumentNode.value;\n        let isNull = valueNode.kind === graphql_1.Kind.NULL;\n        if (valueNode.kind === graphql_1.Kind.VARIABLE) {\n            const variableName = valueNode.name.value;\n            if (variableValues == null || !(0, jsutils_js_1.hasOwnProperty)(variableValues, variableName)) {\n                if (defaultValue !== undefined) {\n                    coercedValues[name] = defaultValue;\n                }\n                else if ((0, graphql_1.isNonNullType)(argType)) {\n                    throw (0, errors_js_1.createGraphQLError)(`Argument \"${name}\" of required type \"${(0, cross_inspect_1.inspect)(argType)}\" ` +\n                        `was provided the variable \"$${variableName}\" which was not provided a runtime value.`, {\n                        nodes: [valueNode],\n                    });\n                }\n                continue;\n            }\n            isNull = variableValues[variableName] == null;\n        }\n        if (isNull && (0, graphql_1.isNonNullType)(argType)) {\n            throw (0, errors_js_1.createGraphQLError)(`Argument \"${name}\" of non-null type \"${(0, cross_inspect_1.inspect)(argType)}\" ` + 'must not be null.', {\n                nodes: [valueNode],\n            });\n        }\n        const coercedValue = (0, graphql_1.valueFromAST)(valueNode, argType, variableValues);\n        if (coercedValue === undefined) {\n            // Note: ValuesOfCorrectTypeRule validation should catch this before\n            // execution. This is a runtime check to ensure execution does not\n            // continue with an invalid argument value.\n            throw (0, errors_js_1.createGraphQLError)(`Argument \"${name}\" has invalid value ${(0, graphql_1.print)(valueNode)}.`, {\n                nodes: [valueNode],\n            });\n        }\n        coercedValues[name] = coercedValue;\n    }\n    return coercedValues;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG;AAC5B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;;;;CAOC,GACD,SAAS,kBAAkB,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;IACrD,MAAM,gBAAgB,CAAC;IACvB,MAAM,gBAAgB,KAAK,SAAS,IAAI,EAAE;IAC1C,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,MAAM,MAAQ,CAAC;YACpD,GAAG,IAAI;YACP,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;QACtB,CAAC,GAAG,CAAC;IACL,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,OAAO,EAAE,YAAY,EAAE,IAAI,IAAI,IAAI,CAAE;QAC1D,MAAM,eAAe,UAAU,CAAC,KAAK;QACrC,IAAI,CAAC,cAAc;YACf,IAAI,iBAAiB,WAAW;gBAC5B,aAAa,CAAC,KAAK,GAAG;YAC1B,OACK,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,UAAU;gBAC5C,MAAM,CAAC,GAAG,YAAY,kBAAkB,EAAE,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,CAAC,GAAG,gBAAgB,OAAO,EAAE,SAAS,EAAE,CAAC,GAAG,qBAAqB;oBAC/I,OAAO;wBAAC;qBAAK;gBACjB;YACJ;YACA;QACJ;QACA,MAAM,YAAY,aAAa,KAAK;QACpC,IAAI,SAAS,UAAU,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI;QACnD,IAAI,UAAU,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,EAAE;YAC5C,MAAM,eAAe,UAAU,IAAI,CAAC,KAAK;YACzC,IAAI,kBAAkB,QAAQ,CAAC,CAAC,GAAG,aAAa,cAAc,EAAE,gBAAgB,eAAe;gBAC3F,IAAI,iBAAiB,WAAW;oBAC5B,aAAa,CAAC,KAAK,GAAG;gBAC1B,OACK,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,UAAU;oBAC5C,MAAM,CAAC,GAAG,YAAY,kBAAkB,EAAE,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,CAAC,GAAG,gBAAgB,OAAO,EAAE,SAAS,EAAE,CAAC,GACvH,CAAC,4BAA4B,EAAE,aAAa,yCAAyC,CAAC,EAAE;wBACxF,OAAO;4BAAC;yBAAU;oBACtB;gBACJ;gBACA;YACJ;YACA,SAAS,cAAc,CAAC,aAAa,IAAI;QAC7C;QACA,IAAI,UAAU,CAAC,GAAG,UAAU,aAAa,EAAE,UAAU;YACjD,MAAM,CAAC,GAAG,YAAY,kBAAkB,EAAE,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,CAAC,GAAG,gBAAgB,OAAO,EAAE,SAAS,EAAE,CAAC,GAAG,qBAAqB;gBAC/I,OAAO;oBAAC;iBAAU;YACtB;QACJ;QACA,MAAM,eAAe,CAAC,GAAG,UAAU,YAAY,EAAE,WAAW,SAAS;QACrE,IAAI,iBAAiB,WAAW;YAC5B,oEAAoE;YACpE,kEAAkE;YAClE,2CAA2C;YAC3C,MAAM,CAAC,GAAG,YAAY,kBAAkB,EAAE,CAAC,UAAU,EAAE,KAAK,oBAAoB,EAAE,CAAC,GAAG,UAAU,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE;gBAClH,OAAO;oBAAC;iBAAU;YACtB;QACJ;QACA,aAAa,CAAC,KAAK,GAAG;IAC1B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/memoize.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.memoize1 = memoize1;\nexports.memoize2 = memoize2;\nexports.memoize3 = memoize3;\nexports.memoize4 = memoize4;\nexports.memoize5 = memoize5;\nexports.memoize2of4 = memoize2of4;\nexports.memoize2of5 = memoize2of5;\nfunction memoize1(fn) {\n    const memoize1cache = new WeakMap();\n    return function memoized(a1) {\n        const cachedValue = memoize1cache.get(a1);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1);\n            memoize1cache.set(a1, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nfunction memoize2(fn) {\n    const memoize2cache = new WeakMap();\n    return function memoized(a1, a2) {\n        let cache2 = memoize2cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2cache.set(a1, cache2);\n            const newValue = fn(a1, a2);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nfunction memoize3(fn) {\n    const memoize3Cache = new WeakMap();\n    return function memoized(a1, a2, a3) {\n        let cache2 = memoize3Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize3Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        const cachedValue = cache3.get(a3);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3);\n            cache3.set(a3, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nfunction memoize4(fn) {\n    const memoize4Cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4) {\n        let cache2 = memoize4Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize4Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        const cache4 = cache3.get(a3);\n        if (!cache4) {\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        const cachedValue = cache4.get(a4);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4);\n            cache4.set(a4, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nfunction memoize5(fn) {\n    const memoize5Cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4, a5) {\n        let cache2 = memoize5Cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize5Cache.set(a1, cache2);\n            const cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache3 = cache2.get(a2);\n        if (!cache3) {\n            cache3 = new WeakMap();\n            cache2.set(a2, cache3);\n            const cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache4 = cache3.get(a3);\n        if (!cache4) {\n            cache4 = new WeakMap();\n            cache3.set(a3, cache4);\n            const cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        let cache5 = cache4.get(a4);\n        if (!cache5) {\n            cache5 = new WeakMap();\n            cache4.set(a4, cache5);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        const cachedValue = cache5.get(a5);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache5.set(a5, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nfunction memoize2of4(fn) {\n    const memoize2of4cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4) {\n        let cache2 = memoize2of4cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2of4cache.set(a1, cache2);\n            const newValue = fn(a1, a2, a3, a4);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\nfunction memoize2of5(fn) {\n    const memoize2of4cache = new WeakMap();\n    return function memoized(a1, a2, a3, a4, a5) {\n        let cache2 = memoize2of4cache.get(a1);\n        if (!cache2) {\n            cache2 = new WeakMap();\n            memoize2of4cache.set(a1, cache2);\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        const cachedValue = cache2.get(a2);\n        if (cachedValue === undefined) {\n            const newValue = fn(a1, a2, a3, a4, a5);\n            cache2.set(a2, newValue);\n            return newValue;\n        }\n        return cachedValue;\n    };\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,WAAW,GAAG;AACtB,QAAQ,WAAW,GAAG;AACtB,SAAS,SAAS,EAAE;IAChB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE;QACvB,MAAM,cAAc,cAAc,GAAG,CAAC;QACtC,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG;YACpB,cAAc,GAAG,CAAC,IAAI;YACtB,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,SAAS,EAAE;IAChB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE;QAC3B,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,WAAW,GAAG,IAAI;YACxB,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI;YACxB,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,SAAS,EAAE;IAChB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE;QAC/B,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI;YAC5B,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI;YAC5B,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI;YAC5B,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,SAAS,EAAE;IAChB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACnC,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,SAAS,OAAO,GAAG,CAAC;QAC1B,IAAI,CAAC,QAAQ;YACT,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,SAAS,EAAE;IAChB,MAAM,gBAAgB,IAAI;IAC1B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvC,IAAI,SAAS,cAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,cAAc,GAAG,CAAC,IAAI;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,IAAI,SAAS,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,OAAO,GAAG,CAAC,IAAI;YACf,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,YAAY,EAAE;IACnB,MAAM,mBAAmB,IAAI;IAC7B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACnC,IAAI,SAAS,iBAAiB,GAAG,CAAC;QAClC,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,iBAAiB,GAAG,CAAC,IAAI;YACzB,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI;YAChC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,YAAY,EAAE;IACnB,MAAM,mBAAmB,IAAI;IAC7B,OAAO,SAAS,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvC,IAAI,SAAS,iBAAiB,GAAG,CAAC;QAClC,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI;YACb,iBAAiB,GAAG,CAAC,IAAI;YACzB,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,MAAM,cAAc,OAAO,GAAG,CAAC;QAC/B,IAAI,gBAAgB,WAAW;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI;YACpC,OAAO,GAAG,CAAC,IAAI;YACf,OAAO;QACX;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3494, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/getDirectiveExtensions.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getDirectiveExtensions = getDirectiveExtensions;\nconst graphql_1 = require(\"graphql\");\nconst getArgumentValues_js_1 = require(\"./getArgumentValues.js\");\nconst memoize_js_1 = require(\"./memoize.js\");\nfunction getDirectiveExtensions(directableObj, schema, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = {};\n    if (directableObj.extensions) {\n        let directivesInExtensions = directableObj.extensions;\n        for (const pathSegment of pathToDirectivesInExtensions) {\n            directivesInExtensions = directivesInExtensions?.[pathSegment];\n        }\n        if (directivesInExtensions != null) {\n            for (const directiveNameProp in directivesInExtensions) {\n                const directiveObjs = directivesInExtensions[directiveNameProp];\n                const directiveName = directiveNameProp;\n                if (Array.isArray(directiveObjs)) {\n                    for (const directiveObj of directiveObjs) {\n                        let existingDirectiveExtensions = directiveExtensions[directiveName];\n                        if (!existingDirectiveExtensions) {\n                            existingDirectiveExtensions = [];\n                            directiveExtensions[directiveName] = existingDirectiveExtensions;\n                        }\n                        existingDirectiveExtensions.push(directiveObj);\n                    }\n                }\n                else {\n                    let existingDirectiveExtensions = directiveExtensions[directiveName];\n                    if (!existingDirectiveExtensions) {\n                        existingDirectiveExtensions = [];\n                        directiveExtensions[directiveName] = existingDirectiveExtensions;\n                    }\n                    existingDirectiveExtensions.push(directiveObjs);\n                }\n            }\n        }\n    }\n    const memoizedStringify = (0, memoize_js_1.memoize1)(obj => JSON.stringify(obj));\n    const astNodes = [];\n    if (directableObj.astNode) {\n        astNodes.push(directableObj.astNode);\n    }\n    if (directableObj.extensionASTNodes) {\n        astNodes.push(...directableObj.extensionASTNodes);\n    }\n    for (const astNode of astNodes) {\n        if (astNode.directives?.length) {\n            for (const directive of astNode.directives) {\n                const directiveName = directive.name.value;\n                let existingDirectiveExtensions = directiveExtensions[directiveName];\n                if (!existingDirectiveExtensions) {\n                    existingDirectiveExtensions = [];\n                    directiveExtensions[directiveName] = existingDirectiveExtensions;\n                }\n                const directiveInSchema = schema?.getDirective(directiveName);\n                let value = {};\n                if (directiveInSchema) {\n                    value = (0, getArgumentValues_js_1.getArgumentValues)(directiveInSchema, directive);\n                }\n                if (directive.arguments) {\n                    for (const argNode of directive.arguments) {\n                        const argName = argNode.name.value;\n                        if (value[argName] == null) {\n                            const argInDirective = directiveInSchema?.args.find(arg => arg.name === argName);\n                            if (argInDirective) {\n                                value[argName] = (0, graphql_1.valueFromAST)(argNode.value, argInDirective.type);\n                            }\n                        }\n                        if (value[argName] == null) {\n                            value[argName] = (0, graphql_1.valueFromASTUntyped)(argNode.value);\n                        }\n                    }\n                }\n                if (astNodes.length > 0 && existingDirectiveExtensions.length > 0) {\n                    const valStr = memoizedStringify(value);\n                    if (existingDirectiveExtensions.some(val => memoizedStringify(val) === valStr)) {\n                        continue;\n                    }\n                }\n                existingDirectiveExtensions.push(value);\n            }\n        }\n    }\n    return directiveExtensions;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG;AACjC,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,uBAAuB,aAAa,EAAE,MAAM,EAAE,+BAA+B;IAAC;CAAa;IAChG,MAAM,sBAAsB,CAAC;IAC7B,IAAI,cAAc,UAAU,EAAE;QAC1B,IAAI,yBAAyB,cAAc,UAAU;QACrD,KAAK,MAAM,eAAe,6BAA8B;YACpD,yBAAyB,wBAAwB,CAAC,YAAY;QAClE;QACA,IAAI,0BAA0B,MAAM;YAChC,IAAK,MAAM,qBAAqB,uBAAwB;gBACpD,MAAM,gBAAgB,sBAAsB,CAAC,kBAAkB;gBAC/D,MAAM,gBAAgB;gBACtB,IAAI,MAAM,OAAO,CAAC,gBAAgB;oBAC9B,KAAK,MAAM,gBAAgB,cAAe;wBACtC,IAAI,8BAA8B,mBAAmB,CAAC,cAAc;wBACpE,IAAI,CAAC,6BAA6B;4BAC9B,8BAA8B,EAAE;4BAChC,mBAAmB,CAAC,cAAc,GAAG;wBACzC;wBACA,4BAA4B,IAAI,CAAC;oBACrC;gBACJ,OACK;oBACD,IAAI,8BAA8B,mBAAmB,CAAC,cAAc;oBACpE,IAAI,CAAC,6BAA6B;wBAC9B,8BAA8B,EAAE;wBAChC,mBAAmB,CAAC,cAAc,GAAG;oBACzC;oBACA,4BAA4B,IAAI,CAAC;gBACrC;YACJ;QACJ;IACJ;IACA,MAAM,oBAAoB,CAAC,GAAG,aAAa,QAAQ,EAAE,CAAA,MAAO,KAAK,SAAS,CAAC;IAC3E,MAAM,WAAW,EAAE;IACnB,IAAI,cAAc,OAAO,EAAE;QACvB,SAAS,IAAI,CAAC,cAAc,OAAO;IACvC;IACA,IAAI,cAAc,iBAAiB,EAAE;QACjC,SAAS,IAAI,IAAI,cAAc,iBAAiB;IACpD;IACA,KAAK,MAAM,WAAW,SAAU;QAC5B,IAAI,QAAQ,UAAU,EAAE,QAAQ;YAC5B,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;gBACxC,MAAM,gBAAgB,UAAU,IAAI,CAAC,KAAK;gBAC1C,IAAI,8BAA8B,mBAAmB,CAAC,cAAc;gBACpE,IAAI,CAAC,6BAA6B;oBAC9B,8BAA8B,EAAE;oBAChC,mBAAmB,CAAC,cAAc,GAAG;gBACzC;gBACA,MAAM,oBAAoB,QAAQ,aAAa;gBAC/C,IAAI,QAAQ,CAAC;gBACb,IAAI,mBAAmB;oBACnB,QAAQ,CAAC,GAAG,uBAAuB,iBAAiB,EAAE,mBAAmB;gBAC7E;gBACA,IAAI,UAAU,SAAS,EAAE;oBACrB,KAAK,MAAM,WAAW,UAAU,SAAS,CAAE;wBACvC,MAAM,UAAU,QAAQ,IAAI,CAAC,KAAK;wBAClC,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM;4BACxB,MAAM,iBAAiB,mBAAmB,KAAK,KAAK,CAAA,MAAO,IAAI,IAAI,KAAK;4BACxE,IAAI,gBAAgB;gCAChB,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,UAAU,YAAY,EAAE,QAAQ,KAAK,EAAE,eAAe,IAAI;4BACnF;wBACJ;wBACA,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM;4BACxB,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,UAAU,mBAAmB,EAAE,QAAQ,KAAK;wBACrE;oBACJ;gBACJ;gBACA,IAAI,SAAS,MAAM,GAAG,KAAK,4BAA4B,MAAM,GAAG,GAAG;oBAC/D,MAAM,SAAS,kBAAkB;oBACjC,IAAI,4BAA4B,IAAI,CAAC,CAAA,MAAO,kBAAkB,SAAS,SAAS;wBAC5E;oBACJ;gBACJ;gBACA,4BAA4B,IAAI,CAAC;YACrC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3586, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/get-directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getDirectivesInExtensions = getDirectivesInExtensions;\nexports.getDirectiveInExtensions = getDirectiveInExtensions;\nexports.getDirectives = getDirectives;\nexports.getDirective = getDirective;\nconst getDirectiveExtensions_js_1 = require(\"./getDirectiveExtensions.js\");\nfunction getDirectivesInExtensions(node, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, undefined, pathToDirectivesInExtensions);\n    return Object.entries(directiveExtensions)\n        .map(([directiveName, directiveArgsArr]) => directiveArgsArr?.map(directiveArgs => ({\n        name: directiveName,\n        args: directiveArgs,\n    })))\n        .flat(Infinity)\n        .filter(Boolean);\n}\nfunction getDirectiveInExtensions(node, directiveName, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, undefined, pathToDirectivesInExtensions);\n    return directiveExtensions[directiveName];\n}\nfunction getDirectives(schema, node, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, schema, pathToDirectivesInExtensions);\n    return Object.entries(directiveExtensions)\n        .map(([directiveName, directiveArgsArr]) => directiveArgsArr?.map(directiveArgs => ({\n        name: directiveName,\n        args: directiveArgs,\n    })))\n        .flat(Infinity)\n        .filter(Boolean);\n}\nfunction getDirective(schema, node, directiveName, pathToDirectivesInExtensions = ['directives']) {\n    const directiveExtensions = (0, getDirectiveExtensions_js_1.getDirectiveExtensions)(node, schema, pathToDirectivesInExtensions);\n    return directiveExtensions[directiveName];\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG;AACpC,QAAQ,wBAAwB,GAAG;AACnC,QAAQ,aAAa,GAAG;AACxB,QAAQ,YAAY,GAAG;AACvB,MAAM;AACN,SAAS,0BAA0B,IAAI,EAAE,+BAA+B;IAAC;CAAa;IAClF,MAAM,sBAAsB,CAAC,GAAG,4BAA4B,sBAAsB,EAAE,MAAM,WAAW;IACrG,OAAO,OAAO,OAAO,CAAC,qBACjB,GAAG,CAAC,CAAC,CAAC,eAAe,iBAAiB,GAAK,kBAAkB,IAAI,CAAA,gBAAiB,CAAC;gBACpF,MAAM;gBACN,MAAM;YACV,CAAC,IACI,IAAI,CAAC,UACL,MAAM,CAAC;AAChB;AACA,SAAS,yBAAyB,IAAI,EAAE,aAAa,EAAE,+BAA+B;IAAC;CAAa;IAChG,MAAM,sBAAsB,CAAC,GAAG,4BAA4B,sBAAsB,EAAE,MAAM,WAAW;IACrG,OAAO,mBAAmB,CAAC,cAAc;AAC7C;AACA,SAAS,cAAc,MAAM,EAAE,IAAI,EAAE,+BAA+B;IAAC;CAAa;IAC9E,MAAM,sBAAsB,CAAC,GAAG,4BAA4B,sBAAsB,EAAE,MAAM,QAAQ;IAClG,OAAO,OAAO,OAAO,CAAC,qBACjB,GAAG,CAAC,CAAC,CAAC,eAAe,iBAAiB,GAAK,kBAAkB,IAAI,CAAA,gBAAiB,CAAC;gBACpF,MAAM;gBACN,MAAM;YACV,CAAC,IACI,IAAI,CAAC,UACL,MAAM,CAAC;AAChB;AACA,SAAS,aAAa,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,+BAA+B;IAAC;CAAa;IAC5F,MAAM,sBAAsB,CAAC,GAAG,4BAA4B,sBAAsB,EAAE,MAAM,QAAQ;IAClG,OAAO,mBAAmB,CAAC,cAAc;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3628, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/get-fields-with-directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getFieldsWithDirectives = getFieldsWithDirectives;\nconst graphql_1 = require(\"graphql\");\nfunction getFieldsWithDirectives(documentNode, options = {}) {\n    const result = {};\n    let selected = ['ObjectTypeDefinition', 'ObjectTypeExtension'];\n    if (options.includeInputTypes) {\n        selected = [...selected, 'InputObjectTypeDefinition', 'InputObjectTypeExtension'];\n    }\n    const allTypes = documentNode.definitions.filter(obj => selected.includes(obj.kind));\n    for (const type of allTypes) {\n        const typeName = type.name.value;\n        if (type.fields == null) {\n            continue;\n        }\n        for (const field of type.fields) {\n            if (field.directives && field.directives.length > 0) {\n                const fieldName = field.name.value;\n                const key = `${typeName}.${fieldName}`;\n                const directives = field.directives.map(d => ({\n                    name: d.name.value,\n                    args: (d.arguments || []).reduce((prev, arg) => ({ ...prev, [arg.name.value]: (0, graphql_1.valueFromASTUntyped)(arg.value) }), {}),\n                }));\n                result[key] = directives;\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,uBAAuB,GAAG;AAClC,MAAM;AACN,SAAS,wBAAwB,YAAY,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,SAAS,CAAC;IAChB,IAAI,WAAW;QAAC;QAAwB;KAAsB;IAC9D,IAAI,QAAQ,iBAAiB,EAAE;QAC3B,WAAW;eAAI;YAAU;YAA6B;SAA2B;IACrF;IACA,MAAM,WAAW,aAAa,WAAW,CAAC,MAAM,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC,IAAI,IAAI;IAClF,KAAK,MAAM,QAAQ,SAAU;QACzB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK;QAChC,IAAI,KAAK,MAAM,IAAI,MAAM;YACrB;QACJ;QACA,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;YAC7B,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,GAAG;gBACjD,MAAM,YAAY,MAAM,IAAI,CAAC,KAAK;gBAClC,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,WAAW;gBACtC,MAAM,aAAa,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC1C,MAAM,EAAE,IAAI,CAAC,KAAK;wBAClB,MAAM,CAAC,EAAE,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,MAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,mBAAmB,EAAE,IAAI,KAAK;4BAAE,CAAC,GAAG,CAAC;oBACrI,CAAC;gBACD,MAAM,CAAC,IAAI,GAAG;YAClB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/get-arguments-with-directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getArgumentsWithDirectives = getArgumentsWithDirectives;\nconst graphql_1 = require(\"graphql\");\nfunction isTypeWithFields(t) {\n    return t.kind === graphql_1.Kind.OBJECT_TYPE_DEFINITION || t.kind === graphql_1.Kind.OBJECT_TYPE_EXTENSION;\n}\nfunction getArgumentsWithDirectives(documentNode) {\n    const result = {};\n    const allTypes = documentNode.definitions.filter(isTypeWithFields);\n    for (const type of allTypes) {\n        if (type.fields == null) {\n            continue;\n        }\n        for (const field of type.fields) {\n            const argsWithDirectives = field.arguments?.filter(arg => arg.directives?.length);\n            if (!argsWithDirectives?.length) {\n                continue;\n            }\n            const typeFieldResult = (result[`${type.name.value}.${field.name.value}`] = {});\n            for (const arg of argsWithDirectives) {\n                const directives = arg.directives.map(d => ({\n                    name: d.name.value,\n                    args: (d.arguments || []).reduce((prev, dArg) => ({ ...prev, [dArg.name.value]: (0, graphql_1.valueFromASTUntyped)(dArg.value) }), {}),\n                }));\n                typeFieldResult[arg.name.value] = directives;\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,0BAA0B,GAAG;AACrC,MAAM;AACN,SAAS,iBAAiB,CAAC;IACvB,OAAO,EAAE,IAAI,KAAK,UAAU,IAAI,CAAC,sBAAsB,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,CAAC,qBAAqB;AAC9G;AACA,SAAS,2BAA2B,YAAY;IAC5C,MAAM,SAAS,CAAC;IAChB,MAAM,WAAW,aAAa,WAAW,CAAC,MAAM,CAAC;IACjD,KAAK,MAAM,QAAQ,SAAU;QACzB,IAAI,KAAK,MAAM,IAAI,MAAM;YACrB;QACJ;QACA,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;YAC7B,MAAM,qBAAqB,MAAM,SAAS,EAAE,OAAO,CAAA,MAAO,IAAI,UAAU,EAAE;YAC1E,IAAI,CAAC,oBAAoB,QAAQ;gBAC7B;YACJ;YACA,MAAM,kBAAmB,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC;YAC7E,KAAK,MAAM,OAAO,mBAAoB;gBAClC,MAAM,aAAa,IAAI,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;wBACxC,MAAM,EAAE,IAAI,CAAC,KAAK;wBAClB,MAAM,CAAC,EAAE,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,OAAS,CAAC;gCAAE,GAAG,IAAI;gCAAE,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,mBAAmB,EAAE,KAAK,KAAK;4BAAE,CAAC,GAAG,CAAC;oBACxI,CAAC;gBACD,eAAe,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG;YACtC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3712, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/get-implementing-types.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getImplementingTypes = getImplementingTypes;\nfunction getImplementingTypes(interfaceName, schema) {\n    const allTypesMap = schema.getTypeMap();\n    const result = [];\n    for (const graphqlTypeName in allTypesMap) {\n        const graphqlType = allTypesMap[graphqlTypeName];\n        if ('getInterfaces' in graphqlType) {\n            const allInterfaces = graphqlType.getInterfaces();\n            if (allInterfaces.find(int => int.name === interfaceName)) {\n                result.push(graphqlType.name);\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,oBAAoB,GAAG;AAC/B,SAAS,qBAAqB,aAAa,EAAE,MAAM;IAC/C,MAAM,cAAc,OAAO,UAAU;IACrC,MAAM,SAAS,EAAE;IACjB,IAAK,MAAM,mBAAmB,YAAa;QACvC,MAAM,cAAc,WAAW,CAAC,gBAAgB;QAChD,IAAI,mBAAmB,aAAa;YAChC,MAAM,gBAAgB,YAAY,aAAa;YAC/C,IAAI,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,gBAAgB;gBACvD,OAAO,IAAI,CAAC,YAAY,IAAI;YAChC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3734, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/astFromType.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.astFromType = astFromType;\nconst cross_inspect_1 = require(\"cross-inspect\");\nconst graphql_1 = require(\"graphql\");\nfunction astFromType(type) {\n    if ((0, graphql_1.isNonNullType)(type)) {\n        const innerType = astFromType(type.ofType);\n        if (innerType.kind === graphql_1.Kind.NON_NULL_TYPE) {\n            throw new Error(`Invalid type node ${(0, cross_inspect_1.inspect)(type)}. Inner type of non-null type cannot be a non-null type.`);\n        }\n        return {\n            kind: graphql_1.Kind.NON_NULL_TYPE,\n            type: innerType,\n        };\n    }\n    else if ((0, graphql_1.isListType)(type)) {\n        return {\n            kind: graphql_1.Kind.LIST_TYPE,\n            type: astFromType(type.ofType),\n        };\n    }\n    return {\n        kind: graphql_1.Kind.NAMED_TYPE,\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n    };\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG;AACtB,MAAM;AACN,MAAM;AACN,SAAS,YAAY,IAAI;IACrB,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,OAAO;QACpC,MAAM,YAAY,YAAY,KAAK,MAAM;QACzC,IAAI,UAAU,IAAI,KAAK,UAAU,IAAI,CAAC,aAAa,EAAE;YACjD,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC,GAAG,gBAAgB,OAAO,EAAE,MAAM,wDAAwD,CAAC;QACrI;QACA,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,aAAa;YAClC,MAAM;QACV;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACtC,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,SAAS;YAC9B,MAAM,YAAY,KAAK,MAAM;QACjC;IACJ;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/astFromValueUntyped.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.astFromValueUntyped = astFromValueUntyped;\nconst graphql_1 = require(\"graphql\");\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using the following mapping.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String               |\n * | Number        | Int / Float          |\n * | BigInt        | Int                  |\n * | null          | NullValue            |\n *\n */\nfunction astFromValueUntyped(value) {\n    // only explicit null, not undefined, NaN\n    if (value === null) {\n        return { kind: graphql_1.Kind.NULL };\n    }\n    // undefined\n    if (value === undefined) {\n        return null;\n    }\n    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n    // the value is not an array, convert the value using the list's item type.\n    if (Array.isArray(value)) {\n        const valuesNodes = [];\n        for (const item of value) {\n            const itemNode = astFromValueUntyped(item);\n            if (itemNode != null) {\n                valuesNodes.push(itemNode);\n            }\n        }\n        return { kind: graphql_1.Kind.LIST, values: valuesNodes };\n    }\n    if (typeof value === 'object') {\n        if (value?.toJSON) {\n            return astFromValueUntyped(value.toJSON());\n        }\n        const fieldNodes = [];\n        for (const fieldName in value) {\n            const fieldValue = value[fieldName];\n            const ast = astFromValueUntyped(fieldValue);\n            if (ast) {\n                fieldNodes.push({\n                    kind: graphql_1.Kind.OBJECT_FIELD,\n                    name: { kind: graphql_1.Kind.NAME, value: fieldName },\n                    value: ast,\n                });\n            }\n        }\n        return { kind: graphql_1.Kind.OBJECT, fields: fieldNodes };\n    }\n    // Others serialize based on their corresponding JavaScript scalar types.\n    if (typeof value === 'boolean') {\n        return { kind: graphql_1.Kind.BOOLEAN, value };\n    }\n    if (typeof value === 'bigint') {\n        return { kind: graphql_1.Kind.INT, value: String(value) };\n    }\n    // JavaScript numbers can be Int or Float values.\n    if (typeof value === 'number' && isFinite(value)) {\n        const stringNum = String(value);\n        return integerStringRegExp.test(stringNum)\n            ? { kind: graphql_1.Kind.INT, value: stringNum }\n            : { kind: graphql_1.Kind.FLOAT, value: stringNum };\n    }\n    if (typeof value === 'string') {\n        return { kind: graphql_1.Kind.STRING, value };\n    }\n    throw new TypeError(`Cannot convert value to AST: ${value}.`);\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG;AAC9B,MAAM;AACN;;;;;;;;;;;;;;;CAeC,GACD,SAAS,oBAAoB,KAAK;IAC9B,yCAAyC;IACzC,IAAI,UAAU,MAAM;QAChB,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,IAAI;QAAC;IACvC;IACA,YAAY;IACZ,IAAI,UAAU,WAAW;QACrB,OAAO;IACX;IACA,8EAA8E;IAC9E,2EAA2E;IAC3E,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,MAAM,cAAc,EAAE;QACtB,KAAK,MAAM,QAAQ,MAAO;YACtB,MAAM,WAAW,oBAAoB;YACrC,IAAI,YAAY,MAAM;gBAClB,YAAY,IAAI,CAAC;YACrB;QACJ;QACA,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,IAAI;YAAE,QAAQ;QAAY;IAC5D;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,IAAI,OAAO,QAAQ;YACf,OAAO,oBAAoB,MAAM,MAAM;QAC3C;QACA,MAAM,aAAa,EAAE;QACrB,IAAK,MAAM,aAAa,MAAO;YAC3B,MAAM,aAAa,KAAK,CAAC,UAAU;YACnC,MAAM,MAAM,oBAAoB;YAChC,IAAI,KAAK;gBACL,WAAW,IAAI,CAAC;oBACZ,MAAM,UAAU,IAAI,CAAC,YAAY;oBACjC,MAAM;wBAAE,MAAM,UAAU,IAAI,CAAC,IAAI;wBAAE,OAAO;oBAAU;oBACpD,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAW;IAC7D;IACA,yEAAyE;IACzE,IAAI,OAAO,UAAU,WAAW;QAC5B,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,OAAO;YAAE;QAAM;IACjD;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,GAAG;YAAE,OAAO,OAAO;QAAO;IAC5D;IACA,iDAAiD;IACjD,IAAI,OAAO,UAAU,YAAY,SAAS,QAAQ;QAC9C,MAAM,YAAY,OAAO;QACzB,OAAO,oBAAoB,IAAI,CAAC,aAC1B;YAAE,MAAM,UAAU,IAAI,CAAC,GAAG;YAAE,OAAO;QAAU,IAC7C;YAAE,MAAM,UAAU,IAAI,CAAC,KAAK;YAAE,OAAO;QAAU;IACzD;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,MAAM;YAAE;QAAM;IAChD;IACA,MAAM,IAAI,UAAU,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;AAChE;AACA;;;;CAIC,GACD,MAAM,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3879, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/astFromValue.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.astFromValue = astFromValue;\nconst cross_inspect_1 = require(\"cross-inspect\");\nconst graphql_1 = require(\"graphql\");\nconst astFromValueUntyped_js_1 = require(\"./astFromValueUntyped.js\");\nconst jsutils_js_1 = require(\"./jsutils.js\");\n/**\n * Produces a GraphQL Value AST given a JavaScript object.\n * Function will match JavaScript/JSON values to GraphQL AST schema format\n * by using suggested GraphQLInputType. For example:\n *\n *     astFromValue(\"value\", GraphQLString)\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * JavaScript values.\n *\n * | JSON Value    | GraphQL Value        |\n * | ------------- | -------------------- |\n * | Object        | Input Object         |\n * | Array         | List                 |\n * | Boolean       | Boolean              |\n * | String        | String / Enum Value  |\n * | Number        | Int / Float          |\n * | BigInt        | Int                  |\n * | Unknown       | Enum Value           |\n * | null          | NullValue            |\n *\n */\nfunction astFromValue(value, type) {\n    if ((0, graphql_1.isNonNullType)(type)) {\n        const astValue = astFromValue(value, type.ofType);\n        if (astValue?.kind === graphql_1.Kind.NULL) {\n            return null;\n        }\n        return astValue;\n    }\n    // only explicit null, not undefined, NaN\n    if (value === null) {\n        return { kind: graphql_1.Kind.NULL };\n    }\n    // undefined\n    if (value === undefined) {\n        return null;\n    }\n    // Convert JavaScript array to GraphQL list. If the GraphQLType is a list, but\n    // the value is not an array, convert the value using the list's item type.\n    if ((0, graphql_1.isListType)(type)) {\n        const itemType = type.ofType;\n        if ((0, jsutils_js_1.isIterableObject)(value)) {\n            const valuesNodes = [];\n            for (const item of value) {\n                const itemNode = astFromValue(item, itemType);\n                if (itemNode != null) {\n                    valuesNodes.push(itemNode);\n                }\n            }\n            return { kind: graphql_1.Kind.LIST, values: valuesNodes };\n        }\n        return astFromValue(value, itemType);\n    }\n    // Populate the fields of the input object by creating ASTs from each value\n    // in the JavaScript object according to the fields in the input type.\n    if ((0, graphql_1.isInputObjectType)(type)) {\n        if (!(0, jsutils_js_1.isObjectLike)(value)) {\n            return null;\n        }\n        const fieldNodes = [];\n        for (const field of Object.values(type.getFields())) {\n            const fieldValue = astFromValue(value[field.name], field.type);\n            if (fieldValue) {\n                fieldNodes.push({\n                    kind: graphql_1.Kind.OBJECT_FIELD,\n                    name: { kind: graphql_1.Kind.NAME, value: field.name },\n                    value: fieldValue,\n                });\n            }\n        }\n        return { kind: graphql_1.Kind.OBJECT, fields: fieldNodes };\n    }\n    if ((0, graphql_1.isLeafType)(type)) {\n        // Since value is an internally represented value, it must be serialized\n        // to an externally represented value before converting into an AST.\n        const serialized = type.serialize(value);\n        if (serialized == null) {\n            return null;\n        }\n        if ((0, graphql_1.isEnumType)(type)) {\n            return { kind: graphql_1.Kind.ENUM, value: serialized };\n        }\n        // ID types can use Int literals.\n        if (type.name === 'ID' &&\n            typeof serialized === 'string' &&\n            integerStringRegExp.test(serialized)) {\n            return { kind: graphql_1.Kind.INT, value: serialized };\n        }\n        return (0, astFromValueUntyped_js_1.astFromValueUntyped)(serialized);\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible types have been considered.\n    console.assert(false, 'Unexpected input type: ' + (0, cross_inspect_1.inspect)(type));\n}\n/**\n * IntValue:\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit ( Digit+ )?\n */\nconst integerStringRegExp = /^-?(?:0|[1-9][0-9]*)$/;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG;AACvB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,SAAS,aAAa,KAAK,EAAE,IAAI;IAC7B,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,OAAO;QACpC,MAAM,WAAW,aAAa,OAAO,KAAK,MAAM;QAChD,IAAI,UAAU,SAAS,UAAU,IAAI,CAAC,IAAI,EAAE;YACxC,OAAO;QACX;QACA,OAAO;IACX;IACA,yCAAyC;IACzC,IAAI,UAAU,MAAM;QAChB,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,IAAI;QAAC;IACvC;IACA,YAAY;IACZ,IAAI,UAAU,WAAW;QACrB,OAAO;IACX;IACA,8EAA8E;IAC9E,2EAA2E;IAC3E,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACjC,MAAM,WAAW,KAAK,MAAM;QAC5B,IAAI,CAAC,GAAG,aAAa,gBAAgB,EAAE,QAAQ;YAC3C,MAAM,cAAc,EAAE;YACtB,KAAK,MAAM,QAAQ,MAAO;gBACtB,MAAM,WAAW,aAAa,MAAM;gBACpC,IAAI,YAAY,MAAM;oBAClB,YAAY,IAAI,CAAC;gBACrB;YACJ;YACA,OAAO;gBAAE,MAAM,UAAU,IAAI,CAAC,IAAI;gBAAE,QAAQ;YAAY;QAC5D;QACA,OAAO,aAAa,OAAO;IAC/B;IACA,2EAA2E;IAC3E,sEAAsE;IACtE,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;QACxC,IAAI,CAAC,CAAC,GAAG,aAAa,YAAY,EAAE,QAAQ;YACxC,OAAO;QACX;QACA,MAAM,aAAa,EAAE;QACrB,KAAK,MAAM,SAAS,OAAO,MAAM,CAAC,KAAK,SAAS,IAAK;YACjD,MAAM,aAAa,aAAa,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,MAAM,IAAI;YAC7D,IAAI,YAAY;gBACZ,WAAW,IAAI,CAAC;oBACZ,MAAM,UAAU,IAAI,CAAC,YAAY;oBACjC,MAAM;wBAAE,MAAM,UAAU,IAAI,CAAC,IAAI;wBAAE,OAAO,MAAM,IAAI;oBAAC;oBACrD,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;YAAE,MAAM,UAAU,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAW;IAC7D;IACA,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACjC,wEAAwE;QACxE,oEAAoE;QACpE,MAAM,aAAa,KAAK,SAAS,CAAC;QAClC,IAAI,cAAc,MAAM;YACpB,OAAO;QACX;QACA,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACjC,OAAO;gBAAE,MAAM,UAAU,IAAI,CAAC,IAAI;gBAAE,OAAO;YAAW;QAC1D;QACA,iCAAiC;QACjC,IAAI,KAAK,IAAI,KAAK,QACd,OAAO,eAAe,YACtB,oBAAoB,IAAI,CAAC,aAAa;YACtC,OAAO;gBAAE,MAAM,UAAU,IAAI,CAAC,GAAG;gBAAE,OAAO;YAAW;QACzD;QACA,OAAO,CAAC,GAAG,yBAAyB,mBAAmB,EAAE;IAC7D;IACA,oBAAoB,GACpB,0DAA0D;IAC1D,QAAQ,MAAM,CAAC,OAAO,4BAA4B,CAAC,GAAG,gBAAgB,OAAO,EAAE;AACnF;AACA;;;;CAIC,GACD,MAAM,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4004, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/descriptionFromObject.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getDescriptionNode = getDescriptionNode;\nconst graphql_1 = require(\"graphql\");\nfunction getDescriptionNode(obj) {\n    if (obj.astNode?.description) {\n        return {\n            ...obj.astNode.description,\n            block: true,\n        };\n    }\n    if (obj.description) {\n        return {\n            kind: graphql_1.Kind.STRING,\n            value: obj.description,\n            block: true,\n        };\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG;AAC7B,MAAM;AACN,SAAS,mBAAmB,GAAG;IAC3B,IAAI,IAAI,OAAO,EAAE,aAAa;QAC1B,OAAO;YACH,GAAG,IAAI,OAAO,CAAC,WAAW;YAC1B,OAAO;QACX;IACJ;IACA,IAAI,IAAI,WAAW,EAAE;QACjB,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,MAAM;YAC3B,OAAO,IAAI,WAAW;YACtB,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/rootTypes.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRootTypeMap = exports.getRootTypes = exports.getRootTypeNames = void 0;\nexports.getDefinedRootType = getDefinedRootType;\nconst errors_js_1 = require(\"./errors.js\");\nconst memoize_js_1 = require(\"./memoize.js\");\nfunction getDefinedRootType(schema, operation, nodes) {\n    const rootTypeMap = (0, exports.getRootTypeMap)(schema);\n    const rootType = rootTypeMap.get(operation);\n    if (rootType == null) {\n        throw (0, errors_js_1.createGraphQLError)(`Schem<PERSON> is not configured to execute ${operation} operation.`, {\n            nodes,\n        });\n    }\n    return rootType;\n}\nexports.getRootTypeNames = (0, memoize_js_1.memoize1)(function getRootTypeNames(schema) {\n    const rootTypes = (0, exports.getRootTypes)(schema);\n    return new Set([...rootTypes].map(type => type.name));\n});\nexports.getRootTypes = (0, memoize_js_1.memoize1)(function getRootTypes(schema) {\n    const rootTypeMap = (0, exports.getRootTypeMap)(schema);\n    return new Set(rootTypeMap.values());\n});\nexports.getRootTypeMap = (0, memoize_js_1.memoize1)(function getRootTypeMap(schema) {\n    const rootTypeMap = new Map();\n    const queryType = schema.getQueryType();\n    if (queryType) {\n        rootTypeMap.set('query', queryType);\n    }\n    const mutationType = schema.getMutationType();\n    if (mutationType) {\n        rootTypeMap.set('mutation', mutationType);\n    }\n    const subscriptionType = schema.getSubscriptionType();\n    if (subscriptionType) {\n        rootTypeMap.set('subscription', subscriptionType);\n    }\n    return rootTypeMap;\n});\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,QAAQ,YAAY,GAAG,QAAQ,gBAAgB,GAAG,KAAK;AAChF,QAAQ,kBAAkB,GAAG;AAC7B,MAAM;AACN,MAAM;AACN,SAAS,mBAAmB,MAAM,EAAE,SAAS,EAAE,KAAK;IAChD,MAAM,cAAc,CAAC,GAAG,QAAQ,cAAc,EAAE;IAChD,MAAM,WAAW,YAAY,GAAG,CAAC;IACjC,IAAI,YAAY,MAAM;QAClB,MAAM,CAAC,GAAG,YAAY,kBAAkB,EAAE,CAAC,oCAAoC,EAAE,UAAU,WAAW,CAAC,EAAE;YACrG;QACJ;IACJ;IACA,OAAO;AACX;AACA,QAAQ,gBAAgB,GAAG,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,iBAAiB,MAAM;IAClF,MAAM,YAAY,CAAC,GAAG,QAAQ,YAAY,EAAE;IAC5C,OAAO,IAAI,IAAI;WAAI;KAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;AACvD;AACA,QAAQ,YAAY,GAAG,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,aAAa,MAAM;IAC1E,MAAM,cAAc,CAAC,GAAG,QAAQ,cAAc,EAAE;IAChD,OAAO,IAAI,IAAI,YAAY,MAAM;AACrC;AACA,QAAQ,cAAc,GAAG,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,eAAe,MAAM;IAC9E,MAAM,cAAc,IAAI;IACxB,MAAM,YAAY,OAAO,YAAY;IACrC,IAAI,WAAW;QACX,YAAY,GAAG,CAAC,SAAS;IAC7B;IACA,MAAM,eAAe,OAAO,eAAe;IAC3C,IAAI,cAAc;QACd,YAAY,GAAG,CAAC,YAAY;IAChC;IACA,MAAM,mBAAmB,OAAO,mBAAmB;IACnD,IAAI,kBAAkB;QAClB,YAAY,GAAG,CAAC,gBAAgB;IACpC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4075, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/print-schema-with-directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getDocumentNodeFromSchema = getDocumentNodeFromSchema;\nexports.printSchemaWithDirectives = printSchemaWithDirectives;\nexports.astFromSchema = astFromSchema;\nexports.astFromDirective = astFromDirective;\nexports.getDirectiveNodes = getDirectiveNodes;\nexports.astFromArg = astFromArg;\nexports.astFromObjectType = astFromObjectType;\nexports.astFromInterfaceType = astFromInterfaceType;\nexports.astFromUnionType = astFromUnionType;\nexports.astFromInputObjectType = astFromInputObjectType;\nexports.astFromEnumType = astFromEnumType;\nexports.astFromScalarType = astFromScalarType;\nexports.astFromField = astFromField;\nexports.astFromInputField = astFromInputField;\nexports.astFromEnumValue = astFromEnumValue;\nexports.makeDeprecatedDirective = makeDeprecatedDirective;\nexports.makeDirectiveNode = makeDirectiveNode;\nexports.makeDirectiveNodes = makeDirectiveNodes;\nconst graphql_1 = require(\"graphql\");\nconst astFromType_js_1 = require(\"./astFromType.js\");\nconst astFromValue_js_1 = require(\"./astFromValue.js\");\nconst astFromValueUntyped_js_1 = require(\"./astFromValueUntyped.js\");\nconst descriptionFromObject_js_1 = require(\"./descriptionFromObject.js\");\nconst get_directives_js_1 = require(\"./get-directives.js\");\nconst helpers_js_1 = require(\"./helpers.js\");\nconst rootTypes_js_1 = require(\"./rootTypes.js\");\nfunction getDocumentNodeFromSchema(schema, options = {}) {\n    const pathToDirectivesInExtensions = options.pathToDirectivesInExtensions;\n    const typesMap = schema.getTypeMap();\n    const schemaNode = astFromSchema(schema, pathToDirectivesInExtensions);\n    const definitions = schemaNode != null ? [schemaNode] : [];\n    const directives = schema.getDirectives();\n    for (const directive of directives) {\n        if ((0, graphql_1.isSpecifiedDirective)(directive)) {\n            continue;\n        }\n        definitions.push(astFromDirective(directive, schema, pathToDirectivesInExtensions));\n    }\n    for (const typeName in typesMap) {\n        const type = typesMap[typeName];\n        const isPredefinedScalar = (0, graphql_1.isSpecifiedScalarType)(type);\n        const isIntrospection = (0, graphql_1.isIntrospectionType)(type);\n        if (isPredefinedScalar || isIntrospection) {\n            continue;\n        }\n        if ((0, graphql_1.isObjectType)(type)) {\n            definitions.push(astFromObjectType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if ((0, graphql_1.isInterfaceType)(type)) {\n            definitions.push(astFromInterfaceType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if ((0, graphql_1.isUnionType)(type)) {\n            definitions.push(astFromUnionType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if ((0, graphql_1.isInputObjectType)(type)) {\n            definitions.push(astFromInputObjectType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if ((0, graphql_1.isEnumType)(type)) {\n            definitions.push(astFromEnumType(type, schema, pathToDirectivesInExtensions));\n        }\n        else if ((0, graphql_1.isScalarType)(type)) {\n            definitions.push(astFromScalarType(type, schema, pathToDirectivesInExtensions));\n        }\n        else {\n            throw new Error(`Unknown type ${type}.`);\n        }\n    }\n    return {\n        kind: graphql_1.Kind.DOCUMENT,\n        definitions,\n    };\n}\n// this approach uses the default schema printer rather than a custom solution, so may be more backwards compatible\n// currently does not allow customization of printSchema options having to do with comments.\nfunction printSchemaWithDirectives(schema, options = {}) {\n    const documentNode = getDocumentNodeFromSchema(schema, options);\n    return (0, graphql_1.print)(documentNode);\n}\nfunction astFromSchema(schema, pathToDirectivesInExtensions) {\n    const operationTypeMap = new Map([\n        ['query', undefined],\n        ['mutation', undefined],\n        ['subscription', undefined],\n    ]);\n    const nodes = [];\n    if (schema.astNode != null) {\n        nodes.push(schema.astNode);\n    }\n    if (schema.extensionASTNodes != null) {\n        for (const extensionASTNode of schema.extensionASTNodes) {\n            nodes.push(extensionASTNode);\n        }\n    }\n    for (const node of nodes) {\n        if (node.operationTypes) {\n            for (const operationTypeDefinitionNode of node.operationTypes) {\n                operationTypeMap.set(operationTypeDefinitionNode.operation, operationTypeDefinitionNode);\n            }\n        }\n    }\n    const rootTypeMap = (0, rootTypes_js_1.getRootTypeMap)(schema);\n    for (const [operationTypeNode, operationTypeDefinitionNode] of operationTypeMap) {\n        const rootType = rootTypeMap.get(operationTypeNode);\n        if (rootType != null) {\n            const rootTypeAST = (0, astFromType_js_1.astFromType)(rootType);\n            if (operationTypeDefinitionNode != null) {\n                operationTypeDefinitionNode.type = rootTypeAST;\n            }\n            else {\n                operationTypeMap.set(operationTypeNode, {\n                    kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,\n                    operation: operationTypeNode,\n                    type: rootTypeAST,\n                });\n            }\n        }\n    }\n    const operationTypes = [...operationTypeMap.values()].filter(helpers_js_1.isSome);\n    const directives = getDirectiveNodes(schema, schema, pathToDirectivesInExtensions);\n    if (!operationTypes.length && !directives.length) {\n        return null;\n    }\n    const schemaNode = {\n        kind: operationTypes != null ? graphql_1.Kind.SCHEMA_DEFINITION : graphql_1.Kind.SCHEMA_EXTENSION,\n        operationTypes,\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: directives,\n    };\n    const descriptionNode = (0, descriptionFromObject_js_1.getDescriptionNode)(schema);\n    if (descriptionNode) {\n        schemaNode.description = descriptionNode;\n    }\n    return schemaNode;\n}\nfunction astFromDirective(directive, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.DIRECTIVE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(directive),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: directive.name,\n        },\n        arguments: directive.args?.map(arg => astFromArg(arg, schema, pathToDirectivesInExtensions)),\n        repeatable: directive.isRepeatable,\n        locations: directive.locations?.map(location => ({\n            kind: graphql_1.Kind.NAME,\n            value: location,\n        })) || [],\n    };\n}\nfunction getDirectiveNodes(entity, schema, pathToDirectivesInExtensions) {\n    let directiveNodesBesidesNativeDirectives = [];\n    const directivesInExtensions = (0, get_directives_js_1.getDirectivesInExtensions)(entity, pathToDirectivesInExtensions);\n    let directives;\n    if (directivesInExtensions != null) {\n        directives = makeDirectiveNodes(schema, directivesInExtensions);\n    }\n    let deprecatedDirectiveNode = null;\n    let specifiedByDirectiveNode = null;\n    let oneOfDirectiveNode = null;\n    if (directives != null) {\n        directiveNodesBesidesNativeDirectives = directives.filter(directive => graphql_1.specifiedDirectives.every(specifiedDirective => specifiedDirective.name !== directive.name.value));\n        deprecatedDirectiveNode = directives.find(directive => directive.name.value === 'deprecated');\n        specifiedByDirectiveNode = directives.find(directive => directive.name.value === 'specifiedBy');\n        oneOfDirectiveNode = directives.find(directive => directive.name.value === 'oneOf');\n    }\n    if (entity.deprecationReason != null && deprecatedDirectiveNode == null) {\n        deprecatedDirectiveNode = makeDeprecatedDirective(entity.deprecationReason);\n    }\n    if (entity.specifiedByUrl != null ||\n        (entity.specifiedByURL != null && specifiedByDirectiveNode == null)) {\n        const specifiedByValue = entity.specifiedByUrl || entity.specifiedByURL;\n        const specifiedByArgs = {\n            url: specifiedByValue,\n        };\n        specifiedByDirectiveNode = makeDirectiveNode('specifiedBy', specifiedByArgs);\n    }\n    if (entity.isOneOf && oneOfDirectiveNode == null) {\n        oneOfDirectiveNode = makeDirectiveNode('oneOf');\n    }\n    if (deprecatedDirectiveNode != null) {\n        directiveNodesBesidesNativeDirectives.push(deprecatedDirectiveNode);\n    }\n    if (specifiedByDirectiveNode != null) {\n        directiveNodesBesidesNativeDirectives.push(specifiedByDirectiveNode);\n    }\n    if (oneOfDirectiveNode != null) {\n        directiveNodesBesidesNativeDirectives.push(oneOfDirectiveNode);\n    }\n    return directiveNodesBesidesNativeDirectives;\n}\nfunction astFromArg(arg, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.INPUT_VALUE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(arg),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: arg.name,\n        },\n        type: (0, astFromType_js_1.astFromType)(arg.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        defaultValue: arg.defaultValue !== undefined\n            ? ((0, astFromValue_js_1.astFromValue)(arg.defaultValue, arg.type) ?? undefined)\n            : undefined,\n        directives: getDirectiveNodes(arg, schema, pathToDirectivesInExtensions),\n    };\n}\nfunction astFromObjectType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.OBJECT_TYPE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromField(field, schema, pathToDirectivesInExtensions)),\n        interfaces: Object.values(type.getInterfaces()).map(iFace => (0, astFromType_js_1.astFromType)(iFace)),\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nfunction astFromInterfaceType(type, schema, pathToDirectivesInExtensions) {\n    const node = {\n        kind: graphql_1.Kind.INTERFACE_TYPE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromField(field, schema, pathToDirectivesInExtensions)),\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n    if ('getInterfaces' in type) {\n        node.interfaces = Object.values(type.getInterfaces()).map(iFace => (0, astFromType_js_1.astFromType)(iFace));\n    }\n    return node;\n}\nfunction astFromUnionType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.UNION_TYPE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n        types: type.getTypes().map(type => (0, astFromType_js_1.astFromType)(type)),\n    };\n}\nfunction astFromInputObjectType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n        fields: Object.values(type.getFields()).map(field => astFromInputField(field, schema, pathToDirectivesInExtensions)),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nfunction astFromEnumType(type, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.ENUM_TYPE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n        values: Object.values(type.getValues()).map(value => astFromEnumValue(value, schema, pathToDirectivesInExtensions)),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(type, schema, pathToDirectivesInExtensions),\n    };\n}\nfunction astFromScalarType(type, schema, pathToDirectivesInExtensions) {\n    const directivesInExtensions = (0, get_directives_js_1.getDirectivesInExtensions)(type, pathToDirectivesInExtensions);\n    const directives = makeDirectiveNodes(schema, directivesInExtensions);\n    const specifiedByValue = (type['specifiedByUrl'] ||\n        type['specifiedByURL']);\n    if (specifiedByValue &&\n        !directives.some(directiveNode => directiveNode.name.value === 'specifiedBy')) {\n        const specifiedByArgs = {\n            url: specifiedByValue,\n        };\n        directives.push(makeDirectiveNode('specifiedBy', specifiedByArgs));\n    }\n    return {\n        kind: graphql_1.Kind.SCALAR_TYPE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(type),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: type.name,\n        },\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: directives,\n    };\n}\nfunction astFromField(field, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.FIELD_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(field),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: field.name,\n        },\n        arguments: field.args.map(arg => astFromArg(arg, schema, pathToDirectivesInExtensions)),\n        type: (0, astFromType_js_1.astFromType)(field.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),\n    };\n}\nfunction astFromInputField(field, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.INPUT_VALUE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(field),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: field.name,\n        },\n        type: (0, astFromType_js_1.astFromType)(field.type),\n        // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n        directives: getDirectiveNodes(field, schema, pathToDirectivesInExtensions),\n        defaultValue: (0, astFromValue_js_1.astFromValue)(field.defaultValue, field.type) ?? undefined,\n    };\n}\nfunction astFromEnumValue(value, schema, pathToDirectivesInExtensions) {\n    return {\n        kind: graphql_1.Kind.ENUM_VALUE_DEFINITION,\n        description: (0, descriptionFromObject_js_1.getDescriptionNode)(value),\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: value.name,\n        },\n        directives: getDirectiveNodes(value, schema, pathToDirectivesInExtensions),\n    };\n}\nfunction makeDeprecatedDirective(deprecationReason) {\n    return makeDirectiveNode('deprecated', { reason: deprecationReason }, graphql_1.GraphQLDeprecatedDirective);\n}\nfunction makeDirectiveNode(name, args, directive) {\n    const directiveArguments = [];\n    for (const argName in args) {\n        const argValue = args[argName];\n        let value;\n        if (directive != null) {\n            const arg = directive.args.find(arg => arg.name === argName);\n            if (arg) {\n                value = (0, astFromValue_js_1.astFromValue)(argValue, arg.type);\n            }\n        }\n        if (value == null) {\n            value = (0, astFromValueUntyped_js_1.astFromValueUntyped)(argValue);\n        }\n        if (value != null) {\n            directiveArguments.push({\n                kind: graphql_1.Kind.ARGUMENT,\n                name: {\n                    kind: graphql_1.Kind.NAME,\n                    value: argName,\n                },\n                value,\n            });\n        }\n    }\n    return {\n        kind: graphql_1.Kind.DIRECTIVE,\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: name,\n        },\n        arguments: directiveArguments,\n    };\n}\nfunction makeDirectiveNodes(schema, directiveValues) {\n    const directiveNodes = [];\n    for (const { name, args } of directiveValues) {\n        const directive = schema?.getDirective(name);\n        directiveNodes.push(makeDirectiveNode(name, args, directive));\n    }\n    return directiveNodes;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG;AACpC,QAAQ,yBAAyB,GAAG;AACpC,QAAQ,aAAa,GAAG;AACxB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,UAAU,GAAG;AACrB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,sBAAsB,GAAG;AACjC,QAAQ,eAAe,GAAG;AAC1B,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,YAAY,GAAG;AACvB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,uBAAuB,GAAG;AAClC,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,kBAAkB,GAAG;AAC7B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,0BAA0B,MAAM,EAAE,UAAU,CAAC,CAAC;IACnD,MAAM,+BAA+B,QAAQ,4BAA4B;IACzE,MAAM,WAAW,OAAO,UAAU;IAClC,MAAM,aAAa,cAAc,QAAQ;IACzC,MAAM,cAAc,cAAc,OAAO;QAAC;KAAW,GAAG,EAAE;IAC1D,MAAM,aAAa,OAAO,aAAa;IACvC,KAAK,MAAM,aAAa,WAAY;QAChC,IAAI,CAAC,GAAG,UAAU,oBAAoB,EAAE,YAAY;YAChD;QACJ;QACA,YAAY,IAAI,CAAC,iBAAiB,WAAW,QAAQ;IACzD;IACA,IAAK,MAAM,YAAY,SAAU;QAC7B,MAAM,OAAO,QAAQ,CAAC,SAAS;QAC/B,MAAM,qBAAqB,CAAC,GAAG,UAAU,qBAAqB,EAAE;QAChE,MAAM,kBAAkB,CAAC,GAAG,UAAU,mBAAmB,EAAE;QAC3D,IAAI,sBAAsB,iBAAiB;YACvC;QACJ;QACA,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;YACnC,YAAY,IAAI,CAAC,kBAAkB,MAAM,QAAQ;QACrD,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;YAC3C,YAAY,IAAI,CAAC,qBAAqB,MAAM,QAAQ;QACxD,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;YACvC,YAAY,IAAI,CAAC,iBAAiB,MAAM,QAAQ;QACpD,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;YAC7C,YAAY,IAAI,CAAC,uBAAuB,MAAM,QAAQ;QAC1D,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACtC,YAAY,IAAI,CAAC,gBAAgB,MAAM,QAAQ;QACnD,OACK,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;YACxC,YAAY,IAAI,CAAC,kBAAkB,MAAM,QAAQ;QACrD,OACK;YACD,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAC3C;IACJ;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B;IACJ;AACJ;AACA,mHAAmH;AACnH,4FAA4F;AAC5F,SAAS,0BAA0B,MAAM,EAAE,UAAU,CAAC,CAAC;IACnD,MAAM,eAAe,0BAA0B,QAAQ;IACvD,OAAO,CAAC,GAAG,UAAU,KAAK,EAAE;AAChC;AACA,SAAS,cAAc,MAAM,EAAE,4BAA4B;IACvD,MAAM,mBAAmB,IAAI,IAAI;QAC7B;YAAC;YAAS;SAAU;QACpB;YAAC;YAAY;SAAU;QACvB;YAAC;YAAgB;SAAU;KAC9B;IACD,MAAM,QAAQ,EAAE;IAChB,IAAI,OAAO,OAAO,IAAI,MAAM;QACxB,MAAM,IAAI,CAAC,OAAO,OAAO;IAC7B;IACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;QAClC,KAAK,MAAM,oBAAoB,OAAO,iBAAiB,CAAE;YACrD,MAAM,IAAI,CAAC;QACf;IACJ;IACA,KAAK,MAAM,QAAQ,MAAO;QACtB,IAAI,KAAK,cAAc,EAAE;YACrB,KAAK,MAAM,+BAA+B,KAAK,cAAc,CAAE;gBAC3D,iBAAiB,GAAG,CAAC,4BAA4B,SAAS,EAAE;YAChE;QACJ;IACJ;IACA,MAAM,cAAc,CAAC,GAAG,eAAe,cAAc,EAAE;IACvD,KAAK,MAAM,CAAC,mBAAmB,4BAA4B,IAAI,iBAAkB;QAC7E,MAAM,WAAW,YAAY,GAAG,CAAC;QACjC,IAAI,YAAY,MAAM;YAClB,MAAM,cAAc,CAAC,GAAG,iBAAiB,WAAW,EAAE;YACtD,IAAI,+BAA+B,MAAM;gBACrC,4BAA4B,IAAI,GAAG;YACvC,OACK;gBACD,iBAAiB,GAAG,CAAC,mBAAmB;oBACpC,MAAM,UAAU,IAAI,CAAC,yBAAyB;oBAC9C,WAAW;oBACX,MAAM;gBACV;YACJ;QACJ;IACJ;IACA,MAAM,iBAAiB;WAAI,iBAAiB,MAAM;KAAG,CAAC,MAAM,CAAC,aAAa,MAAM;IAChF,MAAM,aAAa,kBAAkB,QAAQ,QAAQ;IACrD,IAAI,CAAC,eAAe,MAAM,IAAI,CAAC,WAAW,MAAM,EAAE;QAC9C,OAAO;IACX;IACA,MAAM,aAAa;QACf,MAAM,kBAAkB,OAAO,UAAU,IAAI,CAAC,iBAAiB,GAAG,UAAU,IAAI,CAAC,gBAAgB;QACjG;QACA,0HAA0H;QAC1H,YAAY;IAChB;IACA,MAAM,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;IAC3E,IAAI,iBAAiB;QACjB,WAAW,WAAW,GAAG;IAC7B;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,SAAS,EAAE,MAAM,EAAE,4BAA4B;IACrE,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,oBAAoB;QACzC,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,UAAU,IAAI;QACzB;QACA,WAAW,UAAU,IAAI,EAAE,IAAI,CAAA,MAAO,WAAW,KAAK,QAAQ;QAC9D,YAAY,UAAU,YAAY;QAClC,WAAW,UAAU,SAAS,EAAE,IAAI,CAAA,WAAY,CAAC;gBAC7C,MAAM,UAAU,IAAI,CAAC,IAAI;gBACzB,OAAO;YACX,CAAC,MAAM,EAAE;IACb;AACJ;AACA,SAAS,kBAAkB,MAAM,EAAE,MAAM,EAAE,4BAA4B;IACnE,IAAI,wCAAwC,EAAE;IAC9C,MAAM,yBAAyB,CAAC,GAAG,oBAAoB,yBAAyB,EAAE,QAAQ;IAC1F,IAAI;IACJ,IAAI,0BAA0B,MAAM;QAChC,aAAa,mBAAmB,QAAQ;IAC5C;IACA,IAAI,0BAA0B;IAC9B,IAAI,2BAA2B;IAC/B,IAAI,qBAAqB;IACzB,IAAI,cAAc,MAAM;QACpB,wCAAwC,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,mBAAmB,CAAC,KAAK,CAAC,CAAA,qBAAsB,mBAAmB,IAAI,KAAK,UAAU,IAAI,CAAC,KAAK;QACjL,0BAA0B,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;QAChF,2BAA2B,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;QACjF,qBAAqB,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;IAC/E;IACA,IAAI,OAAO,iBAAiB,IAAI,QAAQ,2BAA2B,MAAM;QACrE,0BAA0B,wBAAwB,OAAO,iBAAiB;IAC9E;IACA,IAAI,OAAO,cAAc,IAAI,QACxB,OAAO,cAAc,IAAI,QAAQ,4BAA4B,MAAO;QACrE,MAAM,mBAAmB,OAAO,cAAc,IAAI,OAAO,cAAc;QACvE,MAAM,kBAAkB;YACpB,KAAK;QACT;QACA,2BAA2B,kBAAkB,eAAe;IAChE;IACA,IAAI,OAAO,OAAO,IAAI,sBAAsB,MAAM;QAC9C,qBAAqB,kBAAkB;IAC3C;IACA,IAAI,2BAA2B,MAAM;QACjC,sCAAsC,IAAI,CAAC;IAC/C;IACA,IAAI,4BAA4B,MAAM;QAClC,sCAAsC,IAAI,CAAC;IAC/C;IACA,IAAI,sBAAsB,MAAM;QAC5B,sCAAsC,IAAI,CAAC;IAC/C;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,4BAA4B;IACzD,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,sBAAsB;QAC3C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,IAAI,IAAI;QACnB;QACA,MAAM,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI,IAAI;QAChD,0HAA0H;QAC1H,cAAc,IAAI,YAAY,KAAK,YAC5B,CAAC,GAAG,kBAAkB,YAAY,EAAE,IAAI,YAAY,EAAE,IAAI,IAAI,KAAK,YACpE;QACN,YAAY,kBAAkB,KAAK,QAAQ;IAC/C;AACJ;AACA,SAAS,kBAAkB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACjE,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,sBAAsB;QAC3C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,aAAa,OAAO,QAAQ;QACjF,YAAY,OAAO,MAAM,CAAC,KAAK,aAAa,IAAI,GAAG,CAAC,CAAA,QAAS,CAAC,GAAG,iBAAiB,WAAW,EAAE;QAC/F,YAAY,kBAAkB,MAAM,QAAQ;IAChD;AACJ;AACA,SAAS,qBAAqB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACpE,MAAM,OAAO;QACT,MAAM,UAAU,IAAI,CAAC,yBAAyB;QAC9C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,aAAa,OAAO,QAAQ;QACjF,YAAY,kBAAkB,MAAM,QAAQ;IAChD;IACA,IAAI,mBAAmB,MAAM;QACzB,KAAK,UAAU,GAAG,OAAO,MAAM,CAAC,KAAK,aAAa,IAAI,GAAG,CAAC,CAAA,QAAS,CAAC,GAAG,iBAAiB,WAAW,EAAE;IACzG;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IAChE,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,qBAAqB;QAC1C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;QACA,0HAA0H;QAC1H,YAAY,kBAAkB,MAAM,QAAQ;QAC5C,OAAO,KAAK,QAAQ,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,GAAG,iBAAiB,WAAW,EAAE;IACzE;AACJ;AACA,SAAS,uBAAuB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACtE,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,4BAA4B;QACjD,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,kBAAkB,OAAO,QAAQ;QACtF,0HAA0H;QAC1H,YAAY,kBAAkB,MAAM,QAAQ;IAChD;AACJ;AACA,SAAS,gBAAgB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IAC/D,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,oBAAoB;QACzC,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;QACA,QAAQ,OAAO,MAAM,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,CAAA,QAAS,iBAAiB,OAAO,QAAQ;QACrF,0HAA0H;QAC1H,YAAY,kBAAkB,MAAM,QAAQ;IAChD;AACJ;AACA,SAAS,kBAAkB,IAAI,EAAE,MAAM,EAAE,4BAA4B;IACjE,MAAM,yBAAyB,CAAC,GAAG,oBAAoB,yBAAyB,EAAE,MAAM;IACxF,MAAM,aAAa,mBAAmB,QAAQ;IAC9C,MAAM,mBAAoB,IAAI,CAAC,iBAAiB,IAC5C,IAAI,CAAC,iBAAiB;IAC1B,IAAI,oBACA,CAAC,WAAW,IAAI,CAAC,CAAA,gBAAiB,cAAc,IAAI,CAAC,KAAK,KAAK,gBAAgB;QAC/E,MAAM,kBAAkB;YACpB,KAAK;QACT;QACA,WAAW,IAAI,CAAC,kBAAkB,eAAe;IACrD;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,sBAAsB;QAC3C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,KAAK,IAAI;QACpB;QACA,0HAA0H;QAC1H,YAAY;IAChB;AACJ;AACA,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,4BAA4B;IAC7D,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,gBAAgB;QACrC,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,MAAM,IAAI;QACrB;QACA,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,WAAW,KAAK,QAAQ;QACzD,MAAM,CAAC,GAAG,iBAAiB,WAAW,EAAE,MAAM,IAAI;QAClD,0HAA0H;QAC1H,YAAY,kBAAkB,OAAO,QAAQ;IACjD;AACJ;AACA,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,4BAA4B;IAClE,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,sBAAsB;QAC3C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,MAAM,IAAI;QACrB;QACA,MAAM,CAAC,GAAG,iBAAiB,WAAW,EAAE,MAAM,IAAI;QAClD,0HAA0H;QAC1H,YAAY,kBAAkB,OAAO,QAAQ;QAC7C,cAAc,CAAC,GAAG,kBAAkB,YAAY,EAAE,MAAM,YAAY,EAAE,MAAM,IAAI,KAAK;IACzF;AACJ;AACA,SAAS,iBAAiB,KAAK,EAAE,MAAM,EAAE,4BAA4B;IACjE,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,qBAAqB;QAC1C,aAAa,CAAC,GAAG,2BAA2B,kBAAkB,EAAE;QAChE,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,MAAM,IAAI;QACrB;QACA,YAAY,kBAAkB,OAAO,QAAQ;IACjD;AACJ;AACA,SAAS,wBAAwB,iBAAiB;IAC9C,OAAO,kBAAkB,cAAc;QAAE,QAAQ;IAAkB,GAAG,UAAU,0BAA0B;AAC9G;AACA,SAAS,kBAAkB,IAAI,EAAE,IAAI,EAAE,SAAS;IAC5C,MAAM,qBAAqB,EAAE;IAC7B,IAAK,MAAM,WAAW,KAAM;QACxB,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,IAAI;QACJ,IAAI,aAAa,MAAM;YACnB,MAAM,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YACpD,IAAI,KAAK;gBACL,QAAQ,CAAC,GAAG,kBAAkB,YAAY,EAAE,UAAU,IAAI,IAAI;YAClE;QACJ;QACA,IAAI,SAAS,MAAM;YACf,QAAQ,CAAC,GAAG,yBAAyB,mBAAmB,EAAE;QAC9D;QACA,IAAI,SAAS,MAAM;YACf,mBAAmB,IAAI,CAAC;gBACpB,MAAM,UAAU,IAAI,CAAC,QAAQ;gBAC7B,MAAM;oBACF,MAAM,UAAU,IAAI,CAAC,IAAI;oBACzB,OAAO;gBACX;gBACA;YACJ;QACJ;IACJ;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,SAAS;QAC9B,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO;QACX;QACA,WAAW;IACf;AACJ;AACA,SAAS,mBAAmB,MAAM,EAAE,eAAe;IAC/C,MAAM,iBAAiB,EAAE;IACzB,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,gBAAiB;QAC1C,MAAM,YAAY,QAAQ,aAAa;QACvC,eAAe,IAAI,CAAC,kBAAkB,MAAM,MAAM;IACtD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/validate-documents.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateGraphQlDocuments = validateGraphQlDocuments;\nexports.createDefaultRules = createDefaultRules;\nconst graphql_1 = require(\"graphql\");\nfunction validateGraphQlDocuments(schema, documents, rules = createDefaultRules()) {\n    const definitions = new Set();\n    const fragmentsDefinitionsMap = new Map();\n    for (const document of documents) {\n        for (const docDefinition of document.definitions) {\n            if (docDefinition.kind === graphql_1.Kind.FRAGMENT_DEFINITION) {\n                fragmentsDefinitionsMap.set(docDefinition.name.value, docDefinition);\n            }\n            else {\n                definitions.add(docDefinition);\n            }\n        }\n    }\n    const fullAST = {\n        kind: graphql_1.Kind.DOCUMENT,\n        definitions: Array.from([...definitions, ...fragmentsDefinitionsMap.values()]),\n    };\n    const errors = (0, graphql_1.validate)(schema, fullAST, rules);\n    for (const error of errors) {\n        error.stack = error.message;\n        if (error.locations) {\n            for (const location of error.locations) {\n                error.stack += `\\n    at ${error.source?.name}:${location.line}:${location.column}`;\n            }\n        }\n    }\n    return errors;\n}\nfunction createDefaultRules() {\n    let ignored = ['NoUnusedFragmentsRule', 'NoUnusedVariablesRule', 'KnownDirectivesRule'];\n    if (graphql_1.versionInfo.major < 15) {\n        ignored = ignored.map(rule => rule.replace(/Rule$/, ''));\n    }\n    return graphql_1.specifiedRules.filter((f) => !ignored.includes(f.name));\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,wBAAwB,GAAG;AACnC,QAAQ,kBAAkB,GAAG;AAC7B,MAAM;AACN,SAAS,yBAAyB,MAAM,EAAE,SAAS,EAAE,QAAQ,oBAAoB;IAC7E,MAAM,cAAc,IAAI;IACxB,MAAM,0BAA0B,IAAI;IACpC,KAAK,MAAM,YAAY,UAAW;QAC9B,KAAK,MAAM,iBAAiB,SAAS,WAAW,CAAE;YAC9C,IAAI,cAAc,IAAI,KAAK,UAAU,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,wBAAwB,GAAG,CAAC,cAAc,IAAI,CAAC,KAAK,EAAE;YAC1D,OACK;gBACD,YAAY,GAAG,CAAC;YACpB;QACJ;IACJ;IACA,MAAM,UAAU;QACZ,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,aAAa,MAAM,IAAI,CAAC;eAAI;eAAgB,wBAAwB,MAAM;SAAG;IACjF;IACA,MAAM,SAAS,CAAC,GAAG,UAAU,QAAQ,EAAE,QAAQ,SAAS;IACxD,KAAK,MAAM,SAAS,OAAQ;QACxB,MAAM,KAAK,GAAG,MAAM,OAAO;QAC3B,IAAI,MAAM,SAAS,EAAE;YACjB,KAAK,MAAM,YAAY,MAAM,SAAS,CAAE;gBACpC,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE,MAAM,MAAM,EAAE,KAAK,CAAC,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,MAAM,EAAE;YACvF;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS;IACL,IAAI,UAAU;QAAC;QAAyB;QAAyB;KAAsB;IACvF,IAAI,UAAU,WAAW,CAAC,KAAK,GAAG,IAAI;QAClC,UAAU,QAAQ,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,SAAS;IACxD;IACA,OAAO,UAAU,cAAc,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,QAAQ,QAAQ,CAAC,EAAE,IAAI;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4518, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/parse-graphql-json.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseGraphQLJSON = parseGraphQLJSON;\nconst graphql_1 = require(\"graphql\");\nfunction stripBOM(content) {\n    content = content.toString();\n    // Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n    // because the buffer-to-string conversion in `fs.readFileSync()`\n    // translates it to FEFF, the UTF-16 BOM.\n    if (content.charCodeAt(0) === 0xfeff) {\n        content = content.slice(1);\n    }\n    return content;\n}\nfunction parseBOM(content) {\n    return JSON.parse(stripBOM(content));\n}\nfunction parseGraphQLJSON(location, jsonContent, options) {\n    let parsedJson = parseBOM(jsonContent);\n    if (parsedJson.data) {\n        parsedJson = parsedJson.data;\n    }\n    if (parsedJson.kind === 'Document') {\n        return {\n            location,\n            document: parsedJson,\n        };\n    }\n    else if (parsedJson.__schema) {\n        const schema = (0, graphql_1.buildClientSchema)(parsedJson, options);\n        return {\n            location,\n            schema,\n        };\n    }\n    else if (typeof parsedJson === 'string') {\n        return {\n            location,\n            rawSDL: parsedJson,\n        };\n    }\n    throw new Error(`Not valid JSON content`);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG;AAC3B,MAAM;AACN,SAAS,SAAS,OAAO;IACrB,UAAU,QAAQ,QAAQ;IAC1B,kEAAkE;IAClE,iEAAiE;IACjE,yCAAyC;IACzC,IAAI,QAAQ,UAAU,CAAC,OAAO,QAAQ;QAClC,UAAU,QAAQ,KAAK,CAAC;IAC5B;IACA,OAAO;AACX;AACA,SAAS,SAAS,OAAO;IACrB,OAAO,KAAK,KAAK,CAAC,SAAS;AAC/B;AACA,SAAS,iBAAiB,QAAQ,EAAE,WAAW,EAAE,OAAO;IACpD,IAAI,aAAa,SAAS;IAC1B,IAAI,WAAW,IAAI,EAAE;QACjB,aAAa,WAAW,IAAI;IAChC;IACA,IAAI,WAAW,IAAI,KAAK,YAAY;QAChC,OAAO;YACH;YACA,UAAU;QACd;IACJ,OACK,IAAI,WAAW,QAAQ,EAAE;QAC1B,MAAM,SAAS,CAAC,GAAG,UAAU,iBAAiB,EAAE,YAAY;QAC5D,OAAO;YACH;YACA;QACJ;IACJ,OACK,IAAI,OAAO,eAAe,UAAU;QACrC,OAAO;YACH;YACA,QAAQ;QACZ;IACJ;IACA,MAAM,IAAI,MAAM,CAAC,sBAAsB,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4564, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/comments.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.resetComments = resetComments;\nexports.collectComment = collectComment;\nexports.pushComment = pushComment;\nexports.printComment = printComment;\nexports.printWithComments = printWithComments;\nexports.getDescription = getDescription;\nexports.getComment = getComment;\nexports.getLeadingCommentBlock = getLeadingCommentBlock;\nexports.dedentBlockStringValue = dedentBlockStringValue;\nexports.getBlockStringIndentation = getBlockStringIndentation;\nconst graphql_1 = require(\"graphql\");\nconst MAX_LINE_LENGTH = 80;\nlet commentsRegistry = {};\nfunction resetComments() {\n    commentsRegistry = {};\n}\nfunction collectComment(node) {\n    const entityName = node.name?.value;\n    if (entityName == null) {\n        return;\n    }\n    pushComment(node, entityName);\n    switch (node.kind) {\n        case 'EnumTypeDefinition':\n            if (node.values) {\n                for (const value of node.values) {\n                    pushComment(value, entityName, value.name.value);\n                }\n            }\n            break;\n        case 'ObjectTypeDefinition':\n        case 'InputObjectTypeDefinition':\n        case 'InterfaceTypeDefinition':\n            if (node.fields) {\n                for (const field of node.fields) {\n                    pushComment(field, entityName, field.name.value);\n                    if (isFieldDefinitionNode(field) && field.arguments) {\n                        for (const arg of field.arguments) {\n                            pushComment(arg, entityName, field.name.value, arg.name.value);\n                        }\n                    }\n                }\n            }\n            break;\n    }\n}\nfunction pushComment(node, entity, field, argument) {\n    const comment = getComment(node);\n    if (typeof comment !== 'string' || comment.length === 0) {\n        return;\n    }\n    const keys = [entity];\n    if (field) {\n        keys.push(field);\n        if (argument) {\n            keys.push(argument);\n        }\n    }\n    const path = keys.join('.');\n    if (!commentsRegistry[path]) {\n        commentsRegistry[path] = [];\n    }\n    commentsRegistry[path].push(comment);\n}\nfunction printComment(comment) {\n    return '\\n# ' + comment.replace(/\\n/g, '\\n# ');\n}\n/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/**\n * NOTE: ==> This file has been modified just to add comments to the printed AST\n * This is a temp measure, we will move to using the original non modified printer.js ASAP.\n */\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\nfunction join(maybeArray, separator) {\n    return maybeArray ? maybeArray.filter(x => x).join(separator || '') : '';\n}\nfunction hasMultilineItems(maybeArray) {\n    return maybeArray?.some(str => str.includes('\\n')) ?? false;\n}\nfunction addDescription(cb) {\n    return (node, _key, _parent, path, ancestors) => {\n        const keys = [];\n        const parent = path.reduce((prev, key) => {\n            if (['fields', 'arguments', 'values'].includes(key) && prev.name) {\n                keys.push(prev.name.value);\n            }\n            return prev[key];\n        }, ancestors[0]);\n        const key = [...keys, parent?.name?.value].filter(Boolean).join('.');\n        const items = [];\n        if (node.kind.includes('Definition') && commentsRegistry[key]) {\n            items.push(...commentsRegistry[key]);\n        }\n        return join([...items.map(printComment), node.description, cb(node, _key, _parent, path, ancestors)], '\\n');\n    };\n}\nfunction indent(maybeString) {\n    return maybeString && `  ${maybeString.replace(/\\n/g, '\\n  ')}`;\n}\n/**\n * Given array, print each item on its own line, wrapped in an\n * indented \"{ }\" block.\n */\nfunction block(array) {\n    return array && array.length !== 0 ? `{\\n${indent(join(array, '\\n'))}\\n}` : '';\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise\n * print an empty string.\n */\nfunction wrap(start, maybeString, end) {\n    return maybeString ? start + maybeString + (end || '') : '';\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n */\nfunction printBlockString(value, isDescription = false) {\n    const escaped = value.replace(/\\\\/g, '\\\\\\\\').replace(/\"\"\"/g, '\\\\\"\"\"');\n    return (value[0] === ' ' || value[0] === '\\t') && value.indexOf('\\n') === -1\n        ? `\"\"\"${escaped.replace(/\"$/, '\"\\n')}\"\"\"`\n        : `\"\"\"\\n${isDescription ? escaped : indent(escaped)}\\n\"\"\"`;\n}\nconst printDocASTReducer = {\n    Name: { leave: node => node.value },\n    Variable: { leave: node => '$' + node.name },\n    // Document\n    Document: {\n        leave: node => join(node.definitions, '\\n\\n'),\n    },\n    OperationDefinition: {\n        leave: node => {\n            const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n            const prefix = join([node.operation, join([node.name, varDefs]), join(node.directives, ' ')], ' ');\n            // the query short form.\n            return prefix + ' ' + node.selectionSet;\n        },\n    },\n    VariableDefinition: {\n        leave: ({ variable, type, defaultValue, directives }) => variable + ': ' + type + wrap(' = ', defaultValue) + wrap(' ', join(directives, ' ')),\n    },\n    SelectionSet: { leave: ({ selections }) => block(selections) },\n    Field: {\n        leave({ alias, name, arguments: args, directives, selectionSet }) {\n            const prefix = wrap('', alias, ': ') + name;\n            let argsLine = prefix + wrap('(', join(args, ', '), ')');\n            if (argsLine.length > MAX_LINE_LENGTH) {\n                argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n            }\n            return join([argsLine, join(directives, ' '), selectionSet], ' ');\n        },\n    },\n    Argument: { leave: ({ name, value }) => name + ': ' + value },\n    // Fragments\n    FragmentSpread: {\n        leave: ({ name, directives }) => '...' + name + wrap(' ', join(directives, ' ')),\n    },\n    InlineFragment: {\n        leave: ({ typeCondition, directives, selectionSet }) => join(['...', wrap('on ', typeCondition), join(directives, ' '), selectionSet], ' '),\n    },\n    FragmentDefinition: {\n        leave: ({ name, typeCondition, variableDefinitions, directives, selectionSet }) => \n        // Note: fragment variable definitions are experimental and may be changed\n        // or removed in the future.\n        `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` +\n            `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` +\n            selectionSet,\n    },\n    // Value\n    IntValue: { leave: ({ value }) => value },\n    FloatValue: { leave: ({ value }) => value },\n    StringValue: {\n        leave: ({ value, block: isBlockString }) => {\n            if (isBlockString) {\n                return printBlockString(value);\n            }\n            return JSON.stringify(value);\n        },\n    },\n    BooleanValue: { leave: ({ value }) => (value ? 'true' : 'false') },\n    NullValue: { leave: () => 'null' },\n    EnumValue: { leave: ({ value }) => value },\n    ListValue: { leave: ({ values }) => '[' + join(values, ', ') + ']' },\n    ObjectValue: { leave: ({ fields }) => '{' + join(fields, ', ') + '}' },\n    ObjectField: { leave: ({ name, value }) => name + ': ' + value },\n    // Directive\n    Directive: {\n        leave: ({ name, arguments: args }) => '@' + name + wrap('(', join(args, ', '), ')'),\n    },\n    // Type\n    NamedType: { leave: ({ name }) => name },\n    ListType: { leave: ({ type }) => '[' + type + ']' },\n    NonNullType: { leave: ({ type }) => type + '!' },\n    // Type System Definitions\n    SchemaDefinition: {\n        leave: ({ directives, operationTypes }) => join(['schema', join(directives, ' '), block(operationTypes)], ' '),\n    },\n    OperationTypeDefinition: {\n        leave: ({ operation, type }) => operation + ': ' + type,\n    },\n    ScalarTypeDefinition: {\n        leave: ({ name, directives }) => join(['scalar', name, join(directives, ' ')], ' '),\n    },\n    ObjectTypeDefinition: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'type',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    FieldDefinition: {\n        leave: ({ name, arguments: args, type, directives }) => name +\n            (hasMultilineItems(args)\n                ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n                : wrap('(', join(args, ', '), ')')) +\n            ': ' +\n            type +\n            wrap(' ', join(directives, ' ')),\n    },\n    InputValueDefinition: {\n        leave: ({ name, type, defaultValue, directives }) => join([name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')], ' '),\n    },\n    InterfaceTypeDefinition: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'interface',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    UnionTypeDefinition: {\n        leave: ({ name, directives, types }) => join(['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' '),\n    },\n    EnumTypeDefinition: {\n        leave: ({ name, directives, values }) => join(['enum', name, join(directives, ' '), block(values)], ' '),\n    },\n    EnumValueDefinition: {\n        leave: ({ name, directives }) => join([name, join(directives, ' ')], ' '),\n    },\n    InputObjectTypeDefinition: {\n        leave: ({ name, directives, fields }) => join(['input', name, join(directives, ' '), block(fields)], ' '),\n    },\n    DirectiveDefinition: {\n        leave: ({ name, arguments: args, repeatable, locations }) => 'directive @' +\n            name +\n            (hasMultilineItems(args)\n                ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n                : wrap('(', join(args, ', '), ')')) +\n            (repeatable ? ' repeatable' : '') +\n            ' on ' +\n            join(locations, ' | '),\n    },\n    SchemaExtension: {\n        leave: ({ directives, operationTypes }) => join(['extend schema', join(directives, ' '), block(operationTypes)], ' '),\n    },\n    ScalarTypeExtension: {\n        leave: ({ name, directives }) => join(['extend scalar', name, join(directives, ' ')], ' '),\n    },\n    ObjectTypeExtension: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'extend type',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    InterfaceTypeExtension: {\n        leave: ({ name, interfaces, directives, fields }) => join([\n            'extend interface',\n            name,\n            wrap('implements ', join(interfaces, ' & ')),\n            join(directives, ' '),\n            block(fields),\n        ], ' '),\n    },\n    UnionTypeExtension: {\n        leave: ({ name, directives, types }) => join(['extend union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' '),\n    },\n    EnumTypeExtension: {\n        leave: ({ name, directives, values }) => join(['extend enum', name, join(directives, ' '), block(values)], ' '),\n    },\n    InputObjectTypeExtension: {\n        leave: ({ name, directives, fields }) => join(['extend input', name, join(directives, ' '), block(fields)], ' '),\n    },\n};\nconst printDocASTReducerWithComments = Object.keys(printDocASTReducer).reduce((prev, key) => ({\n    ...prev,\n    [key]: {\n        leave: addDescription(printDocASTReducer[key].leave),\n    },\n}), {});\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\nfunction printWithComments(ast) {\n    return (0, graphql_1.visit)(ast, printDocASTReducerWithComments);\n}\nfunction isFieldDefinitionNode(node) {\n    return node.kind === 'FieldDefinition';\n}\n// graphql < v13 and > v15 does not export getDescription\nfunction getDescription(node, options) {\n    if (node.description != null) {\n        return node.description.value;\n    }\n    if (options?.commentDescriptions) {\n        return getComment(node);\n    }\n}\nfunction getComment(node) {\n    const rawValue = getLeadingCommentBlock(node);\n    if (rawValue !== undefined) {\n        return dedentBlockStringValue(`\\n${rawValue}`);\n    }\n}\nfunction getLeadingCommentBlock(node) {\n    const loc = node.loc;\n    if (!loc) {\n        return;\n    }\n    const comments = [];\n    let token = loc.startToken.prev;\n    while (token != null &&\n        token.kind === graphql_1.TokenKind.COMMENT &&\n        token.next != null &&\n        token.prev != null &&\n        token.line + 1 === token.next.line &&\n        token.line !== token.prev.line) {\n        const value = String(token.value);\n        comments.push(value);\n        token = token.prev;\n    }\n    return comments.length > 0 ? comments.reverse().join('\\n') : undefined;\n}\nfunction dedentBlockStringValue(rawString) {\n    // Expand a block string's raw value into independent lines.\n    const lines = rawString.split(/\\r\\n|[\\n\\r]/g);\n    // Remove common indentation from all lines but first.\n    const commonIndent = getBlockStringIndentation(lines);\n    if (commonIndent !== 0) {\n        for (let i = 1; i < lines.length; i++) {\n            lines[i] = lines[i].slice(commonIndent);\n        }\n    }\n    // Remove leading and trailing blank lines.\n    while (lines.length > 0 && isBlank(lines[0])) {\n        lines.shift();\n    }\n    while (lines.length > 0 && isBlank(lines[lines.length - 1])) {\n        lines.pop();\n    }\n    // Return a string of the lines joined with U+000A.\n    return lines.join('\\n');\n}\n/**\n * @internal\n */\nfunction getBlockStringIndentation(lines) {\n    let commonIndent = null;\n    for (let i = 1; i < lines.length; i++) {\n        const line = lines[i];\n        const indent = leadingWhitespace(line);\n        if (indent === line.length) {\n            continue; // skip empty lines\n        }\n        if (commonIndent === null || indent < commonIndent) {\n            commonIndent = indent;\n            if (commonIndent === 0) {\n                break;\n            }\n        }\n    }\n    return commonIndent === null ? 0 : commonIndent;\n}\nfunction leadingWhitespace(str) {\n    let i = 0;\n    while (i < str.length && (str[i] === ' ' || str[i] === '\\t')) {\n        i++;\n    }\n    return i;\n}\nfunction isBlank(str) {\n    return leadingWhitespace(str) === str.length;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG;AACxB,QAAQ,cAAc,GAAG;AACzB,QAAQ,WAAW,GAAG;AACtB,QAAQ,YAAY,GAAG;AACvB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,cAAc,GAAG;AACzB,QAAQ,UAAU,GAAG;AACrB,QAAQ,sBAAsB,GAAG;AACjC,QAAQ,sBAAsB,GAAG;AACjC,QAAQ,yBAAyB,GAAG;AACpC,MAAM;AACN,MAAM,kBAAkB;AACxB,IAAI,mBAAmB,CAAC;AACxB,SAAS;IACL,mBAAmB,CAAC;AACxB;AACA,SAAS,eAAe,IAAI;IACxB,MAAM,aAAa,KAAK,IAAI,EAAE;IAC9B,IAAI,cAAc,MAAM;QACpB;IACJ;IACA,YAAY,MAAM;IAClB,OAAQ,KAAK,IAAI;QACb,KAAK;YACD,IAAI,KAAK,MAAM,EAAE;gBACb,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;oBAC7B,YAAY,OAAO,YAAY,MAAM,IAAI,CAAC,KAAK;gBACnD;YACJ;YACA;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,KAAK,MAAM,EAAE;gBACb,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;oBAC7B,YAAY,OAAO,YAAY,MAAM,IAAI,CAAC,KAAK;oBAC/C,IAAI,sBAAsB,UAAU,MAAM,SAAS,EAAE;wBACjD,KAAK,MAAM,OAAO,MAAM,SAAS,CAAE;4BAC/B,YAAY,KAAK,YAAY,MAAM,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK;wBACjE;oBACJ;gBACJ;YACJ;YACA;IACR;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ;IAC9C,MAAM,UAAU,WAAW;IAC3B,IAAI,OAAO,YAAY,YAAY,QAAQ,MAAM,KAAK,GAAG;QACrD;IACJ;IACA,MAAM,OAAO;QAAC;KAAO;IACrB,IAAI,OAAO;QACP,KAAK,IAAI,CAAC;QACV,IAAI,UAAU;YACV,KAAK,IAAI,CAAC;QACd;IACJ;IACA,MAAM,OAAO,KAAK,IAAI,CAAC;IACvB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;QACzB,gBAAgB,CAAC,KAAK,GAAG,EAAE;IAC/B;IACA,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;AAChC;AACA,SAAS,aAAa,OAAO;IACzB,OAAO,SAAS,QAAQ,OAAO,CAAC,OAAO;AAC3C;AACA;;;;;CAKC,GACD;;;CAGC,GACD;;;CAGC,GACD,SAAS,KAAK,UAAU,EAAE,SAAS;IAC/B,OAAO,aAAa,WAAW,MAAM,CAAC,CAAA,IAAK,GAAG,IAAI,CAAC,aAAa,MAAM;AAC1E;AACA,SAAS,kBAAkB,UAAU;IACjC,OAAO,YAAY,KAAK,CAAA,MAAO,IAAI,QAAQ,CAAC,UAAU;AAC1D;AACA,SAAS,eAAe,EAAE;IACtB,OAAO,CAAC,MAAM,MAAM,SAAS,MAAM;QAC/B,MAAM,OAAO,EAAE;QACf,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC,MAAM;YAC9B,IAAI;gBAAC;gBAAU;gBAAa;aAAS,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE;gBAC9D,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK;YAC7B;YACA,OAAO,IAAI,CAAC,IAAI;QACpB,GAAG,SAAS,CAAC,EAAE;QACf,MAAM,MAAM;eAAI;YAAM,QAAQ,MAAM;SAAM,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QAChE,MAAM,QAAQ,EAAE;QAChB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,iBAAiB,gBAAgB,CAAC,IAAI,EAAE;YAC3D,MAAM,IAAI,IAAI,gBAAgB,CAAC,IAAI;QACvC;QACA,OAAO,KAAK;eAAI,MAAM,GAAG,CAAC;YAAe,KAAK,WAAW;YAAE,GAAG,MAAM,MAAM,SAAS,MAAM;SAAW,EAAE;IAC1G;AACJ;AACA,SAAS,OAAO,WAAW;IACvB,OAAO,eAAe,CAAC,EAAE,EAAE,YAAY,OAAO,CAAC,OAAO,SAAS;AACnE;AACA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAChB,OAAO,SAAS,MAAM,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,OAAO,KAAK,OAAO,OAAO,GAAG,CAAC,GAAG;AAChF;AACA;;;CAGC,GACD,SAAS,KAAK,KAAK,EAAE,WAAW,EAAE,GAAG;IACjC,OAAO,cAAc,QAAQ,cAAc,CAAC,OAAO,EAAE,IAAI;AAC7D;AACA;;;;CAIC,GACD,SAAS,iBAAiB,KAAK,EAAE,gBAAgB,KAAK;IAClD,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO,QAAQ,OAAO,CAAC,QAAQ;IAC7D,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,IAAI,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,IACrE,CAAC,GAAG,EAAE,QAAQ,OAAO,CAAC,MAAM,OAAO,GAAG,CAAC,GACvC,CAAC,KAAK,EAAE,gBAAgB,UAAU,OAAO,SAAS,KAAK,CAAC;AAClE;AACA,MAAM,qBAAqB;IACvB,MAAM;QAAE,OAAO,CAAA,OAAQ,KAAK,KAAK;IAAC;IAClC,UAAU;QAAE,OAAO,CAAA,OAAQ,MAAM,KAAK,IAAI;IAAC;IAC3C,WAAW;IACX,UAAU;QACN,OAAO,CAAA,OAAQ,KAAK,KAAK,WAAW,EAAE;IAC1C;IACA,qBAAqB;QACjB,OAAO,CAAA;YACH,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,mBAAmB,EAAE,OAAO;YAChE,MAAM,SAAS,KAAK;gBAAC,KAAK,SAAS;gBAAE,KAAK;oBAAC,KAAK,IAAI;oBAAE;iBAAQ;gBAAG,KAAK,KAAK,UAAU,EAAE;aAAK,EAAE;YAC9F,wBAAwB;YACxB,OAAO,SAAS,MAAM,KAAK,YAAY;QAC3C;IACJ;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,GAAK,WAAW,OAAO,OAAO,KAAK,OAAO,gBAAgB,KAAK,KAAK,KAAK,YAAY;IAC7I;IACA,cAAc;QAAE,OAAO,CAAC,EAAE,UAAU,EAAE,GAAK,MAAM;IAAY;IAC7D,OAAO;QACH,OAAM,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE;YAC5D,MAAM,SAAS,KAAK,IAAI,OAAO,QAAQ;YACvC,IAAI,WAAW,SAAS,KAAK,KAAK,KAAK,MAAM,OAAO;YACpD,IAAI,SAAS,MAAM,GAAG,iBAAiB;gBACnC,WAAW,SAAS,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ;YAC9D;YACA,OAAO,KAAK;gBAAC;gBAAU,KAAK,YAAY;gBAAM;aAAa,EAAE;QACjE;IACJ;IACA,UAAU;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,OAAO,OAAO;IAAM;IAC5D,YAAY;IACZ,gBAAgB;QACZ,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,QAAQ,OAAO,KAAK,KAAK,KAAK,YAAY;IAC/E;IACA,gBAAgB;QACZ,OAAO,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,GAAK,KAAK;gBAAC;gBAAO,KAAK,OAAO;gBAAgB,KAAK,YAAY;gBAAM;aAAa,EAAE;IAC3I;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,mBAAmB,EAAE,UAAU,EAAE,YAAY,EAAE,GAC9E,0EAA0E;YAC1E,4BAA4B;YAC5B,CAAC,SAAS,EAAE,OAAO,KAAK,KAAK,KAAK,qBAAqB,OAAO,KAAK,CAAC,CAAC,GACjE,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,KAAK,IAAI,KAAK,YAAY,MAAM,MAAM,GAC7D;IACR;IACA,QAAQ;IACR,UAAU;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK;IAAM;IACxC,YAAY;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK;IAAM;IAC1C,aAAa;QACT,OAAO,CAAC,EAAE,KAAK,EAAE,OAAO,aAAa,EAAE;YACnC,IAAI,eAAe;gBACf,OAAO,iBAAiB;YAC5B;YACA,OAAO,KAAK,SAAS,CAAC;QAC1B;IACJ;IACA,cAAc;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAM,QAAQ,SAAS;IAAS;IACjE,WAAW;QAAE,OAAO,IAAM;IAAO;IACjC,WAAW;QAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK;IAAM;IACzC,WAAW;QAAE,OAAO,CAAC,EAAE,MAAM,EAAE,GAAK,MAAM,KAAK,QAAQ,QAAQ;IAAI;IACnE,aAAa;QAAE,OAAO,CAAC,EAAE,MAAM,EAAE,GAAK,MAAM,KAAK,QAAQ,QAAQ;IAAI;IACrE,aAAa;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,OAAO,OAAO;IAAM;IAC/D,YAAY;IACZ,WAAW;QACP,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,GAAK,MAAM,OAAO,KAAK,KAAK,KAAK,MAAM,OAAO;IACnF;IACA,OAAO;IACP,WAAW;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK;IAAK;IACvC,UAAU;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK,MAAM,OAAO;IAAI;IAClD,aAAa;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK,OAAO;IAAI;IAC/C,0BAA0B;IAC1B,kBAAkB;QACd,OAAO,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,GAAK,KAAK;gBAAC;gBAAU,KAAK,YAAY;gBAAM,MAAM;aAAgB,EAAE;IAC9G;IACA,yBAAyB;QACrB,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,YAAY,OAAO;IACvD;IACA,sBAAsB;QAClB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC;gBAAU;gBAAM,KAAK,YAAY;aAAK,EAAE;IACnF;IACA,sBAAsB;QAClB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,iBAAiB;QACb,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,OACpD,CAAC,kBAAkB,QACb,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ,SACtC,KAAK,KAAK,KAAK,MAAM,OAAO,IAAI,IACtC,OACA,OACA,KAAK,KAAK,KAAK,YAAY;IACnC;IACA,sBAAsB;QAClB,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC,OAAO,OAAO;gBAAM,KAAK,MAAM;gBAAe,KAAK,YAAY;aAAK,EAAE;IACrI;IACA,yBAAyB;QACrB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAK,KAAK;gBAAC;gBAAS;gBAAM,KAAK,YAAY;gBAAM,KAAK,MAAM,KAAK,OAAO;aAAQ,EAAE;IACzH;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAQ;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IACxG;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC;gBAAM,KAAK,YAAY;aAAK,EAAE;IACzE;IACA,2BAA2B;QACvB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAS;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IACzG;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAK,gBACzD,OACA,CAAC,kBAAkB,QACb,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQ,SACtC,KAAK,KAAK,KAAK,MAAM,OAAO,IAAI,IACtC,CAAC,aAAa,gBAAgB,EAAE,IAChC,SACA,KAAK,WAAW;IACxB;IACA,iBAAiB;QACb,OAAO,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,GAAK,KAAK;gBAAC;gBAAiB,KAAK,YAAY;gBAAM,MAAM;aAAgB,EAAE;IACrH;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,KAAK;gBAAC;gBAAiB;gBAAM,KAAK,YAAY;aAAK,EAAE;IAC1F;IACA,qBAAqB;QACjB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,wBAAwB;QACpB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBACtD;gBACA;gBACA,KAAK,eAAe,KAAK,YAAY;gBACrC,KAAK,YAAY;gBACjB,MAAM;aACT,EAAE;IACP;IACA,oBAAoB;QAChB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAK,KAAK;gBAAC;gBAAgB;gBAAM,KAAK,YAAY;gBAAM,KAAK,MAAM,KAAK,OAAO;aAAQ,EAAE;IAChI;IACA,mBAAmB;QACf,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAe;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IAC/G;IACA,0BAA0B;QACtB,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,GAAK,KAAK;gBAAC;gBAAgB;gBAAM,KAAK,YAAY;gBAAM,MAAM;aAAQ,EAAE;IAChH;AACJ;AACA,MAAM,iCAAiC,OAAO,IAAI,CAAC,oBAAoB,MAAM,CAAC,CAAC,MAAM,MAAQ,CAAC;QAC1F,GAAG,IAAI;QACP,CAAC,IAAI,EAAE;YACH,OAAO,eAAe,kBAAkB,CAAC,IAAI,CAAC,KAAK;QACvD;IACJ,CAAC,GAAG,CAAC;AACL;;;CAGC,GACD,SAAS,kBAAkB,GAAG;IAC1B,OAAO,CAAC,GAAG,UAAU,KAAK,EAAE,KAAK;AACrC;AACA,SAAS,sBAAsB,IAAI;IAC/B,OAAO,KAAK,IAAI,KAAK;AACzB;AACA,yDAAyD;AACzD,SAAS,eAAe,IAAI,EAAE,OAAO;IACjC,IAAI,KAAK,WAAW,IAAI,MAAM;QAC1B,OAAO,KAAK,WAAW,CAAC,KAAK;IACjC;IACA,IAAI,SAAS,qBAAqB;QAC9B,OAAO,WAAW;IACtB;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,MAAM,WAAW,uBAAuB;IACxC,IAAI,aAAa,WAAW;QACxB,OAAO,uBAAuB,CAAC,EAAE,EAAE,UAAU;IACjD;AACJ;AACA,SAAS,uBAAuB,IAAI;IAChC,MAAM,MAAM,KAAK,GAAG;IACpB,IAAI,CAAC,KAAK;QACN;IACJ;IACA,MAAM,WAAW,EAAE;IACnB,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI;IAC/B,MAAO,SAAS,QACZ,MAAM,IAAI,KAAK,UAAU,SAAS,CAAC,OAAO,IAC1C,MAAM,IAAI,IAAI,QACd,MAAM,IAAI,IAAI,QACd,MAAM,IAAI,GAAG,MAAM,MAAM,IAAI,CAAC,IAAI,IAClC,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,CAAE;QAChC,MAAM,QAAQ,OAAO,MAAM,KAAK;QAChC,SAAS,IAAI,CAAC;QACd,QAAQ,MAAM,IAAI;IACtB;IACA,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,OAAO,GAAG,IAAI,CAAC,QAAQ;AACjE;AACA,SAAS,uBAAuB,SAAS;IACrC,4DAA4D;IAC5D,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,sDAAsD;IACtD,MAAM,eAAe,0BAA0B;IAC/C,IAAI,iBAAiB,GAAG;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B;IACJ;IACA,2CAA2C;IAC3C,MAAO,MAAM,MAAM,GAAG,KAAK,QAAQ,KAAK,CAAC,EAAE,EAAG;QAC1C,MAAM,KAAK;IACf;IACA,MAAO,MAAM,MAAM,GAAG,KAAK,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAG;QACzD,MAAM,GAAG;IACb;IACA,mDAAmD;IACnD,OAAO,MAAM,IAAI,CAAC;AACtB;AACA;;CAEC,GACD,SAAS,0BAA0B,KAAK;IACpC,IAAI,eAAe;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,SAAS,kBAAkB;QACjC,IAAI,WAAW,KAAK,MAAM,EAAE;YACxB,UAAU,mBAAmB;QACjC;QACA,IAAI,iBAAiB,QAAQ,SAAS,cAAc;YAChD,eAAe;YACf,IAAI,iBAAiB,GAAG;gBACpB;YACJ;QACJ;IACJ;IACA,OAAO,iBAAiB,OAAO,IAAI;AACvC;AACA,SAAS,kBAAkB,GAAG;IAC1B,IAAI,IAAI;IACR,MAAO,IAAI,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,IAAI,EAAG;QAC1D;IACJ;IACA,OAAO;AACX;AACA,SAAS,QAAQ,GAAG;IAChB,OAAO,kBAAkB,SAAS,IAAI,MAAM;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5050, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/parse-graphql-sdl.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseGraphQLSDL = parseGraphQLSDL;\nexports.transformCommentsToDescriptions = transformCommentsToDescriptions;\nexports.isDescribable = isDescribable;\nconst graphql_1 = require(\"graphql\");\nconst comments_js_1 = require(\"./comments.js\");\nfunction parseGraphQLSDL(location, rawSDL, options = {}) {\n    let document;\n    try {\n        if (options.commentDescriptions && rawSDL.includes('#')) {\n            document = transformCommentsToDescriptions(rawSDL, options);\n            // If noLocation=true, we need to make sure to print and parse it again, to remove locations,\n            // since `transformCommentsToDescriptions` must have locations set in order to transform the comments\n            // into descriptions.\n            if (options.noLocation) {\n                document = (0, graphql_1.parse)((0, graphql_1.print)(document), options);\n            }\n        }\n        else {\n            document = (0, graphql_1.parse)(new graphql_1.Source(rawSDL, location), options);\n        }\n    }\n    catch (e) {\n        if (e.message.includes('EOF') && rawSDL.replace(/(\\#[^*]*)/g, '').trim() === '') {\n            document = {\n                kind: graphql_1.Kind.DOCUMENT,\n                definitions: [],\n            };\n        }\n        else {\n            throw e;\n        }\n    }\n    return {\n        location,\n        document,\n    };\n}\nfunction transformCommentsToDescriptions(sourceSdl, options = {}) {\n    const parsedDoc = (0, graphql_1.parse)(sourceSdl, {\n        ...options,\n        noLocation: false,\n    });\n    const modifiedDoc = (0, graphql_1.visit)(parsedDoc, {\n        leave: (node) => {\n            if (isDescribable(node)) {\n                const rawValue = (0, comments_js_1.getLeadingCommentBlock)(node);\n                if (rawValue !== undefined) {\n                    const commentsBlock = (0, comments_js_1.dedentBlockStringValue)('\\n' + rawValue);\n                    const isBlock = commentsBlock.includes('\\n');\n                    if (!node.description) {\n                        return {\n                            ...node,\n                            description: {\n                                kind: graphql_1.Kind.STRING,\n                                value: commentsBlock,\n                                block: isBlock,\n                            },\n                        };\n                    }\n                    else {\n                        return {\n                            ...node,\n                            description: {\n                                ...node.description,\n                                value: node.description.value + '\\n' + commentsBlock,\n                                block: true,\n                            },\n                        };\n                    }\n                }\n            }\n        },\n    });\n    return modifiedDoc;\n}\nfunction isDescribable(node) {\n    return ((0, graphql_1.isTypeSystemDefinitionNode)(node) ||\n        node.kind === graphql_1.Kind.FIELD_DEFINITION ||\n        node.kind === graphql_1.Kind.INPUT_VALUE_DEFINITION ||\n        node.kind === graphql_1.Kind.ENUM_VALUE_DEFINITION);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG;AAC1B,QAAQ,+BAA+B,GAAG;AAC1C,QAAQ,aAAa,GAAG;AACxB,MAAM;AACN,MAAM;AACN,SAAS,gBAAgB,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IACnD,IAAI;IACJ,IAAI;QACA,IAAI,QAAQ,mBAAmB,IAAI,OAAO,QAAQ,CAAC,MAAM;YACrD,WAAW,gCAAgC,QAAQ;YACnD,6FAA6F;YAC7F,qGAAqG;YACrG,qBAAqB;YACrB,IAAI,QAAQ,UAAU,EAAE;gBACpB,WAAW,CAAC,GAAG,UAAU,KAAK,EAAE,CAAC,GAAG,UAAU,KAAK,EAAE,WAAW;YACpE;QACJ,OACK;YACD,WAAW,CAAC,GAAG,UAAU,KAAK,EAAE,IAAI,UAAU,MAAM,CAAC,QAAQ,WAAW;QAC5E;IACJ,EACA,OAAO,GAAG;QACN,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,OAAO,OAAO,CAAC,cAAc,IAAI,IAAI,OAAO,IAAI;YAC7E,WAAW;gBACP,MAAM,UAAU,IAAI,CAAC,QAAQ;gBAC7B,aAAa,EAAE;YACnB;QACJ,OACK;YACD,MAAM;QACV;IACJ;IACA,OAAO;QACH;QACA;IACJ;AACJ;AACA,SAAS,gCAAgC,SAAS,EAAE,UAAU,CAAC,CAAC;IAC5D,MAAM,YAAY,CAAC,GAAG,UAAU,KAAK,EAAE,WAAW;QAC9C,GAAG,OAAO;QACV,YAAY;IAChB;IACA,MAAM,cAAc,CAAC,GAAG,UAAU,KAAK,EAAE,WAAW;QAChD,OAAO,CAAC;YACJ,IAAI,cAAc,OAAO;gBACrB,MAAM,WAAW,CAAC,GAAG,cAAc,sBAAsB,EAAE;gBAC3D,IAAI,aAAa,WAAW;oBACxB,MAAM,gBAAgB,CAAC,GAAG,cAAc,sBAAsB,EAAE,OAAO;oBACvE,MAAM,UAAU,cAAc,QAAQ,CAAC;oBACvC,IAAI,CAAC,KAAK,WAAW,EAAE;wBACnB,OAAO;4BACH,GAAG,IAAI;4BACP,aAAa;gCACT,MAAM,UAAU,IAAI,CAAC,MAAM;gCAC3B,OAAO;gCACP,OAAO;4BACX;wBACJ;oBACJ,OACK;wBACD,OAAO;4BACH,GAAG,IAAI;4BACP,aAAa;gCACT,GAAG,KAAK,WAAW;gCACnB,OAAO,KAAK,WAAW,CAAC,KAAK,GAAG,OAAO;gCACvC,OAAO;4BACX;wBACJ;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,IAAI;IACvB,OAAQ,CAAC,GAAG,UAAU,0BAA0B,EAAE,SAC9C,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,IAC7C,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,sBAAsB,IACnD,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,qBAAqB;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/build-operation-for-field.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.buildOperationNodeForField = buildOperationNodeForField;\nconst graphql_1 = require(\"graphql\");\nconst rootTypes_js_1 = require(\"./rootTypes.js\");\nlet operationVariables = [];\nlet fieldTypeMap = new Map();\nfunction addOperationVariable(variable) {\n    operationVariables.push(variable);\n}\nfunction resetOperationVariables() {\n    operationVariables = [];\n}\nfunction resetFieldMap() {\n    fieldTypeMap = new Map();\n}\nfunction buildOperationNodeForField({ schema, kind, field, models, ignore = [], depthLimit, circularReferenceDepth, argNames, selectedFields = true, }) {\n    resetOperationVariables();\n    resetFieldMap();\n    const rootTypeNames = (0, rootTypes_js_1.getRootTypeNames)(schema);\n    const operationNode = buildOperationAndCollectVariables({\n        schema,\n        fieldName: field,\n        kind,\n        models: models || [],\n        ignore,\n        depthLimit: depthLimit || Infinity,\n        circularReferenceDepth: circularReferenceDepth || 1,\n        argNames,\n        selectedFields,\n        rootTypeNames,\n    });\n    // attach variables\n    operationNode.variableDefinitions = [...operationVariables];\n    resetOperationVariables();\n    resetFieldMap();\n    return operationNode;\n}\nfunction buildOperationAndCollectVariables({ schema, fieldName, kind, models, ignore, depthLimit, circularReferenceDepth, argNames, selectedFields, rootTypeNames, }) {\n    const type = (0, rootTypes_js_1.getDefinedRootType)(schema, kind);\n    const field = type.getFields()[fieldName];\n    const operationName = `${fieldName}_${kind}`;\n    if (field.args) {\n        for (const arg of field.args) {\n            const argName = arg.name;\n            if (!argNames || argNames.includes(argName)) {\n                addOperationVariable(resolveVariable(arg, argName));\n            }\n        }\n    }\n    return {\n        kind: graphql_1.Kind.OPERATION_DEFINITION,\n        operation: kind,\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: operationName,\n        },\n        variableDefinitions: [],\n        selectionSet: {\n            kind: graphql_1.Kind.SELECTION_SET,\n            selections: [\n                resolveField({\n                    type,\n                    field,\n                    models,\n                    firstCall: true,\n                    path: [],\n                    ancestors: [],\n                    ignore,\n                    depthLimit,\n                    circularReferenceDepth,\n                    schema,\n                    depth: 0,\n                    argNames,\n                    selectedFields,\n                    rootTypeNames,\n                }),\n            ],\n        },\n    };\n}\nfunction resolveSelectionSet({ parent, type, models, firstCall, path, ancestors, ignore, depthLimit, circularReferenceDepth, schema, depth, argNames, selectedFields, rootTypeNames, }) {\n    if (typeof selectedFields === 'boolean' && depth > depthLimit) {\n        return;\n    }\n    if ((0, graphql_1.isUnionType)(type)) {\n        const types = type.getTypes();\n        return {\n            kind: graphql_1.Kind.SELECTION_SET,\n            selections: types\n                .filter(t => !hasCircularRef([...ancestors, t], {\n                depth: circularReferenceDepth,\n            }))\n                .map(t => {\n                return {\n                    kind: graphql_1.Kind.INLINE_FRAGMENT,\n                    typeCondition: {\n                        kind: graphql_1.Kind.NAMED_TYPE,\n                        name: {\n                            kind: graphql_1.Kind.NAME,\n                            value: t.name,\n                        },\n                    },\n                    selectionSet: resolveSelectionSet({\n                        parent: type,\n                        type: t,\n                        models,\n                        path,\n                        ancestors,\n                        ignore,\n                        depthLimit,\n                        circularReferenceDepth,\n                        schema,\n                        depth,\n                        argNames,\n                        selectedFields,\n                        rootTypeNames,\n                    }),\n                };\n            })\n                .filter(fragmentNode => fragmentNode?.selectionSet?.selections?.length > 0),\n        };\n    }\n    if ((0, graphql_1.isInterfaceType)(type)) {\n        const types = Object.values(schema.getTypeMap()).filter((t) => (0, graphql_1.isObjectType)(t) && t.getInterfaces().includes(type));\n        return {\n            kind: graphql_1.Kind.SELECTION_SET,\n            selections: types\n                .filter(t => !hasCircularRef([...ancestors, t], {\n                depth: circularReferenceDepth,\n            }))\n                .map(t => {\n                return {\n                    kind: graphql_1.Kind.INLINE_FRAGMENT,\n                    typeCondition: {\n                        kind: graphql_1.Kind.NAMED_TYPE,\n                        name: {\n                            kind: graphql_1.Kind.NAME,\n                            value: t.name,\n                        },\n                    },\n                    selectionSet: resolveSelectionSet({\n                        parent: type,\n                        type: t,\n                        models,\n                        path,\n                        ancestors,\n                        ignore,\n                        depthLimit,\n                        circularReferenceDepth,\n                        schema,\n                        depth,\n                        argNames,\n                        selectedFields,\n                        rootTypeNames,\n                    }),\n                };\n            })\n                .filter(fragmentNode => fragmentNode?.selectionSet?.selections?.length > 0),\n        };\n    }\n    if ((0, graphql_1.isObjectType)(type) && !rootTypeNames.has(type.name)) {\n        const isIgnored = ignore.includes(type.name) || ignore.includes(`${parent.name}.${path[path.length - 1]}`);\n        const isModel = models.includes(type.name);\n        if (!firstCall && isModel && !isIgnored) {\n            return {\n                kind: graphql_1.Kind.SELECTION_SET,\n                selections: [\n                    {\n                        kind: graphql_1.Kind.FIELD,\n                        name: {\n                            kind: graphql_1.Kind.NAME,\n                            value: 'id',\n                        },\n                    },\n                ],\n            };\n        }\n        const fields = type.getFields();\n        return {\n            kind: graphql_1.Kind.SELECTION_SET,\n            selections: Object.keys(fields)\n                .filter(fieldName => {\n                return !hasCircularRef([...ancestors, (0, graphql_1.getNamedType)(fields[fieldName].type)], {\n                    depth: circularReferenceDepth,\n                });\n            })\n                .map(fieldName => {\n                const selectedSubFields = typeof selectedFields === 'object' ? selectedFields[fieldName] : true;\n                if (selectedSubFields) {\n                    return resolveField({\n                        type,\n                        field: fields[fieldName],\n                        models,\n                        path: [...path, fieldName],\n                        ancestors,\n                        ignore,\n                        depthLimit,\n                        circularReferenceDepth,\n                        schema,\n                        depth,\n                        argNames,\n                        selectedFields: selectedSubFields,\n                        rootTypeNames,\n                    });\n                }\n                return null;\n            })\n                .filter((f) => {\n                if (f == null) {\n                    return false;\n                }\n                else if ('selectionSet' in f) {\n                    return !!f.selectionSet?.selections?.length;\n                }\n                return true;\n            }),\n        };\n    }\n}\nfunction resolveVariable(arg, name) {\n    function resolveVariableType(type) {\n        if ((0, graphql_1.isListType)(type)) {\n            return {\n                kind: graphql_1.Kind.LIST_TYPE,\n                type: resolveVariableType(type.ofType),\n            };\n        }\n        if ((0, graphql_1.isNonNullType)(type)) {\n            return {\n                kind: graphql_1.Kind.NON_NULL_TYPE,\n                // for v16 compatibility\n                type: resolveVariableType(type.ofType),\n            };\n        }\n        return {\n            kind: graphql_1.Kind.NAMED_TYPE,\n            name: {\n                kind: graphql_1.Kind.NAME,\n                value: type.name,\n            },\n        };\n    }\n    return {\n        kind: graphql_1.Kind.VARIABLE_DEFINITION,\n        variable: {\n            kind: graphql_1.Kind.VARIABLE,\n            name: {\n                kind: graphql_1.Kind.NAME,\n                value: name || arg.name,\n            },\n        },\n        type: resolveVariableType(arg.type),\n    };\n}\nfunction getArgumentName(name, path) {\n    return [...path, name].join('_');\n}\nfunction resolveField({ type, field, models, firstCall, path, ancestors, ignore, depthLimit, circularReferenceDepth, schema, depth, argNames, selectedFields, rootTypeNames, }) {\n    const namedType = (0, graphql_1.getNamedType)(field.type);\n    let args = [];\n    let removeField = false;\n    if (field.args && field.args.length) {\n        args = field.args\n            .map(arg => {\n            const argumentName = getArgumentName(arg.name, path);\n            if (argNames && !argNames.includes(argumentName)) {\n                if ((0, graphql_1.isNonNullType)(arg.type)) {\n                    removeField = true;\n                }\n                return null;\n            }\n            if (!firstCall) {\n                addOperationVariable(resolveVariable(arg, argumentName));\n            }\n            return {\n                kind: graphql_1.Kind.ARGUMENT,\n                name: {\n                    kind: graphql_1.Kind.NAME,\n                    value: arg.name,\n                },\n                value: {\n                    kind: graphql_1.Kind.VARIABLE,\n                    name: {\n                        kind: graphql_1.Kind.NAME,\n                        value: getArgumentName(arg.name, path),\n                    },\n                },\n            };\n        })\n            .filter(Boolean);\n    }\n    if (removeField) {\n        return null;\n    }\n    const fieldPath = [...path, field.name];\n    const fieldPathStr = fieldPath.join('.');\n    let fieldName = field.name;\n    if (fieldTypeMap.has(fieldPathStr) && fieldTypeMap.get(fieldPathStr) !== field.type.toString()) {\n        fieldName += field.type\n            .toString()\n            .replace(/!/g, 'NonNull')\n            .replace(/\\[/g, 'List')\n            .replace(/\\]/g, '');\n    }\n    fieldTypeMap.set(fieldPathStr, field.type.toString());\n    if (!(0, graphql_1.isScalarType)(namedType) && !(0, graphql_1.isEnumType)(namedType)) {\n        return {\n            kind: graphql_1.Kind.FIELD,\n            name: {\n                kind: graphql_1.Kind.NAME,\n                value: field.name,\n            },\n            ...(fieldName !== field.name && { alias: { kind: graphql_1.Kind.NAME, value: fieldName } }),\n            selectionSet: resolveSelectionSet({\n                parent: type,\n                type: namedType,\n                models,\n                firstCall,\n                path: fieldPath,\n                ancestors: [...ancestors, type],\n                ignore,\n                depthLimit,\n                circularReferenceDepth,\n                schema,\n                depth: depth + 1,\n                argNames,\n                selectedFields,\n                rootTypeNames,\n            }) || undefined,\n            arguments: args,\n        };\n    }\n    return {\n        kind: graphql_1.Kind.FIELD,\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: field.name,\n        },\n        ...(fieldName !== field.name && { alias: { kind: graphql_1.Kind.NAME, value: fieldName } }),\n        arguments: args,\n    };\n}\nfunction hasCircularRef(types, config = {\n    depth: 1,\n}) {\n    const type = types[types.length - 1];\n    if ((0, graphql_1.isScalarType)(type)) {\n        return false;\n    }\n    const size = types.filter(t => t.name === type.name).length;\n    return size > config.depth;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,0BAA0B,GAAG;AACrC,MAAM;AACN,MAAM;AACN,IAAI,qBAAqB,EAAE;AAC3B,IAAI,eAAe,IAAI;AACvB,SAAS,qBAAqB,QAAQ;IAClC,mBAAmB,IAAI,CAAC;AAC5B;AACA,SAAS;IACL,qBAAqB,EAAE;AAC3B;AACA,SAAS;IACL,eAAe,IAAI;AACvB;AACA,SAAS,2BAA2B,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,sBAAsB,EAAE,QAAQ,EAAE,iBAAiB,IAAI,EAAG;IAClJ;IACA;IACA,MAAM,gBAAgB,CAAC,GAAG,eAAe,gBAAgB,EAAE;IAC3D,MAAM,gBAAgB,kCAAkC;QACpD;QACA,WAAW;QACX;QACA,QAAQ,UAAU,EAAE;QACpB;QACA,YAAY,cAAc;QAC1B,wBAAwB,0BAA0B;QAClD;QACA;QACA;IACJ;IACA,mBAAmB;IACnB,cAAc,mBAAmB,GAAG;WAAI;KAAmB;IAC3D;IACA;IACA,OAAO;AACX;AACA,SAAS,kCAAkC,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAG;IAChK,MAAM,OAAO,CAAC,GAAG,eAAe,kBAAkB,EAAE,QAAQ;IAC5D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;IACzC,MAAM,gBAAgB,GAAG,UAAU,CAAC,EAAE,MAAM;IAC5C,IAAI,MAAM,IAAI,EAAE;QACZ,KAAK,MAAM,OAAO,MAAM,IAAI,CAAE;YAC1B,MAAM,UAAU,IAAI,IAAI;YACxB,IAAI,CAAC,YAAY,SAAS,QAAQ,CAAC,UAAU;gBACzC,qBAAqB,gBAAgB,KAAK;YAC9C;QACJ;IACJ;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,oBAAoB;QACzC,WAAW;QACX,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO;QACX;QACA,qBAAqB,EAAE;QACvB,cAAc;YACV,MAAM,UAAU,IAAI,CAAC,aAAa;YAClC,YAAY;gBACR,aAAa;oBACT;oBACA;oBACA;oBACA,WAAW;oBACX,MAAM,EAAE;oBACR,WAAW,EAAE;oBACb;oBACA;oBACA;oBACA;oBACA,OAAO;oBACP;oBACA;oBACA;gBACJ;aACH;QACL;IACJ;AACJ;AACA,SAAS,oBAAoB,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAG;IAClL,IAAI,OAAO,mBAAmB,aAAa,QAAQ,YAAY;QAC3D;IACJ;IACA,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;QAClC,MAAM,QAAQ,KAAK,QAAQ;QAC3B,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,aAAa;YAClC,YAAY,MACP,MAAM,CAAC,CAAA,IAAK,CAAC,eAAe;uBAAI;oBAAW;iBAAE,EAAE;oBAChD,OAAO;gBACX,IACK,GAAG,CAAC,CAAA;gBACL,OAAO;oBACH,MAAM,UAAU,IAAI,CAAC,eAAe;oBACpC,eAAe;wBACX,MAAM,UAAU,IAAI,CAAC,UAAU;wBAC/B,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,IAAI;4BACzB,OAAO,EAAE,IAAI;wBACjB;oBACJ;oBACA,cAAc,oBAAoB;wBAC9B,QAAQ;wBACR,MAAM;wBACN;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;oBACJ;gBACJ;YACJ,GACK,MAAM,CAAC,CAAA,eAAgB,cAAc,cAAc,YAAY,SAAS;QACjF;IACJ;IACA,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;QACtC,MAAM,QAAQ,OAAO,MAAM,CAAC,OAAO,UAAU,IAAI,MAAM,CAAC,CAAC,IAAM,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,EAAE,aAAa,GAAG,QAAQ,CAAC;QAC5H,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,aAAa;YAClC,YAAY,MACP,MAAM,CAAC,CAAA,IAAK,CAAC,eAAe;uBAAI;oBAAW;iBAAE,EAAE;oBAChD,OAAO;gBACX,IACK,GAAG,CAAC,CAAA;gBACL,OAAO;oBACH,MAAM,UAAU,IAAI,CAAC,eAAe;oBACpC,eAAe;wBACX,MAAM,UAAU,IAAI,CAAC,UAAU;wBAC/B,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,IAAI;4BACzB,OAAO,EAAE,IAAI;wBACjB;oBACJ;oBACA,cAAc,oBAAoB;wBAC9B,QAAQ;wBACR,MAAM;wBACN;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;oBACJ;gBACJ;YACJ,GACK,MAAM,CAAC,CAAA,eAAgB,cAAc,cAAc,YAAY,SAAS;QACjF;IACJ;IACA,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,SAAS,CAAC,cAAc,GAAG,CAAC,KAAK,IAAI,GAAG;QACpE,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,EAAE;QACzG,MAAM,UAAU,OAAO,QAAQ,CAAC,KAAK,IAAI;QACzC,IAAI,CAAC,aAAa,WAAW,CAAC,WAAW;YACrC,OAAO;gBACH,MAAM,UAAU,IAAI,CAAC,aAAa;gBAClC,YAAY;oBACR;wBACI,MAAM,UAAU,IAAI,CAAC,KAAK;wBAC1B,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,IAAI;4BACzB,OAAO;wBACX;oBACJ;iBACH;YACL;QACJ;QACA,MAAM,SAAS,KAAK,SAAS;QAC7B,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,aAAa;YAClC,YAAY,OAAO,IAAI,CAAC,QACnB,MAAM,CAAC,CAAA;gBACR,OAAO,CAAC,eAAe;uBAAI;oBAAW,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;iBAAE,EAAE;oBACxF,OAAO;gBACX;YACJ,GACK,GAAG,CAAC,CAAA;gBACL,MAAM,oBAAoB,OAAO,mBAAmB,WAAW,cAAc,CAAC,UAAU,GAAG;gBAC3F,IAAI,mBAAmB;oBACnB,OAAO,aAAa;wBAChB;wBACA,OAAO,MAAM,CAAC,UAAU;wBACxB;wBACA,MAAM;+BAAI;4BAAM;yBAAU;wBAC1B;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA,gBAAgB;wBAChB;oBACJ;gBACJ;gBACA,OAAO;YACX,GACK,MAAM,CAAC,CAAC;gBACT,IAAI,KAAK,MAAM;oBACX,OAAO;gBACX,OACK,IAAI,kBAAkB,GAAG;oBAC1B,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,YAAY;gBACzC;gBACA,OAAO;YACX;QACJ;IACJ;AACJ;AACA,SAAS,gBAAgB,GAAG,EAAE,IAAI;IAC9B,SAAS,oBAAoB,IAAI;QAC7B,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACjC,OAAO;gBACH,MAAM,UAAU,IAAI,CAAC,SAAS;gBAC9B,MAAM,oBAAoB,KAAK,MAAM;YACzC;QACJ;QACA,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,OAAO;YACpC,OAAO;gBACH,MAAM,UAAU,IAAI,CAAC,aAAa;gBAClC,wBAAwB;gBACxB,MAAM,oBAAoB,KAAK,MAAM;YACzC;QACJ;QACA,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,UAAU;YAC/B,MAAM;gBACF,MAAM,UAAU,IAAI,CAAC,IAAI;gBACzB,OAAO,KAAK,IAAI;YACpB;QACJ;IACJ;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,mBAAmB;QACxC,UAAU;YACN,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM;gBACF,MAAM,UAAU,IAAI,CAAC,IAAI;gBACzB,OAAO,QAAQ,IAAI,IAAI;YAC3B;QACJ;QACA,MAAM,oBAAoB,IAAI,IAAI;IACtC;AACJ;AACA,SAAS,gBAAgB,IAAI,EAAE,IAAI;IAC/B,OAAO;WAAI;QAAM;KAAK,CAAC,IAAI,CAAC;AAChC;AACA,SAAS,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAG;IAC1K,MAAM,YAAY,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,IAAI;IACxD,IAAI,OAAO,EAAE;IACb,IAAI,cAAc;IAClB,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;QACjC,OAAO,MAAM,IAAI,CACZ,GAAG,CAAC,CAAA;YACL,MAAM,eAAe,gBAAgB,IAAI,IAAI,EAAE;YAC/C,IAAI,YAAY,CAAC,SAAS,QAAQ,CAAC,eAAe;gBAC9C,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,IAAI,IAAI,GAAG;oBACxC,cAAc;gBAClB;gBACA,OAAO;YACX;YACA,IAAI,CAAC,WAAW;gBACZ,qBAAqB,gBAAgB,KAAK;YAC9C;YACA,OAAO;gBACH,MAAM,UAAU,IAAI,CAAC,QAAQ;gBAC7B,MAAM;oBACF,MAAM,UAAU,IAAI,CAAC,IAAI;oBACzB,OAAO,IAAI,IAAI;gBACnB;gBACA,OAAO;oBACH,MAAM,UAAU,IAAI,CAAC,QAAQ;oBAC7B,MAAM;wBACF,MAAM,UAAU,IAAI,CAAC,IAAI;wBACzB,OAAO,gBAAgB,IAAI,IAAI,EAAE;oBACrC;gBACJ;YACJ;QACJ,GACK,MAAM,CAAC;IAChB;IACA,IAAI,aAAa;QACb,OAAO;IACX;IACA,MAAM,YAAY;WAAI;QAAM,MAAM,IAAI;KAAC;IACvC,MAAM,eAAe,UAAU,IAAI,CAAC;IACpC,IAAI,YAAY,MAAM,IAAI;IAC1B,IAAI,aAAa,GAAG,CAAC,iBAAiB,aAAa,GAAG,CAAC,kBAAkB,MAAM,IAAI,CAAC,QAAQ,IAAI;QAC5F,aAAa,MAAM,IAAI,CAClB,QAAQ,GACR,OAAO,CAAC,MAAM,WACd,OAAO,CAAC,OAAO,QACf,OAAO,CAAC,OAAO;IACxB;IACA,aAAa,GAAG,CAAC,cAAc,MAAM,IAAI,CAAC,QAAQ;IAClD,IAAI,CAAC,CAAC,GAAG,UAAU,YAAY,EAAE,cAAc,CAAC,CAAC,GAAG,UAAU,UAAU,EAAE,YAAY;QAClF,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,KAAK;YAC1B,MAAM;gBACF,MAAM,UAAU,IAAI,CAAC,IAAI;gBACzB,OAAO,MAAM,IAAI;YACrB;YACA,GAAI,cAAc,MAAM,IAAI,IAAI;gBAAE,OAAO;oBAAE,MAAM,UAAU,IAAI,CAAC,IAAI;oBAAE,OAAO;gBAAU;YAAE,CAAC;YAC1F,cAAc,oBAAoB;gBAC9B,QAAQ;gBACR,MAAM;gBACN;gBACA;gBACA,MAAM;gBACN,WAAW;uBAAI;oBAAW;iBAAK;gBAC/B;gBACA;gBACA;gBACA;gBACA,OAAO,QAAQ;gBACf;gBACA;gBACA;YACJ,MAAM;YACN,WAAW;QACf;IACJ;IACA,OAAO;QACH,MAAM,UAAU,IAAI,CAAC,KAAK;QAC1B,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO,MAAM,IAAI;QACrB;QACA,GAAI,cAAc,MAAM,IAAI,IAAI;YAAE,OAAO;gBAAE,MAAM,UAAU,IAAI,CAAC,IAAI;gBAAE,OAAO;YAAU;QAAE,CAAC;QAC1F,WAAW;IACf;AACJ;AACA,SAAS,eAAe,KAAK,EAAE,SAAS;IACpC,OAAO;AACX,CAAC;IACG,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IACpC,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACnC,OAAO;IACX;IACA,MAAM,OAAO,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE,MAAM;IAC3D,OAAO,OAAO,OAAO,KAAK;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/types.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DirectiveLocation = void 0;\nvar DirectiveLocation;\n(function (DirectiveLocation) {\n    /** Request Definitions */\n    DirectiveLocation[\"QUERY\"] = \"QUERY\";\n    DirectiveLocation[\"MUTATION\"] = \"MUTATION\";\n    DirectiveLocation[\"SUBSCRIPTION\"] = \"SUBSCRIPTION\";\n    DirectiveLocation[\"FIELD\"] = \"FIELD\";\n    DirectiveLocation[\"FRAGMENT_DEFINITION\"] = \"FRAGMENT_DEFINITION\";\n    DirectiveLocation[\"FRAGMENT_SPREAD\"] = \"FRAGMENT_SPREAD\";\n    DirectiveLocation[\"INLINE_FRAGMENT\"] = \"INLINE_FRAGMENT\";\n    DirectiveLocation[\"VARIABLE_DEFINITION\"] = \"VARIABLE_DEFINITION\";\n    /** Type System Definitions */\n    DirectiveLocation[\"SCHEMA\"] = \"SCHEMA\";\n    DirectiveLocation[\"SCALAR\"] = \"SCALAR\";\n    DirectiveLocation[\"OBJECT\"] = \"OBJECT\";\n    DirectiveLocation[\"FIELD_DEFINITION\"] = \"FIELD_DEFINITION\";\n    DirectiveLocation[\"ARGUMENT_DEFINITION\"] = \"ARGUMENT_DEFINITION\";\n    DirectiveLocation[\"INTERFACE\"] = \"INTERFACE\";\n    DirectiveLocation[\"UNION\"] = \"UNION\";\n    DirectiveLocation[\"ENUM\"] = \"ENUM\";\n    DirectiveLocation[\"ENUM_VALUE\"] = \"ENUM_VALUE\";\n    DirectiveLocation[\"INPUT_OBJECT\"] = \"INPUT_OBJECT\";\n    DirectiveLocation[\"INPUT_FIELD_DEFINITION\"] = \"INPUT_FIELD_DEFINITION\";\n})(DirectiveLocation || (exports.DirectiveLocation = DirectiveLocation = {}));\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,KAAK;AACjC,IAAI;AACJ,CAAC,SAAU,iBAAiB;IACxB,wBAAwB,GACxB,iBAAiB,CAAC,QAAQ,GAAG;IAC7B,iBAAiB,CAAC,WAAW,GAAG;IAChC,iBAAiB,CAAC,eAAe,GAAG;IACpC,iBAAiB,CAAC,QAAQ,GAAG;IAC7B,iBAAiB,CAAC,sBAAsB,GAAG;IAC3C,iBAAiB,CAAC,kBAAkB,GAAG;IACvC,iBAAiB,CAAC,kBAAkB,GAAG;IACvC,iBAAiB,CAAC,sBAAsB,GAAG;IAC3C,4BAA4B,GAC5B,iBAAiB,CAAC,SAAS,GAAG;IAC9B,iBAAiB,CAAC,SAAS,GAAG;IAC9B,iBAAiB,CAAC,SAAS,GAAG;IAC9B,iBAAiB,CAAC,mBAAmB,GAAG;IACxC,iBAAiB,CAAC,sBAAsB,GAAG;IAC3C,iBAAiB,CAAC,YAAY,GAAG;IACjC,iBAAiB,CAAC,QAAQ,GAAG;IAC7B,iBAAiB,CAAC,OAAO,GAAG;IAC5B,iBAAiB,CAAC,aAAa,GAAG;IAClC,iBAAiB,CAAC,eAAe,GAAG;IACpC,iBAAiB,CAAC,yBAAyB,GAAG;AAClD,CAAC,EAAE,qBAAqB,CAAC,QAAQ,iBAAiB,GAAG,oBAAoB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5536, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/Interfaces.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MapperKind = void 0;\nvar MapperKind;\n(function (MapperKind) {\n    MapperKind[\"TYPE\"] = \"MapperKind.TYPE\";\n    MapperKind[\"SCALAR_TYPE\"] = \"MapperKind.SCALAR_TYPE\";\n    MapperKind[\"ENUM_TYPE\"] = \"MapperKind.ENUM_TYPE\";\n    MapperKind[\"COMPOSITE_TYPE\"] = \"MapperKind.COMPOSITE_TYPE\";\n    MapperKind[\"OBJECT_TYPE\"] = \"MapperKind.OBJECT_TYPE\";\n    MapperKind[\"INPUT_OBJECT_TYPE\"] = \"MapperKind.INPUT_OBJECT_TYPE\";\n    MapperKind[\"ABSTRACT_TYPE\"] = \"MapperKind.ABSTRACT_TYPE\";\n    MapperKind[\"UNION_TYPE\"] = \"MapperKind.UNION_TYPE\";\n    MapperKind[\"INTERFACE_TYPE\"] = \"MapperKind.INTERFACE_TYPE\";\n    MapperKind[\"ROOT_OBJECT\"] = \"MapperKind.ROOT_OBJECT\";\n    MapperKind[\"QUERY\"] = \"MapperKind.QUERY\";\n    MapperKind[\"MUTATION\"] = \"MapperKind.MUTATION\";\n    MapperKind[\"SUBSCRIPTION\"] = \"MapperKind.SUBSCRIPTION\";\n    MapperKind[\"DIRECTIVE\"] = \"MapperKind.DIRECTIVE\";\n    MapperKind[\"FIELD\"] = \"MapperKind.FIELD\";\n    MapperKind[\"COMPOSITE_FIELD\"] = \"MapperKind.COMPOSITE_FIELD\";\n    MapperKind[\"OBJECT_FIELD\"] = \"MapperKind.OBJECT_FIELD\";\n    MapperKind[\"ROOT_FIELD\"] = \"MapperKind.ROOT_FIELD\";\n    MapperKind[\"QUERY_ROOT_FIELD\"] = \"MapperKind.QUERY_ROOT_FIELD\";\n    MapperKind[\"MUTATION_ROOT_FIELD\"] = \"MapperKind.MUTATION_ROOT_FIELD\";\n    MapperKind[\"SUBSCRIPTION_ROOT_FIELD\"] = \"MapperKind.SUBSCRIPTION_ROOT_FIELD\";\n    MapperKind[\"INTERFACE_FIELD\"] = \"MapperKind.INTERFACE_FIELD\";\n    MapperKind[\"INPUT_OBJECT_FIELD\"] = \"MapperKind.INPUT_OBJECT_FIELD\";\n    MapperKind[\"ARGUMENT\"] = \"MapperKind.ARGUMENT\";\n    MapperKind[\"ENUM_VALUE\"] = \"MapperKind.ENUM_VALUE\";\n})(MapperKind || (exports.MapperKind = MapperKind = {}));\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,IAAI;AACJ,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,OAAO,GAAG;IACrB,UAAU,CAAC,cAAc,GAAG;IAC5B,UAAU,CAAC,YAAY,GAAG;IAC1B,UAAU,CAAC,iBAAiB,GAAG;IAC/B,UAAU,CAAC,cAAc,GAAG;IAC5B,UAAU,CAAC,oBAAoB,GAAG;IAClC,UAAU,CAAC,gBAAgB,GAAG;IAC9B,UAAU,CAAC,aAAa,GAAG;IAC3B,UAAU,CAAC,iBAAiB,GAAG;IAC/B,UAAU,CAAC,cAAc,GAAG;IAC5B,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,WAAW,GAAG;IACzB,UAAU,CAAC,eAAe,GAAG;IAC7B,UAAU,CAAC,YAAY,GAAG;IAC1B,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,kBAAkB,GAAG;IAChC,UAAU,CAAC,eAAe,GAAG;IAC7B,UAAU,CAAC,aAAa,GAAG;IAC3B,UAAU,CAAC,mBAAmB,GAAG;IACjC,UAAU,CAAC,sBAAsB,GAAG;IACpC,UAAU,CAAC,0BAA0B,GAAG;IACxC,UAAU,CAAC,kBAAkB,GAAG;IAChC,UAAU,CAAC,qBAAqB,GAAG;IACnC,UAAU,CAAC,WAAW,GAAG;IACzB,UAAU,CAAC,aAAa,GAAG;AAC/B,CAAC,EAAE,cAAc,CAAC,QAAQ,UAAU,GAAG,aAAa,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5572, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/getObjectTypeFromTypeMap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getObjectTypeFromTypeMap = getObjectTypeFromTypeMap;\nconst graphql_1 = require(\"graphql\");\nfunction getObjectTypeFromTypeMap(typeMap, type) {\n    if (type) {\n        const maybeObjectType = typeMap[type.name];\n        if ((0, graphql_1.isObjectType)(maybeObjectType)) {\n            return maybeObjectType;\n        }\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,wBAAwB,GAAG;AACnC,MAAM;AACN,SAAS,yBAAyB,OAAO,EAAE,IAAI;IAC3C,IAAI,MAAM;QACN,MAAM,kBAAkB,OAAO,CAAC,KAAK,IAAI,CAAC;QAC1C,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,kBAAkB;YAC9C,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5589, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/stub.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createNamedStub = createNamedStub;\nexports.createStub = createStub;\nexports.isNamedStub = isNamedStub;\nexports.getBuiltInForStub = getBuiltInForStub;\nconst graphql_1 = require(\"graphql\");\nfunction createNamedStub(name, type) {\n    let constructor;\n    if (type === 'object') {\n        constructor = graphql_1.GraphQLObjectType;\n    }\n    else if (type === 'interface') {\n        constructor = graphql_1.GraphQLInterfaceType;\n    }\n    else {\n        constructor = graphql_1.GraphQLInputObjectType;\n    }\n    return new constructor({\n        name,\n        fields: {\n            _fake: {\n                type: graphql_1.GraphQLString,\n            },\n        },\n    });\n}\nfunction createStub(node, type) {\n    switch (node.kind) {\n        case graphql_1.Kind.LIST_TYPE:\n            return new graphql_1.GraphQLList(createStub(node.type, type));\n        case graphql_1.Kind.NON_NULL_TYPE:\n            return new graphql_1.GraphQLNonNull(createStub(node.type, type));\n        default:\n            if (type === 'output') {\n                return createNamedStub(node.name.value, 'object');\n            }\n            return createNamedStub(node.name.value, 'input');\n    }\n}\nfunction isNamedStub(type) {\n    if ('getFields' in type) {\n        const fields = type.getFields();\n        // eslint-disable-next-line no-unreachable-loop\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            return field.name === '_fake';\n        }\n    }\n    return false;\n}\nfunction getBuiltInForStub(type) {\n    switch (type.name) {\n        case graphql_1.GraphQLInt.name:\n            return graphql_1.GraphQLInt;\n        case graphql_1.GraphQLFloat.name:\n            return graphql_1.GraphQLFloat;\n        case graphql_1.GraphQLString.name:\n            return graphql_1.GraphQLString;\n        case graphql_1.GraphQLBoolean.name:\n            return graphql_1.GraphQLBoolean;\n        case graphql_1.GraphQLID.name:\n            return graphql_1.GraphQLID;\n        default:\n            return type;\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG;AAC1B,QAAQ,UAAU,GAAG;AACrB,QAAQ,WAAW,GAAG;AACtB,QAAQ,iBAAiB,GAAG;AAC5B,MAAM;AACN,SAAS,gBAAgB,IAAI,EAAE,IAAI;IAC/B,IAAI;IACJ,IAAI,SAAS,UAAU;QACnB,cAAc,UAAU,iBAAiB;IAC7C,OACK,IAAI,SAAS,aAAa;QAC3B,cAAc,UAAU,oBAAoB;IAChD,OACK;QACD,cAAc,UAAU,sBAAsB;IAClD;IACA,OAAO,IAAI,YAAY;QACnB;QACA,QAAQ;YACJ,OAAO;gBACH,MAAM,UAAU,aAAa;YACjC;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,IAAI,EAAE,IAAI;IAC1B,OAAQ,KAAK,IAAI;QACb,KAAK,UAAU,IAAI,CAAC,SAAS;YACzB,OAAO,IAAI,UAAU,WAAW,CAAC,WAAW,KAAK,IAAI,EAAE;QAC3D,KAAK,UAAU,IAAI,CAAC,aAAa;YAC7B,OAAO,IAAI,UAAU,cAAc,CAAC,WAAW,KAAK,IAAI,EAAE;QAC9D;YACI,IAAI,SAAS,UAAU;gBACnB,OAAO,gBAAgB,KAAK,IAAI,CAAC,KAAK,EAAE;YAC5C;YACA,OAAO,gBAAgB,KAAK,IAAI,CAAC,KAAK,EAAE;IAChD;AACJ;AACA,SAAS,YAAY,IAAI;IACrB,IAAI,eAAe,MAAM;QACrB,MAAM,SAAS,KAAK,SAAS;QAC7B,+CAA+C;QAC/C,IAAK,MAAM,aAAa,OAAQ;YAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;YAC/B,OAAO,MAAM,IAAI,KAAK;QAC1B;IACJ;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAQ,KAAK,IAAI;QACb,KAAK,UAAU,UAAU,CAAC,IAAI;YAC1B,OAAO,UAAU,UAAU;QAC/B,KAAK,UAAU,YAAY,CAAC,IAAI;YAC5B,OAAO,UAAU,YAAY;QACjC,KAAK,UAAU,aAAa,CAAC,IAAI;YAC7B,OAAO,UAAU,aAAa;QAClC,KAAK,UAAU,cAAc,CAAC,IAAI;YAC9B,OAAO,UAAU,cAAc;QACnC,KAAK,UAAU,SAAS,CAAC,IAAI;YACzB,OAAO,UAAU,SAAS;QAC9B;YACI,OAAO;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5659, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/rewire.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.rewireTypes = rewireTypes;\nconst graphql_1 = require(\"graphql\");\nconst stub_js_1 = require(\"./stub.js\");\nfunction rewireTypes(originalTypeMap, directives) {\n    const referenceTypeMap = Object.create(null);\n    for (const typeName in originalTypeMap) {\n        referenceTypeMap[typeName] = originalTypeMap[typeName];\n    }\n    const newTypeMap = Object.create(null);\n    for (const typeName in referenceTypeMap) {\n        const namedType = referenceTypeMap[typeName];\n        if (namedType == null || typeName.startsWith('__')) {\n            continue;\n        }\n        const newName = namedType.name;\n        if (newName.startsWith('__')) {\n            continue;\n        }\n        if (newTypeMap[newName] != null) {\n            console.warn(`Duplicate schema type name ${newName} found; keeping the existing one found in the schema`);\n            continue;\n        }\n        newTypeMap[newName] = namedType;\n    }\n    for (const typeName in newTypeMap) {\n        newTypeMap[typeName] = rewireNamedType(newTypeMap[typeName]);\n    }\n    const newDirectives = directives.map(directive => rewireDirective(directive));\n    return {\n        typeMap: newTypeMap,\n        directives: newDirectives,\n    };\n    function rewireDirective(directive) {\n        if ((0, graphql_1.isSpecifiedDirective)(directive)) {\n            return directive;\n        }\n        const directiveConfig = directive.toConfig();\n        directiveConfig.args = rewireArgs(directiveConfig.args);\n        return new graphql_1.GraphQLDirective(directiveConfig);\n    }\n    function rewireArgs(args) {\n        const rewiredArgs = {};\n        for (const argName in args) {\n            const arg = args[argName];\n            const rewiredArgType = rewireType(arg.type);\n            if (rewiredArgType != null) {\n                arg.type = rewiredArgType;\n                rewiredArgs[argName] = arg;\n            }\n        }\n        return rewiredArgs;\n    }\n    function rewireNamedType(type) {\n        if ((0, graphql_1.isObjectType)(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireFields(config.fields),\n                interfaces: () => rewireNamedTypes(config.interfaces),\n            };\n            return new graphql_1.GraphQLObjectType(newConfig);\n        }\n        else if ((0, graphql_1.isInterfaceType)(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireFields(config.fields),\n            };\n            if ('interfaces' in newConfig) {\n                newConfig.interfaces = () => rewireNamedTypes(config.interfaces);\n            }\n            return new graphql_1.GraphQLInterfaceType(newConfig);\n        }\n        else if ((0, graphql_1.isUnionType)(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                types: () => rewireNamedTypes(config.types),\n            };\n            return new graphql_1.GraphQLUnionType(newConfig);\n        }\n        else if ((0, graphql_1.isInputObjectType)(type)) {\n            const config = type.toConfig();\n            const newConfig = {\n                ...config,\n                fields: () => rewireInputFields(config.fields),\n            };\n            return new graphql_1.GraphQLInputObjectType(newConfig);\n        }\n        else if ((0, graphql_1.isEnumType)(type)) {\n            const enumConfig = type.toConfig();\n            return new graphql_1.GraphQLEnumType(enumConfig);\n        }\n        else if ((0, graphql_1.isScalarType)(type)) {\n            if ((0, graphql_1.isSpecifiedScalarType)(type)) {\n                return type;\n            }\n            const scalarConfig = type.toConfig();\n            return new graphql_1.GraphQLScalarType(scalarConfig);\n        }\n        throw new Error(`Unexpected schema type: ${type}`);\n    }\n    function rewireFields(fields) {\n        const rewiredFields = {};\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            const rewiredFieldType = rewireType(field.type);\n            if (rewiredFieldType != null && field.args) {\n                field.type = rewiredFieldType;\n                field.args = rewireArgs(field.args);\n                rewiredFields[fieldName] = field;\n            }\n        }\n        return rewiredFields;\n    }\n    function rewireInputFields(fields) {\n        const rewiredFields = {};\n        for (const fieldName in fields) {\n            const field = fields[fieldName];\n            const rewiredFieldType = rewireType(field.type);\n            if (rewiredFieldType != null) {\n                field.type = rewiredFieldType;\n                rewiredFields[fieldName] = field;\n            }\n        }\n        return rewiredFields;\n    }\n    function rewireNamedTypes(namedTypes) {\n        const rewiredTypes = [];\n        for (const namedType of namedTypes) {\n            const rewiredType = rewireType(namedType);\n            if (rewiredType != null) {\n                rewiredTypes.push(rewiredType);\n            }\n        }\n        return rewiredTypes;\n    }\n    function rewireType(type) {\n        if ((0, graphql_1.isListType)(type)) {\n            const rewiredType = rewireType(type.ofType);\n            return rewiredType != null ? new graphql_1.GraphQLList(rewiredType) : null;\n        }\n        else if ((0, graphql_1.isNonNullType)(type)) {\n            const rewiredType = rewireType(type.ofType);\n            return rewiredType != null ? new graphql_1.GraphQLNonNull(rewiredType) : null;\n        }\n        else if ((0, graphql_1.isNamedType)(type)) {\n            let rewiredType = referenceTypeMap[type.name];\n            if (rewiredType === undefined) {\n                rewiredType = (0, stub_js_1.isNamedStub)(type) ? (0, stub_js_1.getBuiltInForStub)(type) : rewireNamedType(type);\n                newTypeMap[rewiredType.name] = referenceTypeMap[type.name] = rewiredType;\n            }\n            return rewiredType != null ? newTypeMap[rewiredType.name] : null;\n        }\n        return null;\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG;AACtB,MAAM;AACN,MAAM;AACN,SAAS,YAAY,eAAe,EAAE,UAAU;IAC5C,MAAM,mBAAmB,OAAO,MAAM,CAAC;IACvC,IAAK,MAAM,YAAY,gBAAiB;QACpC,gBAAgB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS;IAC1D;IACA,MAAM,aAAa,OAAO,MAAM,CAAC;IACjC,IAAK,MAAM,YAAY,iBAAkB;QACrC,MAAM,YAAY,gBAAgB,CAAC,SAAS;QAC5C,IAAI,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;YAChD;QACJ;QACA,MAAM,UAAU,UAAU,IAAI;QAC9B,IAAI,QAAQ,UAAU,CAAC,OAAO;YAC1B;QACJ;QACA,IAAI,UAAU,CAAC,QAAQ,IAAI,MAAM;YAC7B,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,QAAQ,oDAAoD,CAAC;YACxG;QACJ;QACA,UAAU,CAAC,QAAQ,GAAG;IAC1B;IACA,IAAK,MAAM,YAAY,WAAY;QAC/B,UAAU,CAAC,SAAS,GAAG,gBAAgB,UAAU,CAAC,SAAS;IAC/D;IACA,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,YAAa,gBAAgB;IAClE,OAAO;QACH,SAAS;QACT,YAAY;IAChB;;;IACA,SAAS,gBAAgB,SAAS;QAC9B,IAAI,CAAC,GAAG,UAAU,oBAAoB,EAAE,YAAY;YAChD,OAAO;QACX;QACA,MAAM,kBAAkB,UAAU,QAAQ;QAC1C,gBAAgB,IAAI,GAAG,WAAW,gBAAgB,IAAI;QACtD,OAAO,IAAI,UAAU,gBAAgB,CAAC;IAC1C;IACA,SAAS,WAAW,IAAI;QACpB,MAAM,cAAc,CAAC;QACrB,IAAK,MAAM,WAAW,KAAM;YACxB,MAAM,MAAM,IAAI,CAAC,QAAQ;YACzB,MAAM,iBAAiB,WAAW,IAAI,IAAI;YAC1C,IAAI,kBAAkB,MAAM;gBACxB,IAAI,IAAI,GAAG;gBACX,WAAW,CAAC,QAAQ,GAAG;YAC3B;QACJ;QACA,OAAO;IACX;IACA,SAAS,gBAAgB,IAAI;QACzB,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;YACnC,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,QAAQ,IAAM,aAAa,OAAO,MAAM;gBACxC,YAAY,IAAM,iBAAiB,OAAO,UAAU;YACxD;YACA,OAAO,IAAI,UAAU,iBAAiB,CAAC;QAC3C,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;YAC3C,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,QAAQ,IAAM,aAAa,OAAO,MAAM;YAC5C;YACA,IAAI,gBAAgB,WAAW;gBAC3B,UAAU,UAAU,GAAG,IAAM,iBAAiB,OAAO,UAAU;YACnE;YACA,OAAO,IAAI,UAAU,oBAAoB,CAAC;QAC9C,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;YACvC,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,OAAO,IAAM,iBAAiB,OAAO,KAAK;YAC9C;YACA,OAAO,IAAI,UAAU,gBAAgB,CAAC;QAC1C,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;YAC7C,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,YAAY;gBACd,GAAG,MAAM;gBACT,QAAQ,IAAM,kBAAkB,OAAO,MAAM;YACjD;YACA,OAAO,IAAI,UAAU,sBAAsB,CAAC;QAChD,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACtC,MAAM,aAAa,KAAK,QAAQ;YAChC,OAAO,IAAI,UAAU,eAAe,CAAC;QACzC,OACK,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;YACxC,IAAI,CAAC,GAAG,UAAU,qBAAqB,EAAE,OAAO;gBAC5C,OAAO;YACX;YACA,MAAM,eAAe,KAAK,QAAQ;YAClC,OAAO,IAAI,UAAU,iBAAiB,CAAC;QAC3C;QACA,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM;IACrD;IACA,SAAS,aAAa,MAAM;QACxB,MAAM,gBAAgB,CAAC;QACvB,IAAK,MAAM,aAAa,OAAQ;YAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;YAC/B,MAAM,mBAAmB,WAAW,MAAM,IAAI;YAC9C,IAAI,oBAAoB,QAAQ,MAAM,IAAI,EAAE;gBACxC,MAAM,IAAI,GAAG;gBACb,MAAM,IAAI,GAAG,WAAW,MAAM,IAAI;gBAClC,aAAa,CAAC,UAAU,GAAG;YAC/B;QACJ;QACA,OAAO;IACX;IACA,SAAS,kBAAkB,MAAM;QAC7B,MAAM,gBAAgB,CAAC;QACvB,IAAK,MAAM,aAAa,OAAQ;YAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;YAC/B,MAAM,mBAAmB,WAAW,MAAM,IAAI;YAC9C,IAAI,oBAAoB,MAAM;gBAC1B,MAAM,IAAI,GAAG;gBACb,aAAa,CAAC,UAAU,GAAG;YAC/B;QACJ;QACA,OAAO;IACX;IACA,SAAS,iBAAiB,UAAU;QAChC,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,aAAa,WAAY;YAChC,MAAM,cAAc,WAAW;YAC/B,IAAI,eAAe,MAAM;gBACrB,aAAa,IAAI,CAAC;YACtB;QACJ;QACA,OAAO;IACX;IACA,SAAS,WAAW,IAAI;QACpB,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACjC,MAAM,cAAc,WAAW,KAAK,MAAM;YAC1C,OAAO,eAAe,OAAO,IAAI,UAAU,WAAW,CAAC,eAAe;QAC1E,OACK,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,OAAO;YACzC,MAAM,cAAc,WAAW,KAAK,MAAM;YAC1C,OAAO,eAAe,OAAO,IAAI,UAAU,cAAc,CAAC,eAAe;QAC7E,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;YACvC,IAAI,cAAc,gBAAgB,CAAC,KAAK,IAAI,CAAC;YAC7C,IAAI,gBAAgB,WAAW;gBAC3B,cAAc,CAAC,GAAG,UAAU,WAAW,EAAE,QAAQ,CAAC,GAAG,UAAU,iBAAiB,EAAE,QAAQ,gBAAgB;gBAC1G,UAAU,CAAC,YAAY,IAAI,CAAC,GAAG,gBAAgB,CAAC,KAAK,IAAI,CAAC,GAAG;YACjE;YACA,OAAO,eAAe,OAAO,UAAU,CAAC,YAAY,IAAI,CAAC,GAAG;QAChE;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5818, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/transformInputValue.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.transformInputValue = transformInputValue;\nexports.serializeInputValue = serializeInputValue;\nexports.parseInputValue = parseInputValue;\nexports.parseInputValueLiteral = parseInputValueLiteral;\nconst graphql_1 = require(\"graphql\");\nconst helpers_js_1 = require(\"./helpers.js\");\nfunction transformInputValue(type, value, inputLeafValueTransformer = null, inputObjectValueTransformer = null) {\n    if (value == null) {\n        return value;\n    }\n    const nullableType = (0, graphql_1.getNullableType)(type);\n    if ((0, graphql_1.isLeafType)(nullableType)) {\n        return inputLeafValueTransformer != null\n            ? inputLeafValueTransformer(nullableType, value)\n            : value;\n    }\n    else if ((0, graphql_1.isListType)(nullableType)) {\n        return (0, helpers_js_1.asArray)(value).map((listMember) => transformInputValue(nullableType.ofType, listMember, inputLeafValueTransformer, inputObjectValueTransformer));\n    }\n    else if ((0, graphql_1.isInputObjectType)(nullableType)) {\n        const fields = nullableType.getFields();\n        const newValue = {};\n        for (const key in value) {\n            const field = fields[key];\n            if (field != null) {\n                newValue[key] = transformInputValue(field.type, value[key], inputLeafValueTransformer, inputObjectValueTransformer);\n            }\n        }\n        return inputObjectValueTransformer != null\n            ? inputObjectValueTransformer(nullableType, newValue)\n            : newValue;\n    }\n    // unreachable, no other possible return value\n}\nfunction serializeInputValue(type, value) {\n    return transformInputValue(type, value, (t, v) => {\n        try {\n            return t.serialize(v);\n        }\n        catch {\n            return v;\n        }\n    });\n}\nfunction parseInputValue(type, value) {\n    return transformInputValue(type, value, (t, v) => {\n        try {\n            return t.parseValue(v);\n        }\n        catch {\n            return v;\n        }\n    });\n}\nfunction parseInputValueLiteral(type, value) {\n    return transformInputValue(type, value, (t, v) => t.parseLiteral(v, {}));\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,eAAe,GAAG;AAC1B,QAAQ,sBAAsB,GAAG;AACjC,MAAM;AACN,MAAM;AACN,SAAS,oBAAoB,IAAI,EAAE,KAAK,EAAE,4BAA4B,IAAI,EAAE,8BAA8B,IAAI;IAC1G,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,MAAM,eAAe,CAAC,GAAG,UAAU,eAAe,EAAE;IACpD,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,eAAe;QACzC,OAAO,6BAA6B,OAC9B,0BAA0B,cAAc,SACxC;IACV,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,eAAe;QAC9C,OAAO,CAAC,GAAG,aAAa,OAAO,EAAE,OAAO,GAAG,CAAC,CAAC,aAAe,oBAAoB,aAAa,MAAM,EAAE,YAAY,2BAA2B;IAChJ,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,eAAe;QACrD,MAAM,SAAS,aAAa,SAAS;QACrC,MAAM,WAAW,CAAC;QAClB,IAAK,MAAM,OAAO,MAAO;YACrB,MAAM,QAAQ,MAAM,CAAC,IAAI;YACzB,IAAI,SAAS,MAAM;gBACf,QAAQ,CAAC,IAAI,GAAG,oBAAoB,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,2BAA2B;YAC3F;QACJ;QACA,OAAO,+BAA+B,OAChC,4BAA4B,cAAc,YAC1C;IACV;AACA,8CAA8C;AAClD;AACA,SAAS,oBAAoB,IAAI,EAAE,KAAK;IACpC,OAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG;QACxC,IAAI;YACA,OAAO,EAAE,SAAS,CAAC;QACvB,EACA,OAAM;YACF,OAAO;QACX;IACJ;AACJ;AACA,SAAS,gBAAgB,IAAI,EAAE,KAAK;IAChC,OAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG;QACxC,IAAI;YACA,OAAO,EAAE,UAAU,CAAC;QACxB,EACA,OAAM;YACF,OAAO;QACX;IACJ;AACJ;AACA,SAAS,uBAAuB,IAAI,EAAE,KAAK;IACvC,OAAO,oBAAoB,MAAM,OAAO,CAAC,GAAG,IAAM,EAAE,YAAY,CAAC,GAAG,CAAC;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5874, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/mapSchema.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mapSchema = mapSchema;\nexports.correctASTNodes = correctASTNodes;\nconst graphql_1 = require(\"graphql\");\nconst getObjectTypeFromTypeMap_js_1 = require(\"./getObjectTypeFromTypeMap.js\");\nconst Interfaces_js_1 = require(\"./Interfaces.js\");\nconst rewire_js_1 = require(\"./rewire.js\");\nconst transformInputValue_js_1 = require(\"./transformInputValue.js\");\nfunction mapSchema(schema, schemaMapper = {}) {\n    const newTypeMap = mapArguments(mapFields(mapTypes(mapDefaultValues(mapEnumValues(mapTypes(mapDefaultValues(schema.getTypeMap(), schema, transformInputValue_js_1.serializeInputValue), schema, schemaMapper, type => (0, graphql_1.isLeafType)(type)), schema, schemaMapper), schema, transformInputValue_js_1.parseInputValue), schema, schemaMapper, type => !(0, graphql_1.isLeafType)(type)), schema, schemaMapper), schema, schemaMapper);\n    const originalDirectives = schema.getDirectives();\n    const newDirectives = mapDirectives(originalDirectives, schema, schemaMapper);\n    const { typeMap, directives } = (0, rewire_js_1.rewireTypes)(newTypeMap, newDirectives);\n    return new graphql_1.GraphQLSchema({\n        ...schema.toConfig(),\n        query: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(newTypeMap, schema.getQueryType())),\n        mutation: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(newTypeMap, schema.getMutationType())),\n        subscription: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(newTypeMap, schema.getSubscriptionType())),\n        types: Object.values(typeMap),\n        directives,\n    });\n}\nfunction mapTypes(originalTypeMap, schema, schemaMapper, testFn = () => true) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (originalType == null || !testFn(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const typeMapper = getTypeMapper(schema, schemaMapper, typeName);\n            if (typeMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const maybeNewType = typeMapper(originalType, schema);\n            if (maybeNewType === undefined) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            newTypeMap[typeName] = maybeNewType;\n        }\n    }\n    return newTypeMap;\n}\nfunction mapEnumValues(originalTypeMap, schema, schemaMapper) {\n    const enumValueMapper = getEnumValueMapper(schemaMapper);\n    if (!enumValueMapper) {\n        return originalTypeMap;\n    }\n    return mapTypes(originalTypeMap, schema, {\n        [Interfaces_js_1.MapperKind.ENUM_TYPE]: type => {\n            const config = type.toConfig();\n            const originalEnumValueConfigMap = config.values;\n            const newEnumValueConfigMap = {};\n            for (const externalValue in originalEnumValueConfigMap) {\n                const originalEnumValueConfig = originalEnumValueConfigMap[externalValue];\n                const mappedEnumValue = enumValueMapper(originalEnumValueConfig, type.name, schema, externalValue);\n                if (mappedEnumValue === undefined) {\n                    newEnumValueConfigMap[externalValue] = originalEnumValueConfig;\n                }\n                else if (Array.isArray(mappedEnumValue)) {\n                    const [newExternalValue, newEnumValueConfig] = mappedEnumValue;\n                    newEnumValueConfigMap[newExternalValue] =\n                        newEnumValueConfig === undefined ? originalEnumValueConfig : newEnumValueConfig;\n                }\n                else if (mappedEnumValue !== null) {\n                    newEnumValueConfigMap[externalValue] = mappedEnumValue;\n                }\n            }\n            return correctASTNodes(new graphql_1.GraphQLEnumType({\n                ...config,\n                values: newEnumValueConfigMap,\n            }));\n        },\n    }, type => (0, graphql_1.isEnumType)(type));\n}\nfunction mapDefaultValues(originalTypeMap, schema, fn) {\n    const newTypeMap = mapArguments(originalTypeMap, schema, {\n        [Interfaces_js_1.MapperKind.ARGUMENT]: argumentConfig => {\n            if (argumentConfig.defaultValue === undefined) {\n                return argumentConfig;\n            }\n            const maybeNewType = getNewType(originalTypeMap, argumentConfig.type);\n            if (maybeNewType != null) {\n                return {\n                    ...argumentConfig,\n                    defaultValue: fn(maybeNewType, argumentConfig.defaultValue),\n                };\n            }\n        },\n    });\n    return mapFields(newTypeMap, schema, {\n        [Interfaces_js_1.MapperKind.INPUT_OBJECT_FIELD]: inputFieldConfig => {\n            if (inputFieldConfig.defaultValue === undefined) {\n                return inputFieldConfig;\n            }\n            const maybeNewType = getNewType(newTypeMap, inputFieldConfig.type);\n            if (maybeNewType != null) {\n                return {\n                    ...inputFieldConfig,\n                    defaultValue: fn(maybeNewType, inputFieldConfig.defaultValue),\n                };\n            }\n        },\n    });\n}\nfunction getNewType(newTypeMap, type) {\n    if ((0, graphql_1.isListType)(type)) {\n        const newType = getNewType(newTypeMap, type.ofType);\n        return newType != null ? new graphql_1.GraphQLList(newType) : null;\n    }\n    else if ((0, graphql_1.isNonNullType)(type)) {\n        const newType = getNewType(newTypeMap, type.ofType);\n        return newType != null ? new graphql_1.GraphQLNonNull(newType) : null;\n    }\n    else if ((0, graphql_1.isNamedType)(type)) {\n        const newType = newTypeMap[type.name];\n        return newType != null ? newType : null;\n    }\n    return null;\n}\nfunction mapFields(originalTypeMap, schema, schemaMapper) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (!(0, graphql_1.isObjectType)(originalType) &&\n                !(0, graphql_1.isInterfaceType)(originalType) &&\n                !(0, graphql_1.isInputObjectType)(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const fieldMapper = getFieldMapper(schema, schemaMapper, typeName);\n            if (fieldMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const config = originalType.toConfig();\n            const originalFieldConfigMap = config.fields;\n            const newFieldConfigMap = {};\n            for (const fieldName in originalFieldConfigMap) {\n                const originalFieldConfig = originalFieldConfigMap[fieldName];\n                const mappedField = fieldMapper(originalFieldConfig, fieldName, typeName, schema);\n                if (mappedField === undefined) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                }\n                else if (Array.isArray(mappedField)) {\n                    const [newFieldName, newFieldConfig] = mappedField;\n                    if (newFieldConfig.astNode != null) {\n                        newFieldConfig.astNode = {\n                            ...newFieldConfig.astNode,\n                            name: {\n                                ...newFieldConfig.astNode.name,\n                                value: newFieldName,\n                            },\n                        };\n                    }\n                    newFieldConfigMap[newFieldName] =\n                        newFieldConfig === undefined ? originalFieldConfig : newFieldConfig;\n                }\n                else if (mappedField !== null) {\n                    newFieldConfigMap[fieldName] = mappedField;\n                }\n            }\n            if ((0, graphql_1.isObjectType)(originalType)) {\n                newTypeMap[typeName] = correctASTNodes(new graphql_1.GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n            else if ((0, graphql_1.isInterfaceType)(originalType)) {\n                newTypeMap[typeName] = correctASTNodes(new graphql_1.GraphQLInterfaceType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n            else {\n                newTypeMap[typeName] = correctASTNodes(new graphql_1.GraphQLInputObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        }\n    }\n    return newTypeMap;\n}\nfunction mapArguments(originalTypeMap, schema, schemaMapper) {\n    const newTypeMap = {};\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__')) {\n            const originalType = originalTypeMap[typeName];\n            if (!(0, graphql_1.isObjectType)(originalType) && !(0, graphql_1.isInterfaceType)(originalType)) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const argumentMapper = getArgumentMapper(schemaMapper);\n            if (argumentMapper == null) {\n                newTypeMap[typeName] = originalType;\n                continue;\n            }\n            const config = originalType.toConfig();\n            const originalFieldConfigMap = config.fields;\n            const newFieldConfigMap = {};\n            for (const fieldName in originalFieldConfigMap) {\n                const originalFieldConfig = originalFieldConfigMap[fieldName];\n                const originalArgumentConfigMap = originalFieldConfig.args;\n                if (originalArgumentConfigMap == null) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                    continue;\n                }\n                const argumentNames = Object.keys(originalArgumentConfigMap);\n                if (!argumentNames.length) {\n                    newFieldConfigMap[fieldName] = originalFieldConfig;\n                    continue;\n                }\n                const newArgumentConfigMap = {};\n                for (const argumentName of argumentNames) {\n                    const originalArgumentConfig = originalArgumentConfigMap[argumentName];\n                    const mappedArgument = argumentMapper(originalArgumentConfig, fieldName, typeName, schema);\n                    if (mappedArgument === undefined) {\n                        newArgumentConfigMap[argumentName] = originalArgumentConfig;\n                    }\n                    else if (Array.isArray(mappedArgument)) {\n                        const [newArgumentName, newArgumentConfig] = mappedArgument;\n                        newArgumentConfigMap[newArgumentName] = newArgumentConfig;\n                    }\n                    else if (mappedArgument !== null) {\n                        newArgumentConfigMap[argumentName] = mappedArgument;\n                    }\n                }\n                newFieldConfigMap[fieldName] = {\n                    ...originalFieldConfig,\n                    args: newArgumentConfigMap,\n                };\n            }\n            if ((0, graphql_1.isObjectType)(originalType)) {\n                newTypeMap[typeName] = new graphql_1.GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n            else if ((0, graphql_1.isInterfaceType)(originalType)) {\n                newTypeMap[typeName] = new graphql_1.GraphQLInterfaceType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n            else {\n                newTypeMap[typeName] = new graphql_1.GraphQLInputObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                });\n            }\n        }\n    }\n    return newTypeMap;\n}\nfunction mapDirectives(originalDirectives, schema, schemaMapper) {\n    const directiveMapper = getDirectiveMapper(schemaMapper);\n    if (directiveMapper == null) {\n        return originalDirectives.slice();\n    }\n    const newDirectives = [];\n    for (const directive of originalDirectives) {\n        const mappedDirective = directiveMapper(directive, schema);\n        if (mappedDirective === undefined) {\n            newDirectives.push(directive);\n        }\n        else if (mappedDirective !== null) {\n            newDirectives.push(mappedDirective);\n        }\n    }\n    return newDirectives;\n}\nfunction getTypeSpecifiers(schema, typeName) {\n    const type = schema.getType(typeName);\n    const specifiers = [Interfaces_js_1.MapperKind.TYPE];\n    if ((0, graphql_1.isObjectType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_TYPE, Interfaces_js_1.MapperKind.OBJECT_TYPE);\n        if (typeName === schema.getQueryType()?.name) {\n            specifiers.push(Interfaces_js_1.MapperKind.ROOT_OBJECT, Interfaces_js_1.MapperKind.QUERY);\n        }\n        else if (typeName === schema.getMutationType()?.name) {\n            specifiers.push(Interfaces_js_1.MapperKind.ROOT_OBJECT, Interfaces_js_1.MapperKind.MUTATION);\n        }\n        else if (typeName === schema.getSubscriptionType()?.name) {\n            specifiers.push(Interfaces_js_1.MapperKind.ROOT_OBJECT, Interfaces_js_1.MapperKind.SUBSCRIPTION);\n        }\n    }\n    else if ((0, graphql_1.isInputObjectType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.INPUT_OBJECT_TYPE);\n    }\n    else if ((0, graphql_1.isInterfaceType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_TYPE, Interfaces_js_1.MapperKind.ABSTRACT_TYPE, Interfaces_js_1.MapperKind.INTERFACE_TYPE);\n    }\n    else if ((0, graphql_1.isUnionType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_TYPE, Interfaces_js_1.MapperKind.ABSTRACT_TYPE, Interfaces_js_1.MapperKind.UNION_TYPE);\n    }\n    else if ((0, graphql_1.isEnumType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.ENUM_TYPE);\n    }\n    else if ((0, graphql_1.isScalarType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.SCALAR_TYPE);\n    }\n    return specifiers;\n}\nfunction getTypeMapper(schema, schemaMapper, typeName) {\n    const specifiers = getTypeSpecifiers(schema, typeName);\n    let typeMapper;\n    const stack = [...specifiers];\n    while (!typeMapper && stack.length > 0) {\n        // It is safe to use the ! operator here as we check the length.\n        const next = stack.pop();\n        typeMapper = schemaMapper[next];\n    }\n    return typeMapper != null ? typeMapper : null;\n}\nfunction getFieldSpecifiers(schema, typeName) {\n    const type = schema.getType(typeName);\n    const specifiers = [Interfaces_js_1.MapperKind.FIELD];\n    if ((0, graphql_1.isObjectType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_FIELD, Interfaces_js_1.MapperKind.OBJECT_FIELD);\n        if (typeName === schema.getQueryType()?.name) {\n            specifiers.push(Interfaces_js_1.MapperKind.ROOT_FIELD, Interfaces_js_1.MapperKind.QUERY_ROOT_FIELD);\n        }\n        else if (typeName === schema.getMutationType()?.name) {\n            specifiers.push(Interfaces_js_1.MapperKind.ROOT_FIELD, Interfaces_js_1.MapperKind.MUTATION_ROOT_FIELD);\n        }\n        else if (typeName === schema.getSubscriptionType()?.name) {\n            specifiers.push(Interfaces_js_1.MapperKind.ROOT_FIELD, Interfaces_js_1.MapperKind.SUBSCRIPTION_ROOT_FIELD);\n        }\n    }\n    else if ((0, graphql_1.isInterfaceType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.COMPOSITE_FIELD, Interfaces_js_1.MapperKind.INTERFACE_FIELD);\n    }\n    else if ((0, graphql_1.isInputObjectType)(type)) {\n        specifiers.push(Interfaces_js_1.MapperKind.INPUT_OBJECT_FIELD);\n    }\n    return specifiers;\n}\nfunction getFieldMapper(schema, schemaMapper, typeName) {\n    const specifiers = getFieldSpecifiers(schema, typeName);\n    let fieldMapper;\n    const stack = [...specifiers];\n    while (!fieldMapper && stack.length > 0) {\n        // It is safe to use the ! operator here as we check the length.\n        const next = stack.pop();\n        // TODO: fix this as unknown cast\n        fieldMapper = schemaMapper[next];\n    }\n    return fieldMapper ?? null;\n}\nfunction getArgumentMapper(schemaMapper) {\n    const argumentMapper = schemaMapper[Interfaces_js_1.MapperKind.ARGUMENT];\n    return argumentMapper != null ? argumentMapper : null;\n}\nfunction getDirectiveMapper(schemaMapper) {\n    const directiveMapper = schemaMapper[Interfaces_js_1.MapperKind.DIRECTIVE];\n    return directiveMapper != null ? directiveMapper : null;\n}\nfunction getEnumValueMapper(schemaMapper) {\n    const enumValueMapper = schemaMapper[Interfaces_js_1.MapperKind.ENUM_VALUE];\n    return enumValueMapper != null ? enumValueMapper : null;\n}\nfunction correctASTNodes(type) {\n    if ((0, graphql_1.isObjectType)(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: graphql_1.Kind.OBJECT_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: graphql_1.Kind.OBJECT_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new graphql_1.GraphQLObjectType(config);\n    }\n    else if ((0, graphql_1.isInterfaceType)(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: graphql_1.Kind.INTERFACE_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: graphql_1.Kind.INTERFACE_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new graphql_1.GraphQLInterfaceType(config);\n    }\n    else if ((0, graphql_1.isInputObjectType)(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const fields = [];\n            for (const fieldName in config.fields) {\n                const fieldConfig = config.fields[fieldName];\n                if (fieldConfig.astNode != null) {\n                    fields.push(fieldConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                kind: graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n                fields,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                kind: graphql_1.Kind.INPUT_OBJECT_TYPE_EXTENSION,\n                fields: undefined,\n            }));\n        }\n        return new graphql_1.GraphQLInputObjectType(config);\n    }\n    else if ((0, graphql_1.isEnumType)(type)) {\n        const config = type.toConfig();\n        if (config.astNode != null) {\n            const values = [];\n            for (const enumKey in config.values) {\n                const enumValueConfig = config.values[enumKey];\n                if (enumValueConfig.astNode != null) {\n                    values.push(enumValueConfig.astNode);\n                }\n            }\n            config.astNode = {\n                ...config.astNode,\n                values,\n            };\n        }\n        if (config.extensionASTNodes != null) {\n            config.extensionASTNodes = config.extensionASTNodes.map(node => ({\n                ...node,\n                values: undefined,\n            }));\n        }\n        return new graphql_1.GraphQLEnumType(config);\n    }\n    else {\n        return type;\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG;AACpB,QAAQ,eAAe,GAAG;AAC1B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,MAAM,EAAE,eAAe,CAAC,CAAC;IACxC,MAAM,aAAa,aAAa,UAAU,SAAS,iBAAiB,cAAc,SAAS,iBAAiB,OAAO,UAAU,IAAI,QAAQ,yBAAyB,mBAAmB,GAAG,QAAQ,cAAc,CAAA,OAAQ,CAAC,GAAG,UAAU,UAAU,EAAE,QAAQ,QAAQ,eAAe,QAAQ,yBAAyB,eAAe,GAAG,QAAQ,cAAc,CAAA,OAAQ,CAAC,CAAC,GAAG,UAAU,UAAU,EAAE,QAAQ,QAAQ,eAAe,QAAQ;IACla,MAAM,qBAAqB,OAAO,aAAa;IAC/C,MAAM,gBAAgB,cAAc,oBAAoB,QAAQ;IAChE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,GAAG,YAAY,WAAW,EAAE,YAAY;IACzE,OAAO,IAAI,UAAU,aAAa,CAAC;QAC/B,GAAG,OAAO,QAAQ,EAAE;QACpB,OAAO,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,SAAS,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,YAAY,OAAO,YAAY;QACvK,UAAU,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,SAAS,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,YAAY,OAAO,eAAe;QAC7K,cAAc,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,SAAS,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,YAAY,OAAO,mBAAmB;QACrL,OAAO,OAAO,MAAM,CAAC;QACrB;IACJ;AACJ;AACA,SAAS,SAAS,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,IAAM,IAAI;IACxE,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,eAAe,eAAe,CAAC,SAAS;YAC9C,IAAI,gBAAgB,QAAQ,CAAC,OAAO,eAAe;gBAC/C,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,aAAa,cAAc,QAAQ,cAAc;YACvD,IAAI,cAAc,MAAM;gBACpB,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,eAAe,WAAW,cAAc;YAC9C,IAAI,iBAAiB,WAAW;gBAC5B,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,UAAU,CAAC,SAAS,GAAG;QAC3B;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,eAAe,EAAE,MAAM,EAAE,YAAY;IACxD,MAAM,kBAAkB,mBAAmB;IAC3C,IAAI,CAAC,iBAAiB;QAClB,OAAO;IACX;IACA,OAAO,SAAS,iBAAiB,QAAQ;QACrC,CAAC,gBAAgB,UAAU,CAAC,SAAS,CAAC,EAAE,CAAA;YACpC,MAAM,SAAS,KAAK,QAAQ;YAC5B,MAAM,6BAA6B,OAAO,MAAM;YAChD,MAAM,wBAAwB,CAAC;YAC/B,IAAK,MAAM,iBAAiB,2BAA4B;gBACpD,MAAM,0BAA0B,0BAA0B,CAAC,cAAc;gBACzE,MAAM,kBAAkB,gBAAgB,yBAAyB,KAAK,IAAI,EAAE,QAAQ;gBACpF,IAAI,oBAAoB,WAAW;oBAC/B,qBAAqB,CAAC,cAAc,GAAG;gBAC3C,OACK,IAAI,MAAM,OAAO,CAAC,kBAAkB;oBACrC,MAAM,CAAC,kBAAkB,mBAAmB,GAAG;oBAC/C,qBAAqB,CAAC,iBAAiB,GACnC,uBAAuB,YAAY,0BAA0B;gBACrE,OACK,IAAI,oBAAoB,MAAM;oBAC/B,qBAAqB,CAAC,cAAc,GAAG;gBAC3C;YACJ;YACA,OAAO,gBAAgB,IAAI,UAAU,eAAe,CAAC;gBACjD,GAAG,MAAM;gBACT,QAAQ;YACZ;QACJ;IACJ,GAAG,CAAA,OAAQ,CAAC,GAAG,UAAU,UAAU,EAAE;AACzC;AACA,SAAS,iBAAiB,eAAe,EAAE,MAAM,EAAE,EAAE;IACjD,MAAM,aAAa,aAAa,iBAAiB,QAAQ;QACrD,CAAC,gBAAgB,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAA;YACnC,IAAI,eAAe,YAAY,KAAK,WAAW;gBAC3C,OAAO;YACX;YACA,MAAM,eAAe,WAAW,iBAAiB,eAAe,IAAI;YACpE,IAAI,gBAAgB,MAAM;gBACtB,OAAO;oBACH,GAAG,cAAc;oBACjB,cAAc,GAAG,cAAc,eAAe,YAAY;gBAC9D;YACJ;QACJ;IACJ;IACA,OAAO,UAAU,YAAY,QAAQ;QACjC,CAAC,gBAAgB,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAA;YAC7C,IAAI,iBAAiB,YAAY,KAAK,WAAW;gBAC7C,OAAO;YACX;YACA,MAAM,eAAe,WAAW,YAAY,iBAAiB,IAAI;YACjE,IAAI,gBAAgB,MAAM;gBACtB,OAAO;oBACH,GAAG,gBAAgB;oBACnB,cAAc,GAAG,cAAc,iBAAiB,YAAY;gBAChE;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,UAAU,EAAE,IAAI;IAChC,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACjC,MAAM,UAAU,WAAW,YAAY,KAAK,MAAM;QAClD,OAAO,WAAW,OAAO,IAAI,UAAU,WAAW,CAAC,WAAW;IAClE,OACK,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,OAAO;QACzC,MAAM,UAAU,WAAW,YAAY,KAAK,MAAM;QAClD,OAAO,WAAW,OAAO,IAAI,UAAU,cAAc,CAAC,WAAW;IACrE,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;QACvC,MAAM,UAAU,UAAU,CAAC,KAAK,IAAI,CAAC;QACrC,OAAO,WAAW,OAAO,UAAU;IACvC;IACA,OAAO;AACX;AACA,SAAS,UAAU,eAAe,EAAE,MAAM,EAAE,YAAY;IACpD,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,eAAe,eAAe,CAAC,SAAS;YAC9C,IAAI,CAAC,CAAC,GAAG,UAAU,YAAY,EAAE,iBAC7B,CAAC,CAAC,GAAG,UAAU,eAAe,EAAE,iBAChC,CAAC,CAAC,GAAG,UAAU,iBAAiB,EAAE,eAAe;gBACjD,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,cAAc,eAAe,QAAQ,cAAc;YACzD,IAAI,eAAe,MAAM;gBACrB,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,SAAS,aAAa,QAAQ;YACpC,MAAM,yBAAyB,OAAO,MAAM;YAC5C,MAAM,oBAAoB,CAAC;YAC3B,IAAK,MAAM,aAAa,uBAAwB;gBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;gBAC7D,MAAM,cAAc,YAAY,qBAAqB,WAAW,UAAU;gBAC1E,IAAI,gBAAgB,WAAW;oBAC3B,iBAAiB,CAAC,UAAU,GAAG;gBACnC,OACK,IAAI,MAAM,OAAO,CAAC,cAAc;oBACjC,MAAM,CAAC,cAAc,eAAe,GAAG;oBACvC,IAAI,eAAe,OAAO,IAAI,MAAM;wBAChC,eAAe,OAAO,GAAG;4BACrB,GAAG,eAAe,OAAO;4BACzB,MAAM;gCACF,GAAG,eAAe,OAAO,CAAC,IAAI;gCAC9B,OAAO;4BACX;wBACJ;oBACJ;oBACA,iBAAiB,CAAC,aAAa,GAC3B,mBAAmB,YAAY,sBAAsB;gBAC7D,OACK,IAAI,gBAAgB,MAAM;oBAC3B,iBAAiB,CAAC,UAAU,GAAG;gBACnC;YACJ;YACA,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,eAAe;gBAC3C,UAAU,CAAC,SAAS,GAAG,gBAAgB,IAAI,UAAU,iBAAiB,CAAC;oBACnE,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,eAAe;gBACnD,UAAU,CAAC,SAAS,GAAG,gBAAgB,IAAI,UAAU,oBAAoB,CAAC;oBACtE,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK;gBACD,UAAU,CAAC,SAAS,GAAG,gBAAgB,IAAI,UAAU,sBAAsB,CAAC;oBACxE,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,eAAe,EAAE,MAAM,EAAE,YAAY;IACvD,MAAM,aAAa,CAAC;IACpB,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,eAAe,eAAe,CAAC,SAAS;YAC9C,IAAI,CAAC,CAAC,GAAG,UAAU,YAAY,EAAE,iBAAiB,CAAC,CAAC,GAAG,UAAU,eAAe,EAAE,eAAe;gBAC7F,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,iBAAiB,kBAAkB;YACzC,IAAI,kBAAkB,MAAM;gBACxB,UAAU,CAAC,SAAS,GAAG;gBACvB;YACJ;YACA,MAAM,SAAS,aAAa,QAAQ;YACpC,MAAM,yBAAyB,OAAO,MAAM;YAC5C,MAAM,oBAAoB,CAAC;YAC3B,IAAK,MAAM,aAAa,uBAAwB;gBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;gBAC7D,MAAM,4BAA4B,oBAAoB,IAAI;gBAC1D,IAAI,6BAA6B,MAAM;oBACnC,iBAAiB,CAAC,UAAU,GAAG;oBAC/B;gBACJ;gBACA,MAAM,gBAAgB,OAAO,IAAI,CAAC;gBAClC,IAAI,CAAC,cAAc,MAAM,EAAE;oBACvB,iBAAiB,CAAC,UAAU,GAAG;oBAC/B;gBACJ;gBACA,MAAM,uBAAuB,CAAC;gBAC9B,KAAK,MAAM,gBAAgB,cAAe;oBACtC,MAAM,yBAAyB,yBAAyB,CAAC,aAAa;oBACtE,MAAM,iBAAiB,eAAe,wBAAwB,WAAW,UAAU;oBACnF,IAAI,mBAAmB,WAAW;wBAC9B,oBAAoB,CAAC,aAAa,GAAG;oBACzC,OACK,IAAI,MAAM,OAAO,CAAC,iBAAiB;wBACpC,MAAM,CAAC,iBAAiB,kBAAkB,GAAG;wBAC7C,oBAAoB,CAAC,gBAAgB,GAAG;oBAC5C,OACK,IAAI,mBAAmB,MAAM;wBAC9B,oBAAoB,CAAC,aAAa,GAAG;oBACzC;gBACJ;gBACA,iBAAiB,CAAC,UAAU,GAAG;oBAC3B,GAAG,mBAAmB;oBACtB,MAAM;gBACV;YACJ;YACA,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,eAAe;gBAC3C,UAAU,CAAC,SAAS,GAAG,IAAI,UAAU,iBAAiB,CAAC;oBACnD,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,eAAe;gBACnD,UAAU,CAAC,SAAS,GAAG,IAAI,UAAU,oBAAoB,CAAC;oBACtD,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ,OACK;gBACD,UAAU,CAAC,SAAS,GAAG,IAAI,UAAU,sBAAsB,CAAC;oBACxD,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,kBAAkB,EAAE,MAAM,EAAE,YAAY;IAC3D,MAAM,kBAAkB,mBAAmB;IAC3C,IAAI,mBAAmB,MAAM;QACzB,OAAO,mBAAmB,KAAK;IACnC;IACA,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,aAAa,mBAAoB;QACxC,MAAM,kBAAkB,gBAAgB,WAAW;QACnD,IAAI,oBAAoB,WAAW;YAC/B,cAAc,IAAI,CAAC;QACvB,OACK,IAAI,oBAAoB,MAAM;YAC/B,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,MAAM,EAAE,QAAQ;IACvC,MAAM,OAAO,OAAO,OAAO,CAAC;IAC5B,MAAM,aAAa;QAAC,gBAAgB,UAAU,CAAC,IAAI;KAAC;IACpD,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACnC,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,cAAc,EAAE,gBAAgB,UAAU,CAAC,WAAW;QACjG,IAAI,aAAa,OAAO,YAAY,IAAI,MAAM;YAC1C,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,WAAW,EAAE,gBAAgB,UAAU,CAAC,KAAK;QAC5F,OACK,IAAI,aAAa,OAAO,eAAe,IAAI,MAAM;YAClD,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,WAAW,EAAE,gBAAgB,UAAU,CAAC,QAAQ;QAC/F,OACK,IAAI,aAAa,OAAO,mBAAmB,IAAI,MAAM;YACtD,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,WAAW,EAAE,gBAAgB,UAAU,CAAC,YAAY;QACnG;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;QAC7C,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,iBAAiB;IAChE,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;QAC3C,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,cAAc,EAAE,gBAAgB,UAAU,CAAC,aAAa,EAAE,gBAAgB,UAAU,CAAC,cAAc;IAClJ,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;QACvC,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,cAAc,EAAE,gBAAgB,UAAU,CAAC,aAAa,EAAE,gBAAgB,UAAU,CAAC,UAAU;IAC9I,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACtC,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS;IACxD,OACK,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACxC,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,WAAW;IAC1D;IACA,OAAO;AACX;AACA,SAAS,cAAc,MAAM,EAAE,YAAY,EAAE,QAAQ;IACjD,MAAM,aAAa,kBAAkB,QAAQ;IAC7C,IAAI;IACJ,MAAM,QAAQ;WAAI;KAAW;IAC7B,MAAO,CAAC,cAAc,MAAM,MAAM,GAAG,EAAG;QACpC,gEAAgE;QAChE,MAAM,OAAO,MAAM,GAAG;QACtB,aAAa,YAAY,CAAC,KAAK;IACnC;IACA,OAAO,cAAc,OAAO,aAAa;AAC7C;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ;IACxC,MAAM,OAAO,OAAO,OAAO,CAAC;IAC5B,MAAM,aAAa;QAAC,gBAAgB,UAAU,CAAC,KAAK;KAAC;IACrD,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACnC,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,eAAe,EAAE,gBAAgB,UAAU,CAAC,YAAY;QACnG,IAAI,aAAa,OAAO,YAAY,IAAI,MAAM;YAC1C,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,UAAU,EAAE,gBAAgB,UAAU,CAAC,gBAAgB;QACtG,OACK,IAAI,aAAa,OAAO,eAAe,IAAI,MAAM;YAClD,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,UAAU,EAAE,gBAAgB,UAAU,CAAC,mBAAmB;QACzG,OACK,IAAI,aAAa,OAAO,mBAAmB,IAAI,MAAM;YACtD,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,UAAU,EAAE,gBAAgB,UAAU,CAAC,uBAAuB;QAC7G;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;QAC3C,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,eAAe,EAAE,gBAAgB,UAAU,CAAC,eAAe;IAC1G,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;QAC7C,WAAW,IAAI,CAAC,gBAAgB,UAAU,CAAC,kBAAkB;IACjE;IACA,OAAO;AACX;AACA,SAAS,eAAe,MAAM,EAAE,YAAY,EAAE,QAAQ;IAClD,MAAM,aAAa,mBAAmB,QAAQ;IAC9C,IAAI;IACJ,MAAM,QAAQ;WAAI;KAAW;IAC7B,MAAO,CAAC,eAAe,MAAM,MAAM,GAAG,EAAG;QACrC,gEAAgE;QAChE,MAAM,OAAO,MAAM,GAAG;QACtB,iCAAiC;QACjC,cAAc,YAAY,CAAC,KAAK;IACpC;IACA,OAAO,eAAe;AAC1B;AACA,SAAS,kBAAkB,YAAY;IACnC,MAAM,iBAAiB,YAAY,CAAC,gBAAgB,UAAU,CAAC,QAAQ,CAAC;IACxE,OAAO,kBAAkB,OAAO,iBAAiB;AACrD;AACA,SAAS,mBAAmB,YAAY;IACpC,MAAM,kBAAkB,YAAY,CAAC,gBAAgB,UAAU,CAAC,SAAS,CAAC;IAC1E,OAAO,mBAAmB,OAAO,kBAAkB;AACvD;AACA,SAAS,mBAAmB,YAAY;IACpC,MAAM,kBAAkB,YAAY,CAAC,gBAAgB,UAAU,CAAC,UAAU,CAAC;IAC3E,OAAO,mBAAmB,OAAO,kBAAkB;AACvD;AACA,SAAS,gBAAgB,IAAI;IACzB,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACnC,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;gBACnC,MAAM,cAAc,OAAO,MAAM,CAAC,UAAU;gBAC5C,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC7B,OAAO,IAAI,CAAC,YAAY,OAAO;gBACnC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB,MAAM,UAAU,IAAI,CAAC,sBAAsB;gBAC3C;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,MAAM,UAAU,IAAI,CAAC,qBAAqB;oBAC1C,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,UAAU,iBAAiB,CAAC;IAC3C,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;QAC3C,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;gBACnC,MAAM,cAAc,OAAO,MAAM,CAAC,UAAU;gBAC5C,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC7B,OAAO,IAAI,CAAC,YAAY,OAAO;gBACnC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB,MAAM,UAAU,IAAI,CAAC,yBAAyB;gBAC9C;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,MAAM,UAAU,IAAI,CAAC,wBAAwB;oBAC7C,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,UAAU,oBAAoB,CAAC;IAC9C,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;QAC7C,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;gBACnC,MAAM,cAAc,OAAO,MAAM,CAAC,UAAU;gBAC5C,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC7B,OAAO,IAAI,CAAC,YAAY,OAAO;gBACnC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB,MAAM,UAAU,IAAI,CAAC,4BAA4B;gBACjD;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,MAAM,UAAU,IAAI,CAAC,2BAA2B;oBAChD,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,UAAU,sBAAsB,CAAC;IAChD,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACtC,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAI,OAAO,OAAO,IAAI,MAAM;YACxB,MAAM,SAAS,EAAE;YACjB,IAAK,MAAM,WAAW,OAAO,MAAM,CAAE;gBACjC,MAAM,kBAAkB,OAAO,MAAM,CAAC,QAAQ;gBAC9C,IAAI,gBAAgB,OAAO,IAAI,MAAM;oBACjC,OAAO,IAAI,CAAC,gBAAgB,OAAO;gBACvC;YACJ;YACA,OAAO,OAAO,GAAG;gBACb,GAAG,OAAO,OAAO;gBACjB;YACJ;QACJ;QACA,IAAI,OAAO,iBAAiB,IAAI,MAAM;YAClC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7D,GAAG,IAAI;oBACP,QAAQ;gBACZ,CAAC;QACL;QACA,OAAO,IAAI,UAAU,eAAe,CAAC;IACzC,OACK;QACD,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/filterSchema.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.filterSchema = filterSchema;\nconst graphql_1 = require(\"graphql\");\nconst Interfaces_js_1 = require(\"./Interfaces.js\");\nconst mapSchema_js_1 = require(\"./mapSchema.js\");\nfunction filterSchema({ schema, typeFilter = () => true, fieldFilter = undefined, rootFieldFilter = undefined, objectFieldFilter = undefined, interfaceFieldFilter = undefined, inputObjectFieldFilter = undefined, argumentFilter = undefined, directiveFilter = undefined, enumValueFilter = undefined, }) {\n    const filteredSchema = (0, mapSchema_js_1.mapSchema)(schema, {\n        [Interfaces_js_1.MapperKind.QUERY]: (type) => filterRootFields(type, 'Query', rootFieldFilter, argumentFilter),\n        [Interfaces_js_1.MapperKind.MUTATION]: (type) => filterRootFields(type, 'Mutation', rootFieldFilter, argumentFilter),\n        [Interfaces_js_1.MapperKind.SUBSCRIPTION]: (type) => filterRootFields(type, 'Subscription', rootFieldFilter, argumentFilter),\n        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: (type) => typeFilter(type.name, type)\n            ? filterElementFields(graphql_1.GraphQLObjectType, type, objectFieldFilter || fieldFilter, argumentFilter)\n            : null,\n        [Interfaces_js_1.MapperKind.INTERFACE_TYPE]: (type) => typeFilter(type.name, type)\n            ? filterElementFields(graphql_1.GraphQLInterfaceType, type, interfaceFieldFilter || fieldFilter, argumentFilter)\n            : null,\n        [Interfaces_js_1.MapperKind.INPUT_OBJECT_TYPE]: (type) => typeFilter(type.name, type)\n            ? filterElementFields(graphql_1.GraphQLInputObjectType, type, inputObjectFieldFilter || fieldFilter)\n            : null,\n        [Interfaces_js_1.MapperKind.UNION_TYPE]: (type) => typeFilter(type.name, type) ? undefined : null,\n        [Interfaces_js_1.MapperKind.ENUM_TYPE]: (type) => typeFilter(type.name, type) ? undefined : null,\n        [Interfaces_js_1.MapperKind.SCALAR_TYPE]: (type) => typeFilter(type.name, type) ? undefined : null,\n        [Interfaces_js_1.MapperKind.DIRECTIVE]: directive => directiveFilter && !directiveFilter(directive.name, directive) ? null : undefined,\n        [Interfaces_js_1.MapperKind.ENUM_VALUE]: (valueConfig, typeName, _schema, externalValue) => enumValueFilter && !enumValueFilter(typeName, externalValue, valueConfig) ? null : undefined,\n    });\n    return filteredSchema;\n}\nfunction filterRootFields(type, operation, rootFieldFilter, argumentFilter) {\n    if (rootFieldFilter || argumentFilter) {\n        const config = type.toConfig();\n        for (const fieldName in config.fields) {\n            const field = config.fields[fieldName];\n            if (rootFieldFilter && !rootFieldFilter(operation, fieldName, config.fields[fieldName])) {\n                delete config.fields[fieldName];\n            }\n            else if (argumentFilter && field.args) {\n                for (const argName in field.args) {\n                    if (!argumentFilter(type.name, fieldName, argName, field.args[argName])) {\n                        delete field.args[argName];\n                    }\n                }\n            }\n        }\n        return new graphql_1.GraphQLObjectType(config);\n    }\n    return type;\n}\nfunction filterElementFields(ElementConstructor, type, fieldFilter, argumentFilter) {\n    if (fieldFilter || argumentFilter) {\n        const config = type.toConfig();\n        for (const fieldName in config.fields) {\n            const field = config.fields[fieldName];\n            if (fieldFilter && !fieldFilter(type.name, fieldName, config.fields[fieldName])) {\n                delete config.fields[fieldName];\n            }\n            else if (argumentFilter && 'args' in field) {\n                for (const argName in field.args) {\n                    if (!argumentFilter(type.name, fieldName, argName, field.args[argName])) {\n                        delete field.args[argName];\n                    }\n                }\n            }\n        }\n        return new ElementConstructor(config);\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG;AACvB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,aAAa,EAAE,MAAM,EAAE,aAAa,IAAM,IAAI,EAAE,cAAc,SAAS,EAAE,kBAAkB,SAAS,EAAE,oBAAoB,SAAS,EAAE,uBAAuB,SAAS,EAAE,yBAAyB,SAAS,EAAE,iBAAiB,SAAS,EAAE,kBAAkB,SAAS,EAAE,kBAAkB,SAAS,EAAG;IACvS,MAAM,iBAAiB,CAAC,GAAG,eAAe,SAAS,EAAE,QAAQ;QACzD,CAAC,gBAAgB,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,OAAS,iBAAiB,MAAM,SAAS,iBAAiB;QAC/F,CAAC,gBAAgB,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAS,iBAAiB,MAAM,YAAY,iBAAiB;QACrG,CAAC,gBAAgB,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,OAAS,iBAAiB,MAAM,gBAAgB,iBAAiB;QAC7G,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,OAAS,WAAW,KAAK,IAAI,EAAE,QACpE,oBAAoB,UAAU,iBAAiB,EAAE,MAAM,qBAAqB,aAAa,kBACzF;QACN,CAAC,gBAAgB,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC,OAAS,WAAW,KAAK,IAAI,EAAE,QACvE,oBAAoB,UAAU,oBAAoB,EAAE,MAAM,wBAAwB,aAAa,kBAC/F;QACN,CAAC,gBAAgB,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC,OAAS,WAAW,KAAK,IAAI,EAAE,QAC1E,oBAAoB,UAAU,sBAAsB,EAAE,MAAM,0BAA0B,eACtF;QACN,CAAC,gBAAgB,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,OAAS,WAAW,KAAK,IAAI,EAAE,QAAQ,YAAY;QAC7F,CAAC,gBAAgB,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,OAAS,WAAW,KAAK,IAAI,EAAE,QAAQ,YAAY;QAC5F,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,OAAS,WAAW,KAAK,IAAI,EAAE,QAAQ,YAAY;QAC9F,CAAC,gBAAgB,UAAU,CAAC,SAAS,CAAC,EAAE,CAAA,YAAa,mBAAmB,CAAC,gBAAgB,UAAU,IAAI,EAAE,aAAa,OAAO;QAC7H,CAAC,gBAAgB,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa,UAAU,SAAS,gBAAkB,mBAAmB,CAAC,gBAAgB,UAAU,eAAe,eAAe,OAAO;IACnL;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc;IACtE,IAAI,mBAAmB,gBAAgB;QACnC,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;YACnC,MAAM,QAAQ,OAAO,MAAM,CAAC,UAAU;YACtC,IAAI,mBAAmB,CAAC,gBAAgB,WAAW,WAAW,OAAO,MAAM,CAAC,UAAU,GAAG;gBACrF,OAAO,OAAO,MAAM,CAAC,UAAU;YACnC,OACK,IAAI,kBAAkB,MAAM,IAAI,EAAE;gBACnC,IAAK,MAAM,WAAW,MAAM,IAAI,CAAE;oBAC9B,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,WAAW,SAAS,MAAM,IAAI,CAAC,QAAQ,GAAG;wBACrE,OAAO,MAAM,IAAI,CAAC,QAAQ;oBAC9B;gBACJ;YACJ;QACJ;QACA,OAAO,IAAI,UAAU,iBAAiB,CAAC;IAC3C;IACA,OAAO;AACX;AACA,SAAS,oBAAoB,kBAAkB,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc;IAC9E,IAAI,eAAe,gBAAgB;QAC/B,MAAM,SAAS,KAAK,QAAQ;QAC5B,IAAK,MAAM,aAAa,OAAO,MAAM,CAAE;YACnC,MAAM,QAAQ,OAAO,MAAM,CAAC,UAAU;YACtC,IAAI,eAAe,CAAC,YAAY,KAAK,IAAI,EAAE,WAAW,OAAO,MAAM,CAAC,UAAU,GAAG;gBAC7E,OAAO,OAAO,MAAM,CAAC,UAAU;YACnC,OACK,IAAI,kBAAkB,UAAU,OAAO;gBACxC,IAAK,MAAM,WAAW,MAAM,IAAI,CAAE;oBAC9B,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,WAAW,SAAS,MAAM,IAAI,CAAC,QAAQ,GAAG;wBACrE,OAAO,MAAM,IAAI,CAAC,QAAQ;oBAC9B;gBACJ;YACJ;QACJ;QACA,OAAO,IAAI,mBAAmB;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6389, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/heal.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.healSchema = healSchema;\nexports.healTypes = healTypes;\nconst graphql_1 = require(\"graphql\");\n// Update any references to named schema types that disagree with the named\n// types found in schema.getTypeMap().\n//\n// healSchema and its callers (visitSchema/visitSchemaDirectives) all modify the schema in place.\n// Therefore, private variables (such as the stored implementation map and the proper root types)\n// are not updated.\n//\n// If this causes issues, the schema could be more aggressively healed as follows:\n//\n// healSchema(schema);\n// const config = schema.toConfig()\n// const healedSchema = new GraphQLSchema({\n//   ...config,\n//   query: schema.getType('<desired new root query type name>'),\n//   mutation: schema.getType('<desired new root mutation type name>'),\n//   subscription: schema.getType('<desired new root subscription type name>'),\n// });\n//\n// One can then also -- if necessary --  assign the correct private variables to the initial schema\n// as follows:\n// Object.assign(schema, healedSchema);\n//\n// These steps are not taken automatically to preserve backwards compatibility with graphql-tools v4.\n// See https://github.com/ardatan/graphql-tools/issues/1462\n//\n// They were briefly taken in v5, but can now be phased out as they were only required when other\n// areas of the codebase were using healSchema and visitSchema more extensively.\n//\nfunction healSchema(schema) {\n    healTypes(schema.getTypeMap(), schema.getDirectives());\n    return schema;\n}\nfunction healTypes(originalTypeMap, directives) {\n    const actualNamedTypeMap = Object.create(null);\n    // If any of the .name properties of the GraphQLNamedType objects in\n    // schema.getTypeMap() have changed, the keys of the type map need to\n    // be updated accordingly.\n    for (const typeName in originalTypeMap) {\n        const namedType = originalTypeMap[typeName];\n        if (namedType == null || typeName.startsWith('__')) {\n            continue;\n        }\n        const actualName = namedType.name;\n        if (actualName.startsWith('__')) {\n            continue;\n        }\n        if (actualNamedTypeMap[actualName] != null) {\n            console.warn(`Duplicate schema type name ${actualName} found; keeping the existing one found in the schema`);\n            continue;\n        }\n        actualNamedTypeMap[actualName] = namedType;\n        // Note: we are deliberately leaving namedType in the schema by its\n        // original name (which might be different from actualName), so that\n        // references by that name can be healed.\n    }\n    // Now add back every named type by its actual name.\n    for (const typeName in actualNamedTypeMap) {\n        const namedType = actualNamedTypeMap[typeName];\n        originalTypeMap[typeName] = namedType;\n    }\n    // Directive declaration argument types can refer to named types.\n    for (const decl of directives) {\n        decl.args = decl.args.filter(arg => {\n            arg.type = healType(arg.type);\n            return arg.type !== null;\n        });\n    }\n    for (const typeName in originalTypeMap) {\n        const namedType = originalTypeMap[typeName];\n        // Heal all named types, except for dangling references, kept only to redirect.\n        if (!typeName.startsWith('__') && typeName in actualNamedTypeMap) {\n            if (namedType != null) {\n                healNamedType(namedType);\n            }\n        }\n    }\n    for (const typeName in originalTypeMap) {\n        if (!typeName.startsWith('__') && !(typeName in actualNamedTypeMap)) {\n            delete originalTypeMap[typeName];\n        }\n    }\n    function healNamedType(type) {\n        if ((0, graphql_1.isObjectType)(type)) {\n            healFields(type);\n            healInterfaces(type);\n            return;\n        }\n        else if ((0, graphql_1.isInterfaceType)(type)) {\n            healFields(type);\n            if ('getInterfaces' in type) {\n                healInterfaces(type);\n            }\n            return;\n        }\n        else if ((0, graphql_1.isUnionType)(type)) {\n            healUnderlyingTypes(type);\n            return;\n        }\n        else if ((0, graphql_1.isInputObjectType)(type)) {\n            healInputFields(type);\n            return;\n        }\n        else if ((0, graphql_1.isLeafType)(type)) {\n            return;\n        }\n        throw new Error(`Unexpected schema type: ${type}`);\n    }\n    function healFields(type) {\n        const fieldMap = type.getFields();\n        for (const [key, field] of Object.entries(fieldMap)) {\n            field.args\n                .map(arg => {\n                arg.type = healType(arg.type);\n                return arg.type === null ? null : arg;\n            })\n                .filter(Boolean);\n            field.type = healType(field.type);\n            if (field.type === null) {\n                delete fieldMap[key];\n            }\n        }\n    }\n    function healInterfaces(type) {\n        if ('getInterfaces' in type) {\n            const interfaces = type.getInterfaces();\n            interfaces.push(...interfaces\n                .splice(0)\n                .map(iface => healType(iface))\n                .filter(Boolean));\n        }\n    }\n    function healInputFields(type) {\n        const fieldMap = type.getFields();\n        for (const [key, field] of Object.entries(fieldMap)) {\n            field.type = healType(field.type);\n            if (field.type === null) {\n                delete fieldMap[key];\n            }\n        }\n    }\n    function healUnderlyingTypes(type) {\n        const types = type.getTypes();\n        types.push(...types\n            .splice(0)\n            .map(t => healType(t))\n            .filter(Boolean));\n    }\n    function healType(type) {\n        // Unwrap the two known wrapper types\n        if ((0, graphql_1.isListType)(type)) {\n            const healedType = healType(type.ofType);\n            return healedType != null ? new graphql_1.GraphQLList(healedType) : null;\n        }\n        else if ((0, graphql_1.isNonNullType)(type)) {\n            const healedType = healType(type.ofType);\n            return healedType != null ? new graphql_1.GraphQLNonNull(healedType) : null;\n        }\n        else if ((0, graphql_1.isNamedType)(type)) {\n            // If a type annotation on a field or an argument or a union member is\n            // any `GraphQLNamedType` with a `name`, then it must end up identical\n            // to `schema.getType(name)`, since `schema.getTypeMap()` is the source\n            // of truth for all named schema types.\n            // Note that new types can still be simply added by adding a field, as\n            // the official type will be undefined, not null.\n            const officialType = originalTypeMap[type.name];\n            if (officialType && type !== officialType) {\n                return officialType;\n            }\n        }\n        return type;\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG;AACpB,MAAM;AACN,2EAA2E;AAC3E,sCAAsC;AACtC,EAAE;AACF,iGAAiG;AACjG,iGAAiG;AACjG,mBAAmB;AACnB,EAAE;AACF,kFAAkF;AAClF,EAAE;AACF,sBAAsB;AACtB,mCAAmC;AACnC,2CAA2C;AAC3C,eAAe;AACf,iEAAiE;AACjE,uEAAuE;AACvE,+EAA+E;AAC/E,MAAM;AACN,EAAE;AACF,mGAAmG;AACnG,cAAc;AACd,uCAAuC;AACvC,EAAE;AACF,qGAAqG;AACrG,2DAA2D;AAC3D,EAAE;AACF,iGAAiG;AACjG,gFAAgF;AAChF,EAAE;AACF,SAAS,WAAW,MAAM;IACtB,UAAU,OAAO,UAAU,IAAI,OAAO,aAAa;IACnD,OAAO;AACX;AACA,SAAS,UAAU,eAAe,EAAE,UAAU;IAC1C,MAAM,qBAAqB,OAAO,MAAM,CAAC;IACzC,oEAAoE;IACpE,qEAAqE;IACrE,0BAA0B;IAC1B,IAAK,MAAM,YAAY,gBAAiB;QACpC,MAAM,YAAY,eAAe,CAAC,SAAS;QAC3C,IAAI,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;YAChD;QACJ;QACA,MAAM,aAAa,UAAU,IAAI;QACjC,IAAI,WAAW,UAAU,CAAC,OAAO;YAC7B;QACJ;QACA,IAAI,kBAAkB,CAAC,WAAW,IAAI,MAAM;YACxC,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,oDAAoD,CAAC;YAC3G;QACJ;QACA,kBAAkB,CAAC,WAAW,GAAG;IACjC,mEAAmE;IACnE,oEAAoE;IACpE,yCAAyC;IAC7C;IACA,oDAAoD;IACpD,IAAK,MAAM,YAAY,mBAAoB;QACvC,MAAM,YAAY,kBAAkB,CAAC,SAAS;QAC9C,eAAe,CAAC,SAAS,GAAG;IAChC;IACA,iEAAiE;IACjE,KAAK,MAAM,QAAQ,WAAY;QAC3B,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA;YACzB,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI;YAC5B,OAAO,IAAI,IAAI,KAAK;QACxB;IACJ;IACA,IAAK,MAAM,YAAY,gBAAiB;QACpC,MAAM,YAAY,eAAe,CAAC,SAAS;QAC3C,+EAA+E;QAC/E,IAAI,CAAC,SAAS,UAAU,CAAC,SAAS,YAAY,oBAAoB;YAC9D,IAAI,aAAa,MAAM;gBACnB,cAAc;YAClB;QACJ;IACJ;IACA,IAAK,MAAM,YAAY,gBAAiB;QACpC,IAAI,CAAC,SAAS,UAAU,CAAC,SAAS,CAAC,CAAC,YAAY,kBAAkB,GAAG;YACjE,OAAO,eAAe,CAAC,SAAS;QACpC;IACJ;IACA,SAAS,cAAc,IAAI;QACvB,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;YACnC,WAAW;YACX,eAAe;YACf;QACJ,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;YAC3C,WAAW;YACX,IAAI,mBAAmB,MAAM;gBACzB,eAAe;YACnB;YACA;QACJ,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;YACvC,oBAAoB;YACpB;QACJ,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;YAC7C,gBAAgB;YAChB;QACJ,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACtC;QACJ;QACA,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM;IACrD;IACA,SAAS,WAAW,IAAI;QACpB,MAAM,WAAW,KAAK,SAAS;QAC/B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;YACjD,MAAM,IAAI,CACL,GAAG,CAAC,CAAA;gBACL,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI;gBAC5B,OAAO,IAAI,IAAI,KAAK,OAAO,OAAO;YACtC,GACK,MAAM,CAAC;YACZ,MAAM,IAAI,GAAG,SAAS,MAAM,IAAI;YAChC,IAAI,MAAM,IAAI,KAAK,MAAM;gBACrB,OAAO,QAAQ,CAAC,IAAI;YACxB;QACJ;IACJ;IACA,SAAS,eAAe,IAAI;QACxB,IAAI,mBAAmB,MAAM;YACzB,MAAM,aAAa,KAAK,aAAa;YACrC,WAAW,IAAI,IAAI,WACd,MAAM,CAAC,GACP,GAAG,CAAC,CAAA,QAAS,SAAS,QACtB,MAAM,CAAC;QAChB;IACJ;IACA,SAAS,gBAAgB,IAAI;QACzB,MAAM,WAAW,KAAK,SAAS;QAC/B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;YACjD,MAAM,IAAI,GAAG,SAAS,MAAM,IAAI;YAChC,IAAI,MAAM,IAAI,KAAK,MAAM;gBACrB,OAAO,QAAQ,CAAC,IAAI;YACxB;QACJ;IACJ;IACA,SAAS,oBAAoB,IAAI;QAC7B,MAAM,QAAQ,KAAK,QAAQ;QAC3B,MAAM,IAAI,IAAI,MACT,MAAM,CAAC,GACP,GAAG,CAAC,CAAA,IAAK,SAAS,IAClB,MAAM,CAAC;IAChB;IACA,SAAS,SAAS,IAAI;QAClB,qCAAqC;QACrC,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;YACjC,MAAM,aAAa,SAAS,KAAK,MAAM;YACvC,OAAO,cAAc,OAAO,IAAI,UAAU,WAAW,CAAC,cAAc;QACxE,OACK,IAAI,CAAC,GAAG,UAAU,aAAa,EAAE,OAAO;YACzC,MAAM,aAAa,SAAS,KAAK,MAAM;YACvC,OAAO,cAAc,OAAO,IAAI,UAAU,cAAc,CAAC,cAAc;QAC3E,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;YACvC,sEAAsE;YACtE,sEAAsE;YACtE,uEAAuE;YACvE,uCAAuC;YACvC,sEAAsE;YACtE,iDAAiD;YACjD,MAAM,eAAe,eAAe,CAAC,KAAK,IAAI,CAAC;YAC/C,IAAI,gBAAgB,SAAS,cAAc;gBACvC,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/getResolversFromSchema.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getResolversFromSchema = getResolversFromSchema;\nconst graphql_1 = require(\"graphql\");\nfunction getResolversFromSchema(schema, \n// Include default merged resolvers\nincludeDefaultMergedResolver) {\n    const resolvers = Object.create(null);\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        if (!typeName.startsWith('__')) {\n            const type = typeMap[typeName];\n            if ((0, graphql_1.isScalarType)(type)) {\n                if (!(0, graphql_1.isSpecifiedScalarType)(type)) {\n                    const config = type.toConfig();\n                    delete config.astNode; // avoid AST duplication elsewhere\n                    resolvers[typeName] = new graphql_1.GraphQLScalarType(config);\n                }\n            }\n            else if ((0, graphql_1.isEnumType)(type)) {\n                resolvers[typeName] = {};\n                const values = type.getValues();\n                for (const value of values) {\n                    resolvers[typeName][value.name] = value.value;\n                }\n            }\n            else if ((0, graphql_1.isInterfaceType)(type)) {\n                if (type.resolveType != null) {\n                    resolvers[typeName] = {\n                        __resolveType: type.resolveType,\n                    };\n                }\n            }\n            else if ((0, graphql_1.isUnionType)(type)) {\n                if (type.resolveType != null) {\n                    resolvers[typeName] = {\n                        __resolveType: type.resolveType,\n                    };\n                }\n            }\n            else if ((0, graphql_1.isObjectType)(type)) {\n                resolvers[typeName] = {};\n                if (type.isTypeOf != null) {\n                    resolvers[typeName].__isTypeOf = type.isTypeOf;\n                }\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    if (field.subscribe != null) {\n                        resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};\n                        resolvers[typeName][fieldName].subscribe = field.subscribe;\n                    }\n                    if (field.resolve != null && field.resolve?.name !== 'defaultFieldResolver') {\n                        switch (field.resolve?.name) {\n                            case 'defaultMergedResolver':\n                                if (!includeDefaultMergedResolver) {\n                                    continue;\n                                }\n                                break;\n                            case 'defaultFieldResolver':\n                                continue;\n                        }\n                        resolvers[typeName][fieldName] = resolvers[typeName][fieldName] || {};\n                        resolvers[typeName][fieldName].resolve = field.resolve;\n                    }\n                }\n            }\n        }\n    }\n    return resolvers;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG;AACjC,MAAM;AACN,SAAS,uBAAuB,MAAM,EACtC,mCAAmC;AACnC,4BAA4B;IACxB,MAAM,YAAY,OAAO,MAAM,CAAC;IAChC,MAAM,UAAU,OAAO,UAAU;IACjC,IAAK,MAAM,YAAY,QAAS;QAC5B,IAAI,CAAC,SAAS,UAAU,CAAC,OAAO;YAC5B,MAAM,OAAO,OAAO,CAAC,SAAS;YAC9B,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;gBACnC,IAAI,CAAC,CAAC,GAAG,UAAU,qBAAqB,EAAE,OAAO;oBAC7C,MAAM,SAAS,KAAK,QAAQ;oBAC5B,OAAO,OAAO,OAAO,EAAE,kCAAkC;oBACzD,SAAS,CAAC,SAAS,GAAG,IAAI,UAAU,iBAAiB,CAAC;gBAC1D;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;gBACtC,SAAS,CAAC,SAAS,GAAG,CAAC;gBACvB,MAAM,SAAS,KAAK,SAAS;gBAC7B,KAAK,MAAM,SAAS,OAAQ;oBACxB,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,KAAK;gBACjD;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;gBAC3C,IAAI,KAAK,WAAW,IAAI,MAAM;oBAC1B,SAAS,CAAC,SAAS,GAAG;wBAClB,eAAe,KAAK,WAAW;oBACnC;gBACJ;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;gBACvC,IAAI,KAAK,WAAW,IAAI,MAAM;oBAC1B,SAAS,CAAC,SAAS,GAAG;wBAClB,eAAe,KAAK,WAAW;oBACnC;gBACJ;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;gBACxC,SAAS,CAAC,SAAS,GAAG,CAAC;gBACvB,IAAI,KAAK,QAAQ,IAAI,MAAM;oBACvB,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,KAAK,QAAQ;gBAClD;gBACA,MAAM,SAAS,KAAK,SAAS;gBAC7B,IAAK,MAAM,aAAa,OAAQ;oBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;oBAC/B,IAAI,MAAM,SAAS,IAAI,MAAM;wBACzB,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC;wBACpE,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM,SAAS;oBAC9D;oBACA,IAAI,MAAM,OAAO,IAAI,QAAQ,MAAM,OAAO,EAAE,SAAS,wBAAwB;wBACzE,OAAQ,MAAM,OAAO,EAAE;4BACnB,KAAK;gCACD,IAAI,CAAC,8BAA8B;oCAC/B;gCACJ;gCACA;4BACJ,KAAK;gCACD;wBACR;wBACA,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC;wBACpE,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,GAAG,MAAM,OAAO;oBAC1D;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6628, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/forEachField.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.forEachField = forEachField;\nconst graphql_1 = require(\"graphql\");\nfunction forEachField(schema, fn) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        // TODO: maybe have an option to include these?\n        if (!(0, graphql_1.getNamedType)(type).name.startsWith('__') && (0, graphql_1.isObjectType)(type)) {\n            const fields = type.getFields();\n            for (const fieldName in fields) {\n                const field = fields[fieldName];\n                fn(field, typeName, fieldName);\n            }\n        }\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG;AACvB,MAAM;AACN,SAAS,aAAa,MAAM,EAAE,EAAE;IAC5B,MAAM,UAAU,OAAO,UAAU;IACjC,IAAK,MAAM,YAAY,QAAS;QAC5B,MAAM,OAAO,OAAO,CAAC,SAAS;QAC9B,+CAA+C;QAC/C,IAAI,CAAC,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;YAC/F,MAAM,SAAS,KAAK,SAAS;YAC7B,IAAK,MAAM,aAAa,OAAQ;gBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;gBAC/B,GAAG,OAAO,UAAU;YACxB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6651, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/forEachDefaultValue.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.forEachDefaultValue = forEachDefaultValue;\nconst graphql_1 = require(\"graphql\");\nfunction forEachDefaultValue(schema, fn) {\n    const typeMap = schema.getTypeMap();\n    for (const typeName in typeMap) {\n        const type = typeMap[typeName];\n        if (!(0, graphql_1.getNamedType)(type).name.startsWith('__')) {\n            if ((0, graphql_1.isObjectType)(type)) {\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    for (const arg of field.args) {\n                        arg.defaultValue = fn(arg.type, arg.defaultValue);\n                    }\n                }\n            }\n            else if ((0, graphql_1.isInputObjectType)(type)) {\n                const fields = type.getFields();\n                for (const fieldName in fields) {\n                    const field = fields[fieldName];\n                    field.defaultValue = fn(field.type, field.defaultValue);\n                }\n            }\n        }\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG;AAC9B,MAAM;AACN,SAAS,oBAAoB,MAAM,EAAE,EAAE;IACnC,MAAM,UAAU,OAAO,UAAU;IACjC,IAAK,MAAM,YAAY,QAAS;QAC5B,MAAM,OAAO,OAAO,CAAC,SAAS;QAC9B,IAAI,CAAC,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO;YAC1D,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;gBACnC,MAAM,SAAS,KAAK,SAAS;gBAC7B,IAAK,MAAM,aAAa,OAAQ;oBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;oBAC/B,KAAK,MAAM,OAAO,MAAM,IAAI,CAAE;wBAC1B,IAAI,YAAY,GAAG,GAAG,IAAI,IAAI,EAAE,IAAI,YAAY;oBACpD;gBACJ;YACJ,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;gBAC7C,MAAM,SAAS,KAAK,SAAS;gBAC7B,IAAK,MAAM,aAAa,OAAQ;oBAC5B,MAAM,QAAQ,MAAM,CAAC,UAAU;oBAC/B,MAAM,YAAY,GAAG,GAAG,MAAM,IAAI,EAAE,MAAM,YAAY;gBAC1D;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6683, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/addTypes.js"], "sourcesContent": ["\"use strict\";\n// addTypes uses toConfig to create a new schema with a new or replaced\n// type or directive. Rewiring is employed so that the replaced type can be\n// reconnected with the existing types.\n//\n// Rewiring is employed even for new types or directives as a convenience, so\n// that type references within the new type or directive do not have to be to\n// the identical objects within the original schema.\n//\n// In fact, the type references could even be stub types with entirely different\n// fields, as long as the type references share the same name as the desired\n// type within the original schema's type map.\n//\n// This makes it easy to perform simple schema operations (e.g. adding a new\n// type with a fiew fields removed from an existing type) that could normally be\n// performed by using toConfig directly, but is blocked if any intervening\n// more advanced schema operations have caused the types to be recreated via\n// rewiring.\n//\n// Type recreation happens, for example, with every use of mapSchema, as the\n// types are always rewired. If fields are selected and removed using\n// mapSchema, adding those fields to a new type can no longer be simply done\n// by toConfig, as the types are not the identical JavaScript objects, and\n// schema creation will fail with errors referencing multiple types with the\n// same names.\n//\n// enhanceSchema can fill this gap by adding an additional round of rewiring.\n//\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.addTypes = addTypes;\nconst graphql_1 = require(\"graphql\");\nconst getObjectTypeFromTypeMap_js_1 = require(\"./getObjectTypeFromTypeMap.js\");\nconst rewire_js_1 = require(\"./rewire.js\");\nfunction addTypes(schema, newTypesOrDirectives) {\n    const config = schema.toConfig();\n    const originalTypeMap = {};\n    for (const type of config.types) {\n        originalTypeMap[type.name] = type;\n    }\n    const originalDirectiveMap = {};\n    for (const directive of config.directives) {\n        originalDirectiveMap[directive.name] = directive;\n    }\n    for (const newTypeOrDirective of newTypesOrDirectives) {\n        if ((0, graphql_1.isNamedType)(newTypeOrDirective)) {\n            originalTypeMap[newTypeOrDirective.name] = newTypeOrDirective;\n        }\n        else if ((0, graphql_1.isDirective)(newTypeOrDirective)) {\n            originalDirectiveMap[newTypeOrDirective.name] = newTypeOrDirective;\n        }\n    }\n    const { typeMap, directives } = (0, rewire_js_1.rewireTypes)(originalTypeMap, Object.values(originalDirectiveMap));\n    return new graphql_1.GraphQLSchema({\n        ...config,\n        query: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, schema.getQueryType()),\n        mutation: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, schema.getMutationType()),\n        subscription: (0, getObjectTypeFromTypeMap_js_1.getObjectTypeFromTypeMap)(typeMap, schema.getSubscriptionType()),\n        types: Object.values(typeMap),\n        directives,\n    });\n}\n"], "names": [], "mappings": "AACA,uEAAuE;AACvE,2EAA2E;AAC3E,uCAAuC;AACvC,EAAE;AACF,6EAA6E;AAC7E,6EAA6E;AAC7E,oDAAoD;AACpD,EAAE;AACF,gFAAgF;AAChF,4EAA4E;AAC5E,8CAA8C;AAC9C,EAAE;AACF,4EAA4E;AAC5E,gFAAgF;AAChF,0EAA0E;AAC1E,4EAA4E;AAC5E,YAAY;AACZ,EAAE;AACF,4EAA4E;AAC5E,qEAAqE;AACrE,4EAA4E;AAC5E,0EAA0E;AAC1E,4EAA4E;AAC5E,cAAc;AACd,EAAE;AACF,6EAA6E;AAC7E,EAAE;AACF,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG;AACnB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,SAAS,MAAM,EAAE,oBAAoB;IAC1C,MAAM,SAAS,OAAO,QAAQ;IAC9B,MAAM,kBAAkB,CAAC;IACzB,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAE;QAC7B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG;IACjC;IACA,MAAM,uBAAuB,CAAC;IAC9B,KAAK,MAAM,aAAa,OAAO,UAAU,CAAE;QACvC,oBAAoB,CAAC,UAAU,IAAI,CAAC,GAAG;IAC3C;IACA,KAAK,MAAM,sBAAsB,qBAAsB;QACnD,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,qBAAqB;YAChD,eAAe,CAAC,mBAAmB,IAAI,CAAC,GAAG;QAC/C,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,qBAAqB;YACrD,oBAAoB,CAAC,mBAAmB,IAAI,CAAC,GAAG;QACpD;IACJ;IACA,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,GAAG,YAAY,WAAW,EAAE,iBAAiB,OAAO,MAAM,CAAC;IAC5F,OAAO,IAAI,UAAU,aAAa,CAAC;QAC/B,GAAG,MAAM;QACT,OAAO,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,SAAS,OAAO,YAAY;QAC/F,UAAU,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,SAAS,OAAO,eAAe;QACrG,cAAc,CAAC,GAAG,8BAA8B,wBAAwB,EAAE,SAAS,OAAO,mBAAmB;QAC7G,OAAO,OAAO,MAAM,CAAC;QACrB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/prune.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.pruneSchema = pruneSchema;\nconst graphql_1 = require(\"graphql\");\nconst get_implementing_types_js_1 = require(\"./get-implementing-types.js\");\nconst Interfaces_js_1 = require(\"./Interfaces.js\");\nconst mapSchema_js_1 = require(\"./mapSchema.js\");\nconst rootTypes_js_1 = require(\"./rootTypes.js\");\n/**\n * Prunes the provided schema, removing unused and empty types\n * @param schema The schema to prune\n * @param options Additional options for removing unused types from the schema\n */\nfunction pruneSchema(schema, options = {}) {\n    const { skipEmptyCompositeTypePruning, skipEmptyUnionPruning, skipPruning, skipUnimplementedInterfacesPruning, skipUnusedTypesPruning, } = options;\n    let prunedTypes = []; // Pruned types during mapping\n    let prunedSchema = schema;\n    do {\n        let visited = visitSchema(prunedSchema);\n        // Custom pruning  was defined, so we need to pre-emptively revisit the schema accounting for this\n        if (skipPruning) {\n            const revisit = [];\n            for (const typeName in prunedSchema.getTypeMap()) {\n                if (typeName.startsWith('__')) {\n                    continue;\n                }\n                const type = prunedSchema.getType(typeName);\n                // if we want to skip pruning for this type, add it to the list of types to revisit\n                if (type && skipPruning(type)) {\n                    revisit.push(typeName);\n                }\n            }\n            visited = visitQueue(revisit, prunedSchema, visited); // visit again\n        }\n        prunedTypes = [];\n        prunedSchema = (0, mapSchema_js_1.mapSchema)(prunedSchema, {\n            [Interfaces_js_1.MapperKind.TYPE]: type => {\n                if (!visited.has(type.name) && !(0, graphql_1.isSpecifiedScalarType)(type)) {\n                    if ((0, graphql_1.isUnionType)(type) ||\n                        (0, graphql_1.isInputObjectType)(type) ||\n                        (0, graphql_1.isInterfaceType)(type) ||\n                        (0, graphql_1.isObjectType)(type) ||\n                        (0, graphql_1.isScalarType)(type)) {\n                        // skipUnusedTypesPruning: skip pruning unused types\n                        if (skipUnusedTypesPruning) {\n                            return type;\n                        }\n                        // skipEmptyUnionPruning: skip pruning empty unions\n                        if ((0, graphql_1.isUnionType)(type) &&\n                            skipEmptyUnionPruning &&\n                            !Object.keys(type.getTypes()).length) {\n                            return type;\n                        }\n                        if ((0, graphql_1.isInputObjectType)(type) || (0, graphql_1.isInterfaceType)(type) || (0, graphql_1.isObjectType)(type)) {\n                            // skipEmptyCompositeTypePruning: skip pruning object types or interfaces with no fields\n                            if (skipEmptyCompositeTypePruning && !Object.keys(type.getFields()).length) {\n                                return type;\n                            }\n                        }\n                        // skipUnimplementedInterfacesPruning: skip pruning interfaces that are not implemented by any other types\n                        if ((0, graphql_1.isInterfaceType)(type) && skipUnimplementedInterfacesPruning) {\n                            return type;\n                        }\n                    }\n                    prunedTypes.push(type.name);\n                    visited.delete(type.name);\n                    return null;\n                }\n                return type;\n            },\n        });\n    } while (prunedTypes.length); // Might have empty types and need to prune again\n    return prunedSchema;\n}\nfunction visitSchema(schema) {\n    const queue = []; // queue of nodes to visit\n    // Grab the root types and start there\n    for (const type of (0, rootTypes_js_1.getRootTypes)(schema)) {\n        queue.push(type.name);\n    }\n    return visitQueue(queue, schema);\n}\nfunction visitQueue(queue, schema, visited = new Set()) {\n    // Interfaces encountered that are field return types need to be revisited to add their implementations\n    const revisit = new Map();\n    // Navigate all types starting with pre-queued types (root types)\n    while (queue.length) {\n        const typeName = queue.pop();\n        // Skip types we already visited unless it is an interface type that needs revisiting\n        if (visited.has(typeName) && revisit[typeName] !== true) {\n            continue;\n        }\n        const type = schema.getType(typeName);\n        if (type) {\n            // Get types for union\n            if ((0, graphql_1.isUnionType)(type)) {\n                queue.push(...type.getTypes().map(type => type.name));\n            }\n            // If it is an interface and it is a returned type, grab all implementations so we can use proper __typename in fragments\n            if ((0, graphql_1.isInterfaceType)(type) && revisit[typeName] === true) {\n                queue.push(...(0, get_implementing_types_js_1.getImplementingTypes)(type.name, schema));\n                // No need to revisit this interface again\n                revisit[typeName] = false;\n            }\n            if ((0, graphql_1.isEnumType)(type)) {\n                // Visit enum values directives argument types\n                queue.push(...type.getValues().flatMap(value => getDirectivesArgumentsTypeNames(schema, value)));\n            }\n            // Visit interfaces this type is implementing if they haven't been visited yet\n            if ('getInterfaces' in type) {\n                // Only pushes to queue to visit but not return types\n                queue.push(...type.getInterfaces().map(iface => iface.name));\n            }\n            // If the type has fields visit those field types\n            if ('getFields' in type) {\n                const fields = type.getFields();\n                const entries = Object.entries(fields);\n                if (!entries.length) {\n                    continue;\n                }\n                for (const [, field] of entries) {\n                    if ((0, graphql_1.isObjectType)(type)) {\n                        // Visit arg types and arg directives arguments types\n                        queue.push(...field.args.flatMap(arg => {\n                            const typeNames = [(0, graphql_1.getNamedType)(arg.type).name];\n                            typeNames.push(...getDirectivesArgumentsTypeNames(schema, arg));\n                            return typeNames;\n                        }));\n                    }\n                    const namedType = (0, graphql_1.getNamedType)(field.type);\n                    queue.push(namedType.name);\n                    queue.push(...getDirectivesArgumentsTypeNames(schema, field));\n                    // Interfaces returned on fields need to be revisited to add their implementations\n                    if ((0, graphql_1.isInterfaceType)(namedType) && !(namedType.name in revisit)) {\n                        revisit[namedType.name] = true;\n                    }\n                }\n            }\n            queue.push(...getDirectivesArgumentsTypeNames(schema, type));\n            visited.add(typeName); // Mark as visited (and therefore it is used and should be kept)\n        }\n    }\n    return visited;\n}\nfunction getDirectivesArgumentsTypeNames(schema, directableObj) {\n    const argTypeNames = new Set();\n    if (directableObj.astNode?.directives) {\n        for (const directiveNode of directableObj.astNode.directives) {\n            const directive = schema.getDirective(directiveNode.name.value);\n            if (directive?.args) {\n                for (const arg of directive.args) {\n                    const argType = (0, graphql_1.getNamedType)(arg.type);\n                    argTypeNames.add(argType.name);\n                }\n            }\n        }\n    }\n    if (directableObj.extensions?.['directives']) {\n        for (const directiveName in directableObj.extensions['directives']) {\n            const directive = schema.getDirective(directiveName);\n            if (directive?.args) {\n                for (const arg of directive.args) {\n                    const argType = (0, graphql_1.getNamedType)(arg.type);\n                    argTypeNames.add(argType.name);\n                }\n            }\n        }\n    }\n    return [...argTypeNames];\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG;AACtB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;CAIC,GACD,SAAS,YAAY,MAAM,EAAE,UAAU,CAAC,CAAC;IACrC,MAAM,EAAE,6BAA6B,EAAE,qBAAqB,EAAE,WAAW,EAAE,kCAAkC,EAAE,sBAAsB,EAAG,GAAG;IAC3I,IAAI,cAAc,EAAE,EAAE,8BAA8B;IACpD,IAAI,eAAe;IACnB,GAAG;QACC,IAAI,UAAU,YAAY;QAC1B,kGAAkG;QAClG,IAAI,aAAa;YACb,MAAM,UAAU,EAAE;YAClB,IAAK,MAAM,YAAY,aAAa,UAAU,GAAI;gBAC9C,IAAI,SAAS,UAAU,CAAC,OAAO;oBAC3B;gBACJ;gBACA,MAAM,OAAO,aAAa,OAAO,CAAC;gBAClC,mFAAmF;gBACnF,IAAI,QAAQ,YAAY,OAAO;oBAC3B,QAAQ,IAAI,CAAC;gBACjB;YACJ;YACA,UAAU,WAAW,SAAS,cAAc,UAAU,cAAc;QACxE;QACA,cAAc,EAAE;QAChB,eAAe,CAAC,GAAG,eAAe,SAAS,EAAE,cAAc;YACvD,CAAC,gBAAgB,UAAU,CAAC,IAAI,CAAC,EAAE,CAAA;gBAC/B,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,GAAG,UAAU,qBAAqB,EAAE,OAAO;oBACxE,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,SAC3B,CAAC,GAAG,UAAU,iBAAiB,EAAE,SACjC,CAAC,GAAG,UAAU,eAAe,EAAE,SAC/B,CAAC,GAAG,UAAU,YAAY,EAAE,SAC5B,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;wBACnC,oDAAoD;wBACpD,IAAI,wBAAwB;4BACxB,OAAO;wBACX;wBACA,mDAAmD;wBACnD,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,SAC3B,yBACA,CAAC,OAAO,IAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,EAAE;4BACtC,OAAO;wBACX;wBACA,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,SAAS,CAAC,GAAG,UAAU,eAAe,EAAE,SAAS,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;4BACrH,wFAAwF;4BACxF,IAAI,iCAAiC,CAAC,OAAO,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,EAAE;gCACxE,OAAO;4BACX;wBACJ;wBACA,0GAA0G;wBAC1G,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,SAAS,oCAAoC;4BAC5E,OAAO;wBACX;oBACJ;oBACA,YAAY,IAAI,CAAC,KAAK,IAAI;oBAC1B,QAAQ,MAAM,CAAC,KAAK,IAAI;oBACxB,OAAO;gBACX;gBACA,OAAO;YACX;QACJ;IACJ,QAAS,YAAY,MAAM,CAAE,CAAC,iDAAiD;IAC/E,OAAO;AACX;AACA,SAAS,YAAY,MAAM;IACvB,MAAM,QAAQ,EAAE,EAAE,0BAA0B;IAC5C,sCAAsC;IACtC,KAAK,MAAM,QAAQ,CAAC,GAAG,eAAe,YAAY,EAAE,QAAS;QACzD,MAAM,IAAI,CAAC,KAAK,IAAI;IACxB;IACA,OAAO,WAAW,OAAO;AAC7B;AACA,SAAS,WAAW,KAAK,EAAE,MAAM,EAAE,UAAU,IAAI,KAAK;IAClD,uGAAuG;IACvG,MAAM,UAAU,IAAI;IACpB,iEAAiE;IACjE,MAAO,MAAM,MAAM,CAAE;QACjB,MAAM,WAAW,MAAM,GAAG;QAC1B,qFAAqF;QACrF,IAAI,QAAQ,GAAG,CAAC,aAAa,OAAO,CAAC,SAAS,KAAK,MAAM;YACrD;QACJ;QACA,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,IAAI,MAAM;YACN,sBAAsB;YACtB,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;gBAClC,MAAM,IAAI,IAAI,KAAK,QAAQ,GAAG,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;YACvD;YACA,yHAAyH;YACzH,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,SAAS,OAAO,CAAC,SAAS,KAAK,MAAM;gBACpE,MAAM,IAAI,IAAI,CAAC,GAAG,4BAA4B,oBAAoB,EAAE,KAAK,IAAI,EAAE;gBAC/E,0CAA0C;gBAC1C,OAAO,CAAC,SAAS,GAAG;YACxB;YACA,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;gBACjC,8CAA8C;gBAC9C,MAAM,IAAI,IAAI,KAAK,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,gCAAgC,QAAQ;YAC5F;YACA,8EAA8E;YAC9E,IAAI,mBAAmB,MAAM;gBACzB,qDAAqD;gBACrD,MAAM,IAAI,IAAI,KAAK,aAAa,GAAG,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9D;YACA,iDAAiD;YACjD,IAAI,eAAe,MAAM;gBACrB,MAAM,SAAS,KAAK,SAAS;gBAC7B,MAAM,UAAU,OAAO,OAAO,CAAC;gBAC/B,IAAI,CAAC,QAAQ,MAAM,EAAE;oBACjB;gBACJ;gBACA,KAAK,MAAM,GAAG,MAAM,IAAI,QAAS;oBAC7B,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;wBACnC,qDAAqD;wBACrD,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,CAAA;4BAC7B,MAAM,YAAY;gCAAC,CAAC,GAAG,UAAU,YAAY,EAAE,IAAI,IAAI,EAAE,IAAI;6BAAC;4BAC9D,UAAU,IAAI,IAAI,gCAAgC,QAAQ;4BAC1D,OAAO;wBACX;oBACJ;oBACA,MAAM,YAAY,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,IAAI;oBACxD,MAAM,IAAI,CAAC,UAAU,IAAI;oBACzB,MAAM,IAAI,IAAI,gCAAgC,QAAQ;oBACtD,kFAAkF;oBAClF,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,cAAc,CAAC,CAAC,UAAU,IAAI,IAAI,OAAO,GAAG;wBAC3E,OAAO,CAAC,UAAU,IAAI,CAAC,GAAG;oBAC9B;gBACJ;YACJ;YACA,MAAM,IAAI,IAAI,gCAAgC,QAAQ;YACtD,QAAQ,GAAG,CAAC,WAAW,gEAAgE;QAC3F;IACJ;IACA,OAAO;AACX;AACA,SAAS,gCAAgC,MAAM,EAAE,aAAa;IAC1D,MAAM,eAAe,IAAI;IACzB,IAAI,cAAc,OAAO,EAAE,YAAY;QACnC,KAAK,MAAM,iBAAiB,cAAc,OAAO,CAAC,UAAU,CAAE;YAC1D,MAAM,YAAY,OAAO,YAAY,CAAC,cAAc,IAAI,CAAC,KAAK;YAC9D,IAAI,WAAW,MAAM;gBACjB,KAAK,MAAM,OAAO,UAAU,IAAI,CAAE;oBAC9B,MAAM,UAAU,CAAC,GAAG,UAAU,YAAY,EAAE,IAAI,IAAI;oBACpD,aAAa,GAAG,CAAC,QAAQ,IAAI;gBACjC;YACJ;QACJ;IACJ;IACA,IAAI,cAAc,UAAU,EAAE,CAAC,aAAa,EAAE;QAC1C,IAAK,MAAM,iBAAiB,cAAc,UAAU,CAAC,aAAa,CAAE;YAChE,MAAM,YAAY,OAAO,YAAY,CAAC;YACtC,IAAI,WAAW,MAAM;gBACjB,KAAK,MAAM,OAAO,UAAU,IAAI,CAAE;oBAC9B,MAAM,UAAU,CAAC,GAAG,UAAU,YAAY,EAAE,IAAI,IAAI;oBACpD,aAAa,GAAG,CAAC,QAAQ,IAAI;gBACjC;YACJ;QACJ;IACJ;IACA,OAAO;WAAI;KAAa;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6920, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/mergeDeep.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeDeep = mergeDeep;\nconst helpers_js_1 = require(\"./helpers.js\");\nfunction mergeDeep(sources, respectPrototype = false, respectArrays = false, respectArrayLength = false) {\n    let expectedLength;\n    let allArrays = true;\n    const areArraysInTheSameLength = sources.every(source => {\n        if (Array.isArray(source)) {\n            if (expectedLength === undefined) {\n                expectedLength = source.length;\n                return true;\n            }\n            else if (expectedLength === source.length) {\n                return true;\n            }\n        }\n        else {\n            allArrays = false;\n        }\n        return false;\n    });\n    if (respectArrayLength && areArraysInTheSameLength) {\n        return new Array(expectedLength).fill(null).map((_, index) => mergeDeep(sources.map(source => source[index]), respectPrototype, respectArrays, respectArrayLength));\n    }\n    if (allArrays) {\n        return sources.flat(1);\n    }\n    let output;\n    let firstObjectSource;\n    if (respectPrototype) {\n        firstObjectSource = sources.find(source => isObject(source));\n        if (output == null) {\n            output = {};\n        }\n        if (firstObjectSource) {\n            Object.setPrototypeOf(output, Object.create(Object.getPrototypeOf(firstObjectSource)));\n        }\n    }\n    for (const source of sources) {\n        if (isObject(source)) {\n            if (firstObjectSource) {\n                const outputPrototype = Object.getPrototypeOf(output);\n                const sourcePrototype = Object.getPrototypeOf(source);\n                if (sourcePrototype) {\n                    for (const key of Object.getOwnPropertyNames(sourcePrototype)) {\n                        const descriptor = Object.getOwnPropertyDescriptor(sourcePrototype, key);\n                        if ((0, helpers_js_1.isSome)(descriptor)) {\n                            Object.defineProperty(outputPrototype, key, descriptor);\n                        }\n                    }\n                }\n            }\n            for (const key in source) {\n                if (output == null) {\n                    output = {};\n                }\n                if (key in output) {\n                    output[key] = mergeDeep([output[key], source[key]], respectPrototype, respectArrays, respectArrayLength);\n                }\n                else {\n                    output[key] = source[key];\n                }\n            }\n        }\n        else if (Array.isArray(source)) {\n            if (!Array.isArray(output)) {\n                output = source;\n            }\n            else {\n                output = mergeDeep([output, source], respectPrototype, respectArrays, respectArrayLength);\n            }\n        }\n        else {\n            output = source;\n        }\n    }\n    return output;\n}\nfunction isObject(item) {\n    return item && typeof item === 'object' && !Array.isArray(item);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG;AACpB,MAAM;AACN,SAAS,UAAU,OAAO,EAAE,mBAAmB,KAAK,EAAE,gBAAgB,KAAK,EAAE,qBAAqB,KAAK;IACnG,IAAI;IACJ,IAAI,YAAY;IAChB,MAAM,2BAA2B,QAAQ,KAAK,CAAC,CAAA;QAC3C,IAAI,MAAM,OAAO,CAAC,SAAS;YACvB,IAAI,mBAAmB,WAAW;gBAC9B,iBAAiB,OAAO,MAAM;gBAC9B,OAAO;YACX,OACK,IAAI,mBAAmB,OAAO,MAAM,EAAE;gBACvC,OAAO;YACX;QACJ,OACK;YACD,YAAY;QAChB;QACA,OAAO;IACX;IACA,IAAI,sBAAsB,0BAA0B;QAChD,OAAO,IAAI,MAAM,gBAAgB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAU,UAAU,QAAQ,GAAG,CAAC,CAAA,SAAU,MAAM,CAAC,MAAM,GAAG,kBAAkB,eAAe;IACnJ;IACA,IAAI,WAAW;QACX,OAAO,QAAQ,IAAI,CAAC;IACxB;IACA,IAAI;IACJ,IAAI;IACJ,IAAI,kBAAkB;QAClB,oBAAoB,QAAQ,IAAI,CAAC,CAAA,SAAU,SAAS;QACpD,IAAI,UAAU,MAAM;YAChB,SAAS,CAAC;QACd;QACA,IAAI,mBAAmB;YACnB,OAAO,cAAc,CAAC,QAAQ,OAAO,MAAM,CAAC,OAAO,cAAc,CAAC;QACtE;IACJ;IACA,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,SAAS,SAAS;YAClB,IAAI,mBAAmB;gBACnB,MAAM,kBAAkB,OAAO,cAAc,CAAC;gBAC9C,MAAM,kBAAkB,OAAO,cAAc,CAAC;gBAC9C,IAAI,iBAAiB;oBACjB,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,iBAAkB;wBAC3D,MAAM,aAAa,OAAO,wBAAwB,CAAC,iBAAiB;wBACpE,IAAI,CAAC,GAAG,aAAa,MAAM,EAAE,aAAa;4BACtC,OAAO,cAAc,CAAC,iBAAiB,KAAK;wBAChD;oBACJ;gBACJ;YACJ;YACA,IAAK,MAAM,OAAO,OAAQ;gBACtB,IAAI,UAAU,MAAM;oBAChB,SAAS,CAAC;gBACd;gBACA,IAAI,OAAO,QAAQ;oBACf,MAAM,CAAC,IAAI,GAAG,UAAU;wBAAC,MAAM,CAAC,IAAI;wBAAE,MAAM,CAAC,IAAI;qBAAC,EAAE,kBAAkB,eAAe;gBACzF,OACK;oBACD,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;gBAC7B;YACJ;QACJ,OACK,IAAI,MAAM,OAAO,CAAC,SAAS;YAC5B,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;gBACxB,SAAS;YACb,OACK;gBACD,SAAS,UAAU;oBAAC;oBAAQ;iBAAO,EAAE,kBAAkB,eAAe;YAC1E;QACJ,OACK;YACD,SAAS;QACb;IACJ;IACA,OAAO;AACX;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,OAAO,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7007, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/selectionSets.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseSelectionSet = parseSelectionSet;\nconst graphql_1 = require(\"graphql\");\nfunction parseSelectionSet(selectionSet, options) {\n    const query = (0, graphql_1.parse)(selectionSet, options).definitions[0];\n    return query.selectionSet;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG;AAC5B,MAAM;AACN,SAAS,kBAAkB,YAAY,EAAE,OAAO;IAC5C,MAAM,QAAQ,CAAC,GAAG,UAAU,KAAK,EAAE,cAAc,SAAS,WAAW,CAAC,EAAE;IACxE,OAAO,MAAM,YAAY;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7020, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/getResponseKeyFromInfo.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getResponseKeyFromInfo = getResponseKeyFromInfo;\n/**\n * Get the key under which the result of this resolver will be placed in the response JSON. Basically, just\n * resolves aliases.\n * @param info The info argument to the resolver.\n */\nfunction getResponseKeyFromInfo(info) {\n    return info.fieldNodes[0].alias != null ? info.fieldNodes[0].alias.value : info.fieldName;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG;AACjC;;;;CAIC,GACD,SAAS,uBAAuB,IAAI;IAChC,OAAO,KAAK,UAAU,CAAC,EAAE,CAAC,KAAK,IAAI,OAAO,KAAK,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,SAAS;AAC7F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7035, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/fields.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.appendObjectFields = appendObjectFields;\nexports.removeObjectFields = removeObjectFields;\nexports.selectObjectFields = selectObjectFields;\nexports.modifyObjectFields = modifyObjectFields;\nconst graphql_1 = require(\"graphql\");\nconst addTypes_js_1 = require(\"./addTypes.js\");\nconst Interfaces_js_1 = require(\"./Interfaces.js\");\nconst mapSchema_js_1 = require(\"./mapSchema.js\");\nfunction appendObjectFields(schema, typeName, additionalFields) {\n    if (schema.getType(typeName) == null) {\n        return (0, addTypes_js_1.addTypes)(schema, [\n            new graphql_1.GraphQLObjectType({\n                name: typeName,\n                fields: additionalFields,\n            }),\n        ]);\n    }\n    return (0, mapSchema_js_1.mapSchema)(schema, {\n        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                const newFieldConfigMap = {};\n                for (const fieldName in originalFieldConfigMap) {\n                    newFieldConfigMap[fieldName] = originalFieldConfigMap[fieldName];\n                }\n                for (const fieldName in additionalFields) {\n                    newFieldConfigMap[fieldName] = additionalFields[fieldName];\n                }\n                return (0, mapSchema_js_1.correctASTNodes)(new graphql_1.GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        },\n    });\n}\nfunction removeObjectFields(schema, typeName, testFn) {\n    const removedFields = {};\n    const newSchema = (0, mapSchema_js_1.mapSchema)(schema, {\n        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                const newFieldConfigMap = {};\n                for (const fieldName in originalFieldConfigMap) {\n                    const originalFieldConfig = originalFieldConfigMap[fieldName];\n                    if (testFn(fieldName, originalFieldConfig)) {\n                        removedFields[fieldName] = originalFieldConfig;\n                    }\n                    else {\n                        newFieldConfigMap[fieldName] = originalFieldConfig;\n                    }\n                }\n                return (0, mapSchema_js_1.correctASTNodes)(new graphql_1.GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        },\n    });\n    return [newSchema, removedFields];\n}\nfunction selectObjectFields(schema, typeName, testFn) {\n    const selectedFields = {};\n    (0, mapSchema_js_1.mapSchema)(schema, {\n        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                for (const fieldName in originalFieldConfigMap) {\n                    const originalFieldConfig = originalFieldConfigMap[fieldName];\n                    if (testFn(fieldName, originalFieldConfig)) {\n                        selectedFields[fieldName] = originalFieldConfig;\n                    }\n                }\n            }\n            return undefined;\n        },\n    });\n    return selectedFields;\n}\nfunction modifyObjectFields(schema, typeName, testFn, newFields) {\n    const removedFields = {};\n    const newSchema = (0, mapSchema_js_1.mapSchema)(schema, {\n        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: type => {\n            if (type.name === typeName) {\n                const config = type.toConfig();\n                const originalFieldConfigMap = config.fields;\n                const newFieldConfigMap = {};\n                for (const fieldName in originalFieldConfigMap) {\n                    const originalFieldConfig = originalFieldConfigMap[fieldName];\n                    if (testFn(fieldName, originalFieldConfig)) {\n                        removedFields[fieldName] = originalFieldConfig;\n                    }\n                    else {\n                        newFieldConfigMap[fieldName] = originalFieldConfig;\n                    }\n                }\n                for (const fieldName in newFields) {\n                    const fieldConfig = newFields[fieldName];\n                    newFieldConfigMap[fieldName] = fieldConfig;\n                }\n                return (0, mapSchema_js_1.correctASTNodes)(new graphql_1.GraphQLObjectType({\n                    ...config,\n                    fields: newFieldConfigMap,\n                }));\n            }\n        },\n    });\n    return [newSchema, removedFields];\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,kBAAkB,GAAG;AAC7B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,gBAAgB;IAC1D,IAAI,OAAO,OAAO,CAAC,aAAa,MAAM;QAClC,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,QAAQ;YACvC,IAAI,UAAU,iBAAiB,CAAC;gBAC5B,MAAM;gBACN,QAAQ;YACZ;SACH;IACL;IACA,OAAO,CAAC,GAAG,eAAe,SAAS,EAAE,QAAQ;QACzC,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;YACtC,IAAI,KAAK,IAAI,KAAK,UAAU;gBACxB,MAAM,SAAS,KAAK,QAAQ;gBAC5B,MAAM,yBAAyB,OAAO,MAAM;gBAC5C,MAAM,oBAAoB,CAAC;gBAC3B,IAAK,MAAM,aAAa,uBAAwB;oBAC5C,iBAAiB,CAAC,UAAU,GAAG,sBAAsB,CAAC,UAAU;gBACpE;gBACA,IAAK,MAAM,aAAa,iBAAkB;oBACtC,iBAAiB,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU;gBAC9D;gBACA,OAAO,CAAC,GAAG,eAAe,eAAe,EAAE,IAAI,UAAU,iBAAiB,CAAC;oBACvE,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,MAAM;IAChD,MAAM,gBAAgB,CAAC;IACvB,MAAM,YAAY,CAAC,GAAG,eAAe,SAAS,EAAE,QAAQ;QACpD,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;YACtC,IAAI,KAAK,IAAI,KAAK,UAAU;gBACxB,MAAM,SAAS,KAAK,QAAQ;gBAC5B,MAAM,yBAAyB,OAAO,MAAM;gBAC5C,MAAM,oBAAoB,CAAC;gBAC3B,IAAK,MAAM,aAAa,uBAAwB;oBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;oBAC7D,IAAI,OAAO,WAAW,sBAAsB;wBACxC,aAAa,CAAC,UAAU,GAAG;oBAC/B,OACK;wBACD,iBAAiB,CAAC,UAAU,GAAG;oBACnC;gBACJ;gBACA,OAAO,CAAC,GAAG,eAAe,eAAe,EAAE,IAAI,UAAU,iBAAiB,CAAC;oBACvE,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;QAAW;KAAc;AACrC;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,MAAM;IAChD,MAAM,iBAAiB,CAAC;IACxB,CAAC,GAAG,eAAe,SAAS,EAAE,QAAQ;QAClC,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;YACtC,IAAI,KAAK,IAAI,KAAK,UAAU;gBACxB,MAAM,SAAS,KAAK,QAAQ;gBAC5B,MAAM,yBAAyB,OAAO,MAAM;gBAC5C,IAAK,MAAM,aAAa,uBAAwB;oBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;oBAC7D,IAAI,OAAO,WAAW,sBAAsB;wBACxC,cAAc,CAAC,UAAU,GAAG;oBAChC;gBACJ;YACJ;YACA,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;IAC3D,MAAM,gBAAgB,CAAC;IACvB,MAAM,YAAY,CAAC,GAAG,eAAe,SAAS,EAAE,QAAQ;QACpD,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;YACtC,IAAI,KAAK,IAAI,KAAK,UAAU;gBACxB,MAAM,SAAS,KAAK,QAAQ;gBAC5B,MAAM,yBAAyB,OAAO,MAAM;gBAC5C,MAAM,oBAAoB,CAAC;gBAC3B,IAAK,MAAM,aAAa,uBAAwB;oBAC5C,MAAM,sBAAsB,sBAAsB,CAAC,UAAU;oBAC7D,IAAI,OAAO,WAAW,sBAAsB;wBACxC,aAAa,CAAC,UAAU,GAAG;oBAC/B,OACK;wBACD,iBAAiB,CAAC,UAAU,GAAG;oBACnC;gBACJ;gBACA,IAAK,MAAM,aAAa,UAAW;oBAC/B,MAAM,cAAc,SAAS,CAAC,UAAU;oBACxC,iBAAiB,CAAC,UAAU,GAAG;gBACnC;gBACA,OAAO,CAAC,GAAG,eAAe,eAAe,EAAE,IAAI,UAAU,iBAAiB,CAAC;oBACvE,GAAG,MAAM;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO;QAAC;QAAW;KAAc;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7158, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/renameType.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.renameType = renameType;\nconst graphql_1 = require(\"graphql\");\nfunction renameType(type, newTypeName) {\n    if ((0, graphql_1.isObjectType)(type)) {\n        return new graphql_1.GraphQLObjectType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if ((0, graphql_1.isInterfaceType)(type)) {\n        return new graphql_1.GraphQLInterfaceType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if ((0, graphql_1.isUnionType)(type)) {\n        return new graphql_1.GraphQLUnionType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if ((0, graphql_1.isInputObjectType)(type)) {\n        return new graphql_1.GraphQLInputObjectType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if ((0, graphql_1.isEnumType)(type)) {\n        return new graphql_1.GraphQLEnumType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    else if ((0, graphql_1.isScalarType)(type)) {\n        return new graphql_1.GraphQLScalarType({\n            ...type.toConfig(),\n            name: newTypeName,\n            astNode: type.astNode == null\n                ? type.astNode\n                : {\n                    ...type.astNode,\n                    name: {\n                        ...type.astNode.name,\n                        value: newTypeName,\n                    },\n                },\n            extensionASTNodes: type.extensionASTNodes == null\n                ? type.extensionASTNodes\n                : type.extensionASTNodes.map(node => ({\n                    ...node,\n                    name: {\n                        ...node.name,\n                        value: newTypeName,\n                    },\n                })),\n        });\n    }\n    throw new Error(`Unknown type ${type}.`);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG;AACrB,MAAM;AACN,SAAS,WAAW,IAAI,EAAE,WAAW;IACjC,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACnC,OAAO,IAAI,UAAU,iBAAiB,CAAC;YACnC,GAAG,KAAK,QAAQ,EAAE;YAClB,MAAM;YACN,SAAS,KAAK,OAAO,IAAI,OACnB,KAAK,OAAO,GACZ;gBACE,GAAG,KAAK,OAAO;gBACf,MAAM;oBACF,GAAG,KAAK,OAAO,CAAC,IAAI;oBACpB,OAAO;gBACX;YACJ;YACJ,mBAAmB,KAAK,iBAAiB,IAAI,OACvC,KAAK,iBAAiB,GACtB,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,GAAG,IAAI;oBACP,MAAM;wBACF,GAAG,KAAK,IAAI;wBACZ,OAAO;oBACX;gBACJ,CAAC;QACT;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,OAAO;QAC3C,OAAO,IAAI,UAAU,oBAAoB,CAAC;YACtC,GAAG,KAAK,QAAQ,EAAE;YAClB,MAAM;YACN,SAAS,KAAK,OAAO,IAAI,OACnB,KAAK,OAAO,GACZ;gBACE,GAAG,KAAK,OAAO;gBACf,MAAM;oBACF,GAAG,KAAK,OAAO,CAAC,IAAI;oBACpB,OAAO;gBACX;YACJ;YACJ,mBAAmB,KAAK,iBAAiB,IAAI,OACvC,KAAK,iBAAiB,GACtB,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,GAAG,IAAI;oBACP,MAAM;wBACF,GAAG,KAAK,IAAI;wBACZ,OAAO;oBACX;gBACJ,CAAC;QACT;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,WAAW,EAAE,OAAO;QACvC,OAAO,IAAI,UAAU,gBAAgB,CAAC;YAClC,GAAG,KAAK,QAAQ,EAAE;YAClB,MAAM;YACN,SAAS,KAAK,OAAO,IAAI,OACnB,KAAK,OAAO,GACZ;gBACE,GAAG,KAAK,OAAO;gBACf,MAAM;oBACF,GAAG,KAAK,OAAO,CAAC,IAAI;oBACpB,OAAO;gBACX;YACJ;YACJ,mBAAmB,KAAK,iBAAiB,IAAI,OACvC,KAAK,iBAAiB,GACtB,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,GAAG,IAAI;oBACP,MAAM;wBACF,GAAG,KAAK,IAAI;wBACZ,OAAO;oBACX;gBACJ,CAAC;QACT;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,OAAO;QAC7C,OAAO,IAAI,UAAU,sBAAsB,CAAC;YACxC,GAAG,KAAK,QAAQ,EAAE;YAClB,MAAM;YACN,SAAS,KAAK,OAAO,IAAI,OACnB,KAAK,OAAO,GACZ;gBACE,GAAG,KAAK,OAAO;gBACf,MAAM;oBACF,GAAG,KAAK,OAAO,CAAC,IAAI;oBACpB,OAAO;gBACX;YACJ;YACJ,mBAAmB,KAAK,iBAAiB,IAAI,OACvC,KAAK,iBAAiB,GACtB,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,GAAG,IAAI;oBACP,MAAM;wBACF,GAAG,KAAK,IAAI;wBACZ,OAAO;oBACX;gBACJ,CAAC;QACT;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,OAAO;QACtC,OAAO,IAAI,UAAU,eAAe,CAAC;YACjC,GAAG,KAAK,QAAQ,EAAE;YAClB,MAAM;YACN,SAAS,KAAK,OAAO,IAAI,OACnB,KAAK,OAAO,GACZ;gBACE,GAAG,KAAK,OAAO;gBACf,MAAM;oBACF,GAAG,KAAK,OAAO,CAAC,IAAI;oBACpB,OAAO;gBACX;YACJ;YACJ,mBAAmB,KAAK,iBAAiB,IAAI,OACvC,KAAK,iBAAiB,GACtB,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,GAAG,IAAI;oBACP,MAAM;wBACF,GAAG,KAAK,IAAI;wBACZ,OAAO;oBACX;gBACJ,CAAC;QACT;IACJ,OACK,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,OAAO;QACxC,OAAO,IAAI,UAAU,iBAAiB,CAAC;YACnC,GAAG,KAAK,QAAQ,EAAE;YAClB,MAAM;YACN,SAAS,KAAK,OAAO,IAAI,OACnB,KAAK,OAAO,GACZ;gBACE,GAAG,KAAK,OAAO;gBACf,MAAM;oBACF,GAAG,KAAK,OAAO,CAAC,IAAI;oBACpB,OAAO;gBACX;YACJ;YACJ,mBAAmB,KAAK,iBAAiB,IAAI,OACvC,KAAK,iBAAiB,GACtB,KAAK,iBAAiB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,GAAG,IAAI;oBACP,MAAM;wBACF,GAAG,KAAK,IAAI;wBACZ,OAAO;oBACX;gBACJ,CAAC;QACT;IACJ;IACA,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/updateArgument.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.updateArgument = updateArgument;\nexports.createVariableNameGenerator = createVariableNameGenerator;\nconst graphql_1 = require(\"graphql\");\nconst astFromType_js_1 = require(\"./astFromType.js\");\nfunction updateArgument(argumentNodes, variableDefinitionsMap, variableValues, argName, varName, type, value) {\n    argumentNodes[argName] = {\n        kind: graphql_1.Kind.ARGUMENT,\n        name: {\n            kind: graphql_1.Kind.NAME,\n            value: argName,\n        },\n        value: {\n            kind: graphql_1.Kind.VARIABLE,\n            name: {\n                kind: graphql_1.Kind.NAME,\n                value: varName,\n            },\n        },\n    };\n    variableDefinitionsMap[varName] = {\n        kind: graphql_1.Kind.VARIABLE_DEFINITION,\n        variable: {\n            kind: graphql_1.Kind.VARIABLE,\n            name: {\n                kind: graphql_1.Kind.NAME,\n                value: varName,\n            },\n        },\n        type: (0, astFromType_js_1.astFromType)(type),\n    };\n    if (value !== undefined) {\n        variableValues[varName] = value;\n        return;\n    }\n    // including the variable in the map with value of `undefined`\n    // will actually be translated by graphql-js into `null`\n    // see https://github.com/graphql/graphql-js/issues/2533\n    if (varName in variableValues) {\n        delete variableValues[varName];\n    }\n}\nfunction createVariableNameGenerator(variableDefinitionMap) {\n    let varCounter = 0;\n    return (argName) => {\n        let varName;\n        do {\n            varName = varCounter === 0 ? argName : `_v${varCounter.toString()}_${argName}`;\n            varCounter++;\n        } while (varName in variableDefinitionMap);\n        return varName;\n    };\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG;AACzB,QAAQ,2BAA2B,GAAG;AACtC,MAAM;AACN,MAAM;AACN,SAAS,eAAe,aAAa,EAAE,sBAAsB,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK;IACxG,aAAa,CAAC,QAAQ,GAAG;QACrB,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM;YACF,MAAM,UAAU,IAAI,CAAC,IAAI;YACzB,OAAO;QACX;QACA,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM;gBACF,MAAM,UAAU,IAAI,CAAC,IAAI;gBACzB,OAAO;YACX;QACJ;IACJ;IACA,sBAAsB,CAAC,QAAQ,GAAG;QAC9B,MAAM,UAAU,IAAI,CAAC,mBAAmB;QACxC,UAAU;YACN,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM;gBACF,MAAM,UAAU,IAAI,CAAC,IAAI;gBACzB,OAAO;YACX;QACJ;QACA,MAAM,CAAC,GAAG,iBAAiB,WAAW,EAAE;IAC5C;IACA,IAAI,UAAU,WAAW;QACrB,cAAc,CAAC,QAAQ,GAAG;QAC1B;IACJ;IACA,8DAA8D;IAC9D,wDAAwD;IACxD,wDAAwD;IACxD,IAAI,WAAW,gBAAgB;QAC3B,OAAO,cAAc,CAAC,QAAQ;IAClC;AACJ;AACA,SAAS,4BAA4B,qBAAqB;IACtD,IAAI,aAAa;IACjB,OAAO,CAAC;QACJ,IAAI;QACJ,GAAG;YACC,UAAU,eAAe,IAAI,UAAU,CAAC,EAAE,EAAE,WAAW,QAAQ,GAAG,CAAC,EAAE,SAAS;YAC9E;QACJ,QAAS,WAAW,sBAAuB;QAC3C,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/implementsAbstractType.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.implementsAbstractType = implementsAbstractType;\nconst graphql_1 = require(\"graphql\");\nfunction implementsAbstractType(schema, typeA, typeB) {\n    if (typeB == null || typeA == null) {\n        return false;\n    }\n    else if (typeA === typeB) {\n        return true;\n    }\n    else if ((0, graphql_1.isCompositeType)(typeA) && (0, graphql_1.isCompositeType)(typeB)) {\n        return (0, graphql_1.doTypesOverlap)(schema, typeA, typeB);\n    }\n    return false;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG;AACjC,MAAM;AACN,SAAS,uBAAuB,MAAM,EAAE,KAAK,EAAE,KAAK;IAChD,IAAI,SAAS,QAAQ,SAAS,MAAM;QAChC,OAAO;IACX,OACK,IAAI,UAAU,OAAO;QACtB,OAAO;IACX,OACK,IAAI,CAAC,GAAG,UAAU,eAAe,EAAE,UAAU,CAAC,GAAG,UAAU,eAAe,EAAE,QAAQ;QACrF,OAAO,CAAC,GAAG,UAAU,cAAc,EAAE,QAAQ,OAAO;IACxD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/observableToAsyncIterable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.observableToAsyncIterable = observableToAsyncIterable;\nconst promise_helpers_1 = require(\"@whatwg-node/promise-helpers\");\nfunction observableToAsyncIterable(observable) {\n    const pullQueue = [];\n    const pushQueue = [];\n    let listening = true;\n    const pushValue = (value) => {\n        if (pullQueue.length !== 0) {\n            // It is safe to use the ! operator here as we check the length.\n            pullQueue.shift()({ value, done: false });\n        }\n        else {\n            pushQueue.push({ value, done: false });\n        }\n    };\n    const pushError = (error) => {\n        if (pullQueue.length !== 0) {\n            // It is safe to use the ! operator here as we check the length.\n            pullQueue.shift()({ value: { errors: [error] }, done: false });\n        }\n        else {\n            pushQueue.push({ value: { errors: [error] }, done: false });\n        }\n    };\n    const pushDone = () => {\n        if (pullQueue.length !== 0) {\n            // It is safe to use the ! operator here as we check the length.\n            pullQueue.shift()({ done: true });\n        }\n        else {\n            pushQueue.push({ done: true });\n        }\n    };\n    const pullValue = () => new Promise(resolve => {\n        if (pushQueue.length !== 0) {\n            const element = pushQueue.shift();\n            // either {value: {errors: [...]}} or {value: ...}\n            resolve(element);\n        }\n        else {\n            pullQueue.push(resolve);\n        }\n    });\n    const subscription = observable.subscribe({\n        next(value) {\n            return pushValue(value);\n        },\n        error(err) {\n            return pushError(err);\n        },\n        complete() {\n            return pushDone();\n        },\n    });\n    const emptyQueue = () => {\n        if (listening) {\n            listening = false;\n            subscription.unsubscribe();\n            for (const resolve of pullQueue) {\n                resolve({ value: undefined, done: true });\n            }\n            pullQueue.length = 0;\n            pushQueue.length = 0;\n        }\n    };\n    return {\n        next() {\n            // return is a defined method, so it is safe to call it.\n            return listening ? pullValue() : this.return();\n        },\n        return() {\n            emptyQueue();\n            return (0, promise_helpers_1.fakePromise)({ value: undefined, done: true });\n        },\n        throw(error) {\n            emptyQueue();\n            return (0, promise_helpers_1.fakeRejectPromise)(error);\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG;AACpC,MAAM;AACN,SAAS,0BAA0B,UAAU;IACzC,MAAM,YAAY,EAAE;IACpB,MAAM,YAAY,EAAE;IACpB,IAAI,YAAY;IAChB,MAAM,YAAY,CAAC;QACf,IAAI,UAAU,MAAM,KAAK,GAAG;YACxB,gEAAgE;YAChE,UAAU,KAAK,GAAG;gBAAE;gBAAO,MAAM;YAAM;QAC3C,OACK;YACD,UAAU,IAAI,CAAC;gBAAE;gBAAO,MAAM;YAAM;QACxC;IACJ;IACA,MAAM,YAAY,CAAC;QACf,IAAI,UAAU,MAAM,KAAK,GAAG;YACxB,gEAAgE;YAChE,UAAU,KAAK,GAAG;gBAAE,OAAO;oBAAE,QAAQ;wBAAC;qBAAM;gBAAC;gBAAG,MAAM;YAAM;QAChE,OACK;YACD,UAAU,IAAI,CAAC;gBAAE,OAAO;oBAAE,QAAQ;wBAAC;qBAAM;gBAAC;gBAAG,MAAM;YAAM;QAC7D;IACJ;IACA,MAAM,WAAW;QACb,IAAI,UAAU,MAAM,KAAK,GAAG;YACxB,gEAAgE;YAChE,UAAU,KAAK,GAAG;gBAAE,MAAM;YAAK;QACnC,OACK;YACD,UAAU,IAAI,CAAC;gBAAE,MAAM;YAAK;QAChC;IACJ;IACA,MAAM,YAAY,IAAM,IAAI,QAAQ,CAAA;YAChC,IAAI,UAAU,MAAM,KAAK,GAAG;gBACxB,MAAM,UAAU,UAAU,KAAK;gBAC/B,kDAAkD;gBAClD,QAAQ;YACZ,OACK;gBACD,UAAU,IAAI,CAAC;YACnB;QACJ;IACA,MAAM,eAAe,WAAW,SAAS,CAAC;QACtC,MAAK,KAAK;YACN,OAAO,UAAU;QACrB;QACA,OAAM,GAAG;YACL,OAAO,UAAU;QACrB;QACA;YACI,OAAO;QACX;IACJ;IACA,MAAM,aAAa;QACf,IAAI,WAAW;YACX,YAAY;YACZ,aAAa,WAAW;YACxB,KAAK,MAAM,WAAW,UAAW;gBAC7B,QAAQ;oBAAE,OAAO;oBAAW,MAAM;gBAAK;YAC3C;YACA,UAAU,MAAM,GAAG;YACnB,UAAU,MAAM,GAAG;QACvB;IACJ;IACA,OAAO;QACH;YACI,wDAAwD;YACxD,OAAO,YAAY,cAAc,IAAI,CAAC,MAAM;QAChD;QACA;YACI;YACA,OAAO,CAAC,GAAG,kBAAkB,WAAW,EAAE;gBAAE,OAAO;gBAAW,MAAM;YAAK;QAC7E;QACA,OAAM,KAAK;YACP;YACA,OAAO,CAAC,GAAG,kBAAkB,iBAAiB,EAAE;QACpD;QACA,CAAC,OAAO,aAAa,CAAC;YAClB,OAAO,IAAI;QACf;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7479, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/AccumulatorMap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AccumulatorMap = void 0;\n/**\n * ES6 Map with additional `add` method to accumulate items.\n */\nclass AccumulatorMap extends Map {\n    get [Symbol.toStringTag]() {\n        return 'AccumulatorMap';\n    }\n    add(key, item) {\n        const group = this.get(key);\n        if (group === undefined) {\n            this.set(key, [item]);\n        }\n        else {\n            group.push(item);\n        }\n    }\n}\nexports.AccumulatorMap = AccumulatorMap;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B;;CAEC,GACD,MAAM,uBAAuB;IACzB,IAAI,CAAC,OAAO,WAAW,CAAC,GAAG;QACvB,OAAO;IACX;IACA,IAAI,GAAG,EAAE,IAAI,EAAE;QACX,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC;QACvB,IAAI,UAAU,WAAW;YACrB,IAAI,CAAC,GAAG,CAAC,KAAK;gBAAC;aAAK;QACxB,OACK;YACD,MAAM,IAAI,CAAC;QACf;IACJ;AACJ;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7505, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GraphQLStreamDirective = exports.GraphQLDeferDirective = void 0;\nconst graphql_1 = require(\"graphql\");\n/**\n * Used to conditionally defer fragments.\n */\nexports.GraphQLDeferDirective = new graphql_1.GraphQLDirective({\n    name: 'defer',\n    description: 'Directs the executor to defer this fragment when the `if` argument is true or undefined.',\n    locations: [graphql_1.DirectiveLocation.FRAGMENT_SPREAD, graphql_1.DirectiveLocation.INLINE_FRAGMENT],\n    args: {\n        if: {\n            type: new graphql_1.GraphQLNonNull(graphql_1.GraphQLBoolean),\n            description: 'Deferred when true or undefined.',\n            defaultValue: true,\n        },\n        label: {\n            type: graphql_1.GraphQLString,\n            description: 'Unique name',\n        },\n    },\n});\n/**\n * Used to conditionally stream list fields.\n */\nexports.GraphQLStreamDirective = new graphql_1.GraphQLDirective({\n    name: 'stream',\n    description: 'Directs the executor to stream plural fields when the `if` argument is true or undefined.',\n    locations: [graphql_1.DirectiveLocation.FIELD],\n    args: {\n        if: {\n            type: new graphql_1.GraphQLNonNull(graphql_1.GraphQLBoolean),\n            description: 'Stream when true or undefined.',\n            defaultValue: true,\n        },\n        label: {\n            type: graphql_1.GraphQLString,\n            description: 'Unique name',\n        },\n        initialCount: {\n            defaultValue: 0,\n            type: graphql_1.GraphQLInt,\n            description: 'Number of items to return immediately',\n        },\n    },\n});\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG,QAAQ,qBAAqB,GAAG,KAAK;AACtE,MAAM;AACN;;CAEC,GACD,QAAQ,qBAAqB,GAAG,IAAI,UAAU,gBAAgB,CAAC;IAC3D,MAAM;IACN,aAAa;IACb,WAAW;QAAC,UAAU,iBAAiB,CAAC,eAAe;QAAE,UAAU,iBAAiB,CAAC,eAAe;KAAC;IACrG,MAAM;QACF,IAAI;YACA,MAAM,IAAI,UAAU,cAAc,CAAC,UAAU,cAAc;YAC3D,aAAa;YACb,cAAc;QAClB;QACA,OAAO;YACH,MAAM,UAAU,aAAa;YAC7B,aAAa;QACjB;IACJ;AACJ;AACA;;CAEC,GACD,QAAQ,sBAAsB,GAAG,IAAI,UAAU,gBAAgB,CAAC;IAC5D,MAAM;IACN,aAAa;IACb,WAAW;QAAC,UAAU,iBAAiB,CAAC,KAAK;KAAC;IAC9C,MAAM;QACF,IAAI;YACA,MAAM,IAAI,UAAU,cAAc,CAAC,UAAU,cAAc;YAC3D,aAAa;YACb,cAAc;QAClB;QACA,OAAO;YACH,MAAM,UAAU,aAAa;YAC7B,aAAa;QACjB;QACA,cAAc;YACV,cAAc;YACd,MAAM,UAAU,UAAU;YAC1B,aAAa;QACjB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/collectFields.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.collectSubFields = void 0;\nexports.collectFields = collectFields;\nexports.shouldIncludeNode = shouldIncludeNode;\nexports.doesFragmentConditionMatch = doesFragmentConditionMatch;\nexports.getFieldEntryKey = getFieldEntryKey;\nexports.getDeferValues = getDeferValues;\nconst graphql_1 = require(\"graphql\");\nconst AccumulatorMap_js_1 = require(\"./AccumulatorMap.js\");\nconst directives_js_1 = require(\"./directives.js\");\nconst memoize_js_1 = require(\"./memoize.js\");\nfunction collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, visitedFragmentNames) {\n    for (const selection of selectionSet.selections) {\n        switch (selection.kind) {\n            case graphql_1.Kind.FIELD: {\n                if (!shouldIncludeNode(variableValues, selection)) {\n                    continue;\n                }\n                fields.add(getFieldEntryKey(selection), selection);\n                break;\n            }\n            case graphql_1.Kind.INLINE_FRAGMENT: {\n                if (!shouldIncludeNode(variableValues, selection) ||\n                    !doesFragmentConditionMatch(schema, selection, runtimeType)) {\n                    continue;\n                }\n                const defer = getDeferValues(variableValues, selection);\n                if (defer) {\n                    const patchFields = new AccumulatorMap_js_1.AccumulatorMap();\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, patchFields, patches, visitedFragmentNames);\n                    patches.push({\n                        label: defer.label,\n                        fields: patchFields,\n                    });\n                }\n                else {\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selection.selectionSet, fields, patches, visitedFragmentNames);\n                }\n                break;\n            }\n            case graphql_1.Kind.FRAGMENT_SPREAD: {\n                const fragName = selection.name.value;\n                if (!shouldIncludeNode(variableValues, selection)) {\n                    continue;\n                }\n                const defer = getDeferValues(variableValues, selection);\n                if (visitedFragmentNames.has(fragName) && !defer) {\n                    continue;\n                }\n                const fragment = fragments[fragName];\n                if (!fragment || !doesFragmentConditionMatch(schema, fragment, runtimeType)) {\n                    continue;\n                }\n                if (!defer) {\n                    visitedFragmentNames.add(fragName);\n                }\n                if (defer) {\n                    const patchFields = new AccumulatorMap_js_1.AccumulatorMap();\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, patchFields, patches, visitedFragmentNames);\n                    patches.push({\n                        label: defer.label,\n                        fields: patchFields,\n                    });\n                }\n                else {\n                    collectFieldsImpl(schema, fragments, variableValues, runtimeType, fragment.selectionSet, fields, patches, visitedFragmentNames);\n                }\n                break;\n            }\n        }\n    }\n}\n/**\n * Given a selectionSet, collects all of the fields and returns them.\n *\n * CollectFields requires the \"runtime type\" of an object. For a field that\n * returns an Interface or Union type, the \"runtime type\" will be the actual\n * object type returned by that field.\n *\n */\nfunction collectFields(schema, fragments, variableValues, runtimeType, selectionSet) {\n    const fields = new AccumulatorMap_js_1.AccumulatorMap();\n    const patches = [];\n    collectFieldsImpl(schema, fragments, variableValues, runtimeType, selectionSet, fields, patches, new Set());\n    return { fields, patches };\n}\n/**\n * Determines if a field should be included based on the `@include` and `@skip`\n * directives, where `@skip` has higher precedence than `@include`.\n */\nfunction shouldIncludeNode(variableValues, node) {\n    const skip = (0, graphql_1.getDirectiveValues)(graphql_1.GraphQLSkipDirective, node, variableValues);\n    if (skip?.['if'] === true) {\n        return false;\n    }\n    const include = (0, graphql_1.getDirectiveValues)(graphql_1.GraphQLIncludeDirective, node, variableValues);\n    if (include?.['if'] === false) {\n        return false;\n    }\n    return true;\n}\n/**\n * Determines if a fragment is applicable to the given type.\n */\nfunction doesFragmentConditionMatch(schema, fragment, type) {\n    const typeConditionNode = fragment.typeCondition;\n    if (!typeConditionNode) {\n        return true;\n    }\n    const conditionalType = (0, graphql_1.typeFromAST)(schema, typeConditionNode);\n    if (conditionalType === type) {\n        return true;\n    }\n    if ((0, graphql_1.isAbstractType)(conditionalType)) {\n        const possibleTypes = schema.getPossibleTypes(conditionalType);\n        return possibleTypes.includes(type);\n    }\n    return false;\n}\n/**\n * Implements the logic to compute the key of a given field's entry\n */\nfunction getFieldEntryKey(node) {\n    return node.alias ? node.alias.value : node.name.value;\n}\n/**\n * Returns an object containing the `@defer` arguments if a field should be\n * deferred based on the experimental flag, defer directive present and\n * not disabled by the \"if\" argument.\n */\nfunction getDeferValues(variableValues, node) {\n    const defer = (0, graphql_1.getDirectiveValues)(directives_js_1.GraphQLDeferDirective, node, variableValues);\n    if (!defer) {\n        return;\n    }\n    if (defer['if'] === false) {\n        return;\n    }\n    return {\n        label: typeof defer['label'] === 'string' ? defer['label'] : undefined,\n    };\n}\n/**\n * Given an array of field nodes, collects all of the subfields of the passed\n * in fields, and returns them at the end.\n *\n * CollectSubFields requires the \"return type\" of an object. For a field that\n * returns an Interface or Union type, the \"return type\" will be the actual\n * object type returned by that field.\n *\n */\nexports.collectSubFields = (0, memoize_js_1.memoize5)(function collectSubfields(schema, fragments, variableValues, returnType, fieldNodes) {\n    const subFieldNodes = new AccumulatorMap_js_1.AccumulatorMap();\n    const visitedFragmentNames = new Set();\n    const subPatches = [];\n    const subFieldsAndPatches = {\n        fields: subFieldNodes,\n        patches: subPatches,\n    };\n    for (const node of fieldNodes) {\n        if (node.selectionSet) {\n            collectFieldsImpl(schema, fragments, variableValues, returnType, node.selectionSet, subFieldNodes, subPatches, visitedFragmentNames);\n        }\n    }\n    return subFieldsAndPatches;\n});\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,QAAQ,aAAa,GAAG;AACxB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,0BAA0B,GAAG;AACrC,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,kBAAkB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB;IAC1H,KAAK,MAAM,aAAa,aAAa,UAAU,CAAE;QAC7C,OAAQ,UAAU,IAAI;YAClB,KAAK,UAAU,IAAI,CAAC,KAAK;gBAAE;oBACvB,IAAI,CAAC,kBAAkB,gBAAgB,YAAY;wBAC/C;oBACJ;oBACA,OAAO,GAAG,CAAC,iBAAiB,YAAY;oBACxC;gBACJ;YACA,KAAK,UAAU,IAAI,CAAC,eAAe;gBAAE;oBACjC,IAAI,CAAC,kBAAkB,gBAAgB,cACnC,CAAC,2BAA2B,QAAQ,WAAW,cAAc;wBAC7D;oBACJ;oBACA,MAAM,QAAQ,eAAe,gBAAgB;oBAC7C,IAAI,OAAO;wBACP,MAAM,cAAc,IAAI,oBAAoB,cAAc;wBAC1D,kBAAkB,QAAQ,WAAW,gBAAgB,aAAa,UAAU,YAAY,EAAE,aAAa,SAAS;wBAChH,QAAQ,IAAI,CAAC;4BACT,OAAO,MAAM,KAAK;4BAClB,QAAQ;wBACZ;oBACJ,OACK;wBACD,kBAAkB,QAAQ,WAAW,gBAAgB,aAAa,UAAU,YAAY,EAAE,QAAQ,SAAS;oBAC/G;oBACA;gBACJ;YACA,KAAK,UAAU,IAAI,CAAC,eAAe;gBAAE;oBACjC,MAAM,WAAW,UAAU,IAAI,CAAC,KAAK;oBACrC,IAAI,CAAC,kBAAkB,gBAAgB,YAAY;wBAC/C;oBACJ;oBACA,MAAM,QAAQ,eAAe,gBAAgB;oBAC7C,IAAI,qBAAqB,GAAG,CAAC,aAAa,CAAC,OAAO;wBAC9C;oBACJ;oBACA,MAAM,WAAW,SAAS,CAAC,SAAS;oBACpC,IAAI,CAAC,YAAY,CAAC,2BAA2B,QAAQ,UAAU,cAAc;wBACzE;oBACJ;oBACA,IAAI,CAAC,OAAO;wBACR,qBAAqB,GAAG,CAAC;oBAC7B;oBACA,IAAI,OAAO;wBACP,MAAM,cAAc,IAAI,oBAAoB,cAAc;wBAC1D,kBAAkB,QAAQ,WAAW,gBAAgB,aAAa,SAAS,YAAY,EAAE,aAAa,SAAS;wBAC/G,QAAQ,IAAI,CAAC;4BACT,OAAO,MAAM,KAAK;4BAClB,QAAQ;wBACZ;oBACJ,OACK;wBACD,kBAAkB,QAAQ,WAAW,gBAAgB,aAAa,SAAS,YAAY,EAAE,QAAQ,SAAS;oBAC9G;oBACA;gBACJ;QACJ;IACJ;AACJ;AACA;;;;;;;CAOC,GACD,SAAS,cAAc,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY;IAC/E,MAAM,SAAS,IAAI,oBAAoB,cAAc;IACrD,MAAM,UAAU,EAAE;IAClB,kBAAkB,QAAQ,WAAW,gBAAgB,aAAa,cAAc,QAAQ,SAAS,IAAI;IACrG,OAAO;QAAE;QAAQ;IAAQ;AAC7B;AACA;;;CAGC,GACD,SAAS,kBAAkB,cAAc,EAAE,IAAI;IAC3C,MAAM,OAAO,CAAC,GAAG,UAAU,kBAAkB,EAAE,UAAU,oBAAoB,EAAE,MAAM;IACrF,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM;QACvB,OAAO;IACX;IACA,MAAM,UAAU,CAAC,GAAG,UAAU,kBAAkB,EAAE,UAAU,uBAAuB,EAAE,MAAM;IAC3F,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO;QAC3B,OAAO;IACX;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,2BAA2B,MAAM,EAAE,QAAQ,EAAE,IAAI;IACtD,MAAM,oBAAoB,SAAS,aAAa;IAChD,IAAI,CAAC,mBAAmB;QACpB,OAAO;IACX;IACA,MAAM,kBAAkB,CAAC,GAAG,UAAU,WAAW,EAAE,QAAQ;IAC3D,IAAI,oBAAoB,MAAM;QAC1B,OAAO;IACX;IACA,IAAI,CAAC,GAAG,UAAU,cAAc,EAAE,kBAAkB;QAChD,MAAM,gBAAgB,OAAO,gBAAgB,CAAC;QAC9C,OAAO,cAAc,QAAQ,CAAC;IAClC;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,iBAAiB,IAAI;IAC1B,OAAO,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK;AAC1D;AACA;;;;CAIC,GACD,SAAS,eAAe,cAAc,EAAE,IAAI;IACxC,MAAM,QAAQ,CAAC,GAAG,UAAU,kBAAkB,EAAE,gBAAgB,qBAAqB,EAAE,MAAM;IAC7F,IAAI,CAAC,OAAO;QACR;IACJ;IACA,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO;QACvB;IACJ;IACA,OAAO;QACH,OAAO,OAAO,KAAK,CAAC,QAAQ,KAAK,WAAW,KAAK,CAAC,QAAQ,GAAG;IACjE;AACJ;AACA;;;;;;;;CAQC,GACD,QAAQ,gBAAgB,GAAG,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,iBAAiB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU;IACrI,MAAM,gBAAgB,IAAI,oBAAoB,cAAc;IAC5D,MAAM,uBAAuB,IAAI;IACjC,MAAM,aAAa,EAAE;IACrB,MAAM,sBAAsB;QACxB,QAAQ;QACR,SAAS;IACb;IACA,KAAK,MAAM,QAAQ,WAAY;QAC3B,IAAI,KAAK,YAAY,EAAE;YACnB,kBAAkB,QAAQ,WAAW,gBAAgB,YAAY,KAAK,YAAY,EAAE,eAAe,YAAY;QACnH;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/getOperationASTFromRequest.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getOperationASTFromRequest = void 0;\nexports.getOperationASTFromDocument = getOperationASTFromDocument;\nconst graphql_1 = require(\"graphql\");\nconst memoize_js_1 = require(\"./memoize.js\");\nfunction getOperationASTFromDocument(documentNode, operationName) {\n    const doc = (0, graphql_1.getOperationAST)(documentNode, operationName);\n    if (!doc) {\n        throw new Error(`Cannot infer operation ${operationName || ''}`);\n    }\n    return doc;\n}\nexports.getOperationASTFromRequest = (0, memoize_js_1.memoize1)(function getOperationASTFromRequest(request) {\n    return getOperationASTFromDocument(request.document, request.operationName);\n});\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,0BAA0B,GAAG,KAAK;AAC1C,QAAQ,2BAA2B,GAAG;AACtC,MAAM;AACN,MAAM;AACN,SAAS,4BAA4B,YAAY,EAAE,aAAa;IAC5D,MAAM,MAAM,CAAC,GAAG,UAAU,eAAe,EAAE,cAAc;IACzD,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,iBAAiB,IAAI;IACnE;IACA,OAAO;AACX;AACA,QAAQ,0BAA0B,GAAG,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,2BAA2B,OAAO;IACvG,OAAO,4BAA4B,QAAQ,QAAQ,EAAE,QAAQ,aAAa;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7750, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/visitResult.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.visitData = visitData;\nexports.visitErrors = visitErrors;\nexports.visitResult = visitResult;\nconst graphql_1 = require(\"graphql\");\nconst collectFields_js_1 = require(\"./collectFields.js\");\nconst getOperationASTFromRequest_js_1 = require(\"./getOperationASTFromRequest.js\");\nfunction visitData(data, enter, leave) {\n    if (Array.isArray(data)) {\n        return data.map(value => visitData(value, enter, leave));\n    }\n    else if (typeof data === 'object') {\n        const newData = enter != null ? enter(data) : data;\n        if (newData != null) {\n            for (const key in newData) {\n                const value = newData[key];\n                Object.defineProperty(newData, key, {\n                    value: visitData(value, enter, leave),\n                });\n            }\n        }\n        return leave != null ? leave(newData) : newData;\n    }\n    return data;\n}\nfunction visitErrors(errors, visitor) {\n    return errors.map(error => visitor(error));\n}\nfunction visitResult(result, request, schema, resultVisitorMap, errorVisitorMap) {\n    const fragments = request.document.definitions.reduce((acc, def) => {\n        if (def.kind === graphql_1.Kind.FRAGMENT_DEFINITION) {\n            acc[def.name.value] = def;\n        }\n        return acc;\n    }, {});\n    const variableValues = request.variables || {};\n    const errorInfo = {\n        segmentInfoMap: new Map(),\n        unpathedErrors: new Set(),\n    };\n    const data = result.data;\n    const errors = result.errors;\n    const visitingErrors = errors != null && errorVisitorMap != null;\n    const operationDocumentNode = (0, getOperationASTFromRequest_js_1.getOperationASTFromRequest)(request);\n    if (data != null && operationDocumentNode != null) {\n        result.data = visitRoot(data, operationDocumentNode, schema, fragments, variableValues, resultVisitorMap, visitingErrors ? errors : undefined, errorInfo);\n    }\n    if (errors != null && errorVisitorMap) {\n        result.errors = visitErrorsByType(errors, errorVisitorMap, errorInfo);\n    }\n    return result;\n}\nfunction visitErrorsByType(errors, errorVisitorMap, errorInfo) {\n    const segmentInfoMap = errorInfo.segmentInfoMap;\n    const unpathedErrors = errorInfo.unpathedErrors;\n    const unpathedErrorVisitor = errorVisitorMap['__unpathed'];\n    return errors.map(originalError => {\n        const pathSegmentsInfo = segmentInfoMap.get(originalError);\n        const newError = pathSegmentsInfo == null\n            ? originalError\n            : pathSegmentsInfo.reduceRight((acc, segmentInfo) => {\n                const typeName = segmentInfo.type.name;\n                const typeVisitorMap = errorVisitorMap[typeName];\n                if (typeVisitorMap == null) {\n                    return acc;\n                }\n                const errorVisitor = typeVisitorMap[segmentInfo.fieldName];\n                return errorVisitor == null ? acc : errorVisitor(acc, segmentInfo.pathIndex);\n            }, originalError);\n        if (unpathedErrorVisitor && unpathedErrors.has(originalError)) {\n            return unpathedErrorVisitor(newError);\n        }\n        return newError;\n    });\n}\nfunction getOperationRootType(schema, operationDef) {\n    switch (operationDef.operation) {\n        case 'query':\n            return schema.getQueryType();\n        case 'mutation':\n            return schema.getMutationType();\n        case 'subscription':\n            return schema.getSubscriptionType();\n    }\n}\nfunction visitRoot(root, operation, schema, fragments, variableValues, resultVisitorMap, errors, errorInfo) {\n    const operationRootType = getOperationRootType(schema, operation);\n    const { fields: collectedFields } = (0, collectFields_js_1.collectFields)(schema, fragments, variableValues, operationRootType, operation.selectionSet);\n    return visitObjectValue(root, operationRootType, collectedFields, schema, fragments, variableValues, resultVisitorMap, 0, errors, errorInfo);\n}\nfunction visitObjectValue(object, type, fieldNodeMap, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo) {\n    const fieldMap = type.getFields();\n    const typeVisitorMap = resultVisitorMap?.[type.name];\n    const enterObject = typeVisitorMap?.__enter;\n    const newObject = enterObject != null ? enterObject(object) : object;\n    let sortedErrors;\n    let errorMap = null;\n    if (errors != null) {\n        sortedErrors = sortErrorsByPathSegment(errors, pathIndex);\n        errorMap = sortedErrors.errorMap;\n        for (const error of sortedErrors.unpathedErrors) {\n            errorInfo.unpathedErrors.add(error);\n        }\n    }\n    for (const [responseKey, subFieldNodes] of fieldNodeMap) {\n        const fieldName = subFieldNodes[0].name.value;\n        let fieldType = fieldMap[fieldName]?.type;\n        if (fieldType == null) {\n            switch (fieldName) {\n                case '__typename':\n                    fieldType = graphql_1.TypeNameMetaFieldDef.type;\n                    break;\n                case '__schema':\n                    fieldType = graphql_1.SchemaMetaFieldDef.type;\n                    break;\n                case '__type':\n                    fieldType = graphql_1.TypeMetaFieldDef.type;\n                    break;\n            }\n        }\n        const newPathIndex = pathIndex + 1;\n        let fieldErrors;\n        if (errorMap) {\n            fieldErrors = errorMap[responseKey];\n            if (fieldErrors != null) {\n                delete errorMap[responseKey];\n            }\n            addPathSegmentInfo(type, fieldName, newPathIndex, fieldErrors, errorInfo);\n        }\n        const newValue = visitFieldValue(object[responseKey], fieldType, subFieldNodes, schema, fragments, variableValues, resultVisitorMap, newPathIndex, fieldErrors, errorInfo);\n        updateObject(newObject, responseKey, newValue, typeVisitorMap, fieldName);\n    }\n    const oldTypename = newObject.__typename;\n    if (oldTypename != null) {\n        updateObject(newObject, '__typename', oldTypename, typeVisitorMap, '__typename');\n    }\n    if (errorMap) {\n        for (const errorsKey in errorMap) {\n            const errors = errorMap[errorsKey];\n            for (const error of errors) {\n                errorInfo.unpathedErrors.add(error);\n            }\n        }\n    }\n    const leaveObject = typeVisitorMap?.__leave;\n    return leaveObject != null ? leaveObject(newObject) : newObject;\n}\nfunction updateObject(object, responseKey, newValue, typeVisitorMap, fieldName) {\n    if (typeVisitorMap == null) {\n        object[responseKey] = newValue;\n        return;\n    }\n    const fieldVisitor = typeVisitorMap[fieldName];\n    if (fieldVisitor == null) {\n        object[responseKey] = newValue;\n        return;\n    }\n    const visitedValue = fieldVisitor(newValue);\n    if (visitedValue === undefined) {\n        delete object[responseKey];\n        return;\n    }\n    object[responseKey] = visitedValue;\n}\nfunction visitListValue(list, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo) {\n    return list.map(listMember => visitFieldValue(listMember, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex + 1, errors, errorInfo));\n}\nfunction visitFieldValue(value, returnType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors = [], errorInfo) {\n    if (value == null) {\n        return value;\n    }\n    const nullableType = (0, graphql_1.getNullableType)(returnType);\n    if ((0, graphql_1.isListType)(nullableType)) {\n        return visitListValue(value, nullableType.ofType, fieldNodes, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);\n    }\n    else if ((0, graphql_1.isAbstractType)(nullableType)) {\n        const finalType = schema.getType(value.__typename);\n        let { fields: collectedFields, patches } = (0, collectFields_js_1.collectSubFields)(schema, fragments, variableValues, finalType, fieldNodes);\n        if (patches.length) {\n            collectedFields = new Map(collectedFields);\n            for (const patch of patches) {\n                for (const [responseKey, fields] of patch.fields) {\n                    const existingFields = collectedFields.get(responseKey);\n                    if (existingFields) {\n                        existingFields.push(...fields);\n                    }\n                    else {\n                        collectedFields.set(responseKey, fields);\n                    }\n                }\n            }\n        }\n        return visitObjectValue(value, finalType, collectedFields, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);\n    }\n    else if ((0, graphql_1.isObjectType)(nullableType)) {\n        let { fields: collectedFields, patches } = (0, collectFields_js_1.collectSubFields)(schema, fragments, variableValues, nullableType, fieldNodes);\n        if (patches.length) {\n            collectedFields = new Map(collectedFields);\n            for (const patch of patches) {\n                for (const [responseKey, fields] of patch.fields) {\n                    const existingFields = collectedFields.get(responseKey);\n                    if (existingFields) {\n                        existingFields.push(...fields);\n                    }\n                    else {\n                        collectedFields.set(responseKey, fields);\n                    }\n                }\n            }\n        }\n        return visitObjectValue(value, nullableType, collectedFields, schema, fragments, variableValues, resultVisitorMap, pathIndex, errors, errorInfo);\n    }\n    const typeVisitorMap = resultVisitorMap?.[nullableType.name];\n    if (typeVisitorMap == null) {\n        return value;\n    }\n    const visitedValue = typeVisitorMap(value);\n    return visitedValue === undefined ? value : visitedValue;\n}\nfunction sortErrorsByPathSegment(errors, pathIndex) {\n    const errorMap = Object.create(null);\n    const unpathedErrors = new Set();\n    for (const error of errors) {\n        const pathSegment = error.path?.[pathIndex];\n        if (pathSegment == null) {\n            unpathedErrors.add(error);\n            continue;\n        }\n        if (pathSegment in errorMap) {\n            errorMap[pathSegment].push(error);\n        }\n        else {\n            errorMap[pathSegment] = [error];\n        }\n    }\n    return {\n        errorMap,\n        unpathedErrors,\n    };\n}\nfunction addPathSegmentInfo(type, fieldName, pathIndex, errors = [], errorInfo) {\n    for (const error of errors) {\n        const segmentInfo = {\n            type,\n            fieldName,\n            pathIndex,\n        };\n        const pathSegmentsInfo = errorInfo.segmentInfoMap.get(error);\n        if (pathSegmentsInfo == null) {\n            errorInfo.segmentInfoMap.set(error, [segmentInfo]);\n        }\n        else {\n            pathSegmentsInfo.push(segmentInfo);\n        }\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG;AACpB,QAAQ,WAAW,GAAG;AACtB,QAAQ,WAAW,GAAG;AACtB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,KAAK;IACjC,IAAI,MAAM,OAAO,CAAC,OAAO;QACrB,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,UAAU,OAAO,OAAO;IACrD,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,MAAM,UAAU,SAAS,OAAO,MAAM,QAAQ;QAC9C,IAAI,WAAW,MAAM;YACjB,IAAK,MAAM,OAAO,QAAS;gBACvB,MAAM,QAAQ,OAAO,CAAC,IAAI;gBAC1B,OAAO,cAAc,CAAC,SAAS,KAAK;oBAChC,OAAO,UAAU,OAAO,OAAO;gBACnC;YACJ;QACJ;QACA,OAAO,SAAS,OAAO,MAAM,WAAW;IAC5C;IACA,OAAO;AACX;AACA,SAAS,YAAY,MAAM,EAAE,OAAO;IAChC,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,QAAQ;AACvC;AACA,SAAS,YAAY,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe;IAC3E,MAAM,YAAY,QAAQ,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK;QACxD,IAAI,IAAI,IAAI,KAAK,UAAU,IAAI,CAAC,mBAAmB,EAAE;YACjD,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG;QAC1B;QACA,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,iBAAiB,QAAQ,SAAS,IAAI,CAAC;IAC7C,MAAM,YAAY;QACd,gBAAgB,IAAI;QACpB,gBAAgB,IAAI;IACxB;IACA,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,iBAAiB,UAAU,QAAQ,mBAAmB;IAC5D,MAAM,wBAAwB,CAAC,GAAG,gCAAgC,0BAA0B,EAAE;IAC9F,IAAI,QAAQ,QAAQ,yBAAyB,MAAM;QAC/C,OAAO,IAAI,GAAG,UAAU,MAAM,uBAAuB,QAAQ,WAAW,gBAAgB,kBAAkB,iBAAiB,SAAS,WAAW;IACnJ;IACA,IAAI,UAAU,QAAQ,iBAAiB;QACnC,OAAO,MAAM,GAAG,kBAAkB,QAAQ,iBAAiB;IAC/D;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,MAAM,EAAE,eAAe,EAAE,SAAS;IACzD,MAAM,iBAAiB,UAAU,cAAc;IAC/C,MAAM,iBAAiB,UAAU,cAAc;IAC/C,MAAM,uBAAuB,eAAe,CAAC,aAAa;IAC1D,OAAO,OAAO,GAAG,CAAC,CAAA;QACd,MAAM,mBAAmB,eAAe,GAAG,CAAC;QAC5C,MAAM,WAAW,oBAAoB,OAC/B,gBACA,iBAAiB,WAAW,CAAC,CAAC,KAAK;YACjC,MAAM,WAAW,YAAY,IAAI,CAAC,IAAI;YACtC,MAAM,iBAAiB,eAAe,CAAC,SAAS;YAChD,IAAI,kBAAkB,MAAM;gBACxB,OAAO;YACX;YACA,MAAM,eAAe,cAAc,CAAC,YAAY,SAAS,CAAC;YAC1D,OAAO,gBAAgB,OAAO,MAAM,aAAa,KAAK,YAAY,SAAS;QAC/E,GAAG;QACP,IAAI,wBAAwB,eAAe,GAAG,CAAC,gBAAgB;YAC3D,OAAO,qBAAqB;QAChC;QACA,OAAO;IACX;AACJ;AACA,SAAS,qBAAqB,MAAM,EAAE,YAAY;IAC9C,OAAQ,aAAa,SAAS;QAC1B,KAAK;YACD,OAAO,OAAO,YAAY;QAC9B,KAAK;YACD,OAAO,OAAO,eAAe;QACjC,KAAK;YACD,OAAO,OAAO,mBAAmB;IACzC;AACJ;AACA,SAAS,UAAU,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS;IACtG,MAAM,oBAAoB,qBAAqB,QAAQ;IACvD,MAAM,EAAE,QAAQ,eAAe,EAAE,GAAG,CAAC,GAAG,mBAAmB,aAAa,EAAE,QAAQ,WAAW,gBAAgB,mBAAmB,UAAU,YAAY;IACtJ,OAAO,iBAAiB,MAAM,mBAAmB,iBAAiB,QAAQ,WAAW,gBAAgB,kBAAkB,GAAG,QAAQ;AACtI;AACA,SAAS,iBAAiB,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS;IACnI,MAAM,WAAW,KAAK,SAAS;IAC/B,MAAM,iBAAiB,kBAAkB,CAAC,KAAK,IAAI,CAAC;IACpD,MAAM,cAAc,gBAAgB;IACpC,MAAM,YAAY,eAAe,OAAO,YAAY,UAAU;IAC9D,IAAI;IACJ,IAAI,WAAW;IACf,IAAI,UAAU,MAAM;QAChB,eAAe,wBAAwB,QAAQ;QAC/C,WAAW,aAAa,QAAQ;QAChC,KAAK,MAAM,SAAS,aAAa,cAAc,CAAE;YAC7C,UAAU,cAAc,CAAC,GAAG,CAAC;QACjC;IACJ;IACA,KAAK,MAAM,CAAC,aAAa,cAAc,IAAI,aAAc;QACrD,MAAM,YAAY,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK;QAC7C,IAAI,YAAY,QAAQ,CAAC,UAAU,EAAE;QACrC,IAAI,aAAa,MAAM;YACnB,OAAQ;gBACJ,KAAK;oBACD,YAAY,UAAU,oBAAoB,CAAC,IAAI;oBAC/C;gBACJ,KAAK;oBACD,YAAY,UAAU,kBAAkB,CAAC,IAAI;oBAC7C;gBACJ,KAAK;oBACD,YAAY,UAAU,gBAAgB,CAAC,IAAI;oBAC3C;YACR;QACJ;QACA,MAAM,eAAe,YAAY;QACjC,IAAI;QACJ,IAAI,UAAU;YACV,cAAc,QAAQ,CAAC,YAAY;YACnC,IAAI,eAAe,MAAM;gBACrB,OAAO,QAAQ,CAAC,YAAY;YAChC;YACA,mBAAmB,MAAM,WAAW,cAAc,aAAa;QACnE;QACA,MAAM,WAAW,gBAAgB,MAAM,CAAC,YAAY,EAAE,WAAW,eAAe,QAAQ,WAAW,gBAAgB,kBAAkB,cAAc,aAAa;QAChK,aAAa,WAAW,aAAa,UAAU,gBAAgB;IACnE;IACA,MAAM,cAAc,UAAU,UAAU;IACxC,IAAI,eAAe,MAAM;QACrB,aAAa,WAAW,cAAc,aAAa,gBAAgB;IACvE;IACA,IAAI,UAAU;QACV,IAAK,MAAM,aAAa,SAAU;YAC9B,MAAM,SAAS,QAAQ,CAAC,UAAU;YAClC,KAAK,MAAM,SAAS,OAAQ;gBACxB,UAAU,cAAc,CAAC,GAAG,CAAC;YACjC;QACJ;IACJ;IACA,MAAM,cAAc,gBAAgB;IACpC,OAAO,eAAe,OAAO,YAAY,aAAa;AAC1D;AACA,SAAS,aAAa,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS;IAC1E,IAAI,kBAAkB,MAAM;QACxB,MAAM,CAAC,YAAY,GAAG;QACtB;IACJ;IACA,MAAM,eAAe,cAAc,CAAC,UAAU;IAC9C,IAAI,gBAAgB,MAAM;QACtB,MAAM,CAAC,YAAY,GAAG;QACtB;IACJ;IACA,MAAM,eAAe,aAAa;IAClC,IAAI,iBAAiB,WAAW;QAC5B,OAAO,MAAM,CAAC,YAAY;QAC1B;IACJ;IACA,MAAM,CAAC,YAAY,GAAG;AAC1B;AACA,SAAS,eAAe,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS;IACnI,OAAO,KAAK,GAAG,CAAC,CAAA,aAAc,gBAAgB,YAAY,YAAY,YAAY,QAAQ,WAAW,gBAAgB,kBAAkB,YAAY,GAAG,QAAQ;AAClK;AACA,SAAS,gBAAgB,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,SAAS;IAC1I,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,MAAM,eAAe,CAAC,GAAG,UAAU,eAAe,EAAE;IACpD,IAAI,CAAC,GAAG,UAAU,UAAU,EAAE,eAAe;QACzC,OAAO,eAAe,OAAO,aAAa,MAAM,EAAE,YAAY,QAAQ,WAAW,gBAAgB,kBAAkB,WAAW,QAAQ;IAC1I,OACK,IAAI,CAAC,GAAG,UAAU,cAAc,EAAE,eAAe;QAClD,MAAM,YAAY,OAAO,OAAO,CAAC,MAAM,UAAU;QACjD,IAAI,EAAE,QAAQ,eAAe,EAAE,OAAO,EAAE,GAAG,CAAC,GAAG,mBAAmB,gBAAgB,EAAE,QAAQ,WAAW,gBAAgB,WAAW;QAClI,IAAI,QAAQ,MAAM,EAAE;YAChB,kBAAkB,IAAI,IAAI;YAC1B,KAAK,MAAM,SAAS,QAAS;gBACzB,KAAK,MAAM,CAAC,aAAa,OAAO,IAAI,MAAM,MAAM,CAAE;oBAC9C,MAAM,iBAAiB,gBAAgB,GAAG,CAAC;oBAC3C,IAAI,gBAAgB;wBAChB,eAAe,IAAI,IAAI;oBAC3B,OACK;wBACD,gBAAgB,GAAG,CAAC,aAAa;oBACrC;gBACJ;YACJ;QACJ;QACA,OAAO,iBAAiB,OAAO,WAAW,iBAAiB,QAAQ,WAAW,gBAAgB,kBAAkB,WAAW,QAAQ;IACvI,OACK,IAAI,CAAC,GAAG,UAAU,YAAY,EAAE,eAAe;QAChD,IAAI,EAAE,QAAQ,eAAe,EAAE,OAAO,EAAE,GAAG,CAAC,GAAG,mBAAmB,gBAAgB,EAAE,QAAQ,WAAW,gBAAgB,cAAc;QACrI,IAAI,QAAQ,MAAM,EAAE;YAChB,kBAAkB,IAAI,IAAI;YAC1B,KAAK,MAAM,SAAS,QAAS;gBACzB,KAAK,MAAM,CAAC,aAAa,OAAO,IAAI,MAAM,MAAM,CAAE;oBAC9C,MAAM,iBAAiB,gBAAgB,GAAG,CAAC;oBAC3C,IAAI,gBAAgB;wBAChB,eAAe,IAAI,IAAI;oBAC3B,OACK;wBACD,gBAAgB,GAAG,CAAC,aAAa;oBACrC;gBACJ;YACJ;QACJ;QACA,OAAO,iBAAiB,OAAO,cAAc,iBAAiB,QAAQ,WAAW,gBAAgB,kBAAkB,WAAW,QAAQ;IAC1I;IACA,MAAM,iBAAiB,kBAAkB,CAAC,aAAa,IAAI,CAAC;IAC5D,IAAI,kBAAkB,MAAM;QACxB,OAAO;IACX;IACA,MAAM,eAAe,eAAe;IACpC,OAAO,iBAAiB,YAAY,QAAQ;AAChD;AACA,SAAS,wBAAwB,MAAM,EAAE,SAAS;IAC9C,MAAM,WAAW,OAAO,MAAM,CAAC;IAC/B,MAAM,iBAAiB,IAAI;IAC3B,KAAK,MAAM,SAAS,OAAQ;QACxB,MAAM,cAAc,MAAM,IAAI,EAAE,CAAC,UAAU;QAC3C,IAAI,eAAe,MAAM;YACrB,eAAe,GAAG,CAAC;YACnB;QACJ;QACA,IAAI,eAAe,UAAU;YACzB,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC;QAC/B,OACK;YACD,QAAQ,CAAC,YAAY,GAAG;gBAAC;aAAM;QACnC;IACJ;IACA,OAAO;QACH;QACA;IACJ;AACJ;AACA,SAAS,mBAAmB,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,SAAS;IAC1E,KAAK,MAAM,SAAS,OAAQ;QACxB,MAAM,cAAc;YAChB;YACA;YACA;QACJ;QACA,MAAM,mBAAmB,UAAU,cAAc,CAAC,GAAG,CAAC;QACtD,IAAI,oBAAoB,MAAM;YAC1B,UAAU,cAAc,CAAC,GAAG,CAAC,OAAO;gBAAC;aAAY;QACrD,OACK;YACD,iBAAiB,IAAI,CAAC;QAC1B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8007, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/valueMatchesCriteria.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.valueMatchesCriteria = valueMatchesCriteria;\nfunction valueMatchesCriteria(value, criteria) {\n    if (value == null) {\n        return value === criteria;\n    }\n    else if (Array.isArray(value)) {\n        return (Array.isArray(criteria) &&\n            value.every((val, index) => valueMatchesCriteria(val, criteria[index])));\n    }\n    else if (typeof value === 'object') {\n        return (typeof criteria === 'object' &&\n            criteria &&\n            Object.keys(criteria).every(propertyName => valueMatchesCriteria(value[propertyName], criteria[propertyName])));\n    }\n    else if (criteria instanceof RegExp) {\n        return criteria.test(value);\n    }\n    return value === criteria;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,oBAAoB,GAAG;AAC/B,SAAS,qBAAqB,KAAK,EAAE,QAAQ;IACzC,IAAI,SAAS,MAAM;QACf,OAAO,UAAU;IACrB,OACK,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC3B,OAAQ,MAAM,OAAO,CAAC,aAClB,MAAM,KAAK,CAAC,CAAC,KAAK,QAAU,qBAAqB,KAAK,QAAQ,CAAC,MAAM;IAC7E,OACK,IAAI,OAAO,UAAU,UAAU;QAChC,OAAQ,OAAO,aAAa,YACxB,YACA,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,CAAA,eAAgB,qBAAqB,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa;IACpH,OACK,IAAI,oBAAoB,QAAQ;QACjC,OAAO,SAAS,IAAI,CAAC;IACzB;IACA,OAAO,UAAU;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/isAsyncIterable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isAsyncIterable = isAsyncIterable;\nfunction isAsyncIterable(value) {\n    return value?.[Symbol.asyncIterator] != null;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG;AAC1B,SAAS,gBAAgB,KAAK;IAC1B,OAAO,OAAO,CAAC,OAAO,aAAa,CAAC,IAAI;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8038, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/isDocumentNode.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isDocumentNode = isDocumentNode;\nconst graphql_1 = require(\"graphql\");\nfunction isDocumentNode(object) {\n    return object && typeof object === 'object' && 'kind' in object && object.kind === graphql_1.Kind.DOCUMENT;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,SAAS,eAAe,MAAM;IAC1B,OAAO,UAAU,OAAO,WAAW,YAAY,UAAU,UAAU,OAAO,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ;AAC9G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8049, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8053, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/withCancel.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAsyncIteratorWithCancel = getAsyncIteratorWithCancel;\nexports.getAsyncIterableWithCancel = getAsyncIterableWithCancel;\nexports.withCancel = getAsyncIterableWithCancel;\nconst memoize_js_1 = require(\"./memoize.js\");\nasync function defaultAsyncIteratorReturn(value) {\n    return { value, done: true };\n}\nconst proxyMethodFactory = (0, memoize_js_1.memoize2)(function proxyMethodFactory(target, targetMethod) {\n    return function proxyMethod(...args) {\n        return Reflect.apply(targetMethod, target, args);\n    };\n});\nfunction getAsyncIteratorWithCancel(asyncIterator, onCancel) {\n    return new Proxy(asyncIterator, {\n        has(asyncIterator, prop) {\n            if (prop === 'return') {\n                return true;\n            }\n            return Reflect.has(asyncIterator, prop);\n        },\n        get(asyncIterator, prop, receiver) {\n            const existingPropValue = Reflect.get(asyncIterator, prop, receiver);\n            if (prop === 'return') {\n                const existingReturn = existingPropValue || defaultAsyncIteratorReturn;\n                return async function returnWithCancel(value) {\n                    const returnValue = await onCancel(value);\n                    return Reflect.apply(existingReturn, asyncIterator, [returnValue]);\n                };\n            }\n            else if (typeof existingPropValue === 'function') {\n                return proxyMethodFactory(asyncIterator, existingPropValue);\n            }\n            return existingPropValue;\n        },\n    });\n}\nfunction getAsyncIterableWithCancel(asyncIterable, onCancel) {\n    return new Proxy(asyncIterable, {\n        get(asyncIterable, prop, receiver) {\n            const existingPropValue = Reflect.get(asyncIterable, prop, receiver);\n            if (Symbol.asyncIterator === prop) {\n                return function asyncIteratorFactory() {\n                    const asyncIterator = Reflect.apply(existingPropValue, asyncIterable, []);\n                    return getAsyncIteratorWithCancel(asyncIterator, onCancel);\n                };\n            }\n            else if (typeof existingPropValue === 'function') {\n                return proxyMethodFactory(asyncIterable, existingPropValue);\n            }\n            return existingPropValue;\n        },\n    });\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,0BAA0B,GAAG;AACrC,QAAQ,0BAA0B,GAAG;AACrC,QAAQ,UAAU,GAAG;AACrB,MAAM;AACN,eAAe,2BAA2B,KAAK;IAC3C,OAAO;QAAE;QAAO,MAAM;IAAK;AAC/B;AACA,MAAM,qBAAqB,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,mBAAmB,MAAM,EAAE,YAAY;IAClG,OAAO,SAAS,YAAY,GAAG,IAAI;QAC/B,OAAO,QAAQ,KAAK,CAAC,cAAc,QAAQ;IAC/C;AACJ;AACA,SAAS,2BAA2B,aAAa,EAAE,QAAQ;IACvD,OAAO,IAAI,MAAM,eAAe;QAC5B,KAAI,aAAa,EAAE,IAAI;YACnB,IAAI,SAAS,UAAU;gBACnB,OAAO;YACX;YACA,OAAO,QAAQ,GAAG,CAAC,eAAe;QACtC;QACA,KAAI,aAAa,EAAE,IAAI,EAAE,QAAQ;YAC7B,MAAM,oBAAoB,QAAQ,GAAG,CAAC,eAAe,MAAM;YAC3D,IAAI,SAAS,UAAU;gBACnB,MAAM,iBAAiB,qBAAqB;gBAC5C,OAAO,eAAe,iBAAiB,KAAK;oBACxC,MAAM,cAAc,MAAM,SAAS;oBACnC,OAAO,QAAQ,KAAK,CAAC,gBAAgB,eAAe;wBAAC;qBAAY;gBACrE;YACJ,OACK,IAAI,OAAO,sBAAsB,YAAY;gBAC9C,OAAO,mBAAmB,eAAe;YAC7C;YACA,OAAO;QACX;IACJ;AACJ;AACA,SAAS,2BAA2B,aAAa,EAAE,QAAQ;IACvD,OAAO,IAAI,MAAM,eAAe;QAC5B,KAAI,aAAa,EAAE,IAAI,EAAE,QAAQ;YAC7B,MAAM,oBAAoB,QAAQ,GAAG,CAAC,eAAe,MAAM;YAC3D,IAAI,OAAO,aAAa,KAAK,MAAM;gBAC/B,OAAO,SAAS;oBACZ,MAAM,gBAAgB,QAAQ,KAAK,CAAC,mBAAmB,eAAe,EAAE;oBACxE,OAAO,2BAA2B,eAAe;gBACrD;YACJ,OACK,IAAI,OAAO,sBAAsB,YAAY;gBAC9C,OAAO,mBAAmB,eAAe;YAC7C;YACA,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/fixSchemaAst.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fixSchemaAst = fixSchemaAst;\nconst graphql_1 = require(\"graphql\");\nconst print_schema_with_directives_js_1 = require(\"./print-schema-with-directives.js\");\nfunction buildFixedSchema(schema, options) {\n    const document = (0, print_schema_with_directives_js_1.getDocumentNodeFromSchema)(schema);\n    return (0, graphql_1.buildASTSchema)(document, {\n        ...(options || {}),\n    });\n}\nfunction fixSchemaAst(schema, options) {\n    // eslint-disable-next-line no-undef-init\n    let schemaWithValidAst = undefined;\n    if (!schema.astNode || !schema.extensionASTNodes) {\n        schemaWithValidAst = buildFixedSchema(schema, options);\n    }\n    if (!schema.astNode && schemaWithValidAst?.astNode) {\n        schema.astNode = schemaWithValidAst.astNode;\n    }\n    if (!schema.extensionASTNodes && schemaWithValidAst?.astNode) {\n        schema.extensionASTNodes = schemaWithValidAst.extensionASTNodes;\n    }\n    return schema;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG;AACvB,MAAM;AACN,MAAM;AACN,SAAS,iBAAiB,MAAM,EAAE,OAAO;IACrC,MAAM,WAAW,CAAC,GAAG,kCAAkC,yBAAyB,EAAE;IAClF,OAAO,CAAC,GAAG,UAAU,cAAc,EAAE,UAAU;QAC3C,GAAI,WAAW,CAAC,CAAC;IACrB;AACJ;AACA,SAAS,aAAa,MAAM,EAAE,OAAO;IACjC,yCAAyC;IACzC,IAAI,qBAAqB;IACzB,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC9C,qBAAqB,iBAAiB,QAAQ;IAClD;IACA,IAAI,CAAC,OAAO,OAAO,IAAI,oBAAoB,SAAS;QAChD,OAAO,OAAO,GAAG,mBAAmB,OAAO;IAC/C;IACA,IAAI,CAAC,OAAO,iBAAiB,IAAI,oBAAoB,SAAS;QAC1D,OAAO,iBAAiB,GAAG,mBAAmB,iBAAiB;IACnE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/extractExtensionsFromSchema.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extractExtensionsFromSchema = extractExtensionsFromSchema;\nconst helpers_js_1 = require(\"./helpers.js\");\nconst Interfaces_js_1 = require(\"./Interfaces.js\");\nconst mapSchema_js_1 = require(\"./mapSchema.js\");\nfunction handleDirectiveExtensions(extensions, removeDirectives) {\n    extensions = extensions || {};\n    const { directives: existingDirectives, ...rest } = extensions;\n    const finalExtensions = {\n        ...rest,\n    };\n    if (!removeDirectives) {\n        if (existingDirectives != null) {\n            const directives = {};\n            for (const directiveName in existingDirectives) {\n                directives[directiveName] = [...(0, helpers_js_1.asArray)(existingDirectives[directiveName])];\n            }\n            finalExtensions.directives = directives;\n        }\n    }\n    return finalExtensions;\n}\nfunction extractExtensionsFromSchema(schema, removeDirectives = false) {\n    const result = {\n        schemaExtensions: handleDirectiveExtensions(schema.extensions, removeDirectives),\n        types: {},\n    };\n    (0, mapSchema_js_1.mapSchema)(schema, {\n        [Interfaces_js_1.MapperKind.OBJECT_TYPE]: type => {\n            result.types[type.name] = {\n                fields: {},\n                type: 'object',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [Interfaces_js_1.MapperKind.INTERFACE_TYPE]: type => {\n            result.types[type.name] = {\n                fields: {},\n                type: 'interface',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [Interfaces_js_1.MapperKind.FIELD]: (field, fieldName, typeName) => {\n            result.types[typeName].fields[fieldName] = {\n                arguments: {},\n                extensions: handleDirectiveExtensions(field.extensions, removeDirectives),\n            };\n            const args = field.args;\n            if (args != null) {\n                for (const argName in args) {\n                    result.types[typeName].fields[fieldName].arguments[argName] =\n                        handleDirectiveExtensions(args[argName].extensions, removeDirectives);\n                }\n            }\n            return field;\n        },\n        [Interfaces_js_1.MapperKind.ENUM_TYPE]: type => {\n            result.types[type.name] = {\n                values: {},\n                type: 'enum',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [Interfaces_js_1.MapperKind.ENUM_VALUE]: (value, typeName, _schema, valueName) => {\n            result.types[typeName].values[valueName] = handleDirectiveExtensions(value.extensions, removeDirectives);\n            return value;\n        },\n        [Interfaces_js_1.MapperKind.SCALAR_TYPE]: type => {\n            result.types[type.name] = {\n                type: 'scalar',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [Interfaces_js_1.MapperKind.UNION_TYPE]: type => {\n            result.types[type.name] = {\n                type: 'union',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [Interfaces_js_1.MapperKind.INPUT_OBJECT_TYPE]: type => {\n            result.types[type.name] = {\n                fields: {},\n                type: 'input',\n                extensions: handleDirectiveExtensions(type.extensions, removeDirectives),\n            };\n            return type;\n        },\n        [Interfaces_js_1.MapperKind.INPUT_OBJECT_FIELD]: (field, fieldName, typeName) => {\n            result.types[typeName].fields[fieldName] = {\n                extensions: handleDirectiveExtensions(field.extensions, removeDirectives),\n            };\n            return field;\n        },\n    });\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,2BAA2B,GAAG;AACtC,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,0BAA0B,UAAU,EAAE,gBAAgB;IAC3D,aAAa,cAAc,CAAC;IAC5B,MAAM,EAAE,YAAY,kBAAkB,EAAE,GAAG,MAAM,GAAG;IACpD,MAAM,kBAAkB;QACpB,GAAG,IAAI;IACX;IACA,IAAI,CAAC,kBAAkB;QACnB,IAAI,sBAAsB,MAAM;YAC5B,MAAM,aAAa,CAAC;YACpB,IAAK,MAAM,iBAAiB,mBAAoB;gBAC5C,UAAU,CAAC,cAAc,GAAG;uBAAI,CAAC,GAAG,aAAa,OAAO,EAAE,kBAAkB,CAAC,cAAc;iBAAE;YACjG;YACA,gBAAgB,UAAU,GAAG;QACjC;IACJ;IACA,OAAO;AACX;AACA,SAAS,4BAA4B,MAAM,EAAE,mBAAmB,KAAK;IACjE,MAAM,SAAS;QACX,kBAAkB,0BAA0B,OAAO,UAAU,EAAE;QAC/D,OAAO,CAAC;IACZ;IACA,CAAC,GAAG,eAAe,SAAS,EAAE,QAAQ;QAClC,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;YACtC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACtB,QAAQ,CAAC;gBACT,MAAM;gBACN,YAAY,0BAA0B,KAAK,UAAU,EAAE;YAC3D;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,cAAc,CAAC,EAAE,CAAA;YACzC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACtB,QAAQ,CAAC;gBACT,MAAM;gBACN,YAAY,0BAA0B,KAAK,UAAU,EAAE;YAC3D;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,WAAW;YACnD,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,GAAG;gBACvC,WAAW,CAAC;gBACZ,YAAY,0BAA0B,MAAM,UAAU,EAAE;YAC5D;YACA,MAAM,OAAO,MAAM,IAAI;YACvB,IAAI,QAAQ,MAAM;gBACd,IAAK,MAAM,WAAW,KAAM;oBACxB,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,GACvD,0BAA0B,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC5D;YACJ;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,SAAS,CAAC,EAAE,CAAA;YACpC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACtB,QAAQ,CAAC;gBACT,MAAM;gBACN,YAAY,0BAA0B,KAAK,UAAU,EAAE;YAC3D;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS;YAChE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,GAAG,0BAA0B,MAAM,UAAU,EAAE;YACvF,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;YACtC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACtB,MAAM;gBACN,YAAY,0BAA0B,KAAK,UAAU,EAAE;YAC3D;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,UAAU,CAAC,EAAE,CAAA;YACrC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACtB,MAAM;gBACN,YAAY,0BAA0B,KAAK,UAAU,EAAE;YAC3D;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAA;YAC5C,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACtB,QAAQ,CAAC;gBACT,MAAM;gBACN,YAAY,0BAA0B,KAAK,UAAU,EAAE;YAC3D;YACA,OAAO;QACX;QACA,CAAC,gBAAgB,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC,OAAO,WAAW;YAChE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,GAAG;gBACvC,YAAY,0BAA0B,MAAM,UAAU,EAAE;YAC5D;YACA,OAAO;QACX;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/Path.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.addPath = addPath;\nexports.pathToArray = pathToArray;\nexports.printPathArray = printPathArray;\n/**\n * Given a Path and a key, return a new Path containing the new key.\n */\nfunction addPath(prev, key, typename) {\n    return { prev, key, typename };\n}\n/**\n * Given a Path, return an Array of the path keys.\n */\nfunction pathToArray(path) {\n    const flattened = [];\n    let curr = path;\n    while (curr) {\n        flattened.push(curr.key);\n        curr = curr.prev;\n    }\n    return flattened.reverse();\n}\n/**\n * Build a string describing the path.\n */\nfunction printPathArray(path) {\n    return path\n        .map(key => (typeof key === 'number' ? '[' + key.toString() + ']' : '.' + key))\n        .join('');\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG;AAClB,QAAQ,WAAW,GAAG;AACtB,QAAQ,cAAc,GAAG;AACzB;;CAEC,GACD,SAAS,QAAQ,IAAI,EAAE,GAAG,EAAE,QAAQ;IAChC,OAAO;QAAE;QAAM;QAAK;IAAS;AACjC;AACA;;CAEC,GACD,SAAS,YAAY,IAAI;IACrB,MAAM,YAAY,EAAE;IACpB,IAAI,OAAO;IACX,MAAO,KAAM;QACT,UAAU,IAAI,CAAC,KAAK,GAAG;QACvB,OAAO,KAAK,IAAI;IACpB;IACA,OAAO,UAAU,OAAO;AAC5B;AACA;;CAEC,GACD,SAAS,eAAe,IAAI;IACxB,OAAO,KACF,GAAG,CAAC,CAAA,MAAQ,OAAO,QAAQ,WAAW,MAAM,IAAI,QAAQ,KAAK,MAAM,MAAM,KACzE,IAAI,CAAC;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/mergeIncrementalResult.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeIncrementalResult = mergeIncrementalResult;\nconst merge_1 = require(\"dset/merge\");\nfunction mergeIncrementalResult({ incrementalResult, executionResult, }) {\n    const path = ['data', ...(incrementalResult.path ?? [])];\n    if (incrementalResult.items) {\n        for (const item of incrementalResult.items) {\n            (0, merge_1.dset)(executionResult, path, item);\n            // Increment the last path segment (the array index) to merge the next item at the next index\n            path[path.length - 1]++;\n        }\n    }\n    if (incrementalResult.data) {\n        (0, merge_1.dset)(executionResult, path, incrementalResult.data);\n    }\n    if (incrementalResult.errors) {\n        executionResult.errors = executionResult.errors || [];\n        executionResult.errors.push(...incrementalResult.errors);\n    }\n    if (incrementalResult.extensions) {\n        (0, merge_1.dset)(executionResult, 'extensions', incrementalResult.extensions);\n    }\n    if (incrementalResult.incremental) {\n        incrementalResult.incremental.forEach(incrementalSubResult => {\n            mergeIncrementalResult({\n                incrementalResult: incrementalSubResult,\n                executionResult,\n            });\n        });\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,sBAAsB,GAAG;AACjC,MAAM;AACN,SAAS,uBAAuB,EAAE,iBAAiB,EAAE,eAAe,EAAG;IACnE,MAAM,OAAO;QAAC;WAAY,kBAAkB,IAAI,IAAI,EAAE;KAAE;IACxD,IAAI,kBAAkB,KAAK,EAAE;QACzB,KAAK,MAAM,QAAQ,kBAAkB,KAAK,CAAE;YACxC,CAAC,GAAG,QAAQ,IAAI,EAAE,iBAAiB,MAAM;YACzC,6FAA6F;YAC7F,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzB;IACJ;IACA,IAAI,kBAAkB,IAAI,EAAE;QACxB,CAAC,GAAG,QAAQ,IAAI,EAAE,iBAAiB,MAAM,kBAAkB,IAAI;IACnE;IACA,IAAI,kBAAkB,MAAM,EAAE;QAC1B,gBAAgB,MAAM,GAAG,gBAAgB,MAAM,IAAI,EAAE;QACrD,gBAAgB,MAAM,CAAC,IAAI,IAAI,kBAAkB,MAAM;IAC3D;IACA,IAAI,kBAAkB,UAAU,EAAE;QAC9B,CAAC,GAAG,QAAQ,IAAI,EAAE,iBAAiB,cAAc,kBAAkB,UAAU;IACjF;IACA,IAAI,kBAAkB,WAAW,EAAE;QAC/B,kBAAkB,WAAW,CAAC,OAAO,CAAC,CAAA;YAClC,uBAAuB;gBACnB,mBAAmB;gBACnB;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/debugTimer.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.debugTimerStart = debugTimerStart;\nexports.debugTimerEnd = debugTimerEnd;\nconst debugNamesOngoing = new Set();\nfunction debugTimerStart(name) {\n    const debugEnvVar = globalThis.process?.env?.['DEBUG'] || globalThis.DEBUG;\n    if (debugEnvVar === '1' || debugEnvVar?.includes(name)) {\n        debugNamesOngoing.add(name);\n        console.time(name);\n    }\n}\nfunction debugTimerEnd(name) {\n    if (debugNamesOngoing.has(name)) {\n        console.timeEnd(name);\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG;AAC1B,QAAQ,aAAa,GAAG;AACxB,MAAM,oBAAoB,IAAI;AAC9B,SAAS,gBAAgB,IAAI;IACzB,MAAM,cAAc,WAAW,OAAO,EAAE,KAAK,CAAC,QAAQ,IAAI,WAAW,KAAK;IAC1E,IAAI,gBAAgB,OAAO,aAAa,SAAS,OAAO;QACpD,kBAAkB,GAAG,CAAC;QACtB,QAAQ,IAAI,CAAC;IACjB;AACJ;AACA,SAAS,cAAc,IAAI;IACvB,IAAI,kBAAkB,GAAG,CAAC,OAAO;QAC7B,QAAQ,OAAO,CAAC;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8351, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/registerAbortSignalListener.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAbortPromise = void 0;\nexports.registerAbortSignalListener = registerAbortSignalListener;\nconst promise_helpers_1 = require(\"@whatwg-node/promise-helpers\");\nconst memoize_js_1 = require(\"./memoize.js\");\n// AbortSignal handler cache to avoid the \"possible EventEmitter memory leak detected\"\n// on Node.js\nconst getListenersOfAbortSignal = (0, memoize_js_1.memoize1)(function getListenersOfAbortSignal(signal) {\n    const listeners = new Set();\n    signal.addEventListener('abort', e => {\n        for (const listener of listeners) {\n            listener(e);\n        }\n    }, { once: true });\n    return listeners;\n});\n/**\n * Register an AbortSignal handler for a signal.\n * This helper function mainly exists to work around the\n * \"possible EventEmitter memory leak detected. 11 listeners added. Use emitter.setMaxListeners() to increase limit.\"\n * warning occuring on Node.js\n */\nfunction registerAbortSignalListener(signal, listener) {\n    // If the signal is already aborted, call the listener immediately\n    if (signal.aborted) {\n        listener();\n        return;\n    }\n    getListenersOfAbortSignal(signal).add(listener);\n}\nexports.getAbortPromise = (0, memoize_js_1.memoize1)(function getAbortPromise(signal) {\n    // If the signal is already aborted, return a rejected promise\n    if (signal.aborted) {\n        return (0, promise_helpers_1.fakeRejectPromise)(signal.reason);\n    }\n    return new Promise((_resolve, reject) => {\n        if (signal.aborted) {\n            reject(signal.reason);\n            return;\n        }\n        registerAbortSignalListener(signal, () => {\n            reject(signal.reason);\n        });\n    });\n});\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,QAAQ,2BAA2B,GAAG;AACtC,MAAM;AACN,MAAM;AACN,sFAAsF;AACtF,aAAa;AACb,MAAM,4BAA4B,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,0BAA0B,MAAM;IAClG,MAAM,YAAY,IAAI;IACtB,OAAO,gBAAgB,CAAC,SAAS,CAAA;QAC7B,KAAK,MAAM,YAAY,UAAW;YAC9B,SAAS;QACb;IACJ,GAAG;QAAE,MAAM;IAAK;IAChB,OAAO;AACX;AACA;;;;;CAKC,GACD,SAAS,4BAA4B,MAAM,EAAE,QAAQ;IACjD,kEAAkE;IAClE,IAAI,OAAO,OAAO,EAAE;QAChB;QACA;IACJ;IACA,0BAA0B,QAAQ,GAAG,CAAC;AAC1C;AACA,QAAQ,eAAe,GAAG,CAAC,GAAG,aAAa,QAAQ,EAAE,SAAS,gBAAgB,MAAM;IAChF,8DAA8D;IAC9D,IAAI,OAAO,OAAO,EAAE;QAChB,OAAO,CAAC,GAAG,kBAAkB,iBAAiB,EAAE,OAAO,MAAM;IACjE;IACA,OAAO,IAAI,QAAQ,CAAC,UAAU;QAC1B,IAAI,OAAO,OAAO,EAAE;YAChB,OAAO,OAAO,MAAM;YACpB;QACJ;QACA,4BAA4B,QAAQ;YAChC,OAAO,OAAO,MAAM;QACxB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8403, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/utils/cjs/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createDeferred = exports.fakePromise = exports.mapMaybePromise = exports.mapAsyncIterator = exports.inspect = void 0;\nconst tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./loaders.js\"), exports);\ntslib_1.__exportStar(require(\"./helpers.js\"), exports);\ntslib_1.__exportStar(require(\"./get-directives.js\"), exports);\ntslib_1.__exportStar(require(\"./get-fields-with-directives.js\"), exports);\ntslib_1.__exportStar(require(\"./get-arguments-with-directives.js\"), exports);\ntslib_1.__exportStar(require(\"./get-implementing-types.js\"), exports);\ntslib_1.__exportStar(require(\"./print-schema-with-directives.js\"), exports);\ntslib_1.__exportStar(require(\"./get-fields-with-directives.js\"), exports);\ntslib_1.__exportStar(require(\"./validate-documents.js\"), exports);\ntslib_1.__exportStar(require(\"./parse-graphql-json.js\"), exports);\ntslib_1.__exportStar(require(\"./parse-graphql-sdl.js\"), exports);\ntslib_1.__exportStar(require(\"./build-operation-for-field.js\"), exports);\ntslib_1.__exportStar(require(\"./types.js\"), exports);\ntslib_1.__exportStar(require(\"./filterSchema.js\"), exports);\ntslib_1.__exportStar(require(\"./heal.js\"), exports);\ntslib_1.__exportStar(require(\"./getResolversFromSchema.js\"), exports);\ntslib_1.__exportStar(require(\"./forEachField.js\"), exports);\ntslib_1.__exportStar(require(\"./forEachDefaultValue.js\"), exports);\ntslib_1.__exportStar(require(\"./mapSchema.js\"), exports);\ntslib_1.__exportStar(require(\"./addTypes.js\"), exports);\ntslib_1.__exportStar(require(\"./rewire.js\"), exports);\ntslib_1.__exportStar(require(\"./prune.js\"), exports);\ntslib_1.__exportStar(require(\"./mergeDeep.js\"), exports);\ntslib_1.__exportStar(require(\"./Interfaces.js\"), exports);\ntslib_1.__exportStar(require(\"./stub.js\"), exports);\ntslib_1.__exportStar(require(\"./selectionSets.js\"), exports);\ntslib_1.__exportStar(require(\"./getResponseKeyFromInfo.js\"), exports);\ntslib_1.__exportStar(require(\"./fields.js\"), exports);\ntslib_1.__exportStar(require(\"./renameType.js\"), exports);\ntslib_1.__exportStar(require(\"./transformInputValue.js\"), exports);\ntslib_1.__exportStar(require(\"./updateArgument.js\"), exports);\ntslib_1.__exportStar(require(\"./astFromType.js\"), exports);\ntslib_1.__exportStar(require(\"./implementsAbstractType.js\"), exports);\ntslib_1.__exportStar(require(\"./errors.js\"), exports);\ntslib_1.__exportStar(require(\"./observableToAsyncIterable.js\"), exports);\ntslib_1.__exportStar(require(\"./visitResult.js\"), exports);\ntslib_1.__exportStar(require(\"./getArgumentValues.js\"), exports);\ntslib_1.__exportStar(require(\"./valueMatchesCriteria.js\"), exports);\ntslib_1.__exportStar(require(\"./isAsyncIterable.js\"), exports);\ntslib_1.__exportStar(require(\"./isDocumentNode.js\"), exports);\ntslib_1.__exportStar(require(\"./astFromValueUntyped.js\"), exports);\ntslib_1.__exportStar(require(\"./executor.js\"), exports);\ntslib_1.__exportStar(require(\"./withCancel.js\"), exports);\ntslib_1.__exportStar(require(\"./rootTypes.js\"), exports);\ntslib_1.__exportStar(require(\"./comments.js\"), exports);\ntslib_1.__exportStar(require(\"./collectFields.js\"), exports);\nvar cross_inspect_1 = require(\"cross-inspect\");\nObject.defineProperty(exports, \"inspect\", { enumerable: true, get: function () { return cross_inspect_1.inspect; } });\ntslib_1.__exportStar(require(\"./memoize.js\"), exports);\ntslib_1.__exportStar(require(\"./fixSchemaAst.js\"), exports);\ntslib_1.__exportStar(require(\"./getOperationASTFromRequest.js\"), exports);\ntslib_1.__exportStar(require(\"./extractExtensionsFromSchema.js\"), exports);\ntslib_1.__exportStar(require(\"./Path.js\"), exports);\ntslib_1.__exportStar(require(\"./jsutils.js\"), exports);\ntslib_1.__exportStar(require(\"./directives.js\"), exports);\ntslib_1.__exportStar(require(\"./mergeIncrementalResult.js\"), exports);\ntslib_1.__exportStar(require(\"./debugTimer.js\"), exports);\ntslib_1.__exportStar(require(\"./getDirectiveExtensions.js\"), exports);\nvar promise_helpers_1 = require(\"@whatwg-node/promise-helpers\");\nObject.defineProperty(exports, \"mapAsyncIterator\", { enumerable: true, get: function () { return promise_helpers_1.mapAsyncIterator; } });\nObject.defineProperty(exports, \"mapMaybePromise\", { enumerable: true, get: function () { return promise_helpers_1.mapMaybePromise; } });\nObject.defineProperty(exports, \"fakePromise\", { enumerable: true, get: function () { return promise_helpers_1.fakePromise; } });\nObject.defineProperty(exports, \"createDeferred\", { enumerable: true, get: function () { return promise_helpers_1.createDeferredPromise; } });\ntslib_1.__exportStar(require(\"./registerAbortSignalListener.js\"), exports);\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,QAAQ,WAAW,GAAG,QAAQ,eAAe,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC3H,MAAM;AACN,QAAQ,YAAY,iHAA0B;AAC9C,QAAQ,YAAY,iHAA0B;AAC9C,QAAQ,YAAY,wHAAiC;AACrD,QAAQ,YAAY,oIAA6C;AACjE,QAAQ,YAAY,uIAAgD;AACpE,QAAQ,YAAY,gIAAyC;AAC7D,QAAQ,YAAY,sIAA+C;AACnE,QAAQ,YAAY,oIAA6C;AACjE,QAAQ,YAAY,4HAAqC;AACzD,QAAQ,YAAY,4HAAqC;AACzD,QAAQ,YAAY,2HAAoC;AACxD,QAAQ,YAAY,mIAA4C;AAChE,QAAQ,YAAY,+GAAwB;AAC5C,QAAQ,YAAY,sHAA+B;AACnD,QAAQ,YAAY,8GAAuB;AAC3C,QAAQ,YAAY,gIAAyC;AAC7D,QAAQ,YAAY,sHAA+B;AACnD,QAAQ,YAAY,6HAAsC;AAC1D,QAAQ,YAAY,mHAA4B;AAChD,QAAQ,YAAY,kHAA2B;AAC/C,QAAQ,YAAY,gHAAyB;AAC7C,QAAQ,YAAY,+GAAwB;AAC5C,QAAQ,YAAY,mHAA4B;AAChD,QAAQ,YAAY,oHAA6B;AACjD,QAAQ,YAAY,8GAAuB;AAC3C,QAAQ,YAAY,uHAAgC;AACpD,QAAQ,YAAY,gIAAyC;AAC7D,QAAQ,YAAY,gHAAyB;AAC7C,QAAQ,YAAY,oHAA6B;AACjD,QAAQ,YAAY,6HAAsC;AAC1D,QAAQ,YAAY,wHAAiC;AACrD,QAAQ,YAAY,qHAA8B;AAClD,QAAQ,YAAY,gIAAyC;AAC7D,QAAQ,YAAY,gHAAyB;AAC7C,QAAQ,YAAY,mIAA4C;AAChE,QAAQ,YAAY,qHAA8B;AAClD,QAAQ,YAAY,2HAAoC;AACxD,QAAQ,YAAY,8HAAuC;AAC3D,QAAQ,YAAY,yHAAkC;AACtD,QAAQ,YAAY,wHAAiC;AACrD,QAAQ,YAAY,6HAAsC;AAC1D,QAAQ,YAAY,kHAA2B;AAC/C,QAAQ,YAAY,oHAA6B;AACjD,QAAQ,YAAY,mHAA4B;AAChD,QAAQ,YAAY,kHAA2B;AAC/C,QAAQ,YAAY,uHAAgC;AACpD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,OAAO;IAAE;AAAE;AACnH,QAAQ,YAAY,iHAA0B;AAC9C,QAAQ,YAAY,sHAA+B;AACnD,QAAQ,YAAY,oIAA6C;AACjE,QAAQ,YAAY,qIAA8C;AAClE,QAAQ,YAAY,8GAAuB;AAC3C,QAAQ,YAAY,iHAA0B;AAC9C,QAAQ,YAAY,oHAA6B;AACjD,QAAQ,YAAY,gIAAyC;AAC7D,QAAQ,YAAY,oHAA6B;AACjD,QAAQ,YAAY,gIAAyC;AAC7D,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,kBAAkB,gBAAgB;IAAE;AAAE;AACvI,OAAO,cAAc,CAAC,SAAS,mBAAmB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,kBAAkB,eAAe;IAAE;AAAE;AACrI,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,kBAAkB,WAAW;IAAE;AAAE;AAC7H,OAAO,cAAc,CAAC,SAAS,kBAAkB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,kBAAkB,qBAAqB;IAAE;AAAE;AAC1I,QAAQ,YAAY,qIAA8C", "ignoreList": [0], "debugId": null}}]}