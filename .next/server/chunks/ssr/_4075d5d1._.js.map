{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  selectedRange: string;\n  onRangeChange: (range: string) => void;\n}\n\nexport function Header({ selectedRange, onRangeChange }: HeaderProps) {\n  const dateRanges = [\n    { value: '7d', label: '7d' },\n    { value: '14d', label: '14d' },\n    { value: '30d', label: '30d' },\n  ];\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Logo */}\n        <div className=\"flex items-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            SupplySight\n          </h1>\n        </div>\n\n        {/* Date Range Chips */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-600 mr-2\">Time Range:</span>\n          <div className=\"flex space-x-1\">\n            {dateRanges.map((range) => (\n              <button\n                key={range.value}\n                onClick={() => onRangeChange(range.value)}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  selectedRange === range.value\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                {range.label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,SAAS,OAAO,EAAE,aAAa,EAAE,aAAa,EAAe;IAClE,MAAM,aAAa;QACjB;YAAE,OAAO;YAAM,OAAO;QAAK;QAC3B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;KAC9B;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;8BAMnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA6B;;;;;;sCAC7C,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,8OAAC;oCAEC,SAAS,IAAM,cAAc,MAAM,KAAK;oCACxC,WAAW,CAAC,6DAA6D,EACvE,kBAAkB,MAAM,KAAK,GACzB,2BACA,+CACJ;8CAED,MAAM,KAAK;mCARP,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBhC", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from './Header';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [selectedRange, setSelectedRange] = useState('7d');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header \n        selectedRange={selectedRange} \n        onRangeChange={setSelectedRange} \n      />\n      <main className=\"p-6\">\n        {children}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AASO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gJAAM;gBACL,eAAe;gBACf,eAAe;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}