{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  selectedRange: string;\n  onRangeChange: (range: string) => void;\n}\n\nexport function Header({ selectedRange, onRangeChange }: HeaderProps) {\n  const dateRanges = [\n    { value: '7d', label: '7d' },\n    { value: '14d', label: '14d' },\n    { value: '30d', label: '30d' },\n  ];\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Logo */}\n        <div className=\"flex items-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            SupplySight\n          </h1>\n        </div>\n\n        {/* Date Range Chips */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-600 mr-2\">Time Range:</span>\n          <div className=\"flex space-x-1\">\n            {dateRanges.map((range) => (\n              <button\n                key={range.value}\n                onClick={() => onRangeChange(range.value)}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  selectedRange === range.value\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                {range.label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,SAAS,OAAO,EAAE,aAAa,EAAE,aAAa,EAAe;IAClE,MAAM,aAAa;QACjB;YAAE,OAAO;YAAM,OAAO;QAAK;QAC3B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;KAC9B;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;8BAMnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA6B;;;;;;sCAC7C,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,8OAAC;oCAEC,SAAS,IAAM,cAAc,MAAM,KAAK;oCACxC,WAAW,CAAC,6DAA6D,EACvE,kBAAkB,MAAM,KAAK,GACzB,2BACA,+CACJ;8CAED,MAAM,KAAK;mCARP,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBhC", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, createContext, useContext } from \"react\";\nimport { Header } from \"./Header\";\n\ninterface DashboardContextType {\n  selectedRange: string;\n  setSelectedRange: (range: string) => void;\n}\n\nconst DashboardContext = createContext<DashboardContextType | undefined>(\n  undefined\n);\n\nexport function useDashboard() {\n  const context = useContext(DashboardContext);\n  if (!context) {\n    throw new Error(\"useDashboard must be used within a DashboardLayout\");\n  }\n  return context;\n}\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [selectedRange, setSelectedRange] = useState(\"7d\");\n\n  return (\n    <DashboardContext.Provider value={{ selectedRange, setSelectedRange }}>\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header\n          selectedRange={selectedRange}\n          onRangeChange={setSelectedRange}\n        />\n        <main className=\"p-6\">{children}</main>\n      </div>\n    </DashboardContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,iCAAmB,IAAA,sNAAa,EACpC;AAGK,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IAEnD,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE;YAAe;QAAiB;kBAClE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gJAAM;oBACL,eAAe;oBACf,eAAe;;;;;;8BAEjB,8OAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/lib/graphql/queries.ts"], "sourcesContent": ["import { gql } from \"@apollo/client/core\";\n\nexport const GET_PRODUCTS = gql`\n  query GetProducts($search: String, $status: String, $warehouse: String) {\n    products(search: $search, status: $status, warehouse: $warehouse) {\n      id\n      name\n      sku\n      warehouse\n      stock\n      demand\n    }\n  }\n`;\n\nexport const GET_WAREHOUSES = gql`\n  query GetWarehouses {\n    warehouses {\n      code\n      name\n      city\n      country\n    }\n  }\n`;\n\nexport const GET_KPIS = gql`\n  query GetKPIs($range: String!) {\n    kpis(range: $range) {\n      date\n      stock\n      demand\n    }\n  }\n`;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,MAAM,eAAe,qJAAG,CAAC;;;;;;;;;;;AAWhC,CAAC;AAEM,MAAM,iBAAiB,qJAAG,CAAC;;;;;;;;;AASlC,CAAC;AAEM,MAAM,WAAW,qJAAG,CAAC;;;;;;;;AAQ5B,CAAC", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/utils/index.ts"], "sourcesContent": ["import { Product, ProductStatus } from \"@/types/graphql\";\n\n// Helper function to determine product status\nexport function getProductStatus(product: Product): ProductStatus {\n  if (product.stock > product.demand) return \"healthy\";\n  if (product.stock === product.demand) return \"low\";\n  return \"critical\";\n}\n\n// Helper function to calculate KPIs\nexport function calculateKPIs(products: Product[]) {\n  const totalStock = products.reduce((sum, product) => sum + product.stock, 0);\n  const totalDemand = products.reduce(\n    (sum, product) => sum + product.demand,\n    0\n  );\n  const fillRate =\n    totalDemand > 0\n      ? (products.reduce(\n          (sum, product) => sum + Math.min(product.stock, product.demand),\n          0\n        ) /\n          totalDemand) *\n        100\n      : 0;\n\n  return {\n    totalStock,\n    totalDemand,\n    fillRate: Math.round(fillRate * 100) / 100, // Round to 2 decimal places\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAGO,SAAS,iBAAiB,OAAgB;IAC/C,IAAI,QAAQ,KAAK,GAAG,QAAQ,MAAM,EAAE,OAAO;IAC3C,IAAI,QAAQ,KAAK,KAAK,QAAQ,MAAM,EAAE,OAAO;IAC7C,OAAO;AACT;AAGO,SAAS,cAAc,QAAmB;IAC/C,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,KAAK,EAAE;IAC1E,MAAM,cAAc,SAAS,MAAM,CACjC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EACtC;IAEF,MAAM,WACJ,cAAc,IACV,AAAC,SAAS,MAAM,CACd,CAAC,KAAK,UAAY,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,QAAQ,MAAM,GAC9D,KAEA,cACF,MACA;IAEN,OAAO;QACL;QACA;QACA,UAAU,KAAK,KAAK,CAAC,WAAW,OAAO;IACzC;AACF", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/KPICards.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useQuery } from \"@apollo/client/react\";\nimport { GET_PRODUCTS } from \"@/lib/graphql/queries\";\nimport { Product } from \"@/types/graphql\";\nimport { calculateKPIs } from \"@/utils\";\n\ninterface KPICardProps {\n  title: string;\n  value: string;\n  subtitle: string;\n  valueClassName?: string;\n}\n\nfunction KPICard({ title, value, subtitle, valueClassName }: KPICardProps) {\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow\">\n      <h3 className=\"text-sm font-medium text-gray-500 mb-2\">{title}</h3>\n      <p className={`text-3xl font-bold ${valueClassName}`}>{value}</p>\n      <p className=\"text-xs text-gray-400 mt-1\">{subtitle}</p>\n    </div>\n  );\n}\n\ninterface KPICardsProps {\n  className?: string;\n}\n\nexport function KPICards({ className = \"\" }: KPICardsProps) {\n  const { data, loading, error } = useQuery<{ products: Product[] }>(\n    GET_PRODUCTS,\n    {\n      variables: {},\n    }\n  );\n\n  if (loading) {\n    return (\n      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n        {[1, 2, 3].map((i) => (\n          <div key={i} className=\"bg-white p-6 rounded-lg shadow animate-pulse\">\n            <div className=\"h-4 bg-gray-200 rounded w-20 mb-2\"></div>\n            <div className=\"h-8 bg-gray-200 rounded w-16\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n        <div className=\"bg-red-50 p-6 rounded-lg border border-red-200 col-span-3\">\n          <p className=\"text-red-600\">\n            Error loading KPI data: {error.message}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  const products: Product[] = data?.products || [];\n  const kpis = calculateKPIs(products);\n\n  const fillRateColor =\n    kpis.fillRate >= 90\n      ? \"text-green-600\"\n      : kpis.fillRate >= 70\n      ? \"text-yellow-600\"\n      : \"text-red-600\";\n\n  return (\n    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n      <KPICard\n        title=\"Total Stock\"\n        value={kpis.totalStock.toLocaleString()}\n        subtitle=\"units available\"\n        valueClassName=\"text-blue-600\"\n      />\n      <KPICard\n        title=\"Total Demand\"\n        value={kpis.totalDemand.toLocaleString()}\n        subtitle=\"units required\"\n        valueClassName=\"text-orange-600\"\n      />\n      <KPICard\n        title=\"Fill Rate\"\n        value={`${kpis.fillRate.toFixed(1)}%`}\n        subtitle=\"demand fulfillment\"\n        valueClassName={fillRateColor}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAcA,SAAS,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAgB;IACvE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,8OAAC;gBAAE,WAAW,CAAC,mBAAmB,EAAE,gBAAgB;0BAAG;;;;;;0BACvD,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAGjD;AAMO,SAAS,SAAS,EAAE,YAAY,EAAE,EAAiB;IACxD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAA,4KAAQ,EACvC,gJAAY,EACZ;QACE,WAAW,CAAC;IACd;IAGF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;sBACjE;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;mBAFP;;;;;;;;;;IAOlB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;sBAClE,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAe;wBACD,MAAM,OAAO;;;;;;;;;;;;;;;;;IAKhD;IAEA,MAAM,WAAsB,MAAM,YAAY,EAAE;IAChD,MAAM,OAAO,IAAA,sIAAa,EAAC;IAE3B,MAAM,gBACJ,KAAK,QAAQ,IAAI,KACb,mBACA,KAAK,QAAQ,IAAI,KACjB,oBACA;IAEN,qBACE,8OAAC;QAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;;0BAClE,8OAAC;gBACC,OAAM;gBACN,OAAO,KAAK,UAAU,CAAC,cAAc;gBACrC,UAAS;gBACT,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,OAAM;gBACN,OAAO,KAAK,WAAW,CAAC,cAAc;gBACtC,UAAS;gBACT,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,OAAM;gBACN,OAAO,GAAG,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACrC,UAAS;gBACT,gBAAgB;;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/StockDemandChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useQuery } from \"@apollo/client/react\";\nimport {\n  <PERSON><PERSON>hart,\n  Line,\n  XAxis,\n  <PERSON><PERSON><PERSON>s,\n  CartesianGrid,\n  <PERSON><PERSON><PERSON>,\n  Legend,\n  ResponsiveContainer,\n} from \"recharts\";\nimport { GET_KPIS } from \"@/lib/graphql/queries\";\nimport { KPI } from \"@/types/graphql\";\n\ninterface StockDemandChartProps {\n  selectedRange: string;\n  className?: string;\n}\n\nexport function StockDemandChart({\n  selectedRange,\n  className = \"\",\n}: StockDemandChartProps) {\n  const { data, loading, error } = useQuery(GET_KPIS, {\n    variables: { range: selectedRange },\n  });\n\n  if (loading) {\n    return (\n      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n          Stock vs Demand Trend\n        </h3>\n        <div className=\"h-64 flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n          Stock vs Demand Trend\n        </h3>\n        <div className=\"h-64 flex items-center justify-center\">\n          <p className=\"text-red-600\">\n            Error loading chart data: {error.message}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  const kpis: KPI[] = data?.kpis || [];\n\n  // Format data for the chart\n  const chartData = kpis.map((kpi) => ({\n    date: new Date(kpi.date).toLocaleDateString(\"en-US\", {\n      month: \"short\",\n      day: \"numeric\",\n    }),\n    stock: kpi.stock,\n    demand: kpi.demand,\n  }));\n\n  return (\n    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>\n      <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n        Stock vs Demand Trend\n      </h3>\n      <div className=\"h-64\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <LineChart data={chartData}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis\n              dataKey=\"date\"\n              tick={{ fontSize: 12 }}\n              tickLine={{ stroke: \"#6B7280\" }}\n            />\n            <YAxis tick={{ fontSize: 12 }} tickLine={{ stroke: \"#6B7280\" }} />\n            <Tooltip\n              contentStyle={{\n                backgroundColor: \"#F9FAFB\",\n                border: \"1px solid #E5E7EB\",\n                borderRadius: \"6px\",\n              }}\n            />\n            <Legend />\n            <Line\n              type=\"monotone\"\n              dataKey=\"stock\"\n              stroke=\"#3B82F6\"\n              strokeWidth={2}\n              name=\"Stock\"\n              dot={{ fill: \"#3B82F6\", strokeWidth: 2, r: 4 }}\n            />\n            <Line\n              type=\"monotone\"\n              dataKey=\"demand\"\n              stroke=\"#F59E0B\"\n              strokeWidth={2}\n              name=\"Demand\"\n              dot={{ fill: \"#F59E0B\", strokeWidth: 2, r: 4 }}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAbA;;;;;AAqBO,SAAS,iBAAiB,EAC/B,aAAa,EACb,YAAY,EAAE,EACQ;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAA,4KAAQ,EAAC,4IAAQ,EAAE;QAClD,WAAW;YAAE,OAAO;QAAc;IACpC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW;;8BAC3D,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW;;8BAC3D,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAe;4BACC,MAAM,OAAO;;;;;;;;;;;;;;;;;;IAKlD;IAEA,MAAM,OAAc,MAAM,QAAQ,EAAE;IAEpC,4BAA4B;IAC5B,MAAM,YAAY,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;YACnC,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE,kBAAkB,CAAC,SAAS;gBACnD,OAAO;gBACP,KAAK;YACP;YACA,OAAO,IAAI,KAAK;YAChB,QAAQ,IAAI,MAAM;QACpB,CAAC;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW;;0BAC3D,8OAAC;gBAAG,WAAU;0BAAyC;;;;;;0BAGvD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,kKAAS;wBAAC,MAAM;;0CACf,8OAAC,8KAAa;gCAAC,iBAAgB;;;;;;0CAC/B,8OAAC,8JAAK;gCACJ,SAAQ;gCACR,MAAM;oCAAE,UAAU;gCAAG;gCACrB,UAAU;oCAAE,QAAQ;gCAAU;;;;;;0CAEhC,8OAAC,8JAAK;gCAAC,MAAM;oCAAE,UAAU;gCAAG;gCAAG,UAAU;oCAAE,QAAQ;gCAAU;;;;;;0CAC7D,8OAAC,kKAAO;gCACN,cAAc;oCACZ,iBAAiB;oCACjB,QAAQ;oCACR,cAAc;gCAChB;;;;;;0CAEF,8OAAC,gKAAM;;;;;0CACP,8OAAC,4JAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,MAAK;gCACL,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;;;;;;0CAE/C,8OAAC,4JAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,MAAK;gCACL,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  DashboardLayout,\n  useDashboard,\n} from \"@/components/layout/DashboardLayout\";\nimport { KPICards } from \"@/components/dashboard/KPICards\";\nimport { StockDemandChart } from \"@/components/dashboard/StockDemandChart\";\n\nfunction DashboardContent() {\n  const { selectedRange } = useDashboard();\n\n  return (\n    <div className=\"space-y-6\">\n      <KPICards />\n\n      <StockDemandChart selectedRange={selectedRange} />\n\n      {/* Filters Section */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <div className=\"flex flex-wrap gap-4\">\n          <input\n            type=\"text\"\n            placeholder=\"Search products...\"\n            className=\"flex-1 min-w-64 px-3 py-2 border border-gray-300 rounded-md text-black\"\n          />\n          <select className=\"px-3 py-2 border text-black border-gray-300 rounded-md\">\n            <option value=\"\">All Warehouses</option>\n          </select>\n          <select className=\"px-3 py-2 border border-gray-300 text-black rounded-md\">\n            <option value=\"\">All Status</option>\n            <option value=\"healthy\">Healthy</option>\n            <option value=\"low\">Low</option>\n            <option value=\"critical\">Critical</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Products Table Section */}\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Products</h3>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider\">\n                    Product\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider\">\n                    SKU\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider\">\n                    Warehouse\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider\">\n                    Stock\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider\">\n                    Demand\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider\">\n                    Status\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                <tr>\n                  <td\n                    colSpan={6}\n                    className=\"px-6 py-4 text-center text-gray-500\"\n                  >\n                    Loading products...\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Home() {\n  return (\n    <DashboardLayout>\n      <DashboardContent />\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAIA;AACA;AAPA;;;;;AASA,SAAS;IACP,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,+JAAY;IAEtC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uJAAQ;;;;;0BAET,8OAAC,uKAAgB;gBAAC,eAAe;;;;;;0BAGjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,WAAU;;;;;;sCAEZ,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAO,OAAM;0CAAG;;;;;;;;;;;sCAEnB,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,8OAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA8E;;;;;;8DAG5F,8OAAC;oDAAG,WAAU;8DAA8E;;;;;;8DAG5F,8OAAC;oDAAG,WAAU;8DAA8E;;;;;;8DAG5F,8OAAC;oDAAG,WAAU;8DAA8E;;;;;;8DAG5F,8OAAC;oDAAG,WAAU;8DAA8E;;;;;;8DAG5F,8OAAC;oDAAG,WAAU;8DAA8E;;;;;;;;;;;;;;;;;kDAKhG,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;sDACC,cAAA,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;AAEe,SAAS;IACtB,qBACE,8OAAC,kKAAe;kBACd,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}