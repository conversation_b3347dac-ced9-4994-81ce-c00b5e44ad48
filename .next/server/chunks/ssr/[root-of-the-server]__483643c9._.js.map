{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/DashboardLayout.tsx <module evaluation>\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/DashboardLayout.tsx\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/app/page.tsx"], "sourcesContent": ["import { DashboardLayout } from \"@/components/layout/DashboardLayout\";\nimport { KPICards } from \"@/components/dashboard/KPICards\";\n\nexport default function Home() {\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* KPI Cards Section */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-sm font-medium text-gray-500\">Total Stock</h3>\n            <p className=\"text-2xl font-bold text-gray-900\">Loading...</p>\n          </div>\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-sm font-medium text-gray-500\">Total Demand</h3>\n            <p className=\"text-2xl font-bold text-gray-900\">Loading...</p>\n          </div>\n          <div className=\"bg-white p-6 rounded-lg shadow\">\n            <h3 className=\"text-sm font-medium text-gray-500\">Fill Rate</h3>\n            <p className=\"text-2xl font-bold text-gray-900\">Loading...</p>\n          </div>\n        </div>\n\n        {/* Chart Section */}\n        <div className=\"bg-white p-6 rounded-lg shadow\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            Stock vs Demand Trend\n          </h3>\n          <div className=\"h-64 flex items-center justify-center text-gray-500\">\n            Chart will be implemented here\n          </div>\n        </div>\n\n        {/* Filters Section */}\n        <div className=\"bg-white p-4 rounded-lg shadow\">\n          <div className=\"flex flex-wrap gap-4\">\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              className=\"flex-1 min-w-64 px-3 py-2 border border-gray-300 rounded-md\"\n            />\n            <select className=\"px-3 py-2 border border-gray-300 rounded-md\">\n              <option value=\"\">All Warehouses</option>\n            </select>\n            <select className=\"px-3 py-2 border border-gray-300 rounded-md\">\n              <option value=\"\">All Status</option>\n              <option value=\"healthy\">Healthy</option>\n              <option value=\"low\">Low</option>\n              <option value=\"critical\">Critical</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Products Table Section */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Products</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Product\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      SKU\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Warehouse\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Stock\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Demand\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  <tr>\n                    <td\n                      colSpan={6}\n                      className=\"px-6 py-4 text-center text-gray-500\"\n                    >\n                      Loading products...\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGe,SAAS;IACtB,qBACE,8OAAC,kKAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAI,WAAU;sCAAsD;;;;;;;;;;;;8BAMvE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;0CAEZ,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC;oCAAO,OAAM;8CAAG;;;;;;;;;;;0CAEnB,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;0DACC,cAAA,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}