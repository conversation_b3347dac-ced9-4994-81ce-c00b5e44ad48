{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/DashboardLayout.tsx <module evaluation>\",\n    \"DashboardLayout\",\n);\nexport const useDashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDashboard() from the server but useDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/DashboardLayout.tsx <module evaluation>\",\n    \"useDashboard\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/DashboardLayout.tsx\",\n    \"DashboardLayout\",\n);\nexport const useDashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDashboard() from the server but useDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/DashboardLayout.tsx\",\n    \"useDashboard\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/KPICards.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const KPICards = registerClientReference(\n    function() { throw new Error(\"Attempted to call KPICards() from the server but KPICards is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/KPICards.tsx <module evaluation>\",\n    \"KPICards\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,WAAW,IAAA,wQAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,uEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/KPICards.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const KPICards = registerClientReference(\n    function() { throw new Error(\"Attempted to call KPICards() from the server but KPICards is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/KPICards.tsx\",\n    \"KPICards\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,WAAW,IAAA,wQAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,mDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/StockDemandChart.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StockDemandChart = registerClientReference(\n    function() { throw new Error(\"Attempted to call StockDemandChart() from the server but StockDemandChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/StockDemandChart.tsx <module evaluation>\",\n    \"StockDemandChart\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/StockDemandChart.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StockDemandChart = registerClientReference(\n    function() { throw new Error(\"Attempted to call StockDemandChart() from the server but StockDemandChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/StockDemandChart.tsx\",\n    \"StockDemandChart\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/app/page.tsx"], "sourcesContent": ["import {\n  DashboardLayout,\n  useDashboard,\n} from \"@/components/layout/DashboardLayout\";\nimport { KPICards } from \"@/components/dashboard/KPICards\";\nimport { StockDemandChart } from \"@/components/dashboard/StockDemandChart\";\n\n(\"use client\");\n\nfunction DashboardContent() {\n  const { selectedRange } = useDashboard();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* KPI Cards Section */}\n      <KPICards />\n\n      {/* Chart Section */}\n      <StockDemandChart selectedRange={selectedRange} />\n\n      {/* Filters Section */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <div className=\"flex flex-wrap gap-4\">\n          <input\n            type=\"text\"\n            placeholder=\"Search products...\"\n            className=\"flex-1 min-w-64 px-3 py-2 border border-gray-300 rounded-md\"\n          />\n          <select className=\"px-3 py-2 border border-gray-300 rounded-md\">\n            <option value=\"\">All Warehouses</option>\n          </select>\n          <select className=\"px-3 py-2 border border-gray-300 rounded-md\">\n            <option value=\"\">All Status</option>\n            <option value=\"healthy\">Healthy</option>\n            <option value=\"low\">Low</option>\n            <option value=\"critical\">Critical</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Products Table Section */}\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Products</h3>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Product\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    SKU\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Warehouse\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Stock\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Demand\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                <tr>\n                  <td\n                    colSpan={6}\n                    className=\"px-6 py-4 text-center text-gray-500\"\n                  >\n                    Loading products...\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Home() {\n  return (\n    <DashboardLayout>\n      <DashboardContent />\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;AACA;;;;;AAEC;AAED,SAAS;IACP,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,+JAAY;IAEtC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,uJAAQ;;;;;0BAGT,8OAAC,uKAAgB;gBAAC,eAAe;;;;;;0BAGjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,WAAU;;;;;;sCAEZ,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAO,OAAM;0CAAG;;;;;;;;;;;sCAEnB,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,8OAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;sDACC,cAAA,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;AAEe,SAAS;IACtB,qBACE,8OAAC,kKAAe;kBACd,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}