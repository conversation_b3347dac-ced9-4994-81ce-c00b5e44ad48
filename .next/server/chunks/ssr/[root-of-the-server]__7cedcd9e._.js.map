{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/lib/apollo-client.ts"], "sourcesContent": ["import { ApolloClient, InMemoryCache, HttpLink } from \"@apollo/client\";\n\nconst httpLink = new HttpLink({\n  uri: \"/api/graphql\",\n});\n\nexport const apolloClient = new ApolloClient({\n  link: httpLink,\n  cache: new InMemoryCache(),\n  defaultOptions: {\n    watchQuery: {\n      errorPolicy: \"all\",\n    },\n    query: {\n      errorPolicy: \"all\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;AAEA,MAAM,WAAW,IAAI,0KAAQ,CAAC;IAC5B,KAAK;AACP;AAEO,MAAM,eAAe,IAAI,0KAAY,CAAC;IAC3C,MAAM;IACN,OAAO,IAAI,yLAAa;IACxB,gBAAgB;QACd,YAAY;YACV,aAAa;QACf;QACA,OAAO;YACL,aAAa;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/providers/ApolloProvider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ApolloProvider as BaseApolloProvider } from \"@apollo/client/react\";\nimport { apolloClient } from \"@/lib/apollo-client\";\n\ninterface ApolloProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ApolloProvider({ children }: ApolloProviderProps) {\n  return (\n    <BaseApolloProvider client={apolloClient}>{children}</BaseApolloProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,8OAAC,0LAAkB;QAAC,QAAQ,8IAAY;kBAAG;;;;;;AAE/C", "debugId": null}}]}