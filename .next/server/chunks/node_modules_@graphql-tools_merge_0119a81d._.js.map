{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/extensions.js"], "sourcesContent": ["import { mergeDeep } from '@graphql-tools/utils';\nexport { extractExtensionsFromSchema } from '@graphql-tools/utils';\nexport function mergeExtensions(extensions) {\n    return mergeDeep(extensions, false, true);\n}\nfunction applyExtensionObject(obj, extensions) {\n    if (!obj || !extensions || extensions === obj.extensions) {\n        return;\n    }\n    if (!obj.extensions) {\n        obj.extensions = extensions;\n        return;\n    }\n    obj.extensions = mergeDeep([obj.extensions, extensions], false, true);\n}\nexport function applyExtensions(schema, extensions) {\n    applyExtensionObject(schema, extensions.schemaExtensions);\n    for (const [typeName, data] of Object.entries(extensions.types || {})) {\n        const type = schema.getType(typeName);\n        if (type) {\n            applyExtensionObject(type, data.extensions);\n            if (data.type === 'object' || data.type === 'interface') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    if (field) {\n                        applyExtensionObject(field, fieldData.extensions);\n                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {\n                            applyExtensionObject(field.args.find(a => a.name === arg), argData);\n                        }\n                    }\n                }\n            }\n            else if (data.type === 'input') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    applyExtensionObject(field, fieldData.extensions);\n                }\n            }\n            else if (data.type === 'enum') {\n                for (const [valueName, valueData] of Object.entries(data.values)) {\n                    const value = type.getValue(valueName);\n                    applyExtensionObject(value, valueData);\n                }\n            }\n        }\n    }\n    return schema;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAEO,SAAS,gBAAgB,UAAU;IACtC,OAAO,IAAA,8KAAS,EAAC,YAAY,OAAO;AACxC;AACA,SAAS,qBAAqB,GAAG,EAAE,UAAU;IACzC,IAAI,CAAC,OAAO,CAAC,cAAc,eAAe,IAAI,UAAU,EAAE;QACtD;IACJ;IACA,IAAI,CAAC,IAAI,UAAU,EAAE;QACjB,IAAI,UAAU,GAAG;QACjB;IACJ;IACA,IAAI,UAAU,GAAG,IAAA,8KAAS,EAAC;QAAC,IAAI,UAAU;QAAE;KAAW,EAAE,OAAO;AACpE;AACO,SAAS,gBAAgB,MAAM,EAAE,UAAU;IAC9C,qBAAqB,QAAQ,WAAW,gBAAgB;IACxD,KAAK,MAAM,CAAC,UAAU,KAAK,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,GAAI;QACnE,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,IAAI,MAAM;YACN,qBAAqB,MAAM,KAAK,UAAU;YAC1C,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,aAAa;gBACrD,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,IAAI,OAAO;wBACP,qBAAqB,OAAO,UAAU,UAAU;wBAChD,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAU,SAAS,EAAG;4BAC9D,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM;wBAC/D;oBACJ;gBACJ;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,SAAS;gBAC5B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,qBAAqB,OAAO,UAAU,UAAU;gBACpD;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,QAAQ;gBAC3B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,QAAQ,CAAC;oBAC5B,qBAAqB,OAAO;gBAChC;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/merge-resolvers.js"], "sourcesContent": ["import { mergeDeep } from '@graphql-tools/utils';\n/**\n * Deep merges multiple resolver definition objects into a single definition.\n * @param resolversDefinitions Resolver definitions to be merged\n * @param options Additional options\n *\n * ```js\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const clientResolver = require('./clientResolver');\n * const productResolver = require('./productResolver');\n *\n * const resolvers = mergeResolvers([\n *  clientResolver,\n *  productResolver,\n * ]);\n * ```\n *\n * If you don't want to manually create the array of resolver objects, you can\n * also use this function along with loadFiles:\n *\n * ```js\n * const path = require('path');\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const { loadFilesSync } = require('@graphql-tools/load-files');\n *\n * const resolversArray = loadFilesSync(path.join(__dirname, './resolvers'));\n *\n * const resolvers = mergeResolvers(resolversArray)\n * ```\n */\nexport function mergeResolvers(resolversDefinitions, options) {\n    if (!resolversDefinitions ||\n        (Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0)) {\n        return {};\n    }\n    if (!Array.isArray(resolversDefinitions)) {\n        return resolversDefinitions;\n    }\n    if (resolversDefinitions.length === 1) {\n        return resolversDefinitions[0] || {};\n    }\n    const resolvers = new Array();\n    for (let resolversDefinition of resolversDefinitions) {\n        if (Array.isArray(resolversDefinition)) {\n            resolversDefinition = mergeResolvers(resolversDefinition);\n        }\n        if (typeof resolversDefinition === 'object' && resolversDefinition) {\n            resolvers.push(resolversDefinition);\n        }\n    }\n    const result = mergeDeep(resolvers, true);\n    if (options?.exclusions) {\n        for (const exclusion of options.exclusions) {\n            const [typeName, fieldName] = exclusion.split('.');\n            if (['__proto__', 'constructor', 'prototype'].includes(typeName) ||\n                ['__proto__', 'constructor', 'prototype'].includes(fieldName)) {\n                continue;\n            }\n            if (!fieldName || fieldName === '*') {\n                delete result[typeName];\n            }\n            else if (result[typeName]) {\n                delete result[typeName][fieldName];\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AA8BO,SAAS,eAAe,oBAAoB,EAAE,OAAO;IACxD,IAAI,CAAC,wBACA,MAAM,OAAO,CAAC,yBAAyB,qBAAqB,MAAM,KAAK,GAAI;QAC5E,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,uBAAuB;QACtC,OAAO;IACX;IACA,IAAI,qBAAqB,MAAM,KAAK,GAAG;QACnC,OAAO,oBAAoB,CAAC,EAAE,IAAI,CAAC;IACvC;IACA,MAAM,YAAY,IAAI;IACtB,KAAK,IAAI,uBAAuB,qBAAsB;QAClD,IAAI,MAAM,OAAO,CAAC,sBAAsB;YACpC,sBAAsB,eAAe;QACzC;QACA,IAAI,OAAO,wBAAwB,YAAY,qBAAqB;YAChE,UAAU,IAAI,CAAC;QACnB;IACJ;IACA,MAAM,SAAS,IAAA,8KAAS,EAAC,WAAW;IACpC,IAAI,SAAS,YAAY;QACrB,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;YACxC,MAAM,CAAC,UAAU,UAAU,GAAG,UAAU,KAAK,CAAC;YAC9C,IAAI;gBAAC;gBAAa;gBAAe;aAAY,CAAC,QAAQ,CAAC,aACnD;gBAAC;gBAAa;gBAAe;aAAY,CAAC,QAAQ,CAAC,YAAY;gBAC/D;YACJ;YACA,IAAI,CAAC,aAAa,cAAc,KAAK;gBACjC,OAAO,MAAM,CAAC,SAAS;YAC3B,OACK,IAAI,MAAM,CAAC,SAAS,EAAE;gBACvB,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/links.js"], "sourcesContent": ["/**\n * A simplified, GraphQL v15 compatible version of\n * https://github.com/graphql-hive/federation-composition/blob/main/src/utils/link/index.ts\n * that does not provide the same safeguards or functionality, but still can determine the\n * correct name of an linked resource.\n */\nimport { Kind, } from 'graphql';\nfunction namespace(link) {\n    return link.as ?? link.url.name;\n}\nfunction defaultImport(link) {\n    const name = namespace(link);\n    return name && `@${name}`;\n}\nexport function resolveImportName(link, elementName) {\n    if (link.url.name && elementName === `@${link.url.name}`) {\n        // @note: default is a directive... So remove the `@`\n        return defaultImport(link).substring(1);\n    }\n    const imported = link.imports.find(i => i.name === elementName);\n    const resolvedName = imported?.as ?? imported?.name ?? namespaced(namespace(link), elementName);\n    // Strip the `@` prefix for directives because in all implementations of mapping or visiting a schema,\n    // directive names are not prefixed with `@`. The `@` is only for SDL.\n    return resolvedName.startsWith('@') ? resolvedName.substring(1) : resolvedName;\n}\nfunction namespaced(namespace, name) {\n    if (namespace?.length) {\n        if (name.startsWith('@')) {\n            return `@${namespace}__${name.substring(1)}`;\n        }\n        return `${namespace}__${name}`;\n    }\n    return name;\n}\nexport function extractLinks(typeDefs) {\n    let links = [];\n    for (const definition of typeDefs.definitions) {\n        if (definition.kind === Kind.SCHEMA_EXTENSION || definition.kind === Kind.SCHEMA_DEFINITION) {\n            const defLinks = definition.directives?.filter(directive => directive.name.value === 'link');\n            const parsedLinks = defLinks?.map(l => linkFromArgs(l.arguments ?? [])).filter(l => l !== undefined) ?? [];\n            links = links.concat(parsedLinks);\n            // Federation 1 support... Federation 1 uses \"@core\" instead of \"@link\", but behavior is similar enough that\n            //  it can be translated.\n            const defCores = definition.directives?.filter(({ name }) => name.value === 'core');\n            const coreLinks = defCores\n                ?.map(c => linkFromCoreArgs(c.arguments ?? []))\n                .filter(l => l !== undefined);\n            if (coreLinks) {\n                links = links.concat(...coreLinks);\n            }\n        }\n    }\n    return links;\n}\nfunction linkFromArgs(args) {\n    let url;\n    let imports = [];\n    let as;\n    for (const arg of args) {\n        switch (arg.name.value) {\n            case 'url': {\n                if (arg.value.kind === Kind.STRING) {\n                    url = parseFederationLinkUrl(arg.value.value);\n                }\n                break;\n            }\n            case 'import': {\n                imports = parseImportNode(arg.value);\n                break;\n            }\n            case 'as': {\n                if (arg.value.kind === Kind.STRING) {\n                    as = arg.value.value ?? undefined;\n                }\n                break;\n            }\n            default: {\n                // ignore. It's not the job of this package to validate. Federation should validate links.\n            }\n        }\n    }\n    if (url !== undefined) {\n        return {\n            url,\n            as,\n            imports,\n        };\n    }\n}\n/**\n * Supports federation 1\n */\nfunction linkFromCoreArgs(args) {\n    const feature = args.find(({ name, value }) => name.value === 'feature' && value.kind === Kind.STRING);\n    if (feature) {\n        const url = parseFederationLinkUrl(feature.value.value);\n        return {\n            url,\n            imports: [],\n        };\n    }\n}\nfunction parseImportNode(node) {\n    if (node.kind === Kind.LIST) {\n        const imports = node.values.map((v) => {\n            let namedImport;\n            if (v.kind === Kind.STRING) {\n                namedImport = { name: v.value };\n            }\n            else if (v.kind === Kind.OBJECT) {\n                let name = '';\n                let as;\n                for (const f of v.fields) {\n                    if (f.name.value === 'name') {\n                        if (f.value.kind === Kind.STRING) {\n                            name = f.value.value;\n                        }\n                    }\n                    else if (f.name.value === 'as') {\n                        if (f.value.kind === Kind.STRING) {\n                            as = f.value.value;\n                        }\n                    }\n                }\n                namedImport = { name, as };\n            }\n            return namedImport;\n        });\n        return imports.filter(i => i !== undefined);\n    }\n    return [];\n}\nconst VERSION_MATCH = /v(\\d{1,3})\\.(\\d{1,4})/i;\nfunction parseFederationLinkUrl(urlSource) {\n    const url = new URL(urlSource);\n    const parts = url.pathname.split('/').filter(Boolean);\n    const versionOrName = parts[parts.length - 1];\n    if (versionOrName) {\n        if (VERSION_MATCH.test(versionOrName)) {\n            const maybeName = parts[parts.length - 2];\n            return {\n                identity: url.origin + (maybeName ? `/${parts.slice(0, parts.length - 1).join('/')}` : ''),\n                name: maybeName ?? null,\n                version: versionOrName,\n            };\n        }\n        return {\n            identity: `${url.origin}/${parts.join('/')}`,\n            name: versionOrName,\n            version: null,\n        };\n    }\n    return {\n        identity: url.origin,\n        name: null,\n        version: null,\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;AACD;;AACA,SAAS,UAAU,IAAI;IACnB,OAAO,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI;AACnC;AACA,SAAS,cAAc,IAAI;IACvB,MAAM,OAAO,UAAU;IACvB,OAAO,QAAQ,CAAC,CAAC,EAAE,MAAM;AAC7B;AACO,SAAS,kBAAkB,IAAI,EAAE,WAAW;IAC/C,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,gBAAgB,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,EAAE,EAAE;QACtD,qDAAqD;QACrD,OAAO,cAAc,MAAM,SAAS,CAAC;IACzC;IACA,MAAM,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IACnD,MAAM,eAAe,UAAU,MAAM,UAAU,QAAQ,WAAW,UAAU,OAAO;IACnF,sGAAsG;IACtG,sEAAsE;IACtE,OAAO,aAAa,UAAU,CAAC,OAAO,aAAa,SAAS,CAAC,KAAK;AACtE;AACA,SAAS,WAAW,SAAS,EAAE,IAAI;IAC/B,IAAI,WAAW,QAAQ;QACnB,IAAI,KAAK,UAAU,CAAC,MAAM;YACtB,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,KAAK,SAAS,CAAC,IAAI;QAChD;QACA,OAAO,GAAG,UAAU,EAAE,EAAE,MAAM;IAClC;IACA,OAAO;AACX;AACO,SAAS,aAAa,QAAQ;IACjC,IAAI,QAAQ,EAAE;IACd,KAAK,MAAM,cAAc,SAAS,WAAW,CAAE;QAC3C,IAAI,WAAW,IAAI,KAAK,uJAAI,CAAC,gBAAgB,IAAI,WAAW,IAAI,KAAK,uJAAI,CAAC,iBAAiB,EAAE;YACzF,MAAM,WAAW,WAAW,UAAU,EAAE,OAAO,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;YACrF,MAAM,cAAc,UAAU,IAAI,CAAA,IAAK,aAAa,EAAE,SAAS,IAAI,EAAE,GAAG,OAAO,CAAA,IAAK,MAAM,cAAc,EAAE;YAC1G,QAAQ,MAAM,MAAM,CAAC;YACrB,4GAA4G;YAC5G,yBAAyB;YACzB,MAAM,WAAW,WAAW,UAAU,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK,KAAK,KAAK,KAAK;YAC5E,MAAM,YAAY,UACZ,IAAI,CAAA,IAAK,iBAAiB,EAAE,SAAS,IAAI,EAAE,GAC5C,OAAO,CAAA,IAAK,MAAM;YACvB,IAAI,WAAW;gBACX,QAAQ,MAAM,MAAM,IAAI;YAC5B;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,IAAI;IACtB,IAAI;IACJ,IAAI,UAAU,EAAE;IAChB,IAAI;IACJ,KAAK,MAAM,OAAO,KAAM;QACpB,OAAQ,IAAI,IAAI,CAAC,KAAK;YAClB,KAAK;gBAAO;oBACR,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,uJAAI,CAAC,MAAM,EAAE;wBAChC,MAAM,uBAAuB,IAAI,KAAK,CAAC,KAAK;oBAChD;oBACA;gBACJ;YACA,KAAK;gBAAU;oBACX,UAAU,gBAAgB,IAAI,KAAK;oBACnC;gBACJ;YACA,KAAK;gBAAM;oBACP,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,uJAAI,CAAC,MAAM,EAAE;wBAChC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI;oBAC5B;oBACA;gBACJ;YACA;gBAAS;gBACL,0FAA0F;gBAC9F;QACJ;IACJ;IACA,IAAI,QAAQ,WAAW;QACnB,OAAO;YACH;YACA;YACA;QACJ;IACJ;AACJ;AACA;;CAEC,GACD,SAAS,iBAAiB,IAAI;IAC1B,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,KAAK,KAAK,KAAK,aAAa,MAAM,IAAI,KAAK,uJAAI,CAAC,MAAM;IACrG,IAAI,SAAS;QACT,MAAM,MAAM,uBAAuB,QAAQ,KAAK,CAAC,KAAK;QACtD,OAAO;YACH;YACA,SAAS,EAAE;QACf;IACJ;AACJ;AACA,SAAS,gBAAgB,IAAI;IACzB,IAAI,KAAK,IAAI,KAAK,uJAAI,CAAC,IAAI,EAAE;QACzB,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI;YACJ,IAAI,EAAE,IAAI,KAAK,uJAAI,CAAC,MAAM,EAAE;gBACxB,cAAc;oBAAE,MAAM,EAAE,KAAK;gBAAC;YAClC,OACK,IAAI,EAAE,IAAI,KAAK,uJAAI,CAAC,MAAM,EAAE;gBAC7B,IAAI,OAAO;gBACX,IAAI;gBACJ,KAAK,MAAM,KAAK,EAAE,MAAM,CAAE;oBACtB,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,QAAQ;wBACzB,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,uJAAI,CAAC,MAAM,EAAE;4BAC9B,OAAO,EAAE,KAAK,CAAC,KAAK;wBACxB;oBACJ,OACK,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM;wBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,uJAAI,CAAC,MAAM,EAAE;4BAC9B,KAAK,EAAE,KAAK,CAAC,KAAK;wBACtB;oBACJ;gBACJ;gBACA,cAAc;oBAAE;oBAAM;gBAAG;YAC7B;YACA,OAAO;QACX;QACA,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM;IACrC;IACA,OAAO,EAAE;AACb;AACA,MAAM,gBAAgB;AACtB,SAAS,uBAAuB,SAAS;IACrC,MAAM,MAAM,IAAI,IAAI;IACpB,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC;IAC7C,MAAM,gBAAgB,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IAC7C,IAAI,eAAe;QACf,IAAI,cAAc,IAAI,CAAC,gBAAgB;YACnC,MAAM,YAAY,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;YACzC,OAAO;gBACH,UAAU,IAAI,MAAM,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE;gBACzF,MAAM,aAAa;gBACnB,SAAS;YACb;QACJ;QACA,OAAO;YACH,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,MAAM;YAC5C,MAAM;YACN,SAAS;QACb;IACJ;IACA,OAAO;QACH,UAAU,IAAI,MAAM;QACpB,MAAM;QACN,SAAS;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/directives.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nfunction isRepeatableDirective(directive, directives, repeatableLinkImports) {\n    return !!(directives?.[directive.name.value]?.repeatable ??\n        repeatableLinkImports?.has(directive.name.value));\n}\nfunction nameAlreadyExists(name, namesArr) {\n    return namesArr.some(({ value }) => value === name.value);\n}\nfunction mergeArguments(a1, a2) {\n    const result = [];\n    for (const argument of [...a2, ...a1]) {\n        const existingIndex = result.findIndex(a => a.name.value === argument.name.value);\n        if (existingIndex === -1) {\n            result.push(argument);\n        }\n        else {\n            const existingArg = result[existingIndex];\n            if (existingArg.value.kind === 'ListValue') {\n                const source = existingArg.value.values;\n                const target = argument.value.values;\n                // merge values of two lists\n                existingArg.value = {\n                    ...existingArg.value,\n                    values: deduplicateLists(source, target, (targetVal, source) => {\n                        const value = targetVal.value;\n                        return !value || !source.some((sourceVal) => sourceVal.value === value);\n                    }),\n                };\n            }\n            else {\n                existingArg.value = argument.value;\n            }\n        }\n    }\n    return result;\n}\nconst matchValues = (a, b) => {\n    if (a.kind === b.kind) {\n        switch (a.kind) {\n            case Kind.LIST:\n                return (a.values.length === b.values.length &&\n                    a.values.every(aVal => b.values.find(bVal => matchValues(aVal, bVal))));\n            case Kind.VARIABLE:\n            case Kind.NULL:\n                return true;\n            case Kind.OBJECT:\n                return (a.fields.length === b.fields.length &&\n                    a.fields.every(aField => b.fields.find(bField => aField.name.value === bField.name.value && matchValues(aField.value, bField.value))));\n            default:\n                return a.value === b.value;\n        }\n    }\n    return false;\n};\nconst matchArguments = (a, b) => a.name.value === b.name.value && a.value.kind === b.value.kind && matchValues(a.value, b.value);\n/**\n * Check if a directive is an exact match of another directive based on their\n * arguments.\n */\nconst matchDirectives = (a, b) => {\n    const matched = a.name.value === b.name.value &&\n        (a.arguments === b.arguments ||\n            (a.arguments?.length === b.arguments?.length &&\n                a.arguments?.every(argA => b.arguments?.find(argB => matchArguments(argA, argB)))));\n    return !!matched;\n};\nexport function mergeDirectives(d1 = [], d2 = [], config, directives) {\n    const reverseOrder = config && config.reverseDirectives;\n    const asNext = reverseOrder ? d1 : d2;\n    const asFirst = reverseOrder ? d2 : d1;\n    const result = [];\n    for (const directive of [...asNext, ...asFirst]) {\n        if (isRepeatableDirective(directive, directives, config?.repeatableLinkImports)) {\n            // look for repeated, identical directives that come before this instance\n            // if those exist, return null so that this directive gets removed.\n            const exactDuplicate = result.find(d => matchDirectives(directive, d));\n            if (!exactDuplicate) {\n                result.push(directive);\n            }\n        }\n        else {\n            const firstAt = result.findIndex(d => d.name.value === directive.name.value);\n            if (firstAt === -1) {\n                // if did not find a directive with this name on the result set already\n                result.push(directive);\n            }\n            else {\n                // if not repeatable and found directive with the same name already in the result set,\n                // then merge the arguments of the existing directive and the new directive\n                const mergedArguments = mergeArguments(directive.arguments ?? [], result[firstAt].arguments ?? []);\n                result[firstAt] = {\n                    ...result[firstAt],\n                    arguments: mergedArguments.length === 0 ? undefined : mergedArguments,\n                };\n            }\n        }\n    }\n    return result;\n}\nexport function mergeDirective(node, existingNode) {\n    if (existingNode) {\n        return {\n            ...node,\n            arguments: deduplicateLists(existingNode.arguments || [], node.arguments || [], (arg, existingArgs) => !nameAlreadyExists(arg.name, existingArgs.map(a => a.name))),\n            locations: [\n                ...existingNode.locations,\n                ...node.locations.filter(name => !nameAlreadyExists(name, existingNode.locations)),\n            ],\n        };\n    }\n    return node;\n}\nfunction deduplicateLists(source, target, filterFn) {\n    return source.concat(target.filter(val => filterFn(val, source)));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACA,SAAS,sBAAsB,SAAS,EAAE,UAAU,EAAE,qBAAqB;IACvE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE,cAC1C,uBAAuB,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC;AACxD;AACA,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACrC,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,UAAU,KAAK,KAAK;AAC5D;AACA,SAAS,eAAe,EAAE,EAAE,EAAE;IAC1B,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,YAAY;WAAI;WAAO;KAAG,CAAE;QACnC,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK;QAChF,IAAI,kBAAkB,CAAC,GAAG;YACtB,OAAO,IAAI,CAAC;QAChB,OACK;YACD,MAAM,cAAc,MAAM,CAAC,cAAc;YACzC,IAAI,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa;gBACxC,MAAM,SAAS,YAAY,KAAK,CAAC,MAAM;gBACvC,MAAM,SAAS,SAAS,KAAK,CAAC,MAAM;gBACpC,4BAA4B;gBAC5B,YAAY,KAAK,GAAG;oBAChB,GAAG,YAAY,KAAK;oBACpB,QAAQ,iBAAiB,QAAQ,QAAQ,CAAC,WAAW;wBACjD,MAAM,QAAQ,UAAU,KAAK;wBAC7B,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;oBACrE;gBACJ;YACJ,OACK;gBACD,YAAY,KAAK,GAAG,SAAS,KAAK;YACtC;QACJ;IACJ;IACA,OAAO;AACX;AACA,MAAM,cAAc,CAAC,GAAG;IACpB,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;QACnB,OAAQ,EAAE,IAAI;YACV,KAAK,uJAAI,CAAC,IAAI;gBACV,OAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC,MAAM,IACvC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA,OAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA,OAAQ,YAAY,MAAM;YACvE,KAAK,uJAAI,CAAC,QAAQ;YAClB,KAAK,uJAAI,CAAC,IAAI;gBACV,OAAO;YACX,KAAK,uJAAI,CAAC,MAAM;gBACZ,OAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC,MAAM,IACvC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA,SAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,YAAY,OAAO,KAAK,EAAE,OAAO,KAAK;YAC1I;gBACI,OAAO,EAAE,KAAK,KAAK,EAAE,KAAK;QAClC;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,YAAY,EAAE,KAAK,EAAE,EAAE,KAAK;AAC/H;;;CAGC,GACD,MAAM,kBAAkB,CAAC,GAAG;IACxB,MAAM,UAAU,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,IACzC,CAAC,EAAE,SAAS,KAAK,EAAE,SAAS,IACvB,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAClC,EAAE,SAAS,EAAE,MAAM,CAAA,OAAQ,EAAE,SAAS,EAAE,KAAK,CAAA,OAAQ,eAAe,MAAM,OAAQ;IAC9F,OAAO,CAAC,CAAC;AACb;AACO,SAAS,gBAAgB,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU;IAChE,MAAM,eAAe,UAAU,OAAO,iBAAiB;IACvD,MAAM,SAAS,eAAe,KAAK;IACnC,MAAM,UAAU,eAAe,KAAK;IACpC,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,aAAa;WAAI;WAAW;KAAQ,CAAE;QAC7C,IAAI,sBAAsB,WAAW,YAAY,QAAQ,wBAAwB;YAC7E,yEAAyE;YACzE,mEAAmE;YACnE,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,IAAK,gBAAgB,WAAW;YACnE,IAAI,CAAC,gBAAgB;gBACjB,OAAO,IAAI,CAAC;YAChB;QACJ,OACK;YACD,MAAM,UAAU,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;YAC3E,IAAI,YAAY,CAAC,GAAG;gBAChB,uEAAuE;gBACvE,OAAO,IAAI,CAAC;YAChB,OACK;gBACD,sFAAsF;gBACtF,2EAA2E;gBAC3E,MAAM,kBAAkB,eAAe,UAAU,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE;gBACjG,MAAM,CAAC,QAAQ,GAAG;oBACd,GAAG,MAAM,CAAC,QAAQ;oBAClB,WAAW,gBAAgB,MAAM,KAAK,IAAI,YAAY;gBAC1D;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS,eAAe,IAAI,EAAE,YAAY;IAC7C,IAAI,cAAc;QACd,OAAO;YACH,GAAG,IAAI;YACP,WAAW,iBAAiB,aAAa,SAAS,IAAI,EAAE,EAAE,KAAK,SAAS,IAAI,EAAE,EAAE,CAAC,KAAK,eAAiB,CAAC,kBAAkB,IAAI,IAAI,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAChK,WAAW;mBACJ,aAAa,SAAS;mBACtB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,kBAAkB,MAAM,aAAa,SAAS;aACnF;QACL;IACJ;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC9C,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAA,MAAO,SAAS,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/enum-values.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nimport { mergeDirectives } from './directives.js';\nexport function mergeEnumValues(first, second, config, directives) {\n    if (config?.consistentEnumMerge) {\n        const reversed = [];\n        if (first) {\n            reversed.push(...first);\n        }\n        first = second;\n        second = reversed;\n    }\n    const enumValueMap = new Map();\n    if (first) {\n        for (const firstValue of first) {\n            enumValueMap.set(firstValue.name.value, firstValue);\n        }\n    }\n    if (second) {\n        for (const secondValue of second) {\n            const enumValue = secondValue.name.value;\n            if (enumValueMap.has(enumValue)) {\n                const firstValue = enumValueMap.get(enumValue);\n                firstValue.description = secondValue.description || firstValue.description;\n                firstValue.directives = mergeDirectives(secondValue.directives, firstValue.directives, directives);\n            }\n            else {\n                enumValueMap.set(enumValue, secondValue);\n            }\n        }\n    }\n    const result = [...enumValueMap.values()];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,gBAAgB,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IAC7D,IAAI,QAAQ,qBAAqB;QAC7B,MAAM,WAAW,EAAE;QACnB,IAAI,OAAO;YACP,SAAS,IAAI,IAAI;QACrB;QACA,QAAQ;QACR,SAAS;IACb;IACA,MAAM,eAAe,IAAI;IACzB,IAAI,OAAO;QACP,KAAK,MAAM,cAAc,MAAO;YAC5B,aAAa,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE;QAC5C;IACJ;IACA,IAAI,QAAQ;QACR,KAAK,MAAM,eAAe,OAAQ;YAC9B,MAAM,YAAY,YAAY,IAAI,CAAC,KAAK;YACxC,IAAI,aAAa,GAAG,CAAC,YAAY;gBAC7B,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,WAAW,WAAW,GAAG,YAAY,WAAW,IAAI,WAAW,WAAW;gBAC1E,WAAW,UAAU,GAAG,IAAA,4MAAe,EAAC,YAAY,UAAU,EAAE,WAAW,UAAU,EAAE;YAC3F,OACK;gBACD,aAAa,GAAG,CAAC,WAAW;YAChC;QACJ;IACJ;IACA,MAAM,SAAS;WAAI,aAAa,MAAM;KAAG;IACzC,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+KAAY;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/enum.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeEnumValues } from './enum-values.js';\nexport function mergeEnum(e1, e2, config, directives) {\n    if (e2) {\n        return {\n            name: e1.name,\n            description: e1['description'] || e2['description'],\n            kind: config?.convertExtensions ||\n                e1.kind === 'EnumTypeDefinition' ||\n                e2.kind === 'EnumTypeDefinition'\n                ? 'EnumTypeDefinition'\n                : 'EnumTypeExtension',\n            loc: e1.loc,\n            directives: mergeDirectives(e1.directives, e2.directives, config, directives),\n            values: mergeEnumValues(e1.values, e2.values, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...e1,\n            kind: Kind.ENUM_TYPE_DEFINITION,\n        }\n        : e1;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IAChD,IAAI,IAAI;QACJ,OAAO;YACH,MAAM,GAAG,IAAI;YACb,aAAa,EAAE,CAAC,cAAc,IAAI,EAAE,CAAC,cAAc;YACnD,MAAM,QAAQ,qBACV,GAAG,IAAI,KAAK,wBACZ,GAAG,IAAI,KAAK,uBACV,uBACA;YACN,KAAK,GAAG,GAAG;YACX,YAAY,IAAA,4MAAe,EAAC,GAAG,UAAU,EAAE,GAAG,UAAU,EAAE,QAAQ;YAClE,QAAQ,IAAA,gNAAe,EAAC,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;QAClD;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,EAAE;QACL,MAAM,uJAAI,CAAC,oBAAoB;IACnC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/arguments.js"], "sourcesContent": ["import { compareNodes, isSome } from '@graphql-tools/utils';\nexport function mergeArguments(args1, args2, config) {\n    const result = deduplicateArguments([...args2, ...args1].filter(isSome), config);\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\nfunction deduplicateArguments(args, config) {\n    return args.reduce((acc, current) => {\n        const dupIndex = acc.findIndex(arg => arg.name.value === current.name.value);\n        if (dupIndex === -1) {\n            return acc.concat([current]);\n        }\n        else if (!config?.reverseArguments) {\n            acc[dupIndex] = current;\n        }\n        return acc;\n    }, []);\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,MAAM;IAC/C,MAAM,SAAS,qBAAqB;WAAI;WAAU;KAAM,CAAC,MAAM,CAAC,yKAAM,GAAG;IACzE,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+KAAY;IAC5B;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,IAAI,EAAE,MAAM;IACtC,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,WAAW,IAAI,SAAS,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;QAC3E,IAAI,aAAa,CAAC,GAAG;YACjB,OAAO,IAAI,MAAM,CAAC;gBAAC;aAAQ;QAC/B,OACK,IAAI,CAAC,QAAQ,kBAAkB;YAChC,GAAG,CAAC,SAAS,GAAG;QACpB;QACA,OAAO;IACX,GAAG,EAAE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/utils.js"], "sourcesContent": ["import { Kind, Source } from 'graphql';\nexport function isStringTypes(types) {\n    return typeof types === 'string';\n}\nexport function isSourceTypes(types) {\n    return types instanceof Source;\n}\nexport function extractType(type) {\n    let visitedType = type;\n    while (visitedType.kind === Kind.LIST_TYPE || visitedType.kind === 'NonNullType') {\n        visitedType = visitedType.type;\n    }\n    return visitedType;\n}\nexport function isWrappingTypeNode(type) {\n    return type.kind !== Kind.NAMED_TYPE;\n}\nexport function isListTypeNode(type) {\n    return type.kind === Kind.LIST_TYPE;\n}\nexport function isNonNullTypeNode(type) {\n    return type.kind === Kind.NON_NULL_TYPE;\n}\nexport function printTypeNode(type) {\n    if (isListTypeNode(type)) {\n        return `[${printTypeNode(type.type)}]`;\n    }\n    if (isNonNullTypeNode(type)) {\n        return `${printTypeNode(type.type)}!`;\n    }\n    return type.name.value;\n}\nexport var CompareVal;\n(function (CompareVal) {\n    CompareVal[CompareVal[\"A_SMALLER_THAN_B\"] = -1] = \"A_SMALLER_THAN_B\";\n    CompareVal[CompareVal[\"A_EQUALS_B\"] = 0] = \"A_EQUALS_B\";\n    CompareVal[CompareVal[\"A_GREATER_THAN_B\"] = 1] = \"A_GREATER_THAN_B\";\n})(CompareVal || (CompareVal = {}));\nexport function defaultStringComparator(a, b) {\n    if (a == null && b == null) {\n        return CompareVal.A_EQUALS_B;\n    }\n    if (a == null) {\n        return CompareVal.A_SMALLER_THAN_B;\n    }\n    if (b == null) {\n        return CompareVal.A_GREATER_THAN_B;\n    }\n    if (a < b)\n        return CompareVal.A_SMALLER_THAN_B;\n    if (a > b)\n        return CompareVal.A_GREATER_THAN_B;\n    return CompareVal.A_EQUALS_B;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,OAAO,UAAU;AAC5B;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,iBAAiB,0JAAM;AAClC;AACO,SAAS,YAAY,IAAI;IAC5B,IAAI,cAAc;IAClB,MAAO,YAAY,IAAI,KAAK,uJAAI,CAAC,SAAS,IAAI,YAAY,IAAI,KAAK,cAAe;QAC9E,cAAc,YAAY,IAAI;IAClC;IACA,OAAO;AACX;AACO,SAAS,mBAAmB,IAAI;IACnC,OAAO,KAAK,IAAI,KAAK,uJAAI,CAAC,UAAU;AACxC;AACO,SAAS,eAAe,IAAI;IAC/B,OAAO,KAAK,IAAI,KAAK,uJAAI,CAAC,SAAS;AACvC;AACO,SAAS,kBAAkB,IAAI;IAClC,OAAO,KAAK,IAAI,KAAK,uJAAI,CAAC,aAAa;AAC3C;AACO,SAAS,cAAc,IAAI;IAC9B,IAAI,eAAe,OAAO;QACtB,OAAO,CAAC,CAAC,EAAE,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,kBAAkB,OAAO;QACzB,OAAO,GAAG,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IACzC;IACA,OAAO,KAAK,IAAI,CAAC,KAAK;AAC1B;AACO,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,EAAE,GAAG;IAClD,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,EAAE,GAAG;AACrD,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAC1B,SAAS,wBAAwB,CAAC,EAAE,CAAC;IACxC,IAAI,KAAK,QAAQ,KAAK,MAAM;QACxB,OAAO,WAAW,UAAU;IAChC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,OAAO,WAAW,UAAU;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/fields.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nimport { mergeArguments } from './arguments.js';\nimport { mergeDirectives } from './directives.js';\nimport { extractType, isListTypeNode, isNonNullTypeNode, isWrappingTypeNode, printTypeNode, } from './utils.js';\nfunction fieldAlreadyExists(fieldsArr, otherField) {\n    const resultIndex = fieldsArr.findIndex(field => field.name.value === otherField.name.value);\n    return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];\n}\nexport function mergeFields(type, f1, f2, config, directives) {\n    const result = [];\n    if (f2 != null) {\n        result.push(...f2);\n    }\n    if (f1 != null) {\n        for (const field of f1) {\n            const [existing, existingIndex] = fieldAlreadyExists(result, field);\n            if (existing && !config?.ignoreFieldConflicts) {\n                const newField = (config?.onFieldTypeConflict &&\n                    config.onFieldTypeConflict(existing, field, type, config?.throwOnConflict)) ||\n                    preventConflicts(type, existing, field, config?.throwOnConflict);\n                newField.arguments = mergeArguments(field['arguments'] || [], existing['arguments'] || [], config);\n                newField.directives = mergeDirectives(field.directives, existing.directives, config, directives);\n                newField.description = field.description || existing.description;\n                result[existingIndex] = newField;\n            }\n            else {\n                result.push(field);\n            }\n        }\n    }\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    if (config && config.exclusions) {\n        const exclusions = config.exclusions;\n        return result.filter(field => !exclusions.includes(`${type.name.value}.${field.name.value}`));\n    }\n    return result;\n}\nfunction preventConflicts(type, a, b, ignoreNullability = false) {\n    const aType = printTypeNode(a.type);\n    const bType = printTypeNode(b.type);\n    if (aType !== bType) {\n        const t1 = extractType(a.type);\n        const t2 = extractType(b.type);\n        if (t1.name.value !== t2.name.value) {\n            throw new Error(`Field \"${b.name.value}\" already defined with a different type. Declared as \"${t1.name.value}\", but you tried to override with \"${t2.name.value}\"`);\n        }\n        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {\n            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);\n        }\n    }\n    if (isNonNullTypeNode(b.type) && !isNonNullTypeNode(a.type)) {\n        a.type = b.type;\n    }\n    return a;\n}\nfunction safeChangeForFieldType(oldType, newType, ignoreNullability = false) {\n    // both are named\n    if (!isWrappingTypeNode(oldType) && !isWrappingTypeNode(newType)) {\n        return oldType.toString() === newType.toString();\n    }\n    // new is non-null\n    if (isNonNullTypeNode(newType)) {\n        const ofType = isNonNullTypeNode(oldType) ? oldType.type : oldType;\n        return safeChangeForFieldType(ofType, newType.type);\n    }\n    // old is non-null\n    if (isNonNullTypeNode(oldType)) {\n        return safeChangeForFieldType(newType, oldType, ignoreNullability);\n    }\n    // old is list\n    if (isListTypeNode(oldType)) {\n        return ((isListTypeNode(newType) && safeChangeForFieldType(oldType.type, newType.type)) ||\n            (isNonNullTypeNode(newType) && safeChangeForFieldType(oldType, newType['type'])));\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,mBAAmB,SAAS,EAAE,UAAU;IAC7C,MAAM,cAAc,UAAU,SAAS,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK;IAC3F,OAAO;QAAC,cAAc,CAAC,IAAI,SAAS,CAAC,YAAY,GAAG;QAAM;KAAY;AAC1E;AACO,SAAS,YAAY,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACxD,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM,MAAM;QACZ,OAAO,IAAI,IAAI;IACnB;IACA,IAAI,MAAM,MAAM;QACZ,KAAK,MAAM,SAAS,GAAI;YACpB,MAAM,CAAC,UAAU,cAAc,GAAG,mBAAmB,QAAQ;YAC7D,IAAI,YAAY,CAAC,QAAQ,sBAAsB;gBAC3C,MAAM,WAAW,AAAC,QAAQ,uBACtB,OAAO,mBAAmB,CAAC,UAAU,OAAO,MAAM,QAAQ,oBAC1D,iBAAiB,MAAM,UAAU,OAAO,QAAQ;gBACpD,SAAS,SAAS,GAAG,IAAA,0MAAc,EAAC,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE;gBAC3F,SAAS,UAAU,GAAG,IAAA,4MAAe,EAAC,MAAM,UAAU,EAAE,SAAS,UAAU,EAAE,QAAQ;gBACrF,SAAS,WAAW,GAAG,MAAM,WAAW,IAAI,SAAS,WAAW;gBAChE,MAAM,CAAC,cAAc,GAAG;YAC5B,OACK;gBACD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ;IACA,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+KAAY;IAC5B;IACA,IAAI,UAAU,OAAO,UAAU,EAAE;QAC7B,MAAM,aAAa,OAAO,UAAU;QACpC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;IAC/F;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB,KAAK;IAC3D,MAAM,QAAQ,IAAA,qMAAa,EAAC,EAAE,IAAI;IAClC,MAAM,QAAQ,IAAA,qMAAa,EAAC,EAAE,IAAI;IAClC,IAAI,UAAU,OAAO;QACjB,MAAM,KAAK,IAAA,mMAAW,EAAC,EAAE,IAAI;QAC7B,MAAM,KAAK,IAAA,mMAAW,EAAC,EAAE,IAAI;QAC7B,IAAI,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,sDAAsD,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACtK;QACA,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB;YAC7D,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3G;IACJ;IACA,IAAI,IAAA,yMAAiB,EAAC,EAAE,IAAI,KAAK,CAAC,IAAA,yMAAiB,EAAC,EAAE,IAAI,GAAG;QACzD,EAAE,IAAI,GAAG,EAAE,IAAI;IACnB;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB,KAAK;IACvE,iBAAiB;IACjB,IAAI,CAAC,IAAA,0MAAkB,EAAC,YAAY,CAAC,IAAA,0MAAkB,EAAC,UAAU;QAC9D,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;IAClD;IACA,kBAAkB;IAClB,IAAI,IAAA,yMAAiB,EAAC,UAAU;QAC5B,MAAM,SAAS,IAAA,yMAAiB,EAAC,WAAW,QAAQ,IAAI,GAAG;QAC3D,OAAO,uBAAuB,QAAQ,QAAQ,IAAI;IACtD;IACA,kBAAkB;IAClB,IAAI,IAAA,yMAAiB,EAAC,UAAU;QAC5B,OAAO,uBAAuB,SAAS,SAAS;IACpD;IACA,cAAc;IACd,IAAI,IAAA,sMAAc,EAAC,UAAU;QACzB,OAAQ,AAAC,IAAA,sMAAc,EAAC,YAAY,uBAAuB,QAAQ,IAAI,EAAE,QAAQ,IAAI,KAChF,IAAA,yMAAiB,EAAC,YAAY,uBAAuB,SAAS,OAAO,CAAC,OAAO;IACtF;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/input-type.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nexport function mergeInputType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InputObjectTypeDefinition' ||\n                    existingNode.kind === 'InputObjectTypeDefinition'\n                    ? 'InputObjectTypeDefinition'\n                    : 'InputObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL input type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,+BACd,aAAa,IAAI,KAAK,8BACpB,8BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,IAAA,oMAAW,EAAC,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC5D,YAAY,IAAA,4MAAe,EAAC,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YAClF;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC3F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,uJAAI,CAAC,4BAA4B;IAC3C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nfunction alreadyExists(arr, other) {\n    return !!arr.find(i => i.name.value === other.name.value);\n}\nexport function mergeNamedTypeArray(first = [], second = [], config = {}) {\n    const result = [...second, ...first.filter(d => !alreadyExists(second, d))];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,cAAc,GAAG,EAAE,KAAK;IAC7B,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK;AAC5D;AACO,SAAS,oBAAoB,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;IACpE,MAAM,SAAS;WAAI;WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,cAAc,QAAQ;KAAI;IAC3E,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+KAAY;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/interface.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeInterface(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InterfaceTypeDefinition' ||\n                    existingNode.kind === 'InterfaceTypeDefinition'\n                    ? 'InterfaceTypeDefinition'\n                    : 'InterfaceTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config, directives),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: node['interfaces']\n                    ? mergeNamedTypeArray(node['interfaces'], existingNode['interfaces'], config)\n                    : undefined,\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL interface \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.INTERFACE_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,6BACd,aAAa,IAAI,KAAK,4BACpB,4BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,IAAA,oMAAW,EAAC,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE,QAAQ;gBACpE,YAAY,IAAA,4MAAe,EAAC,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBAC9E,YAAY,IAAI,CAAC,aAAa,GACxB,IAAA,qOAAmB,EAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,EAAE,UACpE;YACV;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC1F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,uJAAI,CAAC,yBAAyB;IACxC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/scalar.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport function mergeScalar(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            name: node.name,\n            description: node['description'] || existingNode['description'],\n            kind: config?.convertExtensions ||\n                node.kind === 'ScalarTypeDefinition' ||\n                existingNode.kind === 'ScalarTypeDefinition'\n                ? 'ScalarTypeDefinition'\n                : 'ScalarTypeExtension',\n            loc: node.loc,\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.SCALAR_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,YAAY,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC9D,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI;YACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;YACN,KAAK,KAAK,GAAG;YACb,YAAY,IAAA,4MAAe,EAAC,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;QAClF;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,uJAAI,CAAC,sBAAsB;IACrC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/schema-def.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport const DEFAULT_OPERATION_TYPE_NAME_MAP = {\n    query: 'Query',\n    mutation: 'Mutation',\n    subscription: 'Subscription',\n};\nfunction mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {\n    const finalOpNodeList = [];\n    for (const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n        const opNode = opNodeList.find(n => n.operation === opNodeType) ||\n            existingOpNodeList.find(n => n.operation === opNodeType);\n        if (opNode) {\n            finalOpNodeList.push(opNode);\n        }\n    }\n    return finalOpNodeList;\n}\nexport function mergeSchemaDefs(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            kind: node.kind === Kind.SCHEMA_DEFINITION || existingNode.kind === Kind.SCHEMA_DEFINITION\n                ? Kind.SCHEMA_DEFINITION\n                : Kind.SCHEMA_EXTENSION,\n            description: node['description'] || existingNode['description'],\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes),\n        };\n    }\n    return (config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.SCHEMA_DEFINITION,\n        }\n        : node);\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AACO,MAAM,kCAAkC;IAC3C,OAAO;IACP,UAAU;IACV,cAAc;AAClB;AACA,SAAS,oBAAoB,aAAa,EAAE,EAAE,qBAAqB,EAAE;IACjE,MAAM,kBAAkB,EAAE;IAC1B,IAAK,MAAM,cAAc,gCAAiC;QACtD,MAAM,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,eAChD,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACjD,IAAI,QAAQ;YACR,gBAAgB,IAAI,CAAC;QACzB;IACJ;IACA,OAAO;AACX;AACO,SAAS,gBAAgB,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAClE,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI,KAAK,uJAAI,CAAC,iBAAiB,IAAI,aAAa,IAAI,KAAK,uJAAI,CAAC,iBAAiB,GACpF,uJAAI,CAAC,iBAAiB,GACtB,uJAAI,CAAC,gBAAgB;YAC3B,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,YAAY,IAAA,4MAAe,EAAC,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YAC9E,gBAAgB,oBAAoB,KAAK,cAAc,EAAE,aAAa,cAAc;QACxF;IACJ;IACA,OAAQ,QAAQ,oBACV;QACE,GAAG,IAAI;QACP,MAAM,uJAAI,CAAC,iBAAiB;IAChC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/type.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'ObjectTypeDefinition' ||\n                    existingNode.kind === 'ObjectTypeDefinition'\n                    ? 'ObjectTypeDefinition'\n                    : 'ObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config, directives),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: mergeNamedTypeArray(node.interfaces, existingNode.interfaces, config),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,UAAU,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC5D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,IAAA,oMAAW,EAAC,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE,QAAQ;gBACpE,YAAY,IAAA,4MAAe,EAAC,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBAC9E,YAAY,IAAA,qOAAmB,EAAC,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE;YAC9E;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QACrF;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,uJAAI,CAAC,sBAAsB;IACrC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/union.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeUnion(first, second, config, directives) {\n    if (second) {\n        return {\n            name: first.name,\n            description: first['description'] || second['description'],\n            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n            directives: mergeDirectives(first.directives, second.directives, config, directives),\n            kind: config?.convertExtensions ||\n                first.kind === 'UnionTypeDefinition' ||\n                second.kind === 'UnionTypeDefinition'\n                ? Kind.UNION_TYPE_DEFINITION\n                : Kind.UNION_TYPE_EXTENSION,\n            loc: first.loc,\n            types: mergeNamedTypeArray(first.types, second.types, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...first,\n            kind: Kind.UNION_TYPE_DEFINITION,\n        }\n        : first;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,SAAS,WAAW,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACxD,IAAI,QAAQ;QACR,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,aAAa,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc;YAC1D,0HAA0H;YAC1H,YAAY,IAAA,4MAAe,EAAC,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,QAAQ;YACzE,MAAM,QAAQ,qBACV,MAAM,IAAI,KAAK,yBACf,OAAO,IAAI,KAAK,wBACd,uJAAI,CAAC,qBAAqB,GAC1B,uJAAI,CAAC,oBAAoB;YAC/B,KAAK,MAAM,GAAG;YACd,OAAO,IAAA,qOAAmB,EAAC,MAAM,KAAK,EAAE,OAAO,KAAK,EAAE;QAC1D;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,KAAK;QACR,MAAM,uJAAI,CAAC,qBAAqB;IACpC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { collectComment } from '@graphql-tools/utils';\nimport { mergeDirective } from './directives.js';\nimport { mergeEnum } from './enum.js';\nimport { mergeInputType } from './input-type.js';\nimport { mergeInterface } from './interface.js';\nimport { mergeScalar } from './scalar.js';\nimport { mergeSchemaDefs } from './schema-def.js';\nimport { mergeType } from './type.js';\nimport { mergeUnion } from './union.js';\nexport const schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';\nexport function isNamedDefinitionNode(definitionNode) {\n    return 'name' in definitionNode;\n}\nexport function mergeGraphQLNodes(nodes, config, directives = {}) {\n    const mergedResultMap = directives;\n    for (const nodeDefinition of nodes) {\n        if (isNamedDefinitionNode(nodeDefinition)) {\n            const name = nodeDefinition.name?.value;\n            if (config?.commentDescriptions) {\n                collectComment(nodeDefinition);\n            }\n            if (name == null) {\n                continue;\n            }\n            if (config?.exclusions?.includes(name + '.*') || config?.exclusions?.includes(name)) {\n                delete mergedResultMap[name];\n            }\n            else {\n                switch (nodeDefinition.kind) {\n                    case Kind.OBJECT_TYPE_DEFINITION:\n                    case Kind.OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.ENUM_TYPE_DEFINITION:\n                    case Kind.ENUM_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeEnum(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.UNION_TYPE_DEFINITION:\n                    case Kind.UNION_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeUnion(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.SCALAR_TYPE_DEFINITION:\n                    case Kind.SCALAR_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeScalar(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n                    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInputType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INTERFACE_TYPE_DEFINITION:\n                    case Kind.INTERFACE_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInterface(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.DIRECTIVE_DEFINITION:\n                        if (mergedResultMap[name]) {\n                            const isInheritedFromPrototype = name in {}; // i.e. toString\n                            if (isInheritedFromPrototype) {\n                                if (!isASTNode(mergedResultMap[name])) {\n                                    mergedResultMap[name] = undefined;\n                                }\n                            }\n                        }\n                        mergedResultMap[name] = mergeDirective(nodeDefinition, mergedResultMap[name]);\n                        break;\n                }\n            }\n        }\n        else if (nodeDefinition.kind === Kind.SCHEMA_DEFINITION ||\n            nodeDefinition.kind === Kind.SCHEMA_EXTENSION) {\n            mergedResultMap[schemaDefSymbol] = mergeSchemaDefs(nodeDefinition, mergedResultMap[schemaDefSymbol], config);\n        }\n    }\n    return mergedResultMap;\n}\nfunction isASTNode(node) {\n    return (node != null && typeof node === 'object' && 'kind' in node && typeof node.kind === 'string');\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,MAAM,kBAAkB;AACxB,SAAS,sBAAsB,cAAc;IAChD,OAAO,UAAU;AACrB;AACO,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC5D,MAAM,kBAAkB;IACxB,KAAK,MAAM,kBAAkB,MAAO;QAChC,IAAI,sBAAsB,iBAAiB;YACvC,MAAM,OAAO,eAAe,IAAI,EAAE;YAClC,IAAI,QAAQ,qBAAqB;gBAC7B,IAAA,kLAAc,EAAC;YACnB;YACA,IAAI,QAAQ,MAAM;gBACd;YACJ;YACA,IAAI,QAAQ,YAAY,SAAS,OAAO,SAAS,QAAQ,YAAY,SAAS,OAAO;gBACjF,OAAO,eAAe,CAAC,KAAK;YAChC,OACK;gBACD,OAAQ,eAAe,IAAI;oBACvB,KAAK,uJAAI,CAAC,sBAAsB;oBAChC,KAAK,uJAAI,CAAC,qBAAqB;wBAC3B,eAAe,CAAC,KAAK,GAAG,IAAA,gMAAS,EAAC,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACjF;oBACJ,KAAK,uJAAI,CAAC,oBAAoB;oBAC9B,KAAK,uJAAI,CAAC,mBAAmB;wBACzB,eAAe,CAAC,KAAK,GAAG,IAAA,gMAAS,EAAC,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACjF;oBACJ,KAAK,uJAAI,CAAC,qBAAqB;oBAC/B,KAAK,uJAAI,CAAC,oBAAoB;wBAC1B,eAAe,CAAC,KAAK,GAAG,IAAA,kMAAU,EAAC,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAClF;oBACJ,KAAK,uJAAI,CAAC,sBAAsB;oBAChC,KAAK,uJAAI,CAAC,qBAAqB;wBAC3B,eAAe,CAAC,KAAK,GAAG,IAAA,oMAAW,EAAC,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACnF;oBACJ,KAAK,uJAAI,CAAC,4BAA4B;oBACtC,KAAK,uJAAI,CAAC,2BAA2B;wBACjC,eAAe,CAAC,KAAK,GAAG,IAAA,8MAAc,EAAC,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACtF;oBACJ,KAAK,uJAAI,CAAC,yBAAyB;oBACnC,KAAK,uJAAI,CAAC,wBAAwB;wBAC9B,eAAe,CAAC,KAAK,GAAG,IAAA,0MAAc,EAAC,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACtF;oBACJ,KAAK,uJAAI,CAAC,oBAAoB;wBAC1B,IAAI,eAAe,CAAC,KAAK,EAAE;4BACvB,MAAM,2BAA2B,QAAQ,CAAC,GAAG,gBAAgB;4BAC7D,IAAI,0BAA0B;gCAC1B,IAAI,CAAC,UAAU,eAAe,CAAC,KAAK,GAAG;oCACnC,eAAe,CAAC,KAAK,GAAG;gCAC5B;4BACJ;wBACJ;wBACA,eAAe,CAAC,KAAK,GAAG,IAAA,2MAAc,EAAC,gBAAgB,eAAe,CAAC,KAAK;wBAC5E;gBACR;YACJ;QACJ,OACK,IAAI,eAAe,IAAI,KAAK,uJAAI,CAAC,iBAAiB,IACnD,eAAe,IAAI,KAAK,uJAAI,CAAC,gBAAgB,EAAE;YAC/C,eAAe,CAAC,gBAAgB,GAAG,IAAA,+MAAe,EAAC,gBAAgB,eAAe,CAAC,gBAAgB,EAAE;QACzG;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAI;IACnB,OAAQ,QAAQ,QAAQ,OAAO,SAAS,YAAY,UAAU,QAAQ,OAAO,KAAK,IAAI,KAAK;AAC/F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js"], "sourcesContent": ["import { isDefinitionNode, isSchema, Kind, parse, } from 'graphql';\nimport { getDocumentNodeFromSchema, isDocumentNode, printWithComments, resetComments, } from '@graphql-tools/utils';\nimport { extractLinks, resolveImportName } from '../links.js';\nimport { mergeGraphQLNodes, schemaDefSymbol } from './merge-nodes.js';\nimport { DEFAULT_OPERATION_TYPE_NAME_MAP } from './schema-def.js';\nimport { defaultStringComparator, isSourceTypes, isStringTypes } from './utils.js';\nexport function mergeTypeDefs(typeSource, config) {\n    resetComments();\n    const doc = {\n        kind: Kind.DOCUMENT,\n        definitions: mergeGraphQLTypes(typeSource, {\n            useSchemaDefinition: true,\n            forceSchemaDefinition: false,\n            throwOnConflict: false,\n            commentDescriptions: false,\n            ...config,\n        }),\n    };\n    let result;\n    if (config?.commentDescriptions) {\n        result = printWithComments(doc);\n    }\n    else {\n        result = doc;\n    }\n    resetComments();\n    return result;\n}\nfunction visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set(), repeatableLinkImports = new Set()) {\n    const addRepeatable = (name) => {\n        repeatableLinkImports.add(name);\n    };\n    if (typeSource && !visitedTypeSources.has(typeSource)) {\n        visitedTypeSources.add(typeSource);\n        if (typeof typeSource === 'function') {\n            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else if (Array.isArray(typeSource)) {\n            for (const type of typeSource) {\n                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n            }\n        }\n        else if (isSchema(typeSource)) {\n            const documentNode = getDocumentNodeFromSchema(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else if (isStringTypes(typeSource) || isSourceTypes(typeSource)) {\n            const documentNode = parse(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else if (typeof typeSource === 'object' && isDefinitionNode(typeSource)) {\n            const links = extractLinks({\n                definitions: [typeSource],\n                kind: Kind.DOCUMENT,\n            });\n            const federationUrl = 'https://specs.apollo.dev/federation';\n            const linkUrl = 'https://specs.apollo.dev/link';\n            /**\n             * Official Federated imports are special because they can be referenced without specifyin the import.\n             * To handle this case, we must prepare a list of all the possible valid usages to check against.\n             * Note that this versioning is not technically correct, since some definitions are after v2.0.\n             * But this is enough information to be comfortable not blocking the imports at this phase. It's\n             * the job of the composer to validate the versions.\n             * */\n            const federationLink = links.find(l => l.url.identity === federationUrl);\n            if (federationLink) {\n                addRepeatable(resolveImportName(federationLink, '@composeDirective'));\n                addRepeatable(resolveImportName(federationLink, '@key'));\n            }\n            const linkLink = links.find(l => l.url.identity === linkUrl);\n            if (linkLink) {\n                addRepeatable(resolveImportName(linkLink, '@link'));\n            }\n            if (typeSource.kind === Kind.DIRECTIVE_DEFINITION) {\n                allDirectives.push(typeSource);\n            }\n            else {\n                allNodes.push(typeSource);\n            }\n        }\n        else if (isDocumentNode(typeSource)) {\n            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else {\n            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);\n        }\n    }\n    return { allDirectives, allNodes, repeatableLinkImports };\n}\nexport function mergeGraphQLTypes(typeSource, config) {\n    resetComments();\n    const { allDirectives, allNodes, repeatableLinkImports } = visitTypeSources(typeSource, config);\n    const mergedDirectives = mergeGraphQLNodes(allDirectives, config);\n    config.repeatableLinkImports = repeatableLinkImports;\n    const mergedNodes = mergeGraphQLNodes(allNodes, config, mergedDirectives);\n    if (config?.useSchemaDefinition) {\n        // XXX: right now we don't handle multiple schema definitions\n        const schemaDef = mergedNodes[schemaDefSymbol] || {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [],\n        };\n        const operationTypes = schemaDef.operationTypes;\n        for (const opTypeDefNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n            const opTypeDefNode = operationTypes.find(operationType => operationType.operation === opTypeDefNodeType);\n            if (!opTypeDefNode) {\n                const possibleRootTypeName = DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];\n                const existingPossibleRootType = mergedNodes[possibleRootTypeName];\n                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {\n                    operationTypes.push({\n                        kind: Kind.OPERATION_TYPE_DEFINITION,\n                        type: {\n                            kind: Kind.NAMED_TYPE,\n                            name: existingPossibleRootType.name,\n                        },\n                        operation: opTypeDefNodeType,\n                    });\n                }\n            }\n        }\n        if (schemaDef?.operationTypes?.length != null && schemaDef.operationTypes.length > 0) {\n            mergedNodes[schemaDefSymbol] = schemaDef;\n        }\n    }\n    if (config?.forceSchemaDefinition && !mergedNodes[schemaDefSymbol]?.operationTypes?.length) {\n        mergedNodes[schemaDefSymbol] = {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [\n                {\n                    kind: Kind.OPERATION_TYPE_DEFINITION,\n                    operation: 'query',\n                    type: {\n                        kind: Kind.NAMED_TYPE,\n                        name: {\n                            kind: Kind.NAME,\n                            value: 'Query',\n                        },\n                    },\n                },\n            ],\n        };\n    }\n    const mergedNodeDefinitions = Object.values(mergedNodes);\n    if (config?.sort) {\n        const sortFn = typeof config.sort === 'function' ? config.sort : defaultStringComparator;\n        mergedNodeDefinitions.sort((a, b) => sortFn(a.name?.value, b.name?.value));\n    }\n    return mergedNodeDefinitions;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,cAAc,UAAU,EAAE,MAAM;IAC5C,IAAA,iLAAa;IACb,MAAM,MAAM;QACR,MAAM,uJAAI,CAAC,QAAQ;QACnB,aAAa,kBAAkB,YAAY;YACvC,qBAAqB;YACrB,uBAAuB;YACvB,iBAAiB;YACjB,qBAAqB;YACrB,GAAG,MAAM;QACb;IACJ;IACA,IAAI;IACJ,IAAI,QAAQ,qBAAqB;QAC7B,SAAS,IAAA,qLAAiB,EAAC;IAC/B,OACK;QACD,SAAS;IACb;IACA,IAAA,iLAAa;IACb,OAAO;AACX;AACA,SAAS,iBAAiB,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,WAAW,EAAE,EAAE,qBAAqB,IAAI,KAAK,EAAE,wBAAwB,IAAI,KAAK;IAC/I,MAAM,gBAAgB,CAAC;QACnB,sBAAsB,GAAG,CAAC;IAC9B;IACA,IAAI,cAAc,CAAC,mBAAmB,GAAG,CAAC,aAAa;QACnD,mBAAmB,GAAG,CAAC;QACvB,IAAI,OAAO,eAAe,YAAY;YAClC,iBAAiB,cAAc,SAAS,eAAe,UAAU,oBAAoB;QACzF,OACK,IAAI,MAAM,OAAO,CAAC,aAAa;YAChC,KAAK,MAAM,QAAQ,WAAY;gBAC3B,iBAAiB,MAAM,SAAS,eAAe,UAAU,oBAAoB;YACjF;QACJ,OACK,IAAI,IAAA,wJAAQ,EAAC,aAAa;YAC3B,MAAM,eAAe,IAAA,0NAAyB,EAAC,YAAY;YAC3D,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU,oBAAoB;QACrG,OACK,IAAI,IAAA,qMAAa,EAAC,eAAe,IAAA,qMAAa,EAAC,aAAa;YAC7D,MAAM,eAAe,IAAA,yJAAK,EAAC,YAAY;YACvC,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU,oBAAoB;QACrG,OACK,IAAI,OAAO,eAAe,YAAY,IAAA,wKAAgB,EAAC,aAAa;YACrE,MAAM,QAAQ,IAAA,6KAAY,EAAC;gBACvB,aAAa;oBAAC;iBAAW;gBACzB,MAAM,uJAAI,CAAC,QAAQ;YACvB;YACA,MAAM,gBAAgB;YACtB,MAAM,UAAU;YAChB;;;;;;eAMG,GACH,MAAM,iBAAiB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,QAAQ,KAAK;YAC1D,IAAI,gBAAgB;gBAChB,cAAc,IAAA,kLAAiB,EAAC,gBAAgB;gBAChD,cAAc,IAAA,kLAAiB,EAAC,gBAAgB;YACpD;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,QAAQ,KAAK;YACpD,IAAI,UAAU;gBACV,cAAc,IAAA,kLAAiB,EAAC,UAAU;YAC9C;YACA,IAAI,WAAW,IAAI,KAAK,uJAAI,CAAC,oBAAoB,EAAE;gBAC/C,cAAc,IAAI,CAAC;YACvB,OACK;gBACD,SAAS,IAAI,CAAC;YAClB;QACJ,OACK,IAAI,IAAA,wLAAc,EAAC,aAAa;YACjC,iBAAiB,WAAW,WAAW,EAAE,SAAS,eAAe,UAAU,oBAAoB;QACnG,OACK;YACD,MAAM,IAAI,MAAM,CAAC,0EAA0E,EAAE,OAAO,YAAY;QACpH;IACJ;IACA,OAAO;QAAE;QAAe;QAAU;IAAsB;AAC5D;AACO,SAAS,kBAAkB,UAAU,EAAE,MAAM;IAChD,IAAA,iLAAa;IACb,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,EAAE,GAAG,iBAAiB,YAAY;IACxF,MAAM,mBAAmB,IAAA,kNAAiB,EAAC,eAAe;IAC1D,OAAO,qBAAqB,GAAG;IAC/B,MAAM,cAAc,IAAA,kNAAiB,EAAC,UAAU,QAAQ;IACxD,IAAI,QAAQ,qBAAqB;QAC7B,6DAA6D;QAC7D,MAAM,YAAY,WAAW,CAAC,gNAAe,CAAC,IAAI;YAC9C,MAAM,uJAAI,CAAC,iBAAiB;YAC5B,gBAAgB,EAAE;QACtB;QACA,MAAM,iBAAiB,UAAU,cAAc;QAC/C,IAAK,MAAM,qBAAqB,+NAA+B,CAAE;YAC7D,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,gBAAiB,cAAc,SAAS,KAAK;YACvF,IAAI,CAAC,eAAe;gBAChB,MAAM,uBAAuB,+NAA+B,CAAC,kBAAkB;gBAC/E,MAAM,2BAA2B,WAAW,CAAC,qBAAqB;gBAClE,IAAI,4BAA4B,QAAQ,yBAAyB,IAAI,IAAI,MAAM;oBAC3E,eAAe,IAAI,CAAC;wBAChB,MAAM,uJAAI,CAAC,yBAAyB;wBACpC,MAAM;4BACF,MAAM,uJAAI,CAAC,UAAU;4BACrB,MAAM,yBAAyB,IAAI;wBACvC;wBACA,WAAW;oBACf;gBACJ;YACJ;QACJ;QACA,IAAI,WAAW,gBAAgB,UAAU,QAAQ,UAAU,cAAc,CAAC,MAAM,GAAG,GAAG;YAClF,WAAW,CAAC,gNAAe,CAAC,GAAG;QACnC;IACJ;IACA,IAAI,QAAQ,yBAAyB,CAAC,WAAW,CAAC,gNAAe,CAAC,EAAE,gBAAgB,QAAQ;QACxF,WAAW,CAAC,gNAAe,CAAC,GAAG;YAC3B,MAAM,uJAAI,CAAC,iBAAiB;YAC5B,gBAAgB;gBACZ;oBACI,MAAM,uJAAI,CAAC,yBAAyB;oBACpC,WAAW;oBACX,MAAM;wBACF,MAAM,uJAAI,CAAC,UAAU;wBACrB,MAAM;4BACF,MAAM,uJAAI,CAAC,IAAI;4BACf,OAAO;wBACX;oBACJ;gBACJ;aACH;QACL;IACJ;IACA,MAAM,wBAAwB,OAAO,MAAM,CAAC;IAC5C,IAAI,QAAQ,MAAM;QACd,MAAM,SAAS,OAAO,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,GAAG,+MAAuB;QACxF,sBAAsB,IAAI,CAAC,CAAC,GAAG,IAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IACvE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/merge-resolvers.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeResolvers = mergeResolvers;\nconst utils_1 = require(\"@graphql-tools/utils\");\n/**\n * Deep merges multiple resolver definition objects into a single definition.\n * @param resolversDefinitions Resolver definitions to be merged\n * @param options Additional options\n *\n * ```js\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const clientResolver = require('./clientResolver');\n * const productResolver = require('./productResolver');\n *\n * const resolvers = mergeResolvers([\n *  clientResolver,\n *  productResolver,\n * ]);\n * ```\n *\n * If you don't want to manually create the array of resolver objects, you can\n * also use this function along with loadFiles:\n *\n * ```js\n * const path = require('path');\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const { loadFilesSync } = require('@graphql-tools/load-files');\n *\n * const resolversArray = loadFilesSync(path.join(__dirname, './resolvers'));\n *\n * const resolvers = mergeResolvers(resolversArray)\n * ```\n */\nfunction mergeResolvers(resolversDefinitions, options) {\n    if (!resolversDefinitions ||\n        (Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0)) {\n        return {};\n    }\n    if (!Array.isArray(resolversDefinitions)) {\n        return resolversDefinitions;\n    }\n    if (resolversDefinitions.length === 1) {\n        return resolversDefinitions[0] || {};\n    }\n    const resolvers = new Array();\n    for (let resolversDefinition of resolversDefinitions) {\n        if (Array.isArray(resolversDefinition)) {\n            resolversDefinition = mergeResolvers(resolversDefinition);\n        }\n        if (typeof resolversDefinition === 'object' && resolversDefinition) {\n            resolvers.push(resolversDefinition);\n        }\n    }\n    const result = (0, utils_1.mergeDeep)(resolvers, true);\n    if (options?.exclusions) {\n        for (const exclusion of options.exclusions) {\n            const [typeName, fieldName] = exclusion.split('.');\n            if (['__proto__', 'constructor', 'prototype'].includes(typeName) ||\n                ['__proto__', 'constructor', 'prototype'].includes(fieldName)) {\n                continue;\n            }\n            if (!fieldName || fieldName === '*') {\n                delete result[typeName];\n            }\n            else if (result[typeName]) {\n                delete result[typeName][fieldName];\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS,eAAe,oBAAoB,EAAE,OAAO;IACjD,IAAI,CAAC,wBACA,MAAM,OAAO,CAAC,yBAAyB,qBAAqB,MAAM,KAAK,GAAI;QAC5E,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,uBAAuB;QACtC,OAAO;IACX;IACA,IAAI,qBAAqB,MAAM,KAAK,GAAG;QACnC,OAAO,oBAAoB,CAAC,EAAE,IAAI,CAAC;IACvC;IACA,MAAM,YAAY,IAAI;IACtB,KAAK,IAAI,uBAAuB,qBAAsB;QAClD,IAAI,MAAM,OAAO,CAAC,sBAAsB;YACpC,sBAAsB,eAAe;QACzC;QACA,IAAI,OAAO,wBAAwB,YAAY,qBAAqB;YAChE,UAAU,IAAI,CAAC;QACnB;IACJ;IACA,MAAM,SAAS,CAAC,GAAG,QAAQ,SAAS,EAAE,WAAW;IACjD,IAAI,SAAS,YAAY;QACrB,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;YACxC,MAAM,CAAC,UAAU,UAAU,GAAG,UAAU,KAAK,CAAC;YAC9C,IAAI;gBAAC;gBAAa;gBAAe;aAAY,CAAC,QAAQ,CAAC,aACnD;gBAAC;gBAAa;gBAAe;aAAY,CAAC,QAAQ,CAAC,YAAY;gBAC/D;YACJ;YACA,IAAI,CAAC,aAAa,cAAc,KAAK;gBACjC,OAAO,MAAM,CAAC,SAAS;YAC3B,OACK,IAAI,MAAM,CAAC,SAAS,EAAE;gBACvB,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/arguments.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeArguments = mergeArguments;\nconst utils_1 = require(\"@graphql-tools/utils\");\nfunction mergeArguments(args1, args2, config) {\n    const result = deduplicateArguments([...args2, ...args1].filter(utils_1.isSome), config);\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    return result;\n}\nfunction deduplicateArguments(args, config) {\n    return args.reduce((acc, current) => {\n        const dupIndex = acc.findIndex(arg => arg.name.value === current.name.value);\n        if (dupIndex === -1) {\n            return acc.concat([current]);\n        }\n        else if (!config?.reverseArguments) {\n            acc[dupIndex] = current;\n        }\n        return acc;\n    }, []);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,MAAM;IACxC,MAAM,SAAS,qBAAqB;WAAI;WAAU;KAAM,CAAC,MAAM,CAAC,QAAQ,MAAM,GAAG;IACjF,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,IAAI,EAAE,MAAM;IACtC,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,WAAW,IAAI,SAAS,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;QAC3E,IAAI,aAAa,CAAC,GAAG;YACjB,OAAO,IAAI,MAAM,CAAC;gBAAC;aAAQ;QAC/B,OACK,IAAI,CAAC,QAAQ,kBAAkB;YAChC,GAAG,CAAC,SAAS,GAAG;QACpB;QACA,OAAO;IACX,GAAG,EAAE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeDirectives = mergeDirectives;\nexports.mergeDirective = mergeDirective;\nconst graphql_1 = require(\"graphql\");\nfunction isRepeatableDirective(directive, directives, repeatableLinkImports) {\n    return !!(directives?.[directive.name.value]?.repeatable ??\n        repeatableLinkImports?.has(directive.name.value));\n}\nfunction nameAlreadyExists(name, namesArr) {\n    return namesArr.some(({ value }) => value === name.value);\n}\nfunction mergeArguments(a1, a2) {\n    const result = [];\n    for (const argument of [...a2, ...a1]) {\n        const existingIndex = result.findIndex(a => a.name.value === argument.name.value);\n        if (existingIndex === -1) {\n            result.push(argument);\n        }\n        else {\n            const existingArg = result[existingIndex];\n            if (existingArg.value.kind === 'ListValue') {\n                const source = existingArg.value.values;\n                const target = argument.value.values;\n                // merge values of two lists\n                existingArg.value = {\n                    ...existingArg.value,\n                    values: deduplicateLists(source, target, (targetVal, source) => {\n                        const value = targetVal.value;\n                        return !value || !source.some((sourceVal) => sourceVal.value === value);\n                    }),\n                };\n            }\n            else {\n                existingArg.value = argument.value;\n            }\n        }\n    }\n    return result;\n}\nconst matchValues = (a, b) => {\n    if (a.kind === b.kind) {\n        switch (a.kind) {\n            case graphql_1.Kind.LIST:\n                return (a.values.length === b.values.length &&\n                    a.values.every(aVal => b.values.find(bVal => matchValues(aVal, bVal))));\n            case graphql_1.Kind.VARIABLE:\n            case graphql_1.Kind.NULL:\n                return true;\n            case graphql_1.Kind.OBJECT:\n                return (a.fields.length === b.fields.length &&\n                    a.fields.every(aField => b.fields.find(bField => aField.name.value === bField.name.value && matchValues(aField.value, bField.value))));\n            default:\n                return a.value === b.value;\n        }\n    }\n    return false;\n};\nconst matchArguments = (a, b) => a.name.value === b.name.value && a.value.kind === b.value.kind && matchValues(a.value, b.value);\n/**\n * Check if a directive is an exact match of another directive based on their\n * arguments.\n */\nconst matchDirectives = (a, b) => {\n    const matched = a.name.value === b.name.value &&\n        (a.arguments === b.arguments ||\n            (a.arguments?.length === b.arguments?.length &&\n                a.arguments?.every(argA => b.arguments?.find(argB => matchArguments(argA, argB)))));\n    return !!matched;\n};\nfunction mergeDirectives(d1 = [], d2 = [], config, directives) {\n    const reverseOrder = config && config.reverseDirectives;\n    const asNext = reverseOrder ? d1 : d2;\n    const asFirst = reverseOrder ? d2 : d1;\n    const result = [];\n    for (const directive of [...asNext, ...asFirst]) {\n        if (isRepeatableDirective(directive, directives, config?.repeatableLinkImports)) {\n            // look for repeated, identical directives that come before this instance\n            // if those exist, return null so that this directive gets removed.\n            const exactDuplicate = result.find(d => matchDirectives(directive, d));\n            if (!exactDuplicate) {\n                result.push(directive);\n            }\n        }\n        else {\n            const firstAt = result.findIndex(d => d.name.value === directive.name.value);\n            if (firstAt === -1) {\n                // if did not find a directive with this name on the result set already\n                result.push(directive);\n            }\n            else {\n                // if not repeatable and found directive with the same name already in the result set,\n                // then merge the arguments of the existing directive and the new directive\n                const mergedArguments = mergeArguments(directive.arguments ?? [], result[firstAt].arguments ?? []);\n                result[firstAt] = {\n                    ...result[firstAt],\n                    arguments: mergedArguments.length === 0 ? undefined : mergedArguments,\n                };\n            }\n        }\n    }\n    return result;\n}\nfunction mergeDirective(node, existingNode) {\n    if (existingNode) {\n        return {\n            ...node,\n            arguments: deduplicateLists(existingNode.arguments || [], node.arguments || [], (arg, existingArgs) => !nameAlreadyExists(arg.name, existingArgs.map(a => a.name))),\n            locations: [\n                ...existingNode.locations,\n                ...node.locations.filter(name => !nameAlreadyExists(name, existingNode.locations)),\n            ],\n        };\n    }\n    return node;\n}\nfunction deduplicateLists(source, target, filterFn) {\n    return source.concat(target.filter(val => filterFn(val, source)));\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG;AAC1B,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,SAAS,sBAAsB,SAAS,EAAE,UAAU,EAAE,qBAAqB;IACvE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE,cAC1C,uBAAuB,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC;AACxD;AACA,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACrC,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,UAAU,KAAK,KAAK;AAC5D;AACA,SAAS,eAAe,EAAE,EAAE,EAAE;IAC1B,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,YAAY;WAAI;WAAO;KAAG,CAAE;QACnC,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK;QAChF,IAAI,kBAAkB,CAAC,GAAG;YACtB,OAAO,IAAI,CAAC;QAChB,OACK;YACD,MAAM,cAAc,MAAM,CAAC,cAAc;YACzC,IAAI,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa;gBACxC,MAAM,SAAS,YAAY,KAAK,CAAC,MAAM;gBACvC,MAAM,SAAS,SAAS,KAAK,CAAC,MAAM;gBACpC,4BAA4B;gBAC5B,YAAY,KAAK,GAAG;oBAChB,GAAG,YAAY,KAAK;oBACpB,QAAQ,iBAAiB,QAAQ,QAAQ,CAAC,WAAW;wBACjD,MAAM,QAAQ,UAAU,KAAK;wBAC7B,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;oBACrE;gBACJ;YACJ,OACK;gBACD,YAAY,KAAK,GAAG,SAAS,KAAK;YACtC;QACJ;IACJ;IACA,OAAO;AACX;AACA,MAAM,cAAc,CAAC,GAAG;IACpB,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;QACnB,OAAQ,EAAE,IAAI;YACV,KAAK,UAAU,IAAI,CAAC,IAAI;gBACpB,OAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC,MAAM,IACvC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA,OAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA,OAAQ,YAAY,MAAM;YACvE,KAAK,UAAU,IAAI,CAAC,QAAQ;YAC5B,KAAK,UAAU,IAAI,CAAC,IAAI;gBACpB,OAAO;YACX,KAAK,UAAU,IAAI,CAAC,MAAM;gBACtB,OAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC,MAAM,IACvC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA,SAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,YAAY,OAAO,KAAK,EAAE,OAAO,KAAK;YAC1I;gBACI,OAAO,EAAE,KAAK,KAAK,EAAE,KAAK;QAClC;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,YAAY,EAAE,KAAK,EAAE,EAAE,KAAK;AAC/H;;;CAGC,GACD,MAAM,kBAAkB,CAAC,GAAG;IACxB,MAAM,UAAU,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,IACzC,CAAC,EAAE,SAAS,KAAK,EAAE,SAAS,IACvB,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAClC,EAAE,SAAS,EAAE,MAAM,CAAA,OAAQ,EAAE,SAAS,EAAE,KAAK,CAAA,OAAQ,eAAe,MAAM,OAAQ;IAC9F,OAAO,CAAC,CAAC;AACb;AACA,SAAS,gBAAgB,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU;IACzD,MAAM,eAAe,UAAU,OAAO,iBAAiB;IACvD,MAAM,SAAS,eAAe,KAAK;IACnC,MAAM,UAAU,eAAe,KAAK;IACpC,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,aAAa;WAAI;WAAW;KAAQ,CAAE;QAC7C,IAAI,sBAAsB,WAAW,YAAY,QAAQ,wBAAwB;YAC7E,yEAAyE;YACzE,mEAAmE;YACnE,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,IAAK,gBAAgB,WAAW;YACnE,IAAI,CAAC,gBAAgB;gBACjB,OAAO,IAAI,CAAC;YAChB;QACJ,OACK;YACD,MAAM,UAAU,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;YAC3E,IAAI,YAAY,CAAC,GAAG;gBAChB,uEAAuE;gBACvE,OAAO,IAAI,CAAC;YAChB,OACK;gBACD,sFAAsF;gBACtF,2EAA2E;gBAC3E,MAAM,kBAAkB,eAAe,UAAU,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE;gBACjG,MAAM,CAAC,QAAQ,GAAG;oBACd,GAAG,MAAM,CAAC,QAAQ;oBAClB,WAAW,gBAAgB,MAAM,KAAK,IAAI,YAAY;gBAC1D;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,IAAI,EAAE,YAAY;IACtC,IAAI,cAAc;QACd,OAAO;YACH,GAAG,IAAI;YACP,WAAW,iBAAiB,aAAa,SAAS,IAAI,EAAE,EAAE,KAAK,SAAS,IAAI,EAAE,EAAE,CAAC,KAAK,eAAiB,CAAC,kBAAkB,IAAI,IAAI,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAChK,WAAW;mBACJ,aAAa,SAAS;mBACtB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,kBAAkB,MAAM,aAAa,SAAS;aACnF;QACL;IACJ;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC9C,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAA,MAAO,SAAS,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/enum-values.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeEnumValues = mergeEnumValues;\nconst utils_1 = require(\"@graphql-tools/utils\");\nconst directives_js_1 = require(\"./directives.js\");\nfunction mergeEnumValues(first, second, config, directives) {\n    if (config?.consistentEnumMerge) {\n        const reversed = [];\n        if (first) {\n            reversed.push(...first);\n        }\n        first = second;\n        second = reversed;\n    }\n    const enumValueMap = new Map();\n    if (first) {\n        for (const firstValue of first) {\n            enumValueMap.set(firstValue.name.value, firstValue);\n        }\n    }\n    if (second) {\n        for (const secondValue of second) {\n            const enumValue = secondValue.name.value;\n            if (enumValueMap.has(enumValue)) {\n                const firstValue = enumValueMap.get(enumValue);\n                firstValue.description = secondValue.description || firstValue.description;\n                firstValue.directives = (0, directives_js_1.mergeDirectives)(secondValue.directives, firstValue.directives, directives);\n            }\n            else {\n                enumValueMap.set(enumValue, secondValue);\n            }\n        }\n    }\n    const result = [...enumValueMap.values()];\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG;AAC1B,MAAM;AACN,MAAM;AACN,SAAS,gBAAgB,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACtD,IAAI,QAAQ,qBAAqB;QAC7B,MAAM,WAAW,EAAE;QACnB,IAAI,OAAO;YACP,SAAS,IAAI,IAAI;QACrB;QACA,QAAQ;QACR,SAAS;IACb;IACA,MAAM,eAAe,IAAI;IACzB,IAAI,OAAO;QACP,KAAK,MAAM,cAAc,MAAO;YAC5B,aAAa,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE;QAC5C;IACJ;IACA,IAAI,QAAQ;QACR,KAAK,MAAM,eAAe,OAAQ;YAC9B,MAAM,YAAY,YAAY,IAAI,CAAC,KAAK;YACxC,IAAI,aAAa,GAAG,CAAC,YAAY;gBAC7B,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,WAAW,WAAW,GAAG,YAAY,WAAW,IAAI,WAAW,WAAW;gBAC1E,WAAW,UAAU,GAAG,CAAC,GAAG,gBAAgB,eAAe,EAAE,YAAY,UAAU,EAAE,WAAW,UAAU,EAAE;YAChH,OACK;gBACD,aAAa,GAAG,CAAC,WAAW;YAChC;QACJ;IACJ;IACA,MAAM,SAAS;WAAI,aAAa,MAAM;KAAG;IACzC,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/enum.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeEnum = mergeEnum;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst enum_values_js_1 = require(\"./enum-values.js\");\nfunction mergeEnum(e1, e2, config, directives) {\n    if (e2) {\n        return {\n            name: e1.name,\n            description: e1['description'] || e2['description'],\n            kind: config?.convertExtensions ||\n                e1.kind === 'EnumTypeDefinition' ||\n                e2.kind === 'EnumTypeDefinition'\n                ? 'EnumTypeDefinition'\n                : 'EnumTypeExtension',\n            loc: e1.loc,\n            directives: (0, directives_js_1.mergeDirectives)(e1.directives, e2.directives, config, directives),\n            values: (0, enum_values_js_1.mergeEnumValues)(e1.values, e2.values, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...e1,\n            kind: graphql_1.Kind.ENUM_TYPE_DEFINITION,\n        }\n        : e1;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG;AACpB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACzC,IAAI,IAAI;QACJ,OAAO;YACH,MAAM,GAAG,IAAI;YACb,aAAa,EAAE,CAAC,cAAc,IAAI,EAAE,CAAC,cAAc;YACnD,MAAM,QAAQ,qBACV,GAAG,IAAI,KAAK,wBACZ,GAAG,IAAI,KAAK,uBACV,uBACA;YACN,KAAK,GAAG,GAAG;YACX,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,GAAG,UAAU,EAAE,GAAG,UAAU,EAAE,QAAQ;YACvF,QAAQ,CAAC,GAAG,iBAAiB,eAAe,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;QACxE;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,EAAE;QACL,MAAM,UAAU,IAAI,CAAC,oBAAoB;IAC7C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/utils.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CompareVal = void 0;\nexports.isStringTypes = isStringTypes;\nexports.isSourceTypes = isSourceTypes;\nexports.extractType = extractType;\nexports.isWrappingTypeNode = isWrappingTypeNode;\nexports.isListTypeNode = isListTypeNode;\nexports.isNonNullTypeNode = isNonNullTypeNode;\nexports.printTypeNode = printTypeNode;\nexports.defaultStringComparator = defaultStringComparator;\nconst graphql_1 = require(\"graphql\");\nfunction isStringTypes(types) {\n    return typeof types === 'string';\n}\nfunction isSourceTypes(types) {\n    return types instanceof graphql_1.Source;\n}\nfunction extractType(type) {\n    let visitedType = type;\n    while (visitedType.kind === graphql_1.Kind.LIST_TYPE || visitedType.kind === 'NonNullType') {\n        visitedType = visitedType.type;\n    }\n    return visitedType;\n}\nfunction isWrappingTypeNode(type) {\n    return type.kind !== graphql_1.Kind.NAMED_TYPE;\n}\nfunction isListTypeNode(type) {\n    return type.kind === graphql_1.Kind.LIST_TYPE;\n}\nfunction isNonNullTypeNode(type) {\n    return type.kind === graphql_1.Kind.NON_NULL_TYPE;\n}\nfunction printTypeNode(type) {\n    if (isListTypeNode(type)) {\n        return `[${printTypeNode(type.type)}]`;\n    }\n    if (isNonNullTypeNode(type)) {\n        return `${printTypeNode(type.type)}!`;\n    }\n    return type.name.value;\n}\nvar CompareVal;\n(function (CompareVal) {\n    CompareVal[CompareVal[\"A_SMALLER_THAN_B\"] = -1] = \"A_SMALLER_THAN_B\";\n    CompareVal[CompareVal[\"A_EQUALS_B\"] = 0] = \"A_EQUALS_B\";\n    CompareVal[CompareVal[\"A_GREATER_THAN_B\"] = 1] = \"A_GREATER_THAN_B\";\n})(CompareVal || (exports.CompareVal = CompareVal = {}));\nfunction defaultStringComparator(a, b) {\n    if (a == null && b == null) {\n        return CompareVal.A_EQUALS_B;\n    }\n    if (a == null) {\n        return CompareVal.A_SMALLER_THAN_B;\n    }\n    if (b == null) {\n        return CompareVal.A_GREATER_THAN_B;\n    }\n    if (a < b)\n        return CompareVal.A_SMALLER_THAN_B;\n    if (a > b)\n        return CompareVal.A_GREATER_THAN_B;\n    return CompareVal.A_EQUALS_B;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,QAAQ,aAAa,GAAG;AACxB,QAAQ,aAAa,GAAG;AACxB,QAAQ,WAAW,GAAG;AACtB,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,cAAc,GAAG;AACzB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,aAAa,GAAG;AACxB,QAAQ,uBAAuB,GAAG;AAClC,MAAM;AACN,SAAS,cAAc,KAAK;IACxB,OAAO,OAAO,UAAU;AAC5B;AACA,SAAS,cAAc,KAAK;IACxB,OAAO,iBAAiB,UAAU,MAAM;AAC5C;AACA,SAAS,YAAY,IAAI;IACrB,IAAI,cAAc;IAClB,MAAO,YAAY,IAAI,KAAK,UAAU,IAAI,CAAC,SAAS,IAAI,YAAY,IAAI,KAAK,cAAe;QACxF,cAAc,YAAY,IAAI;IAClC;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,IAAI;IAC5B,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,UAAU;AAClD;AACA,SAAS,eAAe,IAAI;IACxB,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,SAAS;AACjD;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,aAAa;AACrD;AACA,SAAS,cAAc,IAAI;IACvB,IAAI,eAAe,OAAO;QACtB,OAAO,CAAC,CAAC,EAAE,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,kBAAkB,OAAO;QACzB,OAAO,GAAG,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IACzC;IACA,OAAO,KAAK,IAAI,CAAC,KAAK;AAC1B;AACA,IAAI;AACJ,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,EAAE,GAAG;IAClD,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,EAAE,GAAG;AACrD,CAAC,EAAE,cAAc,CAAC,QAAQ,UAAU,GAAG,aAAa,CAAC,CAAC;AACtD,SAAS,wBAAwB,CAAC,EAAE,CAAC;IACjC,IAAI,KAAK,QAAQ,KAAK,MAAM;QACxB,OAAO,WAAW,UAAU;IAChC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,OAAO,WAAW,UAAU;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/fields.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeFields = mergeFields;\nconst utils_1 = require(\"@graphql-tools/utils\");\nconst arguments_js_1 = require(\"./arguments.js\");\nconst directives_js_1 = require(\"./directives.js\");\nconst utils_js_1 = require(\"./utils.js\");\nfunction fieldAlreadyExists(fieldsArr, otherField) {\n    const resultIndex = fieldsArr.findIndex(field => field.name.value === otherField.name.value);\n    return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];\n}\nfunction mergeFields(type, f1, f2, config, directives) {\n    const result = [];\n    if (f2 != null) {\n        result.push(...f2);\n    }\n    if (f1 != null) {\n        for (const field of f1) {\n            const [existing, existingIndex] = fieldAlreadyExists(result, field);\n            if (existing && !config?.ignoreFieldConflicts) {\n                const newField = (config?.onFieldTypeConflict &&\n                    config.onFieldTypeConflict(existing, field, type, config?.throwOnConflict)) ||\n                    preventConflicts(type, existing, field, config?.throwOnConflict);\n                newField.arguments = (0, arguments_js_1.mergeArguments)(field['arguments'] || [], existing['arguments'] || [], config);\n                newField.directives = (0, directives_js_1.mergeDirectives)(field.directives, existing.directives, config, directives);\n                newField.description = field.description || existing.description;\n                result[existingIndex] = newField;\n            }\n            else {\n                result.push(field);\n            }\n        }\n    }\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    if (config && config.exclusions) {\n        const exclusions = config.exclusions;\n        return result.filter(field => !exclusions.includes(`${type.name.value}.${field.name.value}`));\n    }\n    return result;\n}\nfunction preventConflicts(type, a, b, ignoreNullability = false) {\n    const aType = (0, utils_js_1.printTypeNode)(a.type);\n    const bType = (0, utils_js_1.printTypeNode)(b.type);\n    if (aType !== bType) {\n        const t1 = (0, utils_js_1.extractType)(a.type);\n        const t2 = (0, utils_js_1.extractType)(b.type);\n        if (t1.name.value !== t2.name.value) {\n            throw new Error(`Field \"${b.name.value}\" already defined with a different type. Declared as \"${t1.name.value}\", but you tried to override with \"${t2.name.value}\"`);\n        }\n        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {\n            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);\n        }\n    }\n    if ((0, utils_js_1.isNonNullTypeNode)(b.type) && !(0, utils_js_1.isNonNullTypeNode)(a.type)) {\n        a.type = b.type;\n    }\n    return a;\n}\nfunction safeChangeForFieldType(oldType, newType, ignoreNullability = false) {\n    // both are named\n    if (!(0, utils_js_1.isWrappingTypeNode)(oldType) && !(0, utils_js_1.isWrappingTypeNode)(newType)) {\n        return oldType.toString() === newType.toString();\n    }\n    // new is non-null\n    if ((0, utils_js_1.isNonNullTypeNode)(newType)) {\n        const ofType = (0, utils_js_1.isNonNullTypeNode)(oldType) ? oldType.type : oldType;\n        return safeChangeForFieldType(ofType, newType.type);\n    }\n    // old is non-null\n    if ((0, utils_js_1.isNonNullTypeNode)(oldType)) {\n        return safeChangeForFieldType(newType, oldType, ignoreNullability);\n    }\n    // old is list\n    if ((0, utils_js_1.isListTypeNode)(oldType)) {\n        return (((0, utils_js_1.isListTypeNode)(newType) && safeChangeForFieldType(oldType.type, newType.type)) ||\n            ((0, utils_js_1.isNonNullTypeNode)(newType) && safeChangeForFieldType(oldType, newType['type'])));\n    }\n    return false;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG;AACtB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,mBAAmB,SAAS,EAAE,UAAU;IAC7C,MAAM,cAAc,UAAU,SAAS,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK;IAC3F,OAAO;QAAC,cAAc,CAAC,IAAI,SAAS,CAAC,YAAY,GAAG;QAAM;KAAY;AAC1E;AACA,SAAS,YAAY,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACjD,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM,MAAM;QACZ,OAAO,IAAI,IAAI;IACnB;IACA,IAAI,MAAM,MAAM;QACZ,KAAK,MAAM,SAAS,GAAI;YACpB,MAAM,CAAC,UAAU,cAAc,GAAG,mBAAmB,QAAQ;YAC7D,IAAI,YAAY,CAAC,QAAQ,sBAAsB;gBAC3C,MAAM,WAAW,AAAC,QAAQ,uBACtB,OAAO,mBAAmB,CAAC,UAAU,OAAO,MAAM,QAAQ,oBAC1D,iBAAiB,MAAM,UAAU,OAAO,QAAQ;gBACpD,SAAS,SAAS,GAAG,CAAC,GAAG,eAAe,cAAc,EAAE,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE;gBAC/G,SAAS,UAAU,GAAG,CAAC,GAAG,gBAAgB,eAAe,EAAE,MAAM,UAAU,EAAE,SAAS,UAAU,EAAE,QAAQ;gBAC1G,SAAS,WAAW,GAAG,MAAM,WAAW,IAAI,SAAS,WAAW;gBAChE,MAAM,CAAC,cAAc,GAAG;YAC5B,OACK;gBACD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ;IACA,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,IAAI,UAAU,OAAO,UAAU,EAAE;QAC7B,MAAM,aAAa,OAAO,UAAU;QACpC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;IAC/F;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB,KAAK;IAC3D,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,EAAE,IAAI;IAClD,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,EAAE,IAAI;IAClD,IAAI,UAAU,OAAO;QACjB,MAAM,KAAK,CAAC,GAAG,WAAW,WAAW,EAAE,EAAE,IAAI;QAC7C,MAAM,KAAK,CAAC,GAAG,WAAW,WAAW,EAAE,EAAE,IAAI;QAC7C,IAAI,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,sDAAsD,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACtK;QACA,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB;YAC7D,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3G;IACJ;IACA,IAAI,CAAC,GAAG,WAAW,iBAAiB,EAAE,EAAE,IAAI,KAAK,CAAC,CAAC,GAAG,WAAW,iBAAiB,EAAE,EAAE,IAAI,GAAG;QACzF,EAAE,IAAI,GAAG,EAAE,IAAI;IACnB;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB,KAAK;IACvE,iBAAiB;IACjB,IAAI,CAAC,CAAC,GAAG,WAAW,kBAAkB,EAAE,YAAY,CAAC,CAAC,GAAG,WAAW,kBAAkB,EAAE,UAAU;QAC9F,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;IAClD;IACA,kBAAkB;IAClB,IAAI,CAAC,GAAG,WAAW,iBAAiB,EAAE,UAAU;QAC5C,MAAM,SAAS,CAAC,GAAG,WAAW,iBAAiB,EAAE,WAAW,QAAQ,IAAI,GAAG;QAC3E,OAAO,uBAAuB,QAAQ,QAAQ,IAAI;IACtD;IACA,kBAAkB;IAClB,IAAI,CAAC,GAAG,WAAW,iBAAiB,EAAE,UAAU;QAC5C,OAAO,uBAAuB,SAAS,SAAS;IACpD;IACA,cAAc;IACd,IAAI,CAAC,GAAG,WAAW,cAAc,EAAE,UAAU;QACzC,OAAQ,AAAC,CAAC,GAAG,WAAW,cAAc,EAAE,YAAY,uBAAuB,QAAQ,IAAI,EAAE,QAAQ,IAAI,KAChG,CAAC,GAAG,WAAW,iBAAiB,EAAE,YAAY,uBAAuB,SAAS,OAAO,CAAC,OAAO;IACtG;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/input-type.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeInputType = mergeInputType;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst fields_js_1 = require(\"./fields.js\");\nfunction mergeInputType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InputObjectTypeDefinition' ||\n                    existingNode.kind === 'InputObjectTypeDefinition'\n                    ? 'InputObjectTypeDefinition'\n                    : 'InputObjectTypeExtension',\n                loc: node.loc,\n                fields: (0, fields_js_1.mergeFields)(node, node.fields, existingNode.fields, config),\n                directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL input type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC1D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,+BACd,aAAa,IAAI,KAAK,8BACpB,8BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC,GAAG,YAAY,WAAW,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC7E,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YACvG;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC3F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,4BAA4B;IACrD,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/merge-named-type-array.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeNamedTypeArray = mergeNamedTypeArray;\nconst utils_1 = require(\"@graphql-tools/utils\");\nfunction alreadyExists(arr, other) {\n    return !!arr.find(i => i.name.value === other.name.value);\n}\nfunction mergeNamedTypeArray(first = [], second = [], config = {}) {\n    const result = [...second, ...first.filter(d => !alreadyExists(second, d))];\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG;AAC9B,MAAM;AACN,SAAS,cAAc,GAAG,EAAE,KAAK;IAC7B,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK;AAC5D;AACA,SAAS,oBAAoB,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;IAC7D,MAAM,SAAS;WAAI;WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,cAAc,QAAQ;KAAI;IAC3E,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/interface.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeInterface = mergeInterface;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst fields_js_1 = require(\"./fields.js\");\nconst merge_named_type_array_js_1 = require(\"./merge-named-type-array.js\");\nfunction mergeInterface(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InterfaceTypeDefinition' ||\n                    existingNode.kind === 'InterfaceTypeDefinition'\n                    ? 'InterfaceTypeDefinition'\n                    : 'InterfaceTypeExtension',\n                loc: node.loc,\n                fields: (0, fields_js_1.mergeFields)(node, node.fields, existingNode.fields, config, directives),\n                directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n                interfaces: node['interfaces']\n                    ? (0, merge_named_type_array_js_1.mergeNamedTypeArray)(node['interfaces'], existingNode['interfaces'], config)\n                    : undefined,\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL interface \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: graphql_1.Kind.INTERFACE_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC1D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,6BACd,aAAa,IAAI,KAAK,4BACpB,4BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC,GAAG,YAAY,WAAW,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE,QAAQ;gBACrF,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBACnG,YAAY,IAAI,CAAC,aAAa,GACxB,CAAC,GAAG,4BAA4B,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,EAAE,UACrG;YACV;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC1F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,yBAAyB;IAClD,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/scalar.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeScalar = mergeScalar;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nfunction mergeScalar(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            name: node.name,\n            description: node['description'] || existingNode['description'],\n            kind: config?.convertExtensions ||\n                node.kind === 'ScalarTypeDefinition' ||\n                existingNode.kind === 'ScalarTypeDefinition'\n                ? 'ScalarTypeDefinition'\n                : 'ScalarTypeExtension',\n            loc: node.loc,\n            directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: graphql_1.Kind.SCALAR_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG;AACtB,MAAM;AACN,MAAM;AACN,SAAS,YAAY,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACvD,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI;YACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;YACN,KAAK,KAAK,GAAG;YACb,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;QACvG;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,sBAAsB;IAC/C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/schema-def.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DEFAULT_OPERATION_TYPE_NAME_MAP = void 0;\nexports.mergeSchemaDefs = mergeSchemaDefs;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nexports.DEFAULT_OPERATION_TYPE_NAME_MAP = {\n    query: 'Query',\n    mutation: 'Mutation',\n    subscription: 'Subscription',\n};\nfunction mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {\n    const finalOpNodeList = [];\n    for (const opNodeType in exports.DEFAULT_OPERATION_TYPE_NAME_MAP) {\n        const opNode = opNodeList.find(n => n.operation === opNodeType) ||\n            existingOpNodeList.find(n => n.operation === opNodeType);\n        if (opNode) {\n            finalOpNodeList.push(opNode);\n        }\n    }\n    return finalOpNodeList;\n}\nfunction mergeSchemaDefs(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            kind: node.kind === graphql_1.Kind.SCHEMA_DEFINITION || existingNode.kind === graphql_1.Kind.SCHEMA_DEFINITION\n                ? graphql_1.Kind.SCHEMA_DEFINITION\n                : graphql_1.Kind.SCHEMA_EXTENSION,\n            description: node['description'] || existingNode['description'],\n            directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes),\n        };\n    }\n    return (config?.convertExtensions\n        ? {\n            ...node,\n            kind: graphql_1.Kind.SCHEMA_DEFINITION,\n        }\n        : node);\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,+BAA+B,GAAG,KAAK;AAC/C,QAAQ,eAAe,GAAG;AAC1B,MAAM;AACN,MAAM;AACN,QAAQ,+BAA+B,GAAG;IACtC,OAAO;IACP,UAAU;IACV,cAAc;AAClB;AACA,SAAS,oBAAoB,aAAa,EAAE,EAAE,qBAAqB,EAAE;IACjE,MAAM,kBAAkB,EAAE;IAC1B,IAAK,MAAM,cAAc,QAAQ,+BAA+B,CAAE;QAC9D,MAAM,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,eAChD,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACjD,IAAI,QAAQ;YACR,gBAAgB,IAAI,CAAC;QACzB;IACJ;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC3D,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,IAAI,aAAa,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,GACxG,UAAU,IAAI,CAAC,iBAAiB,GAChC,UAAU,IAAI,CAAC,gBAAgB;YACrC,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YACnG,gBAAgB,oBAAoB,KAAK,cAAc,EAAE,aAAa,cAAc;QACxF;IACJ;IACA,OAAQ,QAAQ,oBACV;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,iBAAiB;IAC1C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1789, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/type.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeType = mergeType;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst fields_js_1 = require(\"./fields.js\");\nconst merge_named_type_array_js_1 = require(\"./merge-named-type-array.js\");\nfunction mergeType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'ObjectTypeDefinition' ||\n                    existingNode.kind === 'ObjectTypeDefinition'\n                    ? 'ObjectTypeDefinition'\n                    : 'ObjectTypeExtension',\n                loc: node.loc,\n                fields: (0, fields_js_1.mergeFields)(node, node.fields, existingNode.fields, config, directives),\n                directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n                interfaces: (0, merge_named_type_array_js_1.mergeNamedTypeArray)(node.interfaces, existingNode.interfaces, config),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: graphql_1.Kind.OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG;AACpB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACrD,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC,GAAG,YAAY,WAAW,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE,QAAQ;gBACrF,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBACnG,YAAY,CAAC,GAAG,4BAA4B,mBAAmB,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE;YAC/G;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QACrF;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,sBAAsB;IAC/C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/union.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeUnion = mergeUnion;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst merge_named_type_array_js_1 = require(\"./merge-named-type-array.js\");\nfunction mergeUnion(first, second, config, directives) {\n    if (second) {\n        return {\n            name: first.name,\n            description: first['description'] || second['description'],\n            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n            directives: (0, directives_js_1.mergeDirectives)(first.directives, second.directives, config, directives),\n            kind: config?.convertExtensions ||\n                first.kind === 'UnionTypeDefinition' ||\n                second.kind === 'UnionTypeDefinition'\n                ? graphql_1.Kind.UNION_TYPE_DEFINITION\n                : graphql_1.Kind.UNION_TYPE_EXTENSION,\n            loc: first.loc,\n            types: (0, merge_named_type_array_js_1.mergeNamedTypeArray)(first.types, second.types, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...first,\n            kind: graphql_1.Kind.UNION_TYPE_DEFINITION,\n        }\n        : first;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG;AACrB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,WAAW,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACjD,IAAI,QAAQ;QACR,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,aAAa,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc;YAC1D,0HAA0H;YAC1H,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,QAAQ;YAC9F,MAAM,QAAQ,qBACV,MAAM,IAAI,KAAK,yBACf,OAAO,IAAI,KAAK,wBACd,UAAU,IAAI,CAAC,qBAAqB,GACpC,UAAU,IAAI,CAAC,oBAAoB;YACzC,KAAK,MAAM,GAAG;YACd,OAAO,CAAC,GAAG,4BAA4B,mBAAmB,EAAE,MAAM,KAAK,EAAE,OAAO,KAAK,EAAE;QAC3F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,KAAK;QACR,MAAM,UAAU,IAAI,CAAC,qBAAqB;IAC9C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1850, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/merge-nodes.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.schemaDefSymbol = void 0;\nexports.isNamedDefinitionNode = isNamedDefinitionNode;\nexports.mergeGraphQLNodes = mergeGraphQLNodes;\nconst graphql_1 = require(\"graphql\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nconst directives_js_1 = require(\"./directives.js\");\nconst enum_js_1 = require(\"./enum.js\");\nconst input_type_js_1 = require(\"./input-type.js\");\nconst interface_js_1 = require(\"./interface.js\");\nconst scalar_js_1 = require(\"./scalar.js\");\nconst schema_def_js_1 = require(\"./schema-def.js\");\nconst type_js_1 = require(\"./type.js\");\nconst union_js_1 = require(\"./union.js\");\nexports.schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';\nfunction isNamedDefinitionNode(definitionNode) {\n    return 'name' in definitionNode;\n}\nfunction mergeGraphQLNodes(nodes, config, directives = {}) {\n    const mergedResultMap = directives;\n    for (const nodeDefinition of nodes) {\n        if (isNamedDefinitionNode(nodeDefinition)) {\n            const name = nodeDefinition.name?.value;\n            if (config?.commentDescriptions) {\n                (0, utils_1.collectComment)(nodeDefinition);\n            }\n            if (name == null) {\n                continue;\n            }\n            if (config?.exclusions?.includes(name + '.*') || config?.exclusions?.includes(name)) {\n                delete mergedResultMap[name];\n            }\n            else {\n                switch (nodeDefinition.kind) {\n                    case graphql_1.Kind.OBJECT_TYPE_DEFINITION:\n                    case graphql_1.Kind.OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, type_js_1.mergeType)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.ENUM_TYPE_DEFINITION:\n                    case graphql_1.Kind.ENUM_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, enum_js_1.mergeEnum)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.UNION_TYPE_DEFINITION:\n                    case graphql_1.Kind.UNION_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, union_js_1.mergeUnion)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.SCALAR_TYPE_DEFINITION:\n                    case graphql_1.Kind.SCALAR_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, scalar_js_1.mergeScalar)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION:\n                    case graphql_1.Kind.INPUT_OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, input_type_js_1.mergeInputType)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.INTERFACE_TYPE_DEFINITION:\n                    case graphql_1.Kind.INTERFACE_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, interface_js_1.mergeInterface)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.DIRECTIVE_DEFINITION:\n                        if (mergedResultMap[name]) {\n                            const isInheritedFromPrototype = name in {}; // i.e. toString\n                            if (isInheritedFromPrototype) {\n                                if (!isASTNode(mergedResultMap[name])) {\n                                    mergedResultMap[name] = undefined;\n                                }\n                            }\n                        }\n                        mergedResultMap[name] = (0, directives_js_1.mergeDirective)(nodeDefinition, mergedResultMap[name]);\n                        break;\n                }\n            }\n        }\n        else if (nodeDefinition.kind === graphql_1.Kind.SCHEMA_DEFINITION ||\n            nodeDefinition.kind === graphql_1.Kind.SCHEMA_EXTENSION) {\n            mergedResultMap[exports.schemaDefSymbol] = (0, schema_def_js_1.mergeSchemaDefs)(nodeDefinition, mergedResultMap[exports.schemaDefSymbol], config);\n        }\n    }\n    return mergedResultMap;\n}\nfunction isASTNode(node) {\n    return (node != null && typeof node === 'object' && 'kind' in node && typeof node.kind === 'string');\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,QAAQ,qBAAqB,GAAG;AAChC,QAAQ,iBAAiB,GAAG;AAC5B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,eAAe,GAAG;AAC1B,SAAS,sBAAsB,cAAc;IACzC,OAAO,UAAU;AACrB;AACA,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACrD,MAAM,kBAAkB;IACxB,KAAK,MAAM,kBAAkB,MAAO;QAChC,IAAI,sBAAsB,iBAAiB;YACvC,MAAM,OAAO,eAAe,IAAI,EAAE;YAClC,IAAI,QAAQ,qBAAqB;gBAC7B,CAAC,GAAG,QAAQ,cAAc,EAAE;YAChC;YACA,IAAI,QAAQ,MAAM;gBACd;YACJ;YACA,IAAI,QAAQ,YAAY,SAAS,OAAO,SAAS,QAAQ,YAAY,SAAS,OAAO;gBACjF,OAAO,eAAe,CAAC,KAAK;YAChC,OACK;gBACD,OAAQ,eAAe,IAAI;oBACvB,KAAK,UAAU,IAAI,CAAC,sBAAsB;oBAC1C,KAAK,UAAU,IAAI,CAAC,qBAAqB;wBACrC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,SAAS,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAChG;oBACJ,KAAK,UAAU,IAAI,CAAC,oBAAoB;oBACxC,KAAK,UAAU,IAAI,CAAC,mBAAmB;wBACnC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,SAAS,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAChG;oBACJ,KAAK,UAAU,IAAI,CAAC,qBAAqB;oBACzC,KAAK,UAAU,IAAI,CAAC,oBAAoB;wBACpC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAClG;oBACJ,KAAK,UAAU,IAAI,CAAC,sBAAsB;oBAC1C,KAAK,UAAU,IAAI,CAAC,qBAAqB;wBACrC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,WAAW,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACpG;oBACJ,KAAK,UAAU,IAAI,CAAC,4BAA4B;oBAChD,KAAK,UAAU,IAAI,CAAC,2BAA2B;wBAC3C,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,cAAc,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAC3G;oBACJ,KAAK,UAAU,IAAI,CAAC,yBAAyB;oBAC7C,KAAK,UAAU,IAAI,CAAC,wBAAwB;wBACxC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe,cAAc,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAC1G;oBACJ,KAAK,UAAU,IAAI,CAAC,oBAAoB;wBACpC,IAAI,eAAe,CAAC,KAAK,EAAE;4BACvB,MAAM,2BAA2B,QAAQ,CAAC,GAAG,gBAAgB;4BAC7D,IAAI,0BAA0B;gCAC1B,IAAI,CAAC,UAAU,eAAe,CAAC,KAAK,GAAG;oCACnC,eAAe,CAAC,KAAK,GAAG;gCAC5B;4BACJ;wBACJ;wBACA,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,cAAc,EAAE,gBAAgB,eAAe,CAAC,KAAK;wBACjG;gBACR;YACJ;QACJ,OACK,IAAI,eAAe,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,IAC7D,eAAe,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,EAAE;YACzD,eAAe,CAAC,QAAQ,eAAe,CAAC,GAAG,CAAC,GAAG,gBAAgB,eAAe,EAAE,gBAAgB,eAAe,CAAC,QAAQ,eAAe,CAAC,EAAE;QAC9I;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAI;IACnB,OAAQ,QAAQ,QAAQ,OAAO,SAAS,YAAY,UAAU,QAAQ,OAAO,KAAK,IAAI,KAAK;AAC/F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1935, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/links.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.resolveImportName = resolveImportName;\nexports.extractLinks = extractLinks;\n/**\n * A simplified, GraphQL v15 compatible version of\n * https://github.com/graphql-hive/federation-composition/blob/main/src/utils/link/index.ts\n * that does not provide the same safeguards or functionality, but still can determine the\n * correct name of an linked resource.\n */\nconst graphql_1 = require(\"graphql\");\nfunction namespace(link) {\n    return link.as ?? link.url.name;\n}\nfunction defaultImport(link) {\n    const name = namespace(link);\n    return name && `@${name}`;\n}\nfunction resolveImportName(link, elementName) {\n    if (link.url.name && elementName === `@${link.url.name}`) {\n        // @note: default is a directive... So remove the `@`\n        return defaultImport(link).substring(1);\n    }\n    const imported = link.imports.find(i => i.name === elementName);\n    const resolvedName = imported?.as ?? imported?.name ?? namespaced(namespace(link), elementName);\n    // Strip the `@` prefix for directives because in all implementations of mapping or visiting a schema,\n    // directive names are not prefixed with `@`. The `@` is only for SDL.\n    return resolvedName.startsWith('@') ? resolvedName.substring(1) : resolvedName;\n}\nfunction namespaced(namespace, name) {\n    if (namespace?.length) {\n        if (name.startsWith('@')) {\n            return `@${namespace}__${name.substring(1)}`;\n        }\n        return `${namespace}__${name}`;\n    }\n    return name;\n}\nfunction extractLinks(typeDefs) {\n    let links = [];\n    for (const definition of typeDefs.definitions) {\n        if (definition.kind === graphql_1.Kind.SCHEMA_EXTENSION || definition.kind === graphql_1.Kind.SCHEMA_DEFINITION) {\n            const defLinks = definition.directives?.filter(directive => directive.name.value === 'link');\n            const parsedLinks = defLinks?.map(l => linkFromArgs(l.arguments ?? [])).filter(l => l !== undefined) ?? [];\n            links = links.concat(parsedLinks);\n            // Federation 1 support... Federation 1 uses \"@core\" instead of \"@link\", but behavior is similar enough that\n            //  it can be translated.\n            const defCores = definition.directives?.filter(({ name }) => name.value === 'core');\n            const coreLinks = defCores\n                ?.map(c => linkFromCoreArgs(c.arguments ?? []))\n                .filter(l => l !== undefined);\n            if (coreLinks) {\n                links = links.concat(...coreLinks);\n            }\n        }\n    }\n    return links;\n}\nfunction linkFromArgs(args) {\n    let url;\n    let imports = [];\n    let as;\n    for (const arg of args) {\n        switch (arg.name.value) {\n            case 'url': {\n                if (arg.value.kind === graphql_1.Kind.STRING) {\n                    url = parseFederationLinkUrl(arg.value.value);\n                }\n                break;\n            }\n            case 'import': {\n                imports = parseImportNode(arg.value);\n                break;\n            }\n            case 'as': {\n                if (arg.value.kind === graphql_1.Kind.STRING) {\n                    as = arg.value.value ?? undefined;\n                }\n                break;\n            }\n            default: {\n                // ignore. It's not the job of this package to validate. Federation should validate links.\n            }\n        }\n    }\n    if (url !== undefined) {\n        return {\n            url,\n            as,\n            imports,\n        };\n    }\n}\n/**\n * Supports federation 1\n */\nfunction linkFromCoreArgs(args) {\n    const feature = args.find(({ name, value }) => name.value === 'feature' && value.kind === graphql_1.Kind.STRING);\n    if (feature) {\n        const url = parseFederationLinkUrl(feature.value.value);\n        return {\n            url,\n            imports: [],\n        };\n    }\n}\nfunction parseImportNode(node) {\n    if (node.kind === graphql_1.Kind.LIST) {\n        const imports = node.values.map((v) => {\n            let namedImport;\n            if (v.kind === graphql_1.Kind.STRING) {\n                namedImport = { name: v.value };\n            }\n            else if (v.kind === graphql_1.Kind.OBJECT) {\n                let name = '';\n                let as;\n                for (const f of v.fields) {\n                    if (f.name.value === 'name') {\n                        if (f.value.kind === graphql_1.Kind.STRING) {\n                            name = f.value.value;\n                        }\n                    }\n                    else if (f.name.value === 'as') {\n                        if (f.value.kind === graphql_1.Kind.STRING) {\n                            as = f.value.value;\n                        }\n                    }\n                }\n                namedImport = { name, as };\n            }\n            return namedImport;\n        });\n        return imports.filter(i => i !== undefined);\n    }\n    return [];\n}\nconst VERSION_MATCH = /v(\\d{1,3})\\.(\\d{1,4})/i;\nfunction parseFederationLinkUrl(urlSource) {\n    const url = new URL(urlSource);\n    const parts = url.pathname.split('/').filter(Boolean);\n    const versionOrName = parts[parts.length - 1];\n    if (versionOrName) {\n        if (VERSION_MATCH.test(versionOrName)) {\n            const maybeName = parts[parts.length - 2];\n            return {\n                identity: url.origin + (maybeName ? `/${parts.slice(0, parts.length - 1).join('/')}` : ''),\n                name: maybeName ?? null,\n                version: versionOrName,\n            };\n        }\n        return {\n            identity: `${url.origin}/${parts.join('/')}`,\n            name: versionOrName,\n            version: null,\n        };\n    }\n    return {\n        identity: url.origin,\n        name: null,\n        version: null,\n    };\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,YAAY,GAAG;AACvB;;;;;CAKC,GACD,MAAM;AACN,SAAS,UAAU,IAAI;IACnB,OAAO,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI;AACnC;AACA,SAAS,cAAc,IAAI;IACvB,MAAM,OAAO,UAAU;IACvB,OAAO,QAAQ,CAAC,CAAC,EAAE,MAAM;AAC7B;AACA,SAAS,kBAAkB,IAAI,EAAE,WAAW;IACxC,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,gBAAgB,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,EAAE,EAAE;QACtD,qDAAqD;QACrD,OAAO,cAAc,MAAM,SAAS,CAAC;IACzC;IACA,MAAM,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IACnD,MAAM,eAAe,UAAU,MAAM,UAAU,QAAQ,WAAW,UAAU,OAAO;IACnF,sGAAsG;IACtG,sEAAsE;IACtE,OAAO,aAAa,UAAU,CAAC,OAAO,aAAa,SAAS,CAAC,KAAK;AACtE;AACA,SAAS,WAAW,SAAS,EAAE,IAAI;IAC/B,IAAI,WAAW,QAAQ;QACnB,IAAI,KAAK,UAAU,CAAC,MAAM;YACtB,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,KAAK,SAAS,CAAC,IAAI;QAChD;QACA,OAAO,GAAG,UAAU,EAAE,EAAE,MAAM;IAClC;IACA,OAAO;AACX;AACA,SAAS,aAAa,QAAQ;IAC1B,IAAI,QAAQ,EAAE;IACd,KAAK,MAAM,cAAc,SAAS,WAAW,CAAE;QAC3C,IAAI,WAAW,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,IAAI,WAAW,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,EAAE;YAC7G,MAAM,WAAW,WAAW,UAAU,EAAE,OAAO,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK;YACrF,MAAM,cAAc,UAAU,IAAI,CAAA,IAAK,aAAa,EAAE,SAAS,IAAI,EAAE,GAAG,OAAO,CAAA,IAAK,MAAM,cAAc,EAAE;YAC1G,QAAQ,MAAM,MAAM,CAAC;YACrB,4GAA4G;YAC5G,yBAAyB;YACzB,MAAM,WAAW,WAAW,UAAU,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK,KAAK,KAAK,KAAK;YAC5E,MAAM,YAAY,UACZ,IAAI,CAAA,IAAK,iBAAiB,EAAE,SAAS,IAAI,EAAE,GAC5C,OAAO,CAAA,IAAK,MAAM;YACvB,IAAI,WAAW;gBACX,QAAQ,MAAM,MAAM,IAAI;YAC5B;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,IAAI;IACtB,IAAI;IACJ,IAAI,UAAU,EAAE;IAChB,IAAI;IACJ,KAAK,MAAM,OAAO,KAAM;QACpB,OAAQ,IAAI,IAAI,CAAC,KAAK;YAClB,KAAK;gBAAO;oBACR,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;wBAC1C,MAAM,uBAAuB,IAAI,KAAK,CAAC,KAAK;oBAChD;oBACA;gBACJ;YACA,KAAK;gBAAU;oBACX,UAAU,gBAAgB,IAAI,KAAK;oBACnC;gBACJ;YACA,KAAK;gBAAM;oBACP,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;wBAC1C,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI;oBAC5B;oBACA;gBACJ;YACA;gBAAS;gBACL,0FAA0F;gBAC9F;QACJ;IACJ;IACA,IAAI,QAAQ,WAAW;QACnB,OAAO;YACH;YACA;YACA;QACJ;IACJ;AACJ;AACA;;CAEC,GACD,SAAS,iBAAiB,IAAI;IAC1B,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,KAAK,KAAK,KAAK,aAAa,MAAM,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM;IAC/G,IAAI,SAAS;QACT,MAAM,MAAM,uBAAuB,QAAQ,KAAK,CAAC,KAAK;QACtD,OAAO;YACH;YACA,SAAS,EAAE;QACf;IACJ;AACJ;AACA,SAAS,gBAAgB,IAAI;IACzB,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,EAAE;QACnC,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI;YACJ,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;gBAClC,cAAc;oBAAE,MAAM,EAAE,KAAK;gBAAC;YAClC,OACK,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;gBACvC,IAAI,OAAO;gBACX,IAAI;gBACJ,KAAK,MAAM,KAAK,EAAE,MAAM,CAAE;oBACtB,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,QAAQ;wBACzB,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BACxC,OAAO,EAAE,KAAK,CAAC,KAAK;wBACxB;oBACJ,OACK,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM;wBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BACxC,KAAK,EAAE,KAAK,CAAC,KAAK;wBACtB;oBACJ;gBACJ;gBACA,cAAc;oBAAE;oBAAM;gBAAG;YAC7B;YACA,OAAO;QACX;QACA,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM;IACrC;IACA,OAAO,EAAE;AACb;AACA,MAAM,gBAAgB;AACtB,SAAS,uBAAuB,SAAS;IACrC,MAAM,MAAM,IAAI,IAAI;IACpB,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC;IAC7C,MAAM,gBAAgB,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IAC7C,IAAI,eAAe;QACf,IAAI,cAAc,IAAI,CAAC,gBAAgB;YACnC,MAAM,YAAY,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;YACzC,OAAO;gBACH,UAAU,IAAI,MAAM,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE;gBACzF,MAAM,aAAa;gBACnB,SAAS;YACb;QACJ;QACA,OAAO;YACH,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,MAAM;YAC5C,MAAM;YACN,SAAS;QACb;IACJ;IACA,OAAO;QACH,UAAU,IAAI,MAAM;QACpB,MAAM;QACN,SAAS;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/merge-typedefs.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeTypeDefs = mergeTypeDefs;\nexports.mergeGraphQLTypes = mergeGraphQLTypes;\nconst graphql_1 = require(\"graphql\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nconst links_js_1 = require(\"../links.js\");\nconst merge_nodes_js_1 = require(\"./merge-nodes.js\");\nconst schema_def_js_1 = require(\"./schema-def.js\");\nconst utils_js_1 = require(\"./utils.js\");\nfunction mergeTypeDefs(typeSource, config) {\n    (0, utils_1.resetComments)();\n    const doc = {\n        kind: graphql_1.Kind.DOCUMENT,\n        definitions: mergeGraphQLTypes(typeSource, {\n            useSchemaDefinition: true,\n            forceSchemaDefinition: false,\n            throwOnConflict: false,\n            commentDescriptions: false,\n            ...config,\n        }),\n    };\n    let result;\n    if (config?.commentDescriptions) {\n        result = (0, utils_1.printWithComments)(doc);\n    }\n    else {\n        result = doc;\n    }\n    (0, utils_1.resetComments)();\n    return result;\n}\nfunction visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set(), repeatableLinkImports = new Set()) {\n    const addRepeatable = (name) => {\n        repeatableLinkImports.add(name);\n    };\n    if (typeSource && !visitedTypeSources.has(typeSource)) {\n        visitedTypeSources.add(typeSource);\n        if (typeof typeSource === 'function') {\n            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else if (Array.isArray(typeSource)) {\n            for (const type of typeSource) {\n                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n            }\n        }\n        else if ((0, graphql_1.isSchema)(typeSource)) {\n            const documentNode = (0, utils_1.getDocumentNodeFromSchema)(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else if ((0, utils_js_1.isStringTypes)(typeSource) || (0, utils_js_1.isSourceTypes)(typeSource)) {\n            const documentNode = (0, graphql_1.parse)(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else if (typeof typeSource === 'object' && (0, graphql_1.isDefinitionNode)(typeSource)) {\n            const links = (0, links_js_1.extractLinks)({\n                definitions: [typeSource],\n                kind: graphql_1.Kind.DOCUMENT,\n            });\n            const federationUrl = 'https://specs.apollo.dev/federation';\n            const linkUrl = 'https://specs.apollo.dev/link';\n            /**\n             * Official Federated imports are special because they can be referenced without specifyin the import.\n             * To handle this case, we must prepare a list of all the possible valid usages to check against.\n             * Note that this versioning is not technically correct, since some definitions are after v2.0.\n             * But this is enough information to be comfortable not blocking the imports at this phase. It's\n             * the job of the composer to validate the versions.\n             * */\n            const federationLink = links.find(l => l.url.identity === federationUrl);\n            if (federationLink) {\n                addRepeatable((0, links_js_1.resolveImportName)(federationLink, '@composeDirective'));\n                addRepeatable((0, links_js_1.resolveImportName)(federationLink, '@key'));\n            }\n            const linkLink = links.find(l => l.url.identity === linkUrl);\n            if (linkLink) {\n                addRepeatable((0, links_js_1.resolveImportName)(linkLink, '@link'));\n            }\n            if (typeSource.kind === graphql_1.Kind.DIRECTIVE_DEFINITION) {\n                allDirectives.push(typeSource);\n            }\n            else {\n                allNodes.push(typeSource);\n            }\n        }\n        else if ((0, utils_1.isDocumentNode)(typeSource)) {\n            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources, repeatableLinkImports);\n        }\n        else {\n            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);\n        }\n    }\n    return { allDirectives, allNodes, repeatableLinkImports };\n}\nfunction mergeGraphQLTypes(typeSource, config) {\n    (0, utils_1.resetComments)();\n    const { allDirectives, allNodes, repeatableLinkImports } = visitTypeSources(typeSource, config);\n    const mergedDirectives = (0, merge_nodes_js_1.mergeGraphQLNodes)(allDirectives, config);\n    config.repeatableLinkImports = repeatableLinkImports;\n    const mergedNodes = (0, merge_nodes_js_1.mergeGraphQLNodes)(allNodes, config, mergedDirectives);\n    if (config?.useSchemaDefinition) {\n        // XXX: right now we don't handle multiple schema definitions\n        const schemaDef = mergedNodes[merge_nodes_js_1.schemaDefSymbol] || {\n            kind: graphql_1.Kind.SCHEMA_DEFINITION,\n            operationTypes: [],\n        };\n        const operationTypes = schemaDef.operationTypes;\n        for (const opTypeDefNodeType in schema_def_js_1.DEFAULT_OPERATION_TYPE_NAME_MAP) {\n            const opTypeDefNode = operationTypes.find(operationType => operationType.operation === opTypeDefNodeType);\n            if (!opTypeDefNode) {\n                const possibleRootTypeName = schema_def_js_1.DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];\n                const existingPossibleRootType = mergedNodes[possibleRootTypeName];\n                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {\n                    operationTypes.push({\n                        kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,\n                        type: {\n                            kind: graphql_1.Kind.NAMED_TYPE,\n                            name: existingPossibleRootType.name,\n                        },\n                        operation: opTypeDefNodeType,\n                    });\n                }\n            }\n        }\n        if (schemaDef?.operationTypes?.length != null && schemaDef.operationTypes.length > 0) {\n            mergedNodes[merge_nodes_js_1.schemaDefSymbol] = schemaDef;\n        }\n    }\n    if (config?.forceSchemaDefinition && !mergedNodes[merge_nodes_js_1.schemaDefSymbol]?.operationTypes?.length) {\n        mergedNodes[merge_nodes_js_1.schemaDefSymbol] = {\n            kind: graphql_1.Kind.SCHEMA_DEFINITION,\n            operationTypes: [\n                {\n                    kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,\n                    operation: 'query',\n                    type: {\n                        kind: graphql_1.Kind.NAMED_TYPE,\n                        name: {\n                            kind: graphql_1.Kind.NAME,\n                            value: 'Query',\n                        },\n                    },\n                },\n            ],\n        };\n    }\n    const mergedNodeDefinitions = Object.values(mergedNodes);\n    if (config?.sort) {\n        const sortFn = typeof config.sort === 'function' ? config.sort : utils_js_1.defaultStringComparator;\n        mergedNodeDefinitions.sort((a, b) => sortFn(a.name?.value, b.name?.value));\n    }\n    return mergedNodeDefinitions;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG;AACxB,QAAQ,iBAAiB,GAAG;AAC5B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,cAAc,UAAU,EAAE,MAAM;IACrC,CAAC,GAAG,QAAQ,aAAa;IACzB,MAAM,MAAM;QACR,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,aAAa,kBAAkB,YAAY;YACvC,qBAAqB;YACrB,uBAAuB;YACvB,iBAAiB;YACjB,qBAAqB;YACrB,GAAG,MAAM;QACb;IACJ;IACA,IAAI;IACJ,IAAI,QAAQ,qBAAqB;QAC7B,SAAS,CAAC,GAAG,QAAQ,iBAAiB,EAAE;IAC5C,OACK;QACD,SAAS;IACb;IACA,CAAC,GAAG,QAAQ,aAAa;IACzB,OAAO;AACX;AACA,SAAS,iBAAiB,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,WAAW,EAAE,EAAE,qBAAqB,IAAI,KAAK,EAAE,wBAAwB,IAAI,KAAK;IAC/I,MAAM,gBAAgB,CAAC;QACnB,sBAAsB,GAAG,CAAC;IAC9B;IACA,IAAI,cAAc,CAAC,mBAAmB,GAAG,CAAC,aAAa;QACnD,mBAAmB,GAAG,CAAC;QACvB,IAAI,OAAO,eAAe,YAAY;YAClC,iBAAiB,cAAc,SAAS,eAAe,UAAU,oBAAoB;QACzF,OACK,IAAI,MAAM,OAAO,CAAC,aAAa;YAChC,KAAK,MAAM,QAAQ,WAAY;gBAC3B,iBAAiB,MAAM,SAAS,eAAe,UAAU,oBAAoB;YACjF;QACJ,OACK,IAAI,CAAC,GAAG,UAAU,QAAQ,EAAE,aAAa;YAC1C,MAAM,eAAe,CAAC,GAAG,QAAQ,yBAAyB,EAAE,YAAY;YACxE,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU,oBAAoB;QACrG,OACK,IAAI,CAAC,GAAG,WAAW,aAAa,EAAE,eAAe,CAAC,GAAG,WAAW,aAAa,EAAE,aAAa;YAC7F,MAAM,eAAe,CAAC,GAAG,UAAU,KAAK,EAAE,YAAY;YACtD,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU,oBAAoB;QACrG,OACK,IAAI,OAAO,eAAe,YAAY,CAAC,GAAG,UAAU,gBAAgB,EAAE,aAAa;YACpF,MAAM,QAAQ,CAAC,GAAG,WAAW,YAAY,EAAE;gBACvC,aAAa;oBAAC;iBAAW;gBACzB,MAAM,UAAU,IAAI,CAAC,QAAQ;YACjC;YACA,MAAM,gBAAgB;YACtB,MAAM,UAAU;YAChB;;;;;;eAMG,GACH,MAAM,iBAAiB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,QAAQ,KAAK;YAC1D,IAAI,gBAAgB;gBAChB,cAAc,CAAC,GAAG,WAAW,iBAAiB,EAAE,gBAAgB;gBAChE,cAAc,CAAC,GAAG,WAAW,iBAAiB,EAAE,gBAAgB;YACpE;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,QAAQ,KAAK;YACpD,IAAI,UAAU;gBACV,cAAc,CAAC,GAAG,WAAW,iBAAiB,EAAE,UAAU;YAC9D;YACA,IAAI,WAAW,IAAI,KAAK,UAAU,IAAI,CAAC,oBAAoB,EAAE;gBACzD,cAAc,IAAI,CAAC;YACvB,OACK;gBACD,SAAS,IAAI,CAAC;YAClB;QACJ,OACK,IAAI,CAAC,GAAG,QAAQ,cAAc,EAAE,aAAa;YAC9C,iBAAiB,WAAW,WAAW,EAAE,SAAS,eAAe,UAAU,oBAAoB;QACnG,OACK;YACD,MAAM,IAAI,MAAM,CAAC,0EAA0E,EAAE,OAAO,YAAY;QACpH;IACJ;IACA,OAAO;QAAE;QAAe;QAAU;IAAsB;AAC5D;AACA,SAAS,kBAAkB,UAAU,EAAE,MAAM;IACzC,CAAC,GAAG,QAAQ,aAAa;IACzB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,EAAE,GAAG,iBAAiB,YAAY;IACxF,MAAM,mBAAmB,CAAC,GAAG,iBAAiB,iBAAiB,EAAE,eAAe;IAChF,OAAO,qBAAqB,GAAG;IAC/B,MAAM,cAAc,CAAC,GAAG,iBAAiB,iBAAiB,EAAE,UAAU,QAAQ;IAC9E,IAAI,QAAQ,qBAAqB;QAC7B,6DAA6D;QAC7D,MAAM,YAAY,WAAW,CAAC,iBAAiB,eAAe,CAAC,IAAI;YAC/D,MAAM,UAAU,IAAI,CAAC,iBAAiB;YACtC,gBAAgB,EAAE;QACtB;QACA,MAAM,iBAAiB,UAAU,cAAc;QAC/C,IAAK,MAAM,qBAAqB,gBAAgB,+BAA+B,CAAE;YAC7E,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,gBAAiB,cAAc,SAAS,KAAK;YACvF,IAAI,CAAC,eAAe;gBAChB,MAAM,uBAAuB,gBAAgB,+BAA+B,CAAC,kBAAkB;gBAC/F,MAAM,2BAA2B,WAAW,CAAC,qBAAqB;gBAClE,IAAI,4BAA4B,QAAQ,yBAAyB,IAAI,IAAI,MAAM;oBAC3E,eAAe,IAAI,CAAC;wBAChB,MAAM,UAAU,IAAI,CAAC,yBAAyB;wBAC9C,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,UAAU;4BAC/B,MAAM,yBAAyB,IAAI;wBACvC;wBACA,WAAW;oBACf;gBACJ;YACJ;QACJ;QACA,IAAI,WAAW,gBAAgB,UAAU,QAAQ,UAAU,cAAc,CAAC,MAAM,GAAG,GAAG;YAClF,WAAW,CAAC,iBAAiB,eAAe,CAAC,GAAG;QACpD;IACJ;IACA,IAAI,QAAQ,yBAAyB,CAAC,WAAW,CAAC,iBAAiB,eAAe,CAAC,EAAE,gBAAgB,QAAQ;QACzG,WAAW,CAAC,iBAAiB,eAAe,CAAC,GAAG;YAC5C,MAAM,UAAU,IAAI,CAAC,iBAAiB;YACtC,gBAAgB;gBACZ;oBACI,MAAM,UAAU,IAAI,CAAC,yBAAyB;oBAC9C,WAAW;oBACX,MAAM;wBACF,MAAM,UAAU,IAAI,CAAC,UAAU;wBAC/B,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,IAAI;4BACzB,OAAO;wBACX;oBACJ;gBACJ;aACH;QACL;IACJ;IACA,MAAM,wBAAwB,OAAO,MAAM,CAAC;IAC5C,IAAI,QAAQ,MAAM;QACd,MAAM,SAAS,OAAO,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,GAAG,WAAW,uBAAuB;QACnG,sBAAsB,IAAI,CAAC,CAAC,GAAG,IAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IACvE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./arguments.js\"), exports);\ntslib_1.__exportStar(require(\"./directives.js\"), exports);\ntslib_1.__exportStar(require(\"./enum-values.js\"), exports);\ntslib_1.__exportStar(require(\"./enum.js\"), exports);\ntslib_1.__exportStar(require(\"./fields.js\"), exports);\ntslib_1.__exportStar(require(\"./input-type.js\"), exports);\ntslib_1.__exportStar(require(\"./interface.js\"), exports);\ntslib_1.__exportStar(require(\"./merge-named-type-array.js\"), exports);\ntslib_1.__exportStar(require(\"./merge-nodes.js\"), exports);\ntslib_1.__exportStar(require(\"./merge-typedefs.js\"), exports);\ntslib_1.__exportStar(require(\"./scalar.js\"), exports);\ntslib_1.__exportStar(require(\"./type.js\"), exports);\ntslib_1.__exportStar(require(\"./union.js\"), exports);\ntslib_1.__exportStar(require(\"./utils.js\"), exports);\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,QAAQ,YAAY,oIAA4B;AAChD,QAAQ,YAAY,qIAA6B;AACjD,QAAQ,YAAY,sIAA8B;AAClD,QAAQ,YAAY,+HAAuB;AAC3C,QAAQ,YAAY,iIAAyB;AAC7C,QAAQ,YAAY,qIAA6B;AACjD,QAAQ,YAAY,oIAA4B;AAChD,QAAQ,YAAY,iJAAyC;AAC7D,QAAQ,YAAY,sIAA8B;AAClD,QAAQ,YAAY,yIAAiC;AACrD,QAAQ,YAAY,iIAAyB;AAC7C,QAAQ,YAAY,+HAAuB;AAC3C,QAAQ,YAAY,gIAAwB;AAC5C,QAAQ,YAAY,gIAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2281, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/extensions.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extractExtensionsFromSchema = void 0;\nexports.mergeExtensions = mergeExtensions;\nexports.applyExtensions = applyExtensions;\nconst utils_1 = require(\"@graphql-tools/utils\");\nvar utils_2 = require(\"@graphql-tools/utils\");\nObject.defineProperty(exports, \"extractExtensionsFromSchema\", { enumerable: true, get: function () { return utils_2.extractExtensionsFromSchema; } });\nfunction mergeExtensions(extensions) {\n    return (0, utils_1.mergeDeep)(extensions, false, true);\n}\nfunction applyExtensionObject(obj, extensions) {\n    if (!obj || !extensions || extensions === obj.extensions) {\n        return;\n    }\n    if (!obj.extensions) {\n        obj.extensions = extensions;\n        return;\n    }\n    obj.extensions = (0, utils_1.mergeDeep)([obj.extensions, extensions], false, true);\n}\nfunction applyExtensions(schema, extensions) {\n    applyExtensionObject(schema, extensions.schemaExtensions);\n    for (const [typeName, data] of Object.entries(extensions.types || {})) {\n        const type = schema.getType(typeName);\n        if (type) {\n            applyExtensionObject(type, data.extensions);\n            if (data.type === 'object' || data.type === 'interface') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    if (field) {\n                        applyExtensionObject(field, fieldData.extensions);\n                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {\n                            applyExtensionObject(field.args.find(a => a.name === arg), argData);\n                        }\n                    }\n                }\n            }\n            else if (data.type === 'input') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    applyExtensionObject(field, fieldData.extensions);\n                }\n            }\n            else if (data.type === 'enum') {\n                for (const [valueName, valueData] of Object.entries(data.values)) {\n                    const value = type.getValue(valueName);\n                    applyExtensionObject(value, valueData);\n                }\n            }\n        }\n    }\n    return schema;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,2BAA2B,GAAG,KAAK;AAC3C,QAAQ,eAAe,GAAG;AAC1B,QAAQ,eAAe,GAAG;AAC1B,MAAM;AACN,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,+BAA+B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,QAAQ,2BAA2B;IAAE;AAAE;AACnJ,SAAS,gBAAgB,UAAU;IAC/B,OAAO,CAAC,GAAG,QAAQ,SAAS,EAAE,YAAY,OAAO;AACrD;AACA,SAAS,qBAAqB,GAAG,EAAE,UAAU;IACzC,IAAI,CAAC,OAAO,CAAC,cAAc,eAAe,IAAI,UAAU,EAAE;QACtD;IACJ;IACA,IAAI,CAAC,IAAI,UAAU,EAAE;QACjB,IAAI,UAAU,GAAG;QACjB;IACJ;IACA,IAAI,UAAU,GAAG,CAAC,GAAG,QAAQ,SAAS,EAAE;QAAC,IAAI,UAAU;QAAE;KAAW,EAAE,OAAO;AACjF;AACA,SAAS,gBAAgB,MAAM,EAAE,UAAU;IACvC,qBAAqB,QAAQ,WAAW,gBAAgB;IACxD,KAAK,MAAM,CAAC,UAAU,KAAK,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,GAAI;QACnE,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,IAAI,MAAM;YACN,qBAAqB,MAAM,KAAK,UAAU;YAC1C,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,aAAa;gBACrD,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,IAAI,OAAO;wBACP,qBAAqB,OAAO,UAAU,UAAU;wBAChD,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAU,SAAS,EAAG;4BAC9D,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM;wBAC/D;oBACJ;gBACJ;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,SAAS;gBAC5B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,qBAAqB,OAAO,UAAU,UAAU;gBACpD;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,QAAQ;gBAC3B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,QAAQ,CAAC;oBAC5B,qBAAqB,OAAO;gBAChC;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/node_modules/%40graphql-tools/merge/cjs/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./merge-resolvers.js\"), exports);\ntslib_1.__exportStar(require(\"./typedefs-mergers/index.js\"), exports);\ntslib_1.__exportStar(require(\"./extensions.js\"), exports);\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,QAAQ,YAAY,yHAAkC;AACtD,QAAQ,YAAY,gIAAyC;AAC7D,QAAQ,YAAY,oHAA6B", "ignoreList": [0], "debugId": null}}]}