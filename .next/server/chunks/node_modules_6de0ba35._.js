module.exports = [
"[project]/node_modules/graphql/index.mjs [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/cacheControl/index.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_4069856d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/cacheControl/index.js [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/usageReporting/index.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_00f79c4b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/usageReporting/index.js [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/schemaReporting/index.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_4ddc2240._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/schemaReporting/index.js [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/inlineTrace/index.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_@apollo_eb752bbd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/inlineTrace/index.js [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_ecc3b0cf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_@apollo_server_dist_esm_plugin_disableSuggestions_index_872cae59.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript)");
    });
});
}),
];