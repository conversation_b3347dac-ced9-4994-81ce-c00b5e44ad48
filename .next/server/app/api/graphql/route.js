var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/graphql/route.js")
R.c("server/chunks/node_modules_6de0ba35._.js")
R.c("server/chunks/node_modules_next_dist_9d5fc1a3._.js")
R.c("server/chunks/node_modules_graphql_1010f8b1._.js")
R.c("server/chunks/node_modules_@graphql-tools_utils_c749fea5._.js")
R.c("server/chunks/node_modules_@graphql-tools_merge_0119a81d._.js")
R.c("server/chunks/node_modules_@apollo_server_dist_7c8ad4e5._.js")
R.c("server/chunks/node_modules_@apollo_usage-reporting-protobuf_generated_cjs_protobuf_ce95f1a2.js")
R.c("server/chunks/node_modules_87b9671d._.js")
R.c("server/chunks/[root-of-the-server]__6ee29bf4._.js")
R.m("[project]/.next-internal/server/app/api/graphql/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
