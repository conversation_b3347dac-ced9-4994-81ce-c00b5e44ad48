{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface HeaderProps {\n  selectedRange: string;\n  onRangeChange: (range: string) => void;\n}\n\nexport function Header({ selectedRange, onRangeChange }: HeaderProps) {\n  const dateRanges = [\n    { value: '7d', label: '7d' },\n    { value: '14d', label: '14d' },\n    { value: '30d', label: '30d' },\n  ];\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Logo */}\n        <div className=\"flex items-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            SupplySight\n          </h1>\n        </div>\n\n        {/* Date Range Chips */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-600 mr-2\">Time Range:</span>\n          <div className=\"flex space-x-1\">\n            {dateRanges.map((range) => (\n              <button\n                key={range.value}\n                onClick={() => onRangeChange(range.value)}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  selectedRange === range.value\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                {range.label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,SAAS,OAAO,KAA6C;QAA7C,EAAE,aAAa,EAAE,aAAa,EAAe,GAA7C;IACrB,MAAM,aAAa;QACjB;YAAE,OAAO;YAAM,OAAO;QAAK;QAC3B;YAAE,OAAO;YAAO,OAAO;QAAM;QAC7B;YAAE,OAAO;YAAO,OAAO;QAAM;KAC9B;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;8BAMnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAA6B;;;;;;sCAC7C,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC;oCAEC,SAAS,IAAM,cAAc,MAAM,KAAK;oCACxC,WAAW,AAAC,gEAIX,OAHC,kBAAkB,MAAM,KAAK,GACzB,2BACA;8CAGL,MAAM,KAAK;mCARP,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBhC;KAvCgB", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, createContext, useContext } from \"react\";\nimport { Header } from \"./Header\";\n\ninterface DashboardContextType {\n  selectedRange: string;\n  setSelectedRange: (range: string) => void;\n}\n\nconst DashboardContext = createContext<DashboardContextType | undefined>(\n  undefined\n);\n\nexport function useDashboard() {\n  const context = useContext(DashboardContext);\n  if (!context) {\n    throw new Error(\"useDashboard must be used within a DashboardLayout\");\n  }\n  return context;\n}\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [selectedRange, setSelectedRange] = useState(\"7d\");\n\n  return (\n    <DashboardContext.Provider value={{ selectedRange, setSelectedRange }}>\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header\n          selectedRange={selectedRange}\n          onRangeChange={setSelectedRange}\n        />\n        <main className=\"p-6\">{children}</main>\n      </div>\n    </DashboardContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,iCAAmB,IAAA,8KAAa,EACpC;AAGK,SAAS;;IACd,MAAM,UAAU,IAAA,2KAAU,EAAC;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IAEnD,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE;YAAe;QAAiB;kBAClE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mJAAM;oBACL,eAAe;oBACf,eAAe;;;;;;8BAEjB,6LAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;;;;;;AAI/B;IAdgB;KAAA", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/lib/graphql/queries.ts"], "sourcesContent": ["import { gql } from \"@apollo/client/core\";\n\nexport const GET_PRODUCTS = gql`\n  query GetProducts($search: String, $status: String, $warehouse: String) {\n    products(search: $search, status: $status, warehouse: $warehouse) {\n      id\n      name\n      sku\n      warehouse\n      stock\n      demand\n    }\n  }\n`;\n\nexport const GET_WAREHOUSES = gql`\n  query GetWarehouses {\n    warehouses {\n      code\n      name\n      city\n      country\n    }\n  }\n`;\n\nexport const GET_KPIS = gql`\n  query GetKPIs($range: String!) {\n    kpis(range: $range) {\n      date\n      stock\n      demand\n    }\n  }\n`;\n\nexport const UPDATE_DEMAND = gql`\n  mutation UpdateDemand($id: ID!, $demand: Int!) {\n    updateDemand(id: $id, demand: $demand) {\n      id\n      name\n      sku\n      warehouse\n      stock\n      demand\n    }\n  }\n`;\n\nexport const TRANSFER_STOCK = gql`\n  mutation TransferStock($id: ID!, $from: String!, $to: String!, $qty: Int!) {\n    transferStock(id: $id, from: $from, to: $to, qty: $qty) {\n      id\n      name\n      sku\n      warehouse\n      stock\n      demand\n    }\n  }\n`;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAe,wJAAG;AAaxB,MAAM,qBAAiB,wJAAG;AAW1B,MAAM,eAAW,wJAAG;AAUpB,MAAM,oBAAgB,wJAAG;AAazB,MAAM,qBAAiB,wJAAG", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/types/graphql.ts"], "sourcesContent": ["export interface Product {\n  id: string;\n  name: string;\n  sku: string;\n  warehouse: string;\n  stock: number;\n  demand: number;\n}\n\nexport interface Warehouse {\n  code: string;\n  name: string;\n  city: string;\n  country: string;\n}\n\nexport interface KPI {\n  date: string;\n  stock: number;\n  demand: number;\n}\n\nexport type ProductStatus = 'healthy' | 'low' | 'critical';\n\nexport interface ProductsQueryVariables {\n  search?: string;\n  status?: string;\n  warehouse?: string;\n}\n\nexport interface KPIsQueryVariables {\n  range: string;\n}\n\nexport interface UpdateDemandMutationVariables {\n  id: string;\n  demand: number;\n}\n\nexport interface TransferStockMutationVariables {\n  id: string;\n  from: string;\n  to: string;\n  qty: number;\n}\n\n// Helper function to determine product status\nexport function getProductStatus(product: Product): ProductStatus {\n  if (product.stock > product.demand) return 'healthy';\n  if (product.stock === product.demand) return 'low';\n  return 'critical';\n}\n\n// Helper function to calculate KPIs\nexport function calculateKPIs(products: Product[]) {\n  const totalStock = products.reduce((sum, product) => sum + product.stock, 0);\n  const totalDemand = products.reduce((sum, product) => sum + product.demand, 0);\n  const fillRate = totalDemand > 0 \n    ? (products.reduce((sum, product) => sum + Math.min(product.stock, product.demand), 0) / totalDemand) * 100 \n    : 0;\n\n  return {\n    totalStock,\n    totalDemand,\n    fillRate: Math.round(fillRate * 100) / 100, // Round to 2 decimal places\n  };\n}\n"], "names": [], "mappings": ";;;;;;AA+CO,SAAS,iBAAiB,OAAgB;IAC/C,IAAI,QAAQ,KAAK,GAAG,QAAQ,MAAM,EAAE,OAAO;IAC3C,IAAI,QAAQ,KAAK,KAAK,QAAQ,MAAM,EAAE,OAAO;IAC7C,OAAO;AACT;AAGO,SAAS,cAAc,QAAmB;IAC/C,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,KAAK,EAAE;IAC1E,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;IAC5E,MAAM,WAAW,cAAc,IAC3B,AAAC,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,QAAQ,MAAM,GAAG,KAAK,cAAe,MACtG;IAEJ,OAAO;QACL;QACA;QACA,UAAU,KAAK,KAAK,CAAC,WAAW,OAAO;IACzC;AACF", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/NextJs/supplysight/src/components/dashboard/KPICards.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useQuery } from \"@apollo/client/react\";\nimport { GET_PRODUCTS } from \"@/lib/graphql/queries\";\nimport { Product, calculateKPIs } from \"@/types/graphql\";\n\ninterface KPICardsProps {\n  className?: string;\n}\n\nexport function KPICards({ className = \"\" }: KPICardsProps) {\n  const { data, loading, error } = useQuery(GET_PRODUCTS, {\n    variables: {},\n  });\n\n  if (loading) {\n    return (\n      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n        {[1, 2, 3].map((i) => (\n          <div key={i} className=\"bg-white p-6 rounded-lg shadow animate-pulse\">\n            <div className=\"h-4 bg-gray-200 rounded w-20 mb-2\"></div>\n            <div className=\"h-8 bg-gray-200 rounded w-16\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n        <div className=\"bg-red-50 p-6 rounded-lg border border-red-200 col-span-3\">\n          <p className=\"text-red-600\">\n            Error loading KPI data: {error.message}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  const products: Product[] = data?.products || [];\n  const kpis = calculateKPIs(products);\n\n  return (\n    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n      {/* Total Stock */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Total Stock</h3>\n        <p className=\"text-3xl font-bold text-blue-600\">\n          {kpis.totalStock.toLocaleString()}\n        </p>\n        <p className=\"text-xs text-gray-400 mt-1\">units available</p>\n      </div>\n\n      {/* Total Demand */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Total Demand</h3>\n        <p className=\"text-3xl font-bold text-orange-600\">\n          {kpis.totalDemand.toLocaleString()}\n        </p>\n        <p className=\"text-xs text-gray-400 mt-1\">units required</p>\n      </div>\n\n      {/* Fill Rate */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Fill Rate</h3>\n        <p\n          className={`text-3xl font-bold ${\n            kpis.fillRate >= 90\n              ? \"text-green-600\"\n              : kpis.fillRate >= 70\n              ? \"text-yellow-600\"\n              : \"text-red-600\"\n          }`}\n        >\n          {kpis.fillRate.toFixed(1)}%\n        </p>\n        <p className=\"text-xs text-gray-400 mt-1\">demand fulfillment</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,SAAS,KAAiC;QAAjC,EAAE,YAAY,EAAE,EAAiB,GAAjC;;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAA,+KAAQ,EAAC,mJAAY,EAAE;QACtD,WAAW,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,AAAC,yCAAkD,OAAV;sBACtD;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;oBAAY,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;mBAFP;;;;;;;;;;IAOlB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAW,AAAC,yCAAkD,OAAV;sBACvD,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAe;wBACD,MAAM,OAAO;;;;;;;;;;;;;;;;;IAKhD;IAEA,MAAM,WAAsB,CAAA,iBAAA,2BAAA,KAAM,QAAQ,KAAI,EAAE;IAChD,MAAM,OAAO,IAAA,2IAAa,EAAC;IAE3B,qBACE,6LAAC;QAAI,WAAW,AAAC,yCAAkD,OAAV;;0BAEvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,KAAK,UAAU,CAAC,cAAc;;;;;;kCAEjC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,KAAK,WAAW,CAAC,cAAc;;;;;;kCAElC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBACC,WAAW,AAAC,sBAMX,OALC,KAAK,QAAQ,IAAI,KACb,mBACA,KAAK,QAAQ,IAAI,KACjB,oBACA;;4BAGL,KAAK,QAAQ,CAAC,OAAO,CAAC;4BAAG;;;;;;;kCAE5B,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAIlD;GAvEgB;;QACmB,+KAAQ;;;KAD3B", "debugId": null}}]}