import { KPI, Product, Warehouse } from "@/types/graphql";

// Mock data for the list of warehouses
export const warehouses: Warehouse[] = [
  {
    code: "BLR-A",
    name: "Bangalore Alpha",
    city: "Bangalore",
    country: "India",
  },
  { code: "PNQ-C", name: "Pune Central", city: "Pune", country: "India" },
  { code: "DEL-B", name: "Delhi Beta", city: "Delhi", country: "India" },
  { code: "MUM-D", name: "Mumbai Delta", city: "Mumbai", country: "India" },
];

// Mock data for the list of prodcuts with relationship to warehouses
export let products: Product[] = [
  {
    id: "P-1001",
    name: "12mm Hex Bolt",
    sku: "HEX-12-100",
    warehouse: "BLR-A",
    stock: 180,
    demand: 120,
  },
  {
    id: "P-1002",
    name: "Steel Washer",
    sku: "WSR-08-500",
    warehouse: "BLR-A",
    stock: 50,
    demand: 80,
  },
  {
    id: "P-1003",
    name: "M8 Nut",
    sku: "NUT-08-200",
    warehouse: "PNQ-C",
    stock: 80,
    demand: 80,
  },
  {
    id: "P-1004",
    name: "Bearing 608ZZ",
    sku: "BRG-608-50",
    warehouse: "DEL-B",
    stock: 24,
    demand: 120,
  },
  {
    id: "P-1005",
    name: "Steel Rod 10mm",
    sku: "ROD-10-300",
    warehouse: "BLR-A",
    stock: 200,
    demand: 150,
  },
  {
    id: "P-1006",
    name: "Aluminum Sheet",
    sku: "ALU-SHT-001",
    warehouse: "MUM-D",
    stock: 45,
    demand: 60,
  },
  {
    id: "P-1007",
    name: "Copper Wire 2mm",
    sku: "COP-WIR-002",
    warehouse: "PNQ-C",
    stock: 300,
    demand: 250,
  },
  {
    id: "P-1008",
    name: "Plastic Tube",
    sku: "PLA-TUB-005",
    warehouse: "DEL-B",
    stock: 15,
    demand: 90,
  },
  {
    id: "P-1009",
    name: "Rubber Gasket",
    sku: "RUB-GAS-008",
    warehouse: "BLR-A",
    stock: 120,
    demand: 100,
  },
  {
    id: "P-1010",
    name: "Stainless Screw",
    sku: "STA-SCR-012",
    warehouse: "MUM-D",
    stock: 75,
    demand: 75,
  },
];

// Generate mock KPI data for different date ranges
export const generateKPIData = (range: string): KPI[] => {
  const days = range === "7d" ? 7 : range === "14d" ? 14 : 30;
  const kpis: KPI[] = [];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    // Calculate totals with some variation
    const baseStock = products.reduce((sum, p) => sum + p.stock, 0);
    const baseDemand = products.reduce((sum, p) => sum + p.demand, 0);

    // Here I am adding some random variation to make the chart interesting
    // so we don't have constant KPIs for all dates
    const stockVariation = Math.floor(Math.random() * 200) - 100;
    const demandVariation = Math.floor(Math.random() * 150) - 75;

    kpis.push({
      date: date.toISOString().split("T")[0],
      stock: Math.max(0, baseStock + stockVariation),
      demand: Math.max(0, baseDemand + demandVariation),
    });
  }

  return kpis;
};
