import { products, warehouses, generateKPIData } from "./mockData";
import { getProductStatus } from "@/utils";

export const resolvers = {
  Query: {
    products: (
      _: any,
      args: { search?: string; status?: string; warehouse?: string }
    ) => {
      let filteredProducts = [...products];

      // Let's filter by search term (name, sku, or id)
      if (args.search) {
        const searchTerm = args.search.toLowerCase();
        filteredProducts = filteredProducts.filter(
          (product) =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.sku.toLowerCase().includes(searchTerm) ||
            product.id.toLowerCase().includes(searchTerm)
        );
      }

      // Let's filter by warehouse
      if (args.warehouse && args.warehouse !== "all") {
        filteredProducts = filteredProducts.filter(
          (product) => product.warehouse === args.warehouse
        );
      }

      // Let's filter by status
      if (args.status && args.status !== "all") {
        filteredProducts = filteredProducts.filter((product) => {
          const status = getProductStatus(product);
          return status.toLowerCase() === args.status!.toLowerCase();
        });
      }

      return filteredProducts;
    },

    warehouses: () => warehouses,

    kpis: (_: any, args: { range: string }) => {
      return generateKPIData(args.range);
    },
  },

  Mutation: {
    updateDemand: (_: any, args: { id: string; demand: number }) => {
      const productIndex = products.findIndex((p) => p.id === args.id);
      if (productIndex === -1) {
        throw new Error(`Product with id ${args.id} not found`);
      }

      products[productIndex] = {
        ...products[productIndex],
        demand: args.demand,
      };

      return products[productIndex];
    },

    transferStock: (
      _: any,
      args: { id: string; from: string; to: string; qty: number }
    ) => {
      const productIndex = products.findIndex((p) => p.id === args.id);
      if (productIndex === -1) {
        throw new Error(`Product with id ${args.id} not found`);
      }

      const product = products[productIndex];

      // We need to validate that the product is in the warehouse the product is being transfered 'from'
      if (product.warehouse !== args.from) {
        throw new Error(`Product is not in warehouse ${args.from}`);
      }

      // we also need to validate if we have enough stock
      if (product.stock < args.qty) {
        throw new Error(
          `Insufficient stock. Available: ${product.stock}, Requested: ${args.qty}`
        );
      }

      // Update  product
      products[productIndex] = {
        ...product,
        warehouse: args.to,
        stock: product.stock - args.qty,
      };

      return products[productIndex];
    },
  },
};
