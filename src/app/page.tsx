"use client";

import { useState } from "react";
import {
  DashboardLayout,
  useDashboard,
} from "@/components/layout/DashboardLayout";
import { KPICards } from "@/components/dashboard/KPICards";
import { StockDemandChart } from "@/components/dashboard/StockDemandChart";
import { ProductFilters } from "@/components/dashboard/ProductFilters";
import { ProductTable } from "@/components/dashboard/ProductTable";

function DashboardContent() {
  const { selectedRange } = useDashboard();
  const [search, setSearch] = useState("");
  const [warehouse, setWarehouse] = useState("");
  const [status, setStatus] = useState("");

  return (
    <div className="space-y-6">
      <KPICards />
      <StockDemandChart selectedRange={selectedRange} />
      <ProductFilters
        search={search}
        onSearchChange={setSearch}
        warehouse={warehouse}
        onWarehouseChange={setWarehouse}
        status={status}
        onStatusChange={setStatus}
      />
      <ProductTable search={search} warehouse={warehouse} status={status} />
    </div>
  );
}

export default function Home() {
  return (
    <DashboardLayout>
      <DashboardContent />
    </DashboardLayout>
  );
}
