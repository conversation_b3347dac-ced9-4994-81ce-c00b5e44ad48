"use client";

import { useQuery } from "@apollo/client/react";
import { GET_PRODUCTS } from "@/lib/graphql/queries";
import { Product } from "@/types/graphql";
import { getProductStatus, getStatusBadgeColor } from "@/utils";

interface ProductTableProps {
  search: string;
  warehouse: string;
  status: string;
}

interface ProductTableRowProps {
  product: Product;
}

function ProductTableRow({ product }: ProductTableRowProps) {
  const productStatus = getProductStatus(product);
  const tdBaseClass = "px-6 py-4 whitespace-nowrap text-sm";

  return (
    <tr key={product.id}>
      <td className={`${tdBaseClass} font-medium text-gray-900`}>
        {product.name}
      </td>
      <td className={`${tdBaseClass} text-gray-500`}>{product.sku}</td>
      <td className={`${tdBaseClass} text-gray-500`}>{product.warehouse}</td>
      <td className={`${tdBaseClass} text-gray-500`}>
        {product.stock.toLocaleString()}
      </td>
      <td className={`${tdBaseClass} text-gray-500`}>
        {product.demand.toLocaleString()}
      </td>
      <td className={`${tdBaseClass} text-gray-500`}>
        <span
          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
            productStatus
          )}`}
        >
          {productStatus}
        </span>
      </td>
    </tr>
  );
}

export function ProductTable({ search, warehouse, status }: ProductTableProps) {
  const { data, loading, error } = useQuery<{ products: Product[] }>(
    GET_PRODUCTS,
    {
      variables: { search, warehouse, status },
    }
  );

  const tableHeaders = [
    "Product",
    "SKU",
    "Warehouse",
    "Stock",
    "Demand",
    "Status",
  ];

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Products</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {tableHeaders.map((header) => (
                  <th
                    key={header}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading && (
                <tr>
                  <td
                    colSpan={6}
                    className="px-6 py-4 text-center text-gray-500"
                  >
                    Loading products...
                  </td>
                </tr>
              )}
              {error && (
                <tr>
                  <td
                    colSpan={6}
                    className="px-6 py-4 text-center text-red-500"
                  >
                    Error loading products: {error.message}
                  </td>
                </tr>
              )}
              {data?.products && data.products.length === 0 && !loading && (
                <tr>
                  <td
                    colSpan={6}
                    className="px-6 py-4 text-center text-gray-500"
                  >
                    No products found.
                  </td>
                </tr>
              )}
              {data?.products.map((product) => (
                <ProductTableRow key={product.id} product={product} />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
