"use client";

import { useQuery } from "@apollo/client/react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { GET_KPIS } from "@/lib/graphql/queries";
import { KPI } from "@/types/graphql";

interface StockDemandChartProps {
  selectedRange: string;
  className?: string;
}

export function StockDemandChart({
  selectedRange,
  className = "",
}: StockDemandChartProps) {
  const { data, loading, error } = useQuery<{
    kpis: KPI[];
  }>(GET_KPIS, {
    variables: { range: selectedRange },
  });

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Stock vs Demand Trend
        </h3>
        <div className="h-64 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Stock vs Demand Trend
        </h3>
        <div className="h-64 flex items-center justify-center">
          <p className="text-red-600">
            Error loading chart data: {error.message}
          </p>
        </div>
      </div>
    );
  }

  const kpis: KPI[] = data?.kpis || [];

  // Format data for the chart
  const chartData = kpis.map((kpi) => ({
    date: new Date(kpi.date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    }),
    stock: kpi.stock,
    demand: kpi.demand,
  }));

  return (
    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Stock vs Demand Trend
      </h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12 }}
              tickLine={{ stroke: "#6B7280" }}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={{ stroke: "#6B7280" }} />
            <Tooltip
              contentStyle={{
                backgroundColor: "#F9FAFB",
                border: "1px solid #E5E7EB",
                borderRadius: "6px",
              }}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="stock"
              stroke="#3B82F6"
              strokeWidth={2}
              name="Stock"
              dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="demand"
              stroke="#F59E0B"
              strokeWidth={2}
              name="Demand"
              dot={{ fill: "#F59E0B", strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
