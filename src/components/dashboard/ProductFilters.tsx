"use client";

import { useQuery } from "@apollo/client/react";
import { GET_WAREHOUSES } from "@/lib/graphql/queries";
import { Warehouse } from "@/types/graphql";

interface ProductFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  warehouse: string;
  onWarehouseChange: (value: string) => void;
  status: string;
  onStatusChange: (value: string) => void;
}

export function ProductFilters({
  search,
  onSearchChange,
  warehouse,
  onWarehouseChange,
  status,
  onStatusChange,
}: ProductFiltersProps) {
  const { data: warehousesData, loading: warehousesLoading } = useQuery<{
    warehouses: Warehouse[];
  }>(GET_WAREHOUSES);

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex flex-wrap gap-4">
        <input
          type="text"
          placeholder="Search products..."
          className="flex-1 min-w-64 px-3 py-2 border border-gray-300 rounded-md text-black"
          value={search}
          onChange={(e) => onSearchChange(e.target.value)}
        />
        <select
          className="px-3 py-2 border text-black border-gray-300 rounded-md"
          value={warehouse}
          onChange={(e) => onWarehouseChange(e.target.value)}
          disabled={warehousesLoading}
        >
          <option value="">All Warehouses</option>
          {warehousesData?.warehouses.map((w) => (
            <option key={w.code} value={w.code}>
              {w.name}
            </option>
          ))}
        </select>
        <select
          className="px-3 py-2 border border-gray-300 text-black rounded-md"
          value={status}
          onChange={(e) => onStatusChange(e.target.value)}
        >
          <option value="">All Status</option>
          <option value="healthy">Healthy</option>
          <option value="low">Low</option>
          <option value="critical">Critical</option>
        </select>
      </div>
    </div>
  );
}
