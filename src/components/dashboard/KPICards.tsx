"use client";

import { useQuery } from "@apollo/client/react";
import { GET_PRODUCTS } from "@/lib/graphql/queries";
import { Product } from "@/types/graphql";
import { calculateKPIs } from "@/utils";

interface KPICardProps {
  title: string;
  value: string;
  subtitle: string;
  valueClassName?: string;
}

function KPICard({ title, value, subtitle, valueClassName }: KPICardProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-sm font-medium text-gray-500 mb-2">{title}</h3>
      <p className={`text-3xl font-bold ${valueClassName}`}>{value}</p>
      <p className="text-xs text-gray-400 mt-1">{subtitle}</p>
    </div>
  );
}

interface KPICardsProps {
  className?: string;
}

export function KPICards({ className = "" }: KPICardsProps) {
  const { data, loading, error } = useQuery<{ products: Product[] }>(
    GET_PRODUCTS,
    {
      variables: {},
    }
  );

  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white p-6 rounded-lg shadow animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-16"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
        <div className="bg-red-50 p-6 rounded-lg border border-red-200 col-span-3">
          <p className="text-red-600">
            Error loading KPI data: {error.message}
          </p>
        </div>
      </div>
    );
  }

  const products: Product[] = data?.products || [];
  const kpis = calculateKPIs(products);

  const fillRateColor =
    kpis.fillRate >= 90
      ? "text-green-600"
      : kpis.fillRate >= 70
      ? "text-yellow-600"
      : "text-red-600";

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
      <KPICard
        title="Total Stock"
        value={kpis.totalStock.toLocaleString()}
        subtitle="units available"
        valueClassName="text-blue-600"
      />
      <KPICard
        title="Total Demand"
        value={kpis.totalDemand.toLocaleString()}
        subtitle="units required"
        valueClassName="text-orange-600"
      />
      <KPICard
        title="Fill Rate"
        value={`${kpis.fillRate.toFixed(1)}%`}
        subtitle="demand fulfillment"
        valueClassName={fillRateColor}
      />
    </div>
  );
}
