"use client";

import { useState } from "react";

interface HeaderProps {
  selectedRange: string;
  onRangeChange: (range: string) => void;
}

export function Header({ selectedRange, onRangeChange }: HeaderProps) {
  const dateRanges = [
    { value: "7d", label: "7d" },
    { value: "14d", label: "14d" },
    { value: "30d", label: "30d" },
  ];

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-gray-900">SupplySight</h1>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 mr-2">Date Range:</span>
          <div className="flex space-x-1">
            {dateRanges.map((range) => (
              <button
                key={range.value}
                onClick={() => onRangeChange(range.value)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedRange === range.value
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </header>
  );
}
