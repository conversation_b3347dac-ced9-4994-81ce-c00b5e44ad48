"use client";

import { useState, createContext, useContext } from "react";
import { Header } from "./Header";

interface DashboardContextType {
  selectedRange: string;
  setSelectedRange: (range: string) => void;
}

const DashboardContext = createContext<DashboardContextType | undefined>(
  undefined
);

export function useDashboard() {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error("useDashboard must be used within a DashboardLayout");
  }
  return context;
}

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [selectedRange, setSelectedRange] = useState("7d");

  return (
    <DashboardContext.Provider value={{ selectedRange, setSelectedRange }}>
      <div className="min-h-screen bg-gray-50">
        <Header
          selectedRange={selectedRange}
          onRangeChange={setSelectedRange}
        />
        <main className="p-6">{children}</main>
      </div>
    </DashboardContext.Provider>
  );
}
